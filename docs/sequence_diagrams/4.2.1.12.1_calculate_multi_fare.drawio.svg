<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than draw.io -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="5664px" height="8462px" viewBox="-0.5 -0.5 5664 8462" content="&lt;mxfile host=&quot;Electron&quot; modified=&quot;2024-12-29T19:51:03.980Z&quot; agent=&quot;Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.4.8 Chrome/124.0.6367.207 Electron/30.0.6 Safari/537.36&quot; etag=&quot;nVaOBYmiYYF51EqHQM5X&quot; version=&quot;24.4.8&quot; type=&quot;device&quot;&gt;&#10;  &lt;diagram name=&quot;Page-1&quot; id=&quot;Ki1UnvLfCiUZ55iQNjq-&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;4009&quot; dy=&quot;2480&quot; grid=&quot;1&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;1169&quot; pageHeight=&quot;827&quot; background=&quot;#ffffff&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-1&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;DynamicPricingController&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;labelBackgroundColor=none;fillColor=#dae8fc;strokeColor=#6c8ebf;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;200&quot; y=&quot;80&quot; width=&quot;270&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-2&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;-470&quot; y=&quot;160&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;5190&quot; y=&quot;160&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-3&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;DynamicPricingServiceImpl&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;labelBackgroundColor=none;fillColor=#d5e8d4;strokeColor=#82b366;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;710&quot; y=&quot;80&quot; width=&quot;270&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-10&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; parent=&quot;1&quot; source=&quot;-IJH7Msd9MsLSg7A33z7-9&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;320&quot; y=&quot;1484.800048828125&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;320&quot; y=&quot;170&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-11&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; parent=&quot;1&quot; target=&quot;-IJH7Msd9MsLSg7A33z7-9&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;320&quot; y=&quot;8280&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;320&quot; y=&quot;170&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-9&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;310&quot; y=&quot;190&quot; width=&quot;20&quot; height=&quot;7910&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-12&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;-340&quot; y=&quot;276&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;300&quot; y=&quot;276&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-13&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;Call calculation for multi-fare via &amp;lt;b&amp;gt;/v1.0/pricing/multi-fare&amp;lt;/b&amp;gt; endpoint.&amp;lt;br&amp;gt;This is handled by getMultiFare function on DynamicPricingController&amp;lt;br&amp;gt;This is usually triggered when mobile app has to show flat-fare information after the pax has entered in a pickup point and dropoff point&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-340&quot; y=&quot;180&quot; width=&quot;600&quot; height=&quot;90&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-14&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;dcp-pax-booking&amp;lt;/font&amp;gt;&quot; style=&quot;shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;labelBackgroundColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-380&quot; y=&quot;60&quot; width=&quot;30&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-15&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; parent=&quot;1&quot; source=&quot;-IJH7Msd9MsLSg7A33z7-16&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;-360&quot; y=&quot;1301.60009765625&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;-360&quot; y=&quot;170&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-17&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; parent=&quot;1&quot; target=&quot;-IJH7Msd9MsLSg7A33z7-16&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;-360&quot; y=&quot;8290&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;-360&quot; y=&quot;170&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-16&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-370&quot; y=&quot;190&quot; width=&quot;20&quot; height=&quot;1410&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-18&quot; value=&quot;&quot; style=&quot;group;labelBackgroundColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-430&quot; y=&quot;-170&quot; width=&quot;240&quot; height=&quot;110&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-4&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;LEGEND&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; parent=&quot;-IJH7Msd9MsLSg7A33z7-18&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;90&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-5&quot; value=&quot;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;labelBackgroundColor=none;fillColor=#dae8fc;strokeColor=#6c8ebf;&quot; parent=&quot;-IJH7Msd9MsLSg7A33z7-18&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry y=&quot;40&quot; width=&quot;40&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-6&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;TechFramework Classes&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; parent=&quot;-IJH7Msd9MsLSg7A33z7-18&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;50&quot; y=&quot;40&quot; width=&quot;190&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-7&quot; value=&quot;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;labelBackgroundColor=none;fillColor=#d5e8d4;strokeColor=#82b366;&quot; parent=&quot;-IJH7Msd9MsLSg7A33z7-18&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry y=&quot;80&quot; width=&quot;40&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-8&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;Application Classes&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; parent=&quot;-IJH7Msd9MsLSg7A33z7-18&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;50&quot; y=&quot;80&quot; width=&quot;160&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-19&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; parent=&quot;1&quot; edge=&quot;1&quot; source=&quot;-IJH7Msd9MsLSg7A33z7-22&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;850&quot; y=&quot;1800&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;850&quot; y=&quot;170&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-20&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;CommonUtils&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;labelBackgroundColor=none;fillColor=#d5e8d4;strokeColor=#82b366;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1169&quot; y=&quot;80&quot; width=&quot;291&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-21&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; parent=&quot;1&quot; source=&quot;-IJH7Msd9MsLSg7A33z7-26&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1310&quot; y=&quot;1460&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1310&quot; y=&quot;170&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-23&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;340&quot; y=&quot;433&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;820&quot; y=&quot;433&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-29&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;AddressServiceAdapter&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;labelBackgroundColor=none;fillColor=#dae8fc;strokeColor=#6c8ebf;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1550&quot; y=&quot;80&quot; width=&quot;291&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-30&quot; value=&quot;Postgres&quot; style=&quot;shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fontSize=20;labelBackgroundColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;4073&quot; y=&quot;50&quot; width=&quot;120&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-31&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1720&quot; y=&quot;8270&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1720&quot; y=&quot;170&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-32&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; parent=&quot;1&quot; target=&quot;-IJH7Msd9MsLSg7A33z7-26&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1310&quot; y=&quot;8270&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1310&quot; y=&quot;170&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-26&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1304.5&quot; y=&quot;744&quot; width=&quot;20&quot; height=&quot;180&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-38&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;4142&quot; y=&quot;8267&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;4142.29&quot; y=&quot;167&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-47&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;endFill=0;startArrow=classic;startFill=1;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1294&quot; y=&quot;764&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;874&quot; y=&quot;764&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-48&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Get route information by calling getRoute function&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;874&quot; y=&quot;960&quot; width=&quot;486&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-53&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;CacheServiceImpl&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;labelBackgroundColor=none;fillColor=#dae8fc;strokeColor=#6c8ebf;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1984&quot; y=&quot;80&quot; width=&quot;186&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-54&quot; value=&quot;Redis&quot; style=&quot;shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fontSize=20;labelBackgroundColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;4340&quot; y=&quot;50&quot; width=&quot;120&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-56&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; parent=&quot;1&quot; source=&quot;TGh-a3C1ltE8V3vRHCkM-28&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2084&quot; y=&quot;1800&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2075.79&quot; y=&quot;160&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-60&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;RedisServiceImpl&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;labelBackgroundColor=none;fillColor=#dae8fc;strokeColor=#6c8ebf;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2922&quot; y=&quot;80&quot; width=&quot;163&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-63&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;3001.230592485549&quot; y=&quot;5628&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2999.89&quot; y=&quot;171&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-68&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;4407.8328358208955&quot; y=&quot;1810&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;4400&quot; y=&quot;170&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-80&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;fontColor=default;strokeWidth=2;labelBackgroundColor=none;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;340&quot; y=&quot;301&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;340&quot; y=&quot;360&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;390&quot; y=&quot;301&quot; /&gt;&#10;              &lt;mxPoint x=&quot;390&quot; y=&quot;360&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;bMEIsFKa5_iYitLpxKlz-1&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Perform mapping from GetEstimatedFareInboundRequest to MultiFareRequestQuery&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;400&quot; y=&quot;300&quot; width=&quot;370&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;bMEIsFKa5_iYitLpxKlz-2&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Call getMultiFare function&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;350&quot; y=&quot;399&quot; width=&quot;380&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;bMEIsFKa5_iYitLpxKlz-3&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;fontColor=default;strokeWidth=2;labelBackgroundColor=none;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;868&quot; y=&quot;466&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;868&quot; y=&quot;525&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;918&quot; y=&quot;466&quot; /&gt;&#10;              &lt;mxPoint x=&quot;918&quot; y=&quot;525&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;bMEIsFKa5_iYitLpxKlz-4&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Perform mapping from MultiFareRequestQuery to MultiFareRequestEntity&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;928&quot; y=&quot;465&quot; width=&quot;272&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;bMEIsFKa5_iYitLpxKlz-5&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;fontColor=default;strokeWidth=2;labelBackgroundColor=none;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1325&quot; y=&quot;785&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1325&quot; y=&quot;874&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;1375&quot; y=&quot;785&quot; /&gt;&#10;              &lt;mxPoint x=&quot;1375&quot; y=&quot;874&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;bMEIsFKa5_iYitLpxKlz-6&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Generate tripId by concatenating an UUID appended by Date in YYYYMMDDHHMMSS format&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1385&quot; y=&quot;789&quot; width=&quot;272&quot; height=&quot;90&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;bMEIsFKa5_iYitLpxKlz-7&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;endFill=1;startArrow=none;startFill=0;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1289&quot; y=&quot;914&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;869&quot; y=&quot;914&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;bMEIsFKa5_iYitLpxKlz-8&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return unique tripID&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;874&quot; y=&quot;874&quot; width=&quot;206&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-1&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;fontColor=default;strokeWidth=2;labelBackgroundColor=none;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;868&quot; y=&quot;563&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;868&quot; y=&quot;650&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;918&quot; y=&quot;563&quot; /&gt;&#10;              &lt;mxPoint x=&quot;920&quot; y=&quot;650&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-2&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Perform validation on the following fields in MultiFareRequestEntity:&amp;lt;br&amp;gt;- BookingChannel&amp;lt;br&amp;gt;- JobType&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;928&quot; y=&quot;560&quot; width=&quot;332&quot; height=&quot;90&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-3&quot; value=&quot;ngp-me-address-svc&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fontSize=20;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;4630&quot; y=&quot;70&quot; width=&quot;200&quot; height=&quot;70&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-4&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;4720&quot; y=&quot;1043.9999999999995&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;4720&quot; y=&quot;170&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-5&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; target=&quot;-IJH7Msd9MsLSg7A33z7-22&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;850&quot; y=&quot;8270&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;850&quot; y=&quot;170&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-22&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;840&quot; y=&quot;250&quot; width=&quot;20&quot; height=&quot;7780&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-7&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; target=&quot;TGh-a3C1ltE8V3vRHCkM-6&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;4720&quot; y=&quot;8270&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;4720&quot; y=&quot;170&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-6&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;4710&quot; y=&quot;1044&quot; width=&quot;20&quot; height=&quot;126&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-8&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;fontSize=12;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;870&quot; y=&quot;1000&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1700&quot; y=&quot;1000&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-9&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1710&quot; y=&quot;950&quot; width=&quot;20&quot; height=&quot;270&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-10&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Call generateTripId with curent Date.now() to generate a unique tripId.&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;884&quot; y=&quot;704&quot; width=&quot;310&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-11&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;fontSize=12;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1740&quot; y=&quot;1059&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;4700&quot; y=&quot;1070&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-12&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Retrieve Route Information for me-address-service.&amp;lt;br&amp;gt;This is done by calling the &amp;lt;b&amp;gt;&amp;lt;font color=&amp;quot;#2130ff&amp;quot;&amp;gt;/v1.0/address/generate-route&amp;lt;/font&amp;gt;&amp;lt;/b&amp;gt; endpoint&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1740&quot; y=&quot;1005&quot; width=&quot;670&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-13&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;4690&quot; y=&quot;1150&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1740&quot; y=&quot;1140&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-14&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return List of RouteInfo, containing distance and estimated duration&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1740&quot; y=&quot;1093&quot; width=&quot;670&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-15&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1700&quot; y=&quot;1190&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;870&quot; y=&quot;1190&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-16&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return shortest route&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;874&quot; y=&quot;1140&quot; width=&quot;216&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-17&quot; value=&quot;ngp-me-fare-svc&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fontSize=20;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;4932&quot; y=&quot;65&quot; width=&quot;200&quot; height=&quot;70&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-18&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;TGh-a3C1ltE8V3vRHCkM-168&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;5050&quot; y=&quot;6950&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;5040&quot; y=&quot;173&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-20&quot; value=&quot;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-430&quot; y=&quot;-50&quot; width=&quot;40&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-21&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;External Components&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-380&quot; y=&quot;-50&quot; width=&quot;160&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-22&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1302&quot; y=&quot;1241&quot; width=&quot;20&quot; height=&quot;159&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-23&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;870&quot; y=&quot;1280&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1290&quot; y=&quot;1280&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-24&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Generate FareId using RequestTime and mobileNo via generateFareId function&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;874&quot; y=&quot;1230&quot; width=&quot;406&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-25&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;endFill=1;startArrow=none;startFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1294&quot; y=&quot;1360&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;874&quot; y=&quot;1360&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-27&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return unique fareId in the format of UUID-RequestTime-MobileNo&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;874&quot; y=&quot;1310&quot; width=&quot;370&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-31&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;TGh-a3C1ltE8V3vRHCkM-135&quot; target=&quot;TGh-a3C1ltE8V3vRHCkM-28&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2080&quot; y=&quot;6960&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2075.79&quot; y=&quot;160&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-28&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2070&quot; y=&quot;1697&quot; width=&quot;20&quot; height=&quot;3463&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-32&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;874&quot; y=&quot;1750&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2064&quot; y=&quot;1750&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-33&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Retrieve CommonConfigSet from Cache via&amp;amp;nbsp;&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;background-color: initial;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;&amp;lt;b style=&amp;quot;color: rgb(33, 48, 255);&amp;quot;&amp;gt;DYNAMIC_PRICING:FLAT_FARE:COMMON_CONFIG_SET&amp;lt;/b&amp;gt;&amp;lt;font color=&amp;quot;#2130ff&amp;quot;&amp;gt;&amp;amp;nbsp;or&amp;lt;/font&amp;gt;&amp;lt;br&amp;gt;&amp;lt;font style=&amp;quot;&amp;quot; color=&amp;quot;#2130ff&amp;quot;&amp;gt;&amp;lt;b&amp;gt;DYNAMIC_PRICING:COMMON:FLAT_FARE:COMMON_CONFIG_SET &amp;lt;/b&amp;gt;i&amp;lt;/font&amp;gt;f the fomer is not available&amp;lt;/font&amp;gt;&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- limoFlatFareVehIds&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- estLimoFlatFareVehIds&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- estFareVehIds&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- dynamicPricingVehIds&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- advanceVehIds&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- vehGrpShowMeterOnly&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- vehGroShowFFOnly&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- cacheTimerMinsMultiFlareFare&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- cacheTimerMinsBreakdownFlatFare&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- driverSurgeLevelIndications&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- surgeIndicatorThreshold&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- surgeIndicatorThresholdZero&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;&amp;lt;br&amp;gt;&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;874&quot; y=&quot;1430&quot; width=&quot;1016&quot; height=&quot;300&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-36&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2991.25&quot; y=&quot;1740&quot; width=&quot;20&quot; height=&quot;180&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-37&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2100&quot; y=&quot;1770&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2980&quot; y=&quot;1770&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-38&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Retrieve Data via getValue() function&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2100&quot; y=&quot;1720&quot; width=&quot;406&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-40&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; target=&quot;TGh-a3C1ltE8V3vRHCkM-39&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;4410&quot; y=&quot;8270&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;3958&quot; y=&quot;170&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-39&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;4398.75&quot; y=&quot;1810&quot; width=&quot;20&quot; height=&quot;70&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-41&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;3022.75&quot; y=&quot;1820&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;4390&quot; y=&quot;1820&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-42&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Retrieve data using redisTemplate.opsForValue() function&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3022.75&quot; y=&quot;1770&quot; width=&quot;540&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-44&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;3022.75&quot; y=&quot;1870&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;4390&quot; y=&quot;1870&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-45&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return CommonConfigSet&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3022.75&quot; y=&quot;1825&quot; width=&quot;270&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-47&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2100&quot; y=&quot;1897.97&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2980&quot; y=&quot;1898&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-48&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return CommonConfigSet&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2110&quot; y=&quot;1850&quot; width=&quot;290&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-49&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;874&quot; y=&quot;1930&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2064&quot; y=&quot;1930&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-50&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return CommonConfigSet&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;874&quot; y=&quot;1880&quot; width=&quot;290&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-51&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;874&quot; y=&quot;2410&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2064&quot; y=&quot;2410&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-52&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Retrieve limoConfigSet (FlatFareConfigSet::class) from Cache via&amp;amp;nbsp;&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;background-color: initial;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;&amp;lt;b style=&amp;quot;color: rgb(33, 48, 255);&amp;quot;&amp;gt;DYNAMIC_PRICING:FLAT_FARE:LIMO_CONFIG_SET&amp;lt;/b&amp;gt;&amp;lt;font color=&amp;quot;#2130ff&amp;quot;&amp;gt;&amp;amp;nbsp;or&amp;lt;/font&amp;gt;&amp;lt;br&amp;gt;&amp;lt;font style=&amp;quot;&amp;quot; color=&amp;quot;#2130ff&amp;quot;&amp;gt;&amp;lt;b&amp;gt;DYNAMIC_PRICING:COMMON:FLAT_FARE:LIMO_CONFIG_SET &amp;lt;/b&amp;gt;i&amp;lt;/font&amp;gt;f the fomer is not available&amp;lt;/font&amp;gt;&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- prefixKey&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- peakHoursRates&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- midnightHoursRates&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- singleConfigs&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- flatdownRate&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- tier1Fare&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- tier2Fare&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- estimateRateConfig&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- durationUnitConfig&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- durationRateConfig&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- maxFlatFareCap&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- multiStopSurcharge&amp;lt;br&amp;gt;&amp;amp;nbsp;- locationSurchargeConfigList&amp;lt;br&amp;gt;&amp;amp;nbsp;- holidayList&amp;lt;br&amp;gt;&amp;amp;nbsp;- bookingFeeList&amp;lt;br&amp;gt;&amp;amp;nbsp;- additionalChargeList&amp;lt;br&amp;gt;&amp;amp;nbsp;- eventSurgeAddressConfigList&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;884&quot; y=&quot;1990&quot; width=&quot;1016&quot; height=&quot;410&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-53&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2991.5&quot; y=&quot;2432&quot; width=&quot;20&quot; height=&quot;180&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-54&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2100&quot; y=&quot;2460&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2980&quot; y=&quot;2460&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-55&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Retrieve Data via getValue() function&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2100&quot; y=&quot;2410&quot; width=&quot;406&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-56&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;4399&quot; y=&quot;2490&quot; width=&quot;20&quot; height=&quot;70&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-57&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;3023&quot; y=&quot;2500&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;4390&quot; y=&quot;2500&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-58&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Retrieve data using redisTemplate.opsForValue() function&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3023&quot; y=&quot;2450&quot; width=&quot;540&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-59&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;3023&quot; y=&quot;2550&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;4380&quot; y=&quot;2550&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-60&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return limoConfigSet (FlatFareConfigSet::class)&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3023&quot; y=&quot;2505&quot; width=&quot;500&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-61&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2100&quot; y=&quot;2597.9700000000003&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2980&quot; y=&quot;2598&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-62&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return limoConfigSet (FlatFareConfigSet::class)&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2100&quot; y=&quot;2545&quot; width=&quot;440&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-64&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;874&quot; y=&quot;2662&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2064&quot; y=&quot;2662&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-65&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return limoConfigSet (FlatFareConfigSet::class)&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;874&quot; y=&quot;2612&quot; width=&quot;476&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-66&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Retrieve standardConfigSet (FlatFareConfigSet::class) from Cache via&amp;amp;nbsp;&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;background-color: initial;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;&amp;lt;b style=&amp;quot;color: rgb(33, 48, 255);&amp;quot;&amp;gt;DYNAMIC_PRICING:FLAT_FARE:STANDARD_CONFIG_SET&amp;lt;/b&amp;gt;&amp;lt;font color=&amp;quot;#2130ff&amp;quot;&amp;gt;&amp;amp;nbsp;or&amp;lt;/font&amp;gt;&amp;lt;br&amp;gt;&amp;lt;font style=&amp;quot;&amp;quot; color=&amp;quot;#2130ff&amp;quot;&amp;gt;&amp;lt;b&amp;gt;DYNAMIC_PRICING:COMMON:FLAT_FARE:STANDARD_CONFIG_SET &amp;lt;/b&amp;gt;i&amp;lt;/font&amp;gt;f the fomer is not available&amp;lt;/font&amp;gt;&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- prefixKey&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- peakHoursRates&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- midnightHoursRates&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- singleConfigs&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- flatdownRate&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- tier1Fare&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- tier2Fare&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- estimateRateConfig&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- durationUnitConfig&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- durationRateConfig&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- maxFlatFareCap&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- multiStopSurcharge&amp;lt;br&amp;gt;&amp;amp;nbsp;- locationSurchargeConfigList&amp;lt;br&amp;gt;&amp;amp;nbsp;- holidayList&amp;lt;br&amp;gt;&amp;amp;nbsp;- bookingFeeList&amp;lt;br&amp;gt;&amp;amp;nbsp;- additionalChargeList&amp;lt;br&amp;gt;&amp;amp;nbsp;- eventSurgeAddressConfigList&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;884&quot; y=&quot;2690&quot; width=&quot;1016&quot; height=&quot;410&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-67&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2989.5&quot; y=&quot;3162&quot; width=&quot;20&quot; height=&quot;180&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-68&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2100&quot; y=&quot;3190&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2980&quot; y=&quot;3190&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-69&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Retrieve Data via getValue() function&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2100&quot; y=&quot;3140&quot; width=&quot;350&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-70&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;4397&quot; y=&quot;3220&quot; width=&quot;20&quot; height=&quot;70&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-71&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;3021&quot; y=&quot;3230&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;4390&quot; y=&quot;3230&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-72&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Retrieve data using redisTemplate.opsForValue() function&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3021&quot; y=&quot;3180&quot; width=&quot;540&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-73&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;3021&quot; y=&quot;3280&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;4390&quot; y=&quot;3280&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-74&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return standardConfigSet (FlatFareConfigSet::class)&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3021&quot; y=&quot;3235&quot; width=&quot;500&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-75&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2100&quot; y=&quot;3327.9700000000003&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2980&quot; y=&quot;3330&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-76&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return standardConfigSet (FlatFareConfigSet::class)&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2100&quot; y=&quot;3275&quot; width=&quot;270&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-77&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;874&quot; y=&quot;3110&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2064&quot; y=&quot;3110&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-78&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;874&quot; y=&quot;3370&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2064&quot; y=&quot;3370&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-79&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return standardConfigSet (FlatFareConfigSet::class)&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;874&quot; y=&quot;3320&quot; width=&quot;476&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-80&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Retrieve estStandardConfigSet (FlatFareConfigSet::class) from Cache via&amp;amp;nbsp;&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;background-color: initial;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;&amp;lt;b style=&amp;quot;color: rgb(33, 48, 255);&amp;quot;&amp;gt;DYNAMIC_PRICING:FLAT_FARE:EST_STANDARD_CONFIG_SET&amp;lt;/b&amp;gt;&amp;lt;font color=&amp;quot;#2130ff&amp;quot;&amp;gt;&amp;amp;nbsp;or&amp;lt;/font&amp;gt;&amp;lt;br&amp;gt;&amp;lt;font style=&amp;quot;&amp;quot; color=&amp;quot;#2130ff&amp;quot;&amp;gt;&amp;lt;b&amp;gt;DYNAMIC_PRICING:COMMON:FLAT_FARE:EST_STANDARD_CONFIG_SET &amp;lt;/b&amp;gt;i&amp;lt;/font&amp;gt;f the fomer is not available&amp;lt;/font&amp;gt;&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- prefixKey&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- peakHoursRates&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- midnightHoursRates&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- singleConfigs&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- flatdownRate&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- tier1Fare&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- tier2Fare&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- estimateRateConfig&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- durationUnitConfig&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- durationRateConfig&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- maxFlatFareCap&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- multiStopSurcharge&amp;lt;br&amp;gt;&amp;amp;nbsp;- locationSurchargeConfigList&amp;lt;br&amp;gt;&amp;amp;nbsp;- holidayList&amp;lt;br&amp;gt;&amp;amp;nbsp;- bookingFeeList&amp;lt;br&amp;gt;&amp;amp;nbsp;- additionalChargeList&amp;lt;br&amp;gt;&amp;amp;nbsp;- eventSurgeAddressConfigList&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;884&quot; y=&quot;3390&quot; width=&quot;1016&quot; height=&quot;410&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-81&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;874&quot; y=&quot;3810&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2064&quot; y=&quot;3810&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-82&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2992.5&quot; y=&quot;3832&quot; width=&quot;20&quot; height=&quot;180&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-83&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2100&quot; y=&quot;3860&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2980&quot; y=&quot;3860&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-84&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Retrieve Data via getValue() function&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2100&quot; y=&quot;3810&quot; width=&quot;330&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-85&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;4400&quot; y=&quot;3890&quot; width=&quot;20&quot; height=&quot;70&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-86&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;3024&quot; y=&quot;3900&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;4390&quot; y=&quot;3900&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-87&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Retrieve data using redisTemplate.opsForValue() function&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3024&quot; y=&quot;3850&quot; width=&quot;540&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-88&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;3024&quot; y=&quot;3950&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;4390&quot; y=&quot;3950&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-89&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return estStandardConfigSet (FlatFareConfigSet::class)&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3024&quot; y=&quot;3905&quot; width=&quot;620&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-90&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2100&quot; y=&quot;3997.9700000000003&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2980&quot; y=&quot;3998&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-91&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return estStandardConfigSet (FlatFareConfigSet::class)&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2100&quot; y=&quot;3945&quot; width=&quot;290&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-92&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;874&quot; y=&quot;4040&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2064&quot; y=&quot;4040&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-93&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return estStandardConfigSet (FlatFareConfigSet::class)&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;874&quot; y=&quot;3990&quot; width=&quot;576&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-94&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Retrieve fareTypeConfigSet(DynamicPricingConfigSet::class) from Cache via&amp;amp;nbsp;&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;background-color: initial;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;&amp;lt;b style=&amp;quot;color: rgb(33, 48, 255);&amp;quot;&amp;gt;DYNAMIC_PRICING:FLAT_FARE:FARE_TYPE_CONFIG_SET&amp;lt;/b&amp;gt;&amp;lt;font color=&amp;quot;#2130ff&amp;quot;&amp;gt;&amp;amp;nbsp;or&amp;lt;/font&amp;gt;&amp;lt;br&amp;gt;&amp;lt;font style=&amp;quot;&amp;quot; color=&amp;quot;#2130ff&amp;quot;&amp;gt;&amp;lt;b&amp;gt;DYNAMIC_PRICING:COMMON:FLAT_FARE:FARE_TYPE_CONFIG_SET &amp;lt;/b&amp;gt;i&amp;lt;/font&amp;gt;f the fomer is not available&amp;lt;/font&amp;gt;&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- desurgeMaxCapConfigList&amp;lt;br&amp;gt;&amp;amp;nbsp;- minSurgeAmountConfigList&amp;lt;br&amp;gt;&amp;amp;nbsp;- minCapConfigList&amp;lt;br&amp;gt;&amp;amp;nbsp;- maxCapConfigList&amp;lt;br&amp;gt;&amp;amp;nbsp;- surgeBufferConfigList&amp;lt;br&amp;gt;&amp;amp;nbsp;- flagDownConfigList&amp;lt;br&amp;gt;&amp;amp;nbsp;- durationRateConfigList&amp;lt;br&amp;gt;&amp;amp;nbsp;- tier!PriceMultiplierConfigList&amp;lt;br&amp;gt;&amp;amp;nbsp;- tier1StartDestConfigList&amp;lt;br&amp;gt;&amp;amp;nbsp;- tier1EndDestConfigList&amp;lt;br&amp;gt;&amp;amp;nbsp;- tier2PriceMultiplierConfigList&amp;lt;br&amp;gt;&amp;amp;nbsp;- tier2STartDestConfigList&amp;lt;br&amp;gt;&amp;amp;nbsp;- teir2EndDestConfigList&amp;lt;br&amp;gt;&amp;amp;nbsp;- bookingFee&amp;lt;br&amp;gt;&amp;amp;nbsp;- hourlySurcharge&amp;lt;br&amp;gt;&amp;amp;nbsp;- holidayList&amp;lt;br&amp;gt;&amp;amp;nbsp;-multiStopSurcharge&amp;lt;br&amp;gt;&amp;amp;nbsp;- eventSurgeAddressConfigList&amp;lt;br&amp;gt;&amp;amp;nbsp;- locationSurchargeConfigList&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;884&quot; y=&quot;4080&quot; width=&quot;1016&quot; height=&quot;440&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-95&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;874&quot; y=&quot;4528&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2064&quot; y=&quot;4528&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-96&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2991.5&quot; y=&quot;4542&quot; width=&quot;20&quot; height=&quot;180&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-97&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2100&quot; y=&quot;4570&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2980&quot; y=&quot;4570&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-98&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Retrieve Data via getValue() function&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2100&quot; y=&quot;4520&quot; width=&quot;340&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-99&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;4399&quot; y=&quot;4600&quot; width=&quot;20&quot; height=&quot;70&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-100&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;3023&quot; y=&quot;4610&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;4390&quot; y=&quot;4610&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-101&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Retrieve data using redisTemplate.opsForValue() function&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3023&quot; y=&quot;4560&quot; width=&quot;540&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-102&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;3023&quot; y=&quot;4660&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;4390&quot; y=&quot;4660&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-103&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return fareTypeConfigSet (DynamicPricingConfigSet::class)&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3023&quot; y=&quot;4615&quot; width=&quot;620&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-104&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2100&quot; y=&quot;4707.97&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2980&quot; y=&quot;4708&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-105&quot; value=&quot;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return fareTypeConfigSet (DynamicPricingConfigSet::class)&amp;lt;/span&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2100&quot; y=&quot;4655&quot; width=&quot;310&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-106&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;874&quot; y=&quot;4730&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2064&quot; y=&quot;4730&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-107&quot; value=&quot;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return fareTypeConfigSet (DynamicPricingConfigSet::class)&amp;lt;/span&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;874&quot; y=&quot;4680&quot; width=&quot;576&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-108&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Retrieve holidayList (flatFareHoliday::class) from Cache via&amp;amp;nbsp;&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;background-color: initial;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;&amp;lt;b style=&amp;quot;color: rgb(33, 48, 255);&amp;quot;&amp;gt;DYNAMIC_PRICING:COMPANY_HOLIDAY&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp;- date&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;884&quot; y=&quot;4770&quot; width=&quot;1016&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-109&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;874&quot; y=&quot;4870&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2064&quot; y=&quot;4870&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-118&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2990.5&quot; y=&quot;4882&quot; width=&quot;20&quot; height=&quot;180&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-119&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2100&quot; y=&quot;4910&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2980&quot; y=&quot;4910&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-120&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Retrieve Data via getListValue() function&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2100&quot; y=&quot;4860&quot; width=&quot;370&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-121&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;4398&quot; y=&quot;4940&quot; width=&quot;20&quot; height=&quot;70&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-122&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;3022&quot; y=&quot;4950&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;4390&quot; y=&quot;4950&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-123&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Retrieve data using redisTemplate.opsForList() function&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3022&quot; y=&quot;4900&quot; width=&quot;540&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-124&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;3022&quot; y=&quot;5000&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;4380&quot; y=&quot;5000&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-125&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return List&amp;amp;lt;FlatFareHoliday&amp;amp;gt;&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3022&quot; y=&quot;4955&quot; width=&quot;620&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-126&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2100&quot; y=&quot;5047.97&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2980&quot; y=&quot;5048&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-127&quot; value=&quot;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return List&amp;amp;lt;FlatFareHoliday&amp;amp;gt;&amp;lt;/span&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2100&quot; y=&quot;4995&quot; width=&quot;290&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-128&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;874&quot; y=&quot;5090&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2064&quot; y=&quot;5090&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-129&quot; value=&quot;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return List&amp;amp;lt;FlatFareHoliday&amp;amp;gt;&amp;lt;/span&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;874&quot; y=&quot;5040&quot; width=&quot;576&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-130&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;LocationSurchargeServiceImpl&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;labelBackgroundColor=none;fillColor=#dae8fc;strokeColor=#6c8ebf;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2400&quot; y=&quot;80&quot; width=&quot;300&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-131&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2530&quot; y=&quot;8280&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2520&quot; y=&quot;165&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-132&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2518&quot; y=&quot;5368&quot; width=&quot;20&quot; height=&quot;552&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-133&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;870&quot; y=&quot;5396&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2510&quot; y=&quot;5396&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-134&quot; value=&quot;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Get List&amp;amp;lt;LocationSurchargeConfig&amp;amp;gt; via getLocationSurchargeConfigList method&amp;lt;/span&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;span style=&amp;quot;&amp;quot;&amp;gt;&amp;amp;nbsp;- fareType&amp;lt;br&amp;gt;&amp;amp;nbsp;- applicableDays&amp;lt;br&amp;gt;&amp;amp;nbsp;- startTime&amp;lt;br&amp;gt;&amp;amp;nbsp;- endTime&amp;lt;br&amp;gt;&amp;amp;nbsp;- chargeBy&amp;lt;br&amp;gt;&amp;amp;nbsp;- surchargeValue&amp;lt;br&amp;gt;&amp;amp;nbsp;- locationId&amp;lt;br&amp;gt;&amp;amp;nbsp;- locationName&amp;lt;br&amp;gt;&amp;amp;nbsp;addressRef&amp;lt;br&amp;gt;&amp;amp;nbsp;- zoneId&amp;lt;br&amp;gt;&amp;amp;nbsp;- productId&amp;lt;br&amp;gt;&amp;amp;nbsp;- dayIndicator&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;874&quot; y=&quot;5110&quot; width=&quot;836&quot; height=&quot;270&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-136&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;TGh-a3C1ltE8V3vRHCkM-255&quot; target=&quot;TGh-a3C1ltE8V3vRHCkM-135&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2090&quot; y=&quot;8270&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2080&quot; y=&quot;5160&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-135&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2065&quot; y=&quot;5441&quot; width=&quot;20&quot; height=&quot;429&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-137&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2510&quot; y=&quot;5596&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2100&quot; y=&quot;5596&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-138&quot; value=&quot;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Retrieve locationSurcharge config list via the following key:&amp;lt;br&amp;gt;&amp;lt;br&amp;gt;&amp;lt;font color=&amp;quot;#2130ff&amp;quot;&amp;gt;&amp;lt;b&amp;gt;DYNAMIC_PRICING:LOC_SURC:{DAY_OF_WEEK}:{ADDRESS_PART}&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&amp;lt;/span&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2100&quot; y=&quot;5456&quot; width=&quot;370&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-139&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2090&quot; y=&quot;5858&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2510&quot; y=&quot;5858&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-140&quot; value=&quot;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return List&amp;amp;lt;LocationSurchargeConfig&amp;amp;gt;&amp;lt;/span&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2100&quot; y=&quot;5808&quot; width=&quot;350&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-142&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; target=&quot;TGh-a3C1ltE8V3vRHCkM-141&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;3000&quot; y=&quot;8270&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2999.89&quot; y=&quot;175&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-141&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2991.25&quot; y=&quot;5632&quot; width=&quot;20&quot; height=&quot;158&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-143&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2100&quot; y=&quot;5668.17&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2980&quot; y=&quot;5668.17&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-144&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Retrieve Data via getListValue() function&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2100&quot; y=&quot;5618.17&quot; width=&quot;370&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-145&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;4400&quot; y=&quot;5700&quot; width=&quot;20&quot; height=&quot;70&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-146&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;3024&quot; y=&quot;5710&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;4390&quot; y=&quot;5720&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-147&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Retrieve data using redisTemplate.opsForList() function&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3024&quot; y=&quot;5660&quot; width=&quot;540&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-148&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;3024&quot; y=&quot;5760&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;4390&quot; y=&quot;5760&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-149&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return List&amp;amp;lt;LocationSurchargeConfig&amp;amp;gt;&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3024&quot; y=&quot;5715&quot; width=&quot;620&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-150&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2090&quot; y=&quot;5780&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2970&quot; y=&quot;5780.03&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-151&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return List&amp;amp;lt;LocationSurchargeConfig&amp;amp;gt;&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2100&quot; y=&quot;5730&quot; width=&quot;620&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-152&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;FareServiceAdapter&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;labelBackgroundColor=none;fillColor=#dae8fc;strokeColor=#6c8ebf;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3250&quot; y=&quot;80&quot; width=&quot;210&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-153&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;FareOutboundAdapter&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;labelBackgroundColor=none;fillColor=#dae8fc;strokeColor=#6c8ebf;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3610&quot; y=&quot;80&quot; width=&quot;210&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-154&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;TGh-a3C1ltE8V3vRHCkM-207&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;3354.23&quot; y=&quot;8269&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;3354.52&quot; y=&quot;169&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-155&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;TGh-a3C1ltE8V3vRHCkM-164&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;3720&quot; y=&quot;8267&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;3720.29&quot; y=&quot;167&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-156&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3345&quot; y=&quot;6101&quot; width=&quot;20&quot; height=&quot;199&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-157&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;870&quot; y=&quot;6113&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;3340&quot; y=&quot;6120&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-158&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;870&quot; y=&quot;5885&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2512&quot; y=&quot;5885&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-159&quot; value=&quot;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return List&amp;amp;lt;LocationSurchargeConfig&amp;amp;gt;&amp;lt;/span&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;874&quot; y=&quot;5840&quot; width=&quot;350&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-160&quot; value=&quot;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Get BookingFeeList configuration via getBookingFeeByList method&amp;lt;/span&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;span style=&amp;quot;&amp;quot;&amp;gt;&amp;amp;nbsp;- vehicleTypeId&amp;lt;br&amp;gt;&amp;amp;nbsp;- productId&amp;lt;br&amp;gt;&amp;amp;nbsp;- flatFareType&amp;lt;br&amp;gt;&amp;amp;nbsp;- bookingFee&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;874&quot; y=&quot;5990&quot; width=&quot;836&quot; height=&quot;110&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-165&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; target=&quot;TGh-a3C1ltE8V3vRHCkM-164&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;3720&quot; y=&quot;8267&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;3720.29&quot; y=&quot;167&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-164&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3720&quot; y=&quot;6149&quot; width=&quot;20&quot; height=&quot;111&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-166&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;3370&quot; y=&quot;6159&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;3710&quot; y=&quot;6160&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-167&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Retrieve BookingFeeList via getBookingFeeByList function&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3374&quot; y=&quot;6110&quot; width=&quot;270&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-169&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; target=&quot;TGh-a3C1ltE8V3vRHCkM-168&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;5060&quot; y=&quot;8270&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;5040&quot; y=&quot;173&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-168&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;5040&quot; y=&quot;6172&quot; width=&quot;20&quot; height=&quot;70&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-170&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;3750&quot; y=&quot;6190&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;5030&quot; y=&quot;6190&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-171&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Retrieve BookingFeeList via &amp;lt;b&amp;gt;&amp;lt;font color=&amp;quot;#2130ff&amp;quot;&amp;gt;/v1.0/fares/booking-fee-list&amp;lt;/font&amp;gt;&amp;lt;/b&amp;gt; endpoint&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3750&quot; y=&quot;6140&quot; width=&quot;640&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-172&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;3750&quot; y=&quot;6230&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;5030&quot; y=&quot;6230&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-173&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return BookingFeeList&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3750&quot; y=&quot;6187&quot; width=&quot;640&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-174&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;3374&quot; y=&quot;6250&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;3714&quot; y=&quot;6251&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-175&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return BookingFeeList&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3374&quot; y=&quot;6203&quot; width=&quot;236&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-176&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;870&quot; y=&quot;6270&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;3340&quot; y=&quot;6277&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-177&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return BookingFeeList&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;874&quot; y=&quot;6227&quot; width=&quot;236&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-178&quot; value=&quot;&quot; style=&quot;shape=curlyBracket;whiteSpace=wrap;html=1;rounded=1;labelPosition=left;verticalLabelPosition=middle;align=right;verticalAlign=middle;strokeWidth=4;fillColor=#d5e8d4;strokeColor=#82b366;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;790&quot; y=&quot;950&quot; width=&quot;20&quot; height=&quot;260&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-179&quot; value=&quot;&amp;lt;font color=&amp;quot;#006600&amp;quot; style=&amp;quot;font-size: 24px;&amp;quot;&amp;gt;Get Route Information from me-address-service&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;480&quot; y=&quot;1045&quot; width=&quot;272&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-180&quot; value=&quot;&quot; style=&quot;shape=curlyBracket;whiteSpace=wrap;html=1;rounded=1;labelPosition=left;verticalLabelPosition=middle;align=right;verticalAlign=middle;strokeWidth=4;fillColor=#d5e8d4;strokeColor=#82b366;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;790&quot; y=&quot;1220&quot; width=&quot;20&quot; height=&quot;180&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-182&quot; value=&quot;&amp;lt;font color=&amp;quot;#006600&amp;quot; style=&amp;quot;font-size: 24px;&amp;quot;&amp;gt;Generate Unique FareId&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;480&quot; y=&quot;1280&quot; width=&quot;272&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-184&quot; value=&quot;&quot; style=&quot;shape=curlyBracket;whiteSpace=wrap;html=1;rounded=1;labelPosition=left;verticalLabelPosition=middle;align=right;verticalAlign=middle;strokeWidth=4;fillColor=#d5e8d4;strokeColor=#82b366;size=0.5;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;790&quot; y=&quot;1420&quot; width=&quot;20&quot; height=&quot;540&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-185&quot; value=&quot;&amp;lt;font color=&amp;quot;#006600&amp;quot; style=&amp;quot;font-size: 24px;&amp;quot;&amp;gt;Retrieve CommonConfigSet from Redis Cache&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;490&quot; y=&quot;1650&quot; width=&quot;272&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-186&quot; value=&quot;&quot; style=&quot;shape=curlyBracket;whiteSpace=wrap;html=1;rounded=1;labelPosition=left;verticalLabelPosition=middle;align=right;verticalAlign=middle;strokeWidth=4;fillColor=#d5e8d4;strokeColor=#82b366;size=0.5;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;790&quot; y=&quot;1980&quot; width=&quot;20&quot; height=&quot;700&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-187&quot; value=&quot;&amp;lt;font color=&amp;quot;#006600&amp;quot; style=&amp;quot;font-size: 24px;&amp;quot;&amp;gt;Retrieve LimoConfiguration from Redis Cache&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;470&quot; y=&quot;2300&quot; width=&quot;272&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-188&quot; value=&quot;&quot; style=&quot;shape=curlyBracket;whiteSpace=wrap;html=1;rounded=1;labelPosition=left;verticalLabelPosition=middle;align=right;verticalAlign=middle;strokeWidth=4;fillColor=#d5e8d4;strokeColor=#82b366;size=0.5;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;790&quot; y=&quot;2690&quot; width=&quot;20&quot; height=&quot;700&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-189&quot; value=&quot;&amp;lt;font color=&amp;quot;#006600&amp;quot; style=&amp;quot;font-size: 24px;&amp;quot;&amp;gt;Retrieve StandardConfiguration from Redis Cache&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;480&quot; y=&quot;3020&quot; width=&quot;272&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-191&quot; value=&quot;&quot; style=&quot;shape=curlyBracket;whiteSpace=wrap;html=1;rounded=1;labelPosition=left;verticalLabelPosition=middle;align=right;verticalAlign=middle;strokeWidth=4;fillColor=#d5e8d4;strokeColor=#82b366;size=0.5;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;790&quot; y=&quot;3400&quot; width=&quot;20&quot; height=&quot;660&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-192&quot; value=&quot;&amp;lt;font color=&amp;quot;#006600&amp;quot; style=&amp;quot;font-size: 24px;&amp;quot;&amp;gt;Retrieve Estimated StandardConfiguration from Redis Cache&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;470&quot; y=&quot;3700&quot; width=&quot;272&quot; height=&quot;90&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-193&quot; value=&quot;&quot; style=&quot;shape=curlyBracket;whiteSpace=wrap;html=1;rounded=1;labelPosition=left;verticalLabelPosition=middle;align=right;verticalAlign=middle;strokeWidth=4;fillColor=#d5e8d4;strokeColor=#82b366;size=0.5;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;790&quot; y=&quot;4070&quot; width=&quot;20&quot; height=&quot;690&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-194&quot; value=&quot;&amp;lt;font color=&amp;quot;#006600&amp;quot; style=&amp;quot;font-size: 24px;&amp;quot;&amp;gt;Retrieve FareType Configuration from Redis Cache&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;470&quot; y=&quot;4380&quot; width=&quot;272&quot; height=&quot;90&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-195&quot; value=&quot;&quot; style=&quot;shape=curlyBracket;whiteSpace=wrap;html=1;rounded=1;labelPosition=left;verticalLabelPosition=middle;align=right;verticalAlign=middle;strokeWidth=4;fillColor=#d5e8d4;strokeColor=#82b366;size=0.5;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;790&quot; y=&quot;4770&quot; width=&quot;20&quot; height=&quot;330&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-196&quot; value=&quot;&amp;lt;font color=&amp;quot;#006600&amp;quot; style=&amp;quot;font-size: 24px;&amp;quot;&amp;gt;Retrieve Holiday Surcharge from Redis Cache&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;470&quot; y=&quot;4905&quot; width=&quot;272&quot; height=&quot;90&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-197&quot; value=&quot;&quot; style=&quot;shape=curlyBracket;whiteSpace=wrap;html=1;rounded=1;labelPosition=left;verticalLabelPosition=middle;align=right;verticalAlign=middle;strokeWidth=4;fillColor=#d5e8d4;strokeColor=#82b366;size=0.5;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;790&quot; y=&quot;5111&quot; width=&quot;20&quot; height=&quot;799&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-198&quot; value=&quot;&amp;lt;font color=&amp;quot;#006600&amp;quot; style=&amp;quot;font-size: 24px;&amp;quot;&amp;gt;Retrieve Location Surcharge from Redis Cache&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;480&quot; y=&quot;5471&quot; width=&quot;272&quot; height=&quot;90&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-199&quot; value=&quot;&quot; style=&quot;shape=curlyBracket;whiteSpace=wrap;html=1;rounded=1;labelPosition=left;verticalLabelPosition=middle;align=right;verticalAlign=middle;strokeWidth=4;fillColor=#d5e8d4;strokeColor=#82b366;size=0.5;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;790&quot; y=&quot;5920&quot; width=&quot;20&quot; height=&quot;370&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-200&quot; value=&quot;&amp;lt;font color=&amp;quot;#006600&amp;quot; style=&amp;quot;font-size: 24px;&amp;quot;&amp;gt;Retrieve BookingFee Configuration from me-fare-service&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;490&quot; y=&quot;6060&quot; width=&quot;272&quot; height=&quot;90&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-201&quot; value=&quot;&quot; style=&quot;shape=curlyBracket;whiteSpace=wrap;html=1;rounded=1;labelPosition=left;verticalLabelPosition=middle;align=right;verticalAlign=middle;strokeWidth=4;fillColor=#d5e8d4;strokeColor=#82b366;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;790&quot; y=&quot;700&quot; width=&quot;20&quot; height=&quot;240&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-202&quot; value=&quot;&amp;lt;font color=&amp;quot;#006600&amp;quot; style=&amp;quot;font-size: 24px;&amp;quot;&amp;gt;Generate Unique TripId&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;498&quot; y=&quot;800&quot; width=&quot;272&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-205&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;865&quot; y=&quot;6659&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;3335&quot; y=&quot;6666&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-206&quot; value=&quot;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Get PlatformFeeList configuration via getPlatformFeeByList method&amp;lt;/span&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;span style=&amp;quot;&amp;quot;&amp;gt;&amp;amp;nbsp;- id&amp;lt;br&amp;gt;&amp;amp;nbsp;- bookingChannel&amp;lt;br&amp;gt;&amp;amp;nbsp;- vehicleGroupId&amp;lt;br&amp;gt;&amp;amp;nbsp;- productId&amp;lt;br&amp;gt;&amp;amp;nbsp;- platformFeeApplicability&amp;lt;br&amp;gt;&amp;amp;nbsp;- remarks&amp;lt;br&amp;gt;&amp;amp;nbsp;- platformFeeThresholdLimit&amp;lt;br&amp;gt;&amp;amp;nbsp;- lowerPlatformFee&amp;lt;br&amp;gt;&amp;amp;nbsp;- upperPlatformFee&amp;lt;br&amp;gt;&amp;amp;nbsp;- effectiveFrom&amp;lt;br&amp;gt;&amp;amp;nbsp;- effectiveTo&amp;lt;br&amp;gt;&amp;amp;nbsp;- deleted&amp;lt;br&amp;gt;&amp;amp;nbsp;- createdDate&amp;lt;br&amp;gt;&amp;amp;nbsp;- createdBy&amp;lt;br&amp;gt;&amp;amp;nbsp;- updatedDate&amp;lt;br&amp;gt;&amp;amp;nbsp;- updatedBy&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;874&quot; y=&quot;6310.5&quot; width=&quot;626&quot; height=&quot;339.5&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-208&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; target=&quot;TGh-a3C1ltE8V3vRHCkM-207&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;3354.23&quot; y=&quot;8269&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;3354.52&quot; y=&quot;169&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-207&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3345&quot; y=&quot;6640&quot; width=&quot;20&quot; height=&quot;199&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-220&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3720&quot; y=&quot;6679&quot; width=&quot;20&quot; height=&quot;111&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-221&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;3370&quot; y=&quot;6689&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;3710&quot; y=&quot;6690&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-222&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Retrieve PlatformFeeList via getPlatformFeeByList function&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3374&quot; y=&quot;6640&quot; width=&quot;270&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-223&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;5040&quot; y=&quot;6702&quot; width=&quot;20&quot; height=&quot;70&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-224&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;3750&quot; y=&quot;6720&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;5030&quot; y=&quot;6720&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-225&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Retrieve PlatformFeeList via &amp;lt;font color=&amp;quot;#2130ff&amp;quot;&amp;gt;&amp;lt;b&amp;gt;/v1.0/fares/getPlatformFeeByList&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt; endpoint&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3750&quot; y=&quot;6670&quot; width=&quot;790&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-226&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;3750&quot; y=&quot;6760&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;5030&quot; y=&quot;6760&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-227&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return PlatformFeeList&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3750&quot; y=&quot;6717&quot; width=&quot;640&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-228&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;3374&quot; y=&quot;6780&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;3714&quot; y=&quot;6781&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-229&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return PlatformFeeList&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3374&quot; y=&quot;6733&quot; width=&quot;236&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-230&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;870&quot; y=&quot;6807&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;3340&quot; y=&quot;6814&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-231&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return BookingFeeList&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;874&quot; y=&quot;6764&quot; width=&quot;236&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-232&quot; value=&quot;&quot; style=&quot;shape=curlyBracket;whiteSpace=wrap;html=1;rounded=1;labelPosition=left;verticalLabelPosition=middle;align=right;verticalAlign=middle;strokeWidth=4;fillColor=#d5e8d4;strokeColor=#82b366;size=0.5;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;790&quot; y=&quot;6309&quot; width=&quot;20&quot; height=&quot;511&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-233&quot; value=&quot;&amp;lt;font color=&amp;quot;#006600&amp;quot; style=&amp;quot;font-size: 24px;&amp;quot;&amp;gt;Retrieve PlatformFee Configuration from me-fare-service&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;480&quot; y=&quot;6519.5&quot; width=&quot;272&quot; height=&quot;90&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-234&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;866&quot; y=&quot;7001.5&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;3336&quot; y=&quot;7008.5&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-235&quot; value=&quot;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Get AdditionalChargeFeeConfigList via getAdditionalChargeFeeConfigMap method&amp;lt;/span&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;span style=&amp;quot;&amp;quot;&amp;gt;&amp;amp;nbsp;- chargeId&amp;lt;br&amp;gt;&amp;amp;nbsp;- chargeType&amp;lt;br&amp;gt;&amp;amp;nbsp;- chargeKey&amp;lt;br&amp;gt;&amp;amp;nbsp;- chargeValue&amp;lt;br&amp;gt;&amp;amp;nbsp;- chargeDescription&amp;lt;br&amp;gt;&amp;amp;nbsp;- chargeFormula&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;875&quot; y=&quot;6839&quot; width=&quot;775&quot; height=&quot;151&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-236&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3345&quot; y=&quot;7000&quot; width=&quot;20&quot; height=&quot;199&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-237&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3720&quot; y=&quot;7039&quot; width=&quot;20&quot; height=&quot;111&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-238&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;3370&quot; y=&quot;7049&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;3710&quot; y=&quot;7050&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-239&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Retrieve AdditionalChargeFeeConfigList via getAdditionalChargeFeeConfigMap function&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3374&quot; y=&quot;6939&quot; width=&quot;326&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-240&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;5040&quot; y=&quot;7062&quot; width=&quot;20&quot; height=&quot;70&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-241&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;3750&quot; y=&quot;7080&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;5030&quot; y=&quot;7080&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-242&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Retrieve AdditionalChargeFeeConfigList via&amp;lt;font color=&amp;quot;#2130ff&amp;quot;&amp;gt;&amp;lt;b&amp;gt; /v1.0/fares/addtional-charge-fees&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt; endpoint&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3750&quot; y=&quot;7030&quot; width=&quot;930&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-243&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;3750&quot; y=&quot;7120&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;5030&quot; y=&quot;7120&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-244&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return PlatformFeeList&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3750&quot; y=&quot;7077&quot; width=&quot;640&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-245&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;3374&quot; y=&quot;7140&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;3714&quot; y=&quot;7141&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-246&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return AdditionalChargeFeeConfigList&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3374&quot; y=&quot;7093&quot; width=&quot;346&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-247&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;870&quot; y=&quot;7163&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;3340&quot; y=&quot;7170&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-249&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return AdditionalChargeFeeConfigList&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;874&quot; y=&quot;7110&quot; width=&quot;346&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-250&quot; value=&quot;&quot; style=&quot;shape=curlyBracket;whiteSpace=wrap;html=1;rounded=1;labelPosition=left;verticalLabelPosition=middle;align=right;verticalAlign=middle;strokeWidth=4;fillColor=#d5e8d4;strokeColor=#82b366;size=0.5;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;800&quot; y=&quot;7220&quot; width=&quot;20&quot; height=&quot;620&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-252&quot; value=&quot;&amp;lt;font color=&amp;quot;#006600&amp;quot; style=&amp;quot;font-size: 24px;&amp;quot;&amp;gt;Retrieve AdditionalChargeFee Configuration from me-fare-service&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;480&quot; y=&quot;6960&quot; width=&quot;272&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-253&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;870&quot; y=&quot;7210&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;870&quot; y=&quot;7340&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;900&quot; y=&quot;7210&quot; /&gt;&#10;              &lt;mxPoint x=&quot;900&quot; y=&quot;7340&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-254&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Calculate the estimated flatfare for each vehicleTypeId specified in the request.&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;This includes dynamic-pricing, surcharges and optional driver fees. The results are then prepared as fare breakdowns.&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;909&quot; y=&quot;7210&quot; width=&quot;406&quot; height=&quot;130&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-256&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;TGh-a3C1ltE8V3vRHCkM-268&quot; target=&quot;TGh-a3C1ltE8V3vRHCkM-255&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2090&quot; y=&quot;8270&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2076&quot; y=&quot;5870&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-255&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2080&quot; y=&quot;7409&quot; width=&quot;20&quot; height=&quot;171&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-257&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;870&quot; y=&quot;7443&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2070&quot; y=&quot;7443&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-258&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Cache FareBreakdown using the following key: &amp;lt;font color=&amp;quot;#2130ff&amp;quot;&amp;gt;DYNAMIC_PRICING:BREAKDOWN:{fareId}-{vehicleTypeId} &amp;lt;/font&amp;gt;via setValue function call.&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;875&quot; y=&quot;7370&quot; width=&quot;552&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-259&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2991.25&quot; y=&quot;7443&quot; width=&quot;20&quot; height=&quot;127&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-260&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2109.5&quot; y=&quot;7490&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2989.5&quot; y=&quot;7490&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-261&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Set value for key using setValue function&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2109.5&quot; y=&quot;7440&quot; width=&quot;370&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-262&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;4400&quot; y=&quot;7520&quot; width=&quot;20&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-263&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;3024&quot; y=&quot;7530&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;4391&quot; y=&quot;7530&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-264&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Set data using redisTemplate.setValue() function&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3024&quot; y=&quot;7480&quot; width=&quot;540&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-266&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;870&quot; y=&quot;7623&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2070&quot; y=&quot;7623&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-267&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Cache MultiFare using the following key: &amp;lt;font color=&amp;quot;#2130ff&amp;quot;&amp;gt;DYNAMIC_PRICING:MULTI_FARE:{fareId}&amp;amp;nbsp;&amp;lt;/font&amp;gt;via setValue function call.&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;875&quot; y=&quot;7550&quot; width=&quot;552&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-269&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; target=&quot;TGh-a3C1ltE8V3vRHCkM-268&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2090&quot; y=&quot;8270&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2090&quot; y=&quot;7580&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-268&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2080&quot; y=&quot;7600&quot; width=&quot;20&quot; height=&quot;171&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-276&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2990.25&quot; y=&quot;7603&quot; width=&quot;20&quot; height=&quot;127&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-277&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2108.5&quot; y=&quot;7650&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2988.5&quot; y=&quot;7650&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-278&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Set value for key using setValue function&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2108.5&quot; y=&quot;7600&quot; width=&quot;370&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-279&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;4399&quot; y=&quot;7680&quot; width=&quot;20&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-280&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;3023&quot; y=&quot;7690&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;4390&quot; y=&quot;7690&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-281&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Set data using redisTemplate.setValue() function&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3023&quot; y=&quot;7640&quot; width=&quot;540&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-283&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;870&quot; y=&quot;7833&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2070&quot; y=&quot;7833&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-284&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Cache Route using the following key: &amp;lt;font color=&amp;quot;#2130ff&amp;quot;&amp;gt;DYNAMIC_PRICING:ROUTE:{tripId}&amp;amp;nbsp;&amp;lt;/font&amp;gt;via setValue function call.&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;875&quot; y=&quot;7760&quot; width=&quot;585&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-285&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2080&quot; y=&quot;7810&quot; width=&quot;20&quot; height=&quot;171&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-286&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2991.25&quot; y=&quot;7813&quot; width=&quot;20&quot; height=&quot;127&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-287&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2109.5&quot; y=&quot;7860&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2989.5&quot; y=&quot;7860&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-288&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Set value for key using setValue function&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2109.5&quot; y=&quot;7810&quot; width=&quot;370&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-289&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;4400&quot; y=&quot;7890&quot; width=&quot;20&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-290&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;3024&quot; y=&quot;7900&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;4391&quot; y=&quot;7900&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-291&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Set data using redisTemplate.setValue() function&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3024&quot; y=&quot;7850&quot; width=&quot;540&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-292&quot; value=&quot;&quot; style=&quot;shape=curlyBracket;whiteSpace=wrap;html=1;rounded=1;labelPosition=left;verticalLabelPosition=middle;align=right;verticalAlign=middle;strokeWidth=4;fillColor=#d5e8d4;strokeColor=#82b366;size=0.5;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;800&quot; y=&quot;6840&quot; width=&quot;20&quot; height=&quot;360&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-293&quot; value=&quot;&amp;lt;font color=&amp;quot;#006600&amp;quot; style=&amp;quot;font-size: 24px;&amp;quot;&amp;gt;Loop through each vehicleTypeId and cache the following info:&amp;lt;/font&amp;gt;&amp;lt;div&amp;gt;&amp;lt;font color=&amp;quot;#006600&amp;quot;&amp;gt;&amp;lt;span style=&amp;quot;font-size: 24px;&amp;quot;&amp;gt;&amp;amp;nbsp;- fareBreakdown&amp;lt;br&amp;gt;&amp;amp;nbsp;- multiFare&amp;lt;br&amp;gt;&amp;amp;nbsp;- route&amp;lt;/span&amp;gt;&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;470&quot; y=&quot;7470&quot; width=&quot;272&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-294&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;820&quot; y=&quot;7960&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;350&quot; y=&quot;7960&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;TGh-a3C1ltE8V3vRHCkM-295&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return MultiFareResponse&amp;lt;/span&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;360&quot; y=&quot;7895&quot; width=&quot;370&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;&lt;/mxfile&gt;&#10;" style="background-color: rgb(255, 255, 255);"><defs/><rect fill="#ffffff" width="100%" height="100%" x="0" y="0"/><g><g><rect x="671" y="250" width="270" height="60" rx="9" ry="9" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 268px; height: 1px; padding-top: 280px; margin-left: 672px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">DynamicPricingController</font></div></div></div></foreignObject><text x="806" y="284" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">DynamicPricingController</text></switch></g></g><g><path d="M 1 330 L 5661 330" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/></g><g><rect x="1181" y="250" width="270" height="60" rx="9" ry="9" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 268px; height: 1px; padding-top: 280px; margin-left: 1182px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">DynamicPricingServiceImpl</font></div></div></div></foreignObject><text x="1316" y="284" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">DynamicPricingServiceImpl</text></switch></g></g><g><path d="M 791 360 L 791 340" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><path d="M 791 8450 L 791 8270" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><rect x="781" y="360" width="20" height="7910" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 131 446 L 762.76 446" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 768.76 446 L 760.76 450 L 762.76 446 L 760.76 442 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="131" y="350" width="600" height="90" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 598px; height: 1px; padding-top: 395px; margin-left: 133px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 16px;">Call calculation for multi-fare via <b>/v1.0/pricing/multi-fare</b> endpoint.<br />This is handled by getMultiFare function on DynamicPricingController<br />This is usually triggered when mobile app has to show flat-fare information after the pax has entered in a pickup point and dropoff point</font></div></div></div></foreignObject><text x="133" y="399" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Call calculation for multi-fare via /v1.0/pricing/multi-fare endpoint....</text></switch></g></g><g><ellipse cx="106" cy="237.5" rx="7.499999999999999" ry="7.499999999999999" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><path d="M 106 245 L 106 270 M 106 250 L 91 250 M 106 250 L 121 250 M 106 270 L 91 290 M 106 270 L 121 290" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 297px; margin-left: 106px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;"><font style="font-size: 20px;">dcp-pax-booking</font></div></div></div></foreignObject><text x="106" y="309" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">dcp-p...</text></switch></g></g><g><path d="M 111 360 L 111 340" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><path d="M 111 8460 L 111 1770" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><rect x="101" y="360" width="20" height="1410" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g/><g><rect x="41" y="0" width="90" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 88px; height: 1px; padding-top: 15px; margin-left: 43px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">LEGEND</font></div></div></div></foreignObject><text x="43" y="19" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">LEGEND</text></switch></g></g><g><rect x="41" y="40" width="40" height="30" rx="4.5" ry="4.5" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/></g><g><rect x="91" y="40" width="190" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 188px; height: 1px; padding-top: 55px; margin-left: 93px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 16px;">TechFramework Classes</font></div></div></div></foreignObject><text x="93" y="59" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">TechFramework Classes</text></switch></g></g><g><rect x="41" y="80" width="40" height="30" rx="4.5" ry="4.5" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/></g><g><rect x="91" y="80" width="160" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 158px; height: 1px; padding-top: 95px; margin-left: 93px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 16px;">Application Classes</font></div></div></div></foreignObject><text x="93" y="99" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Application Classes</text></switch></g></g><g><path d="M 1321 420 L 1321 340" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><rect x="1640" y="250" width="291" height="60" rx="9" ry="9" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 289px; height: 1px; padding-top: 280px; margin-left: 1641px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">CommonUtils</font></div></div></div></foreignObject><text x="1786" y="284" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">CommonUtils</text></switch></g></g><g><path d="M 1784.89 914 L 1781 340" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><path d="M 811 603 L 1282.76 603" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1288.76 603 L 1280.76 607 L 1282.76 603 L 1280.76 599 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="2021" y="250" width="291" height="60" rx="9" ry="9" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 289px; height: 1px; padding-top: 280px; margin-left: 2022px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">AddressServiceAdapter</font></div></div></div></foreignObject><text x="2167" y="284" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">AddressServiceAdapter</text></switch></g></g><g><path d="M 4544 235 C 4544 226.72 4570.86 220 4604 220 C 4619.91 220 4635.17 221.58 4646.43 224.39 C 4657.68 227.21 4664 231.02 4664 235 L 4664 305 C 4664 308.98 4657.68 312.79 4646.43 315.61 C 4635.17 318.42 4619.91 320 4604 320 C 4570.86 320 4544 313.28 4544 305 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 4664 235 C 4664 238.98 4657.68 242.79 4646.43 245.61 C 4635.17 248.42 4619.91 250 4604 250 C 4570.86 250 4544 243.28 4544 235" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 284px; margin-left: 4545px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Postgres</div></div></div></foreignObject><text x="4604" y="290" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="20px" text-anchor="middle">Postgres</text></switch></g></g><g><path d="M 2191 8440 L 2191 340" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><path d="M 1781 8440 L 1785.45 1094" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><rect x="1775.5" y="914" width="20" height="180" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 4613 8437 L 4613.29 337" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><path d="M 1756.76 934 L 1345 934" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1762.76 934 L 1754.76 938 L 1756.76 934 L 1754.76 930 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1345" y="1130" width="486" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 484px; height: 1px; padding-top: 1150px; margin-left: 1347px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><font style="font-size: 20px;">Get route information by calling getRoute function</font></div></div></div></div></foreignObject><text x="1347" y="1154" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Get route information by calling getRoute function</text></switch></g></g><g><rect x="2455" y="250" width="186" height="60" rx="9" ry="9" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 184px; height: 1px; padding-top: 280px; margin-left: 2456px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">CacheServiceImpl</font></div></div></div></foreignObject><text x="2548" y="284" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">CacheServiceImpl</text></switch></g></g><g><path d="M 4811 235 C 4811 226.72 4837.86 220 4871 220 C 4886.91 220 4902.17 221.58 4913.43 224.39 C 4924.68 227.21 4931 231.02 4931 235 L 4931 305 C 4931 308.98 4924.68 312.79 4913.43 315.61 C 4902.17 318.42 4886.91 320 4871 320 C 4837.86 320 4811 313.28 4811 305 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 4931 235 C 4931 238.98 4924.68 242.79 4913.43 245.61 C 4902.17 248.42 4886.91 250 4871 250 C 4837.86 250 4811 243.28 4811 235" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 284px; margin-left: 4812px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Redis</div></div></div></foreignObject><text x="4871" y="290" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="20px" text-anchor="middle">Redis</text></switch></g></g><g><path d="M 2548.77 1867 L 2546.79 330" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><rect x="3393" y="250" width="163" height="60" rx="9" ry="9" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 161px; height: 1px; padding-top: 280px; margin-left: 3394px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">RedisServiceImpl</font></div></div></div></foreignObject><text x="3474" y="284" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">RedisServiceImpl</text></switch></g></g><g><path d="M 3472.23 5798 L 3470.89 341" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><path d="M 4878.83 1980 L 4871 340" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><path d="M 811 471 L 861 471 L 861 530 L 819.24 530" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 813.24 530 L 821.24 526 L 819.24 530 L 821.24 534 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="871" y="470" width="370" height="60" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 368px; height: 1px; padding-top: 500px; margin-left: 873px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Perform mapping from GetEstimatedFareInboundRequest to MultiFareRequestQuery</font></div></div></div></foreignObject><text x="873" y="504" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Perform mapping from GetEstimatedFareInboundRequest to MultiFa...</text></switch></g></g><g><rect x="821" y="569" width="380" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 378px; height: 1px; padding-top: 584px; margin-left: 823px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Call getMultiFare function</font></div></div></div></foreignObject><text x="823" y="588" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Call getMultiFare function</text></switch></g></g><g><path d="M 1339 636 L 1389 636 L 1389 695 L 1347.24 695" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1341.24 695 L 1349.24 691 L 1347.24 695 L 1349.24 699 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1399" y="635" width="272" height="60" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 270px; height: 1px; padding-top: 665px; margin-left: 1401px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Perform mapping from MultiFareRequestQuery to MultiFareRequestEntity</font></div></div></div></foreignObject><text x="1401" y="669" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Perform mapping from MultiFareRequestQuery to...</text></switch></g></g><g><path d="M 1796 955 L 1846 955 L 1846 1044 L 1804.24 1044" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1798.24 1044 L 1806.24 1040 L 1804.24 1044 L 1806.24 1048 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1856" y="959" width="272" height="90" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 270px; height: 1px; padding-top: 1004px; margin-left: 1858px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Generate tripId by concatenating an UUID appended by Date in YYYYMMDDHHMMSS format</font></div></div></div></foreignObject><text x="1858" y="1008" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Generate tripId by concatenating an UUID appe...</text></switch></g></g><g><path d="M 1760 1084 L 1348.24 1084" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1342.24 1084 L 1350.24 1080 L 1348.24 1084 L 1350.24 1088 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1345" y="1044" width="206" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 204px; height: 1px; padding-top: 1059px; margin-left: 1347px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Return unique tripID</font></div></div></div></foreignObject><text x="1347" y="1063" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return unique tripID</text></switch></g></g><g><path d="M 1339 733 L 1389 733 L 1391 820 L 1347.24 820" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1341.24 820 L 1349.24 816 L 1347.24 820 L 1349.24 824 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1399" y="730" width="332" height="90" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 330px; height: 1px; padding-top: 775px; margin-left: 1401px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Perform validation on the following fields in MultiFareRequestEntity:<br />- BookingChannel<br />- JobType</font></div></div></div></foreignObject><text x="1401" y="779" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Perform validation on the following fields in MultiFare...</text></switch></g></g><g><rect x="5101" y="240" width="200" height="70" rx="10.5" ry="10.5" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 198px; height: 1px; padding-top: 275px; margin-left: 5102px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">ngp-me-address-svc</div></div></div></foreignObject><text x="5201" y="281" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="20px" text-anchor="middle">ngp-me-address-svc</text></switch></g></g><g><path d="M 5191 1214 L 5191 340" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><path d="M 1321 8440 L 1321 8200" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><rect x="1311" y="420" width="20" height="7780" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 5191 8440 L 5191 1340" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><rect x="5181" y="1214" width="20" height="126" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 1341 1170 L 2162.76 1170" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2168.76 1170 L 2160.76 1174 L 2162.76 1170 L 2160.76 1166 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="2181" y="1120" width="20" height="270" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><rect x="1355" y="874" width="310" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 308px; height: 1px; padding-top: 894px; margin-left: 1357px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><font style="font-size: 20px;">Call generateTripId with curent Date.now() to generate a unique tripId.</font></div></div></div></div></foreignObject><text x="1357" y="898" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Call generateTripId with curent Date.now() to gener...</text></switch></g></g><g><path d="M 2211 1229 L 5162.76 1239.97" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 5168.76 1239.99 L 5160.75 1243.96 L 5162.76 1239.97 L 5160.78 1235.96 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="2211" y="1175" width="670" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 668px; height: 1px; padding-top: 1195px; margin-left: 2213px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><font style="font-size: 20px;">Retrieve Route Information for me-address-service.<br />This is done by calling the <b><font color="#2130ff">/v1.0/address/generate-route</font></b> endpoint</font></div></div></div></div></foreignObject><text x="2213" y="1199" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve Route Information for me-address-service....</text></switch></g></g><g><path d="M 5161 1320 L 2219.24 1310.03" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2213.24 1310.01 L 2221.25 1306.03 L 2219.24 1310.03 L 2221.22 1314.03 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="2211" y="1263" width="670" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 668px; height: 1px; padding-top: 1283px; margin-left: 2213px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Return List of RouteInfo, containing distance and estimated duration</span></div></div></div></div></foreignObject><text x="2213" y="1287" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return List of RouteInfo, containing distance and estimated duration</text></switch></g></g><g><path d="M 2171 1360 L 1349.24 1360" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1343.24 1360 L 1351.24 1356 L 1349.24 1360 L 1351.24 1364 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1345" y="1310" width="216" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 214px; height: 1px; padding-top: 1330px; margin-left: 1347px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Return shortest route</span></div></div></div></div></foreignObject><text x="1347" y="1334" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return shortest route</text></switch></g></g><g><rect x="5403" y="235" width="200" height="70" rx="10.5" ry="10.5" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 198px; height: 1px; padding-top: 270px; margin-left: 5404px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">ngp-me-fare-svc</div></div></div></foreignObject><text x="5503" y="276" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="20px" text-anchor="middle">ngp-me-fare-svc</text></switch></g></g><g><path d="M 5520.94 6342 L 5511 343" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><rect x="41" y="120" width="40" height="30" rx="4.5" ry="4.5" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><rect x="91" y="120" width="160" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 158px; height: 1px; padding-top: 135px; margin-left: 93px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 16px;">External Components</font></div></div></div></foreignObject><text x="93" y="139" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">External Components</text></switch></g></g><g><rect x="1773" y="1411" width="20" height="159" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 1341 1450 L 1752.76 1450" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1758.76 1450 L 1750.76 1454 L 1752.76 1450 L 1750.76 1446 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1345" y="1400" width="406" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 404px; height: 1px; padding-top: 1420px; margin-left: 1347px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Generate FareId using RequestTime and mobileNo via generateFareId function</span></div></div></div></div></foreignObject><text x="1347" y="1424" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Generate FareId using RequestTime and mobileNo via generateFareId f...</text></switch></g></g><g><path d="M 1765 1530 L 1353.24 1530" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1347.24 1530 L 1355.24 1526 L 1353.24 1530 L 1355.24 1534 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1345" y="1480" width="370" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 368px; height: 1px; padding-top: 1495px; margin-left: 1347px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Return unique fareId in the format of UUID-RequestTime-MobileNo</font></div></div></div></foreignObject><text x="1347" y="1499" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return unique fareId in the format of UUID-RequestTime-MobileNo</text></switch></g></g><g><path d="M 2546.48 5611 L 2547.11 5330" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><rect x="2541" y="1867" width="20" height="3463" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 1345 1920 L 2526.76 1920" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2532.76 1920 L 2524.76 1924 L 2526.76 1920 L 2524.76 1916 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1345" y="1600" width="1016" height="300" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1014px; height: 1px; padding-top: 1750px; margin-left: 1347px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Retrieve CommonConfigSet from Cache via </span></div><div><span style="background-color: initial;"><font style="font-size: 20px;"><b style="color: rgb(33, 48, 255);">DYNAMIC_PRICING:FLAT_FARE:COMMON_CONFIG_SET</b><font color="#2130ff"> or</font><br /><font color="#2130ff" style=""><b>DYNAMIC_PRICING:COMMON:FLAT_FARE:COMMON_CONFIG_SET </b>i</font>f the fomer is not available</font></span></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - limoFlatFareVehIds</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - estLimoFlatFareVehIds</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - estFareVehIds</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - dynamicPricingVehIds</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - advanceVehIds</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - vehGrpShowMeterOnly</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - vehGroShowFFOnly</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - cacheTimerMinsMultiFlareFare</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - cacheTimerMinsBreakdownFlatFare</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - driverSurgeLevelIndications</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - surgeIndicatorThreshold</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - surgeIndicatorThresholdZero</font></div><div><span style="font-size: 20px;"><br /></span></div></div></div></div></foreignObject><text x="1347" y="1754" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve CommonConfigSet from Cache via...</text></switch></g></g><g><rect x="3462.25" y="1910" width="20" height="180" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 2571 1940 L 3442.76 1940" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 3448.76 1940 L 3440.76 1944 L 3442.76 1940 L 3440.76 1936 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="2571" y="1890" width="406" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 404px; height: 1px; padding-top: 1910px; margin-left: 2573px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Retrieve Data via getValue() function</span></div></div></div></div></foreignObject><text x="2573" y="1914" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve Data via getValue() function</text></switch></g></g><g><path d="M 4881 8440 L 4879.76 2050" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><rect x="4869.75" y="1980" width="20" height="70" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 3493.75 1990 L 4852.76 1990" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 4858.76 1990 L 4850.76 1994 L 4852.76 1990 L 4850.76 1986 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="3493.75" y="1940" width="540" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 538px; height: 1px; padding-top: 1960px; margin-left: 3496px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Retrieve data using redisTemplate.opsForValue() function</span></div></div></div></div></foreignObject><text x="3496" y="1964" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve data using redisTemplate.opsForValue() function</text></switch></g></g><g><path d="M 3501.99 2040 L 4861 2040" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 3495.99 2040 L 3503.99 2036 L 3501.99 2040 L 3503.99 2044 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="3493.75" y="1995" width="270" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 268px; height: 1px; padding-top: 2015px; margin-left: 3496px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Return CommonConfigSet</span></div></div></div></div></foreignObject><text x="3496" y="2019" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return CommonConfigSet</text></switch></g></g><g><path d="M 2579.24 2067.97 L 3451 2068" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2573.24 2067.97 L 2581.24 2063.97 L 2579.24 2067.97 L 2581.24 2071.97 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="2581" y="2020" width="290" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 288px; height: 1px; padding-top: 2040px; margin-left: 2583px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Return CommonConfigSet</span></div></div></div></div></foreignObject><text x="2583" y="2044" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return CommonConfigSet</text></switch></g></g><g><path d="M 1353.24 2100 L 2535 2100" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1347.24 2100 L 1355.24 2096 L 1353.24 2100 L 1355.24 2104 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1345" y="2050" width="290" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 288px; height: 1px; padding-top: 2070px; margin-left: 1347px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Return CommonConfigSet</span></div></div></div></div></foreignObject><text x="1347" y="2074" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return CommonConfigSet</text></switch></g></g><g><path d="M 1345 2580 L 2526.76 2580" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2532.76 2580 L 2524.76 2584 L 2526.76 2580 L 2524.76 2576 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1355" y="2160" width="1016" height="410" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1014px; height: 1px; padding-top: 2365px; margin-left: 1357px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Retrieve limoConfigSet (FlatFareConfigSet::class) from Cache via </span></div><div><span style="background-color: initial;"><font style="font-size: 20px;"><b style="color: rgb(33, 48, 255);">DYNAMIC_PRICING:FLAT_FARE:LIMO_CONFIG_SET</b><font color="#2130ff"> or</font><br /><font color="#2130ff" style=""><b>DYNAMIC_PRICING:COMMON:FLAT_FARE:LIMO_CONFIG_SET </b>i</font>f the fomer is not available</font></span></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - prefixKey</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - peakHoursRates</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - midnightHoursRates</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - singleConfigs</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - flatdownRate</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - tier1Fare</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - tier2Fare</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - estimateRateConfig</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - durationUnitConfig</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - durationRateConfig</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - maxFlatFareCap</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - multiStopSurcharge<br /> - locationSurchargeConfigList<br /> - holidayList<br /> - bookingFeeList<br /> - additionalChargeList<br /> - eventSurgeAddressConfigList</font></div></div></div></div></foreignObject><text x="1357" y="2369" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve limoConfigSet (FlatFareConfigSet::class) from Cache via...</text></switch></g></g><g><rect x="3462.5" y="2602" width="20" height="180" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 2571 2630 L 3442.76 2630" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 3448.76 2630 L 3440.76 2634 L 3442.76 2630 L 3440.76 2626 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="2571" y="2580" width="406" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 404px; height: 1px; padding-top: 2600px; margin-left: 2573px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Retrieve Data via getValue() function</span></div></div></div></div></foreignObject><text x="2573" y="2604" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve Data via getValue() function</text></switch></g></g><g><rect x="4870" y="2660" width="20" height="70" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 3494 2670 L 4852.76 2670" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 4858.76 2670 L 4850.76 2674 L 4852.76 2670 L 4850.76 2666 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="3494" y="2620" width="540" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 538px; height: 1px; padding-top: 2640px; margin-left: 3496px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Retrieve data using redisTemplate.opsForValue() function</span></div></div></div></div></foreignObject><text x="3496" y="2644" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve data using redisTemplate.opsForValue() function</text></switch></g></g><g><path d="M 3502.24 2720 L 4851 2720" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 3496.24 2720 L 3504.24 2716 L 3502.24 2720 L 3504.24 2724 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="3494" y="2675" width="500" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 498px; height: 1px; padding-top: 2695px; margin-left: 3496px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Return limoConfigSet (FlatFareConfigSet::class)</span></div></div></div></div></foreignObject><text x="3496" y="2699" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return limoConfigSet (FlatFareConfigSet::class)</text></switch></g></g><g><path d="M 2579.24 2767.97 L 3451 2768" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2573.24 2767.97 L 2581.24 2763.97 L 2579.24 2767.97 L 2581.24 2771.97 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="2571" y="2715" width="440" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 438px; height: 1px; padding-top: 2735px; margin-left: 2573px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Return limoConfigSet (FlatFareConfigSet::class)</span></div></div></div></div></foreignObject><text x="2573" y="2739" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return limoConfigSet (FlatFareConfigSet::class)</text></switch></g></g><g><path d="M 1353.24 2832 L 2535 2832" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1347.24 2832 L 1355.24 2828 L 1353.24 2832 L 1355.24 2836 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1345" y="2782" width="476" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 474px; height: 1px; padding-top: 2802px; margin-left: 1347px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Return limoConfigSet (FlatFareConfigSet::class)</span></div></div></div></div></foreignObject><text x="1347" y="2806" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return limoConfigSet (FlatFareConfigSet::class)</text></switch></g></g><g><rect x="1355" y="2860" width="1016" height="410" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1014px; height: 1px; padding-top: 3065px; margin-left: 1357px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Retrieve standardConfigSet (FlatFareConfigSet::class) from Cache via </span></div><div><span style="background-color: initial;"><font style="font-size: 20px;"><b style="color: rgb(33, 48, 255);">DYNAMIC_PRICING:FLAT_FARE:STANDARD_CONFIG_SET</b><font color="#2130ff"> or</font><br /><font color="#2130ff" style=""><b>DYNAMIC_PRICING:COMMON:FLAT_FARE:STANDARD_CONFIG_SET </b>i</font>f the fomer is not available</font></span></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - prefixKey</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - peakHoursRates</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - midnightHoursRates</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - singleConfigs</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - flatdownRate</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - tier1Fare</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - tier2Fare</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - estimateRateConfig</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - durationUnitConfig</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - durationRateConfig</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - maxFlatFareCap</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - multiStopSurcharge<br /> - locationSurchargeConfigList<br /> - holidayList<br /> - bookingFeeList<br /> - additionalChargeList<br /> - eventSurgeAddressConfigList</font></div></div></div></div></foreignObject><text x="1357" y="3069" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve standardConfigSet (FlatFareConfigSet::class) from Cache via...</text></switch></g></g><g><rect x="3460.5" y="3332" width="20" height="180" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 2571 3360 L 3442.76 3360" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 3448.76 3360 L 3440.76 3364 L 3442.76 3360 L 3440.76 3356 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="2571" y="3310" width="350" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 348px; height: 1px; padding-top: 3330px; margin-left: 2573px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Retrieve Data via getValue() function</span></div></div></div></div></foreignObject><text x="2573" y="3334" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve Data via getValue() function</text></switch></g></g><g><rect x="4868" y="3390" width="20" height="70" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 3492 3400 L 4852.76 3400" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 4858.76 3400 L 4850.76 3404 L 4852.76 3400 L 4850.76 3396 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="3492" y="3350" width="540" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 538px; height: 1px; padding-top: 3370px; margin-left: 3494px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Retrieve data using redisTemplate.opsForValue() function</span></div></div></div></div></foreignObject><text x="3494" y="3374" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve data using redisTemplate.opsForValue() function</text></switch></g></g><g><path d="M 3500.24 3450 L 4861 3450" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 3494.24 3450 L 3502.24 3446 L 3500.24 3450 L 3502.24 3454 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="3492" y="3405" width="500" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 498px; height: 1px; padding-top: 3425px; margin-left: 3494px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Return standardConfigSet (FlatFareConfigSet::class)</span></div></div></div></div></foreignObject><text x="3494" y="3429" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return standardConfigSet (FlatFareConfigSet::class)</text></switch></g></g><g><path d="M 2579.24 3497.99 L 3451 3500" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2573.24 3497.98 L 2581.25 3493.99 L 2579.24 3497.99 L 2581.23 3501.99 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="2571" y="3445" width="270" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 268px; height: 1px; padding-top: 3465px; margin-left: 2573px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Return standardConfigSet (FlatFareConfigSet::class)</span></div></div></div></div></foreignObject><text x="2573" y="3469" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return standardConfigSet (FlatFareConfigSet::...</text></switch></g></g><g><path d="M 1345 3280 L 2526.76 3280" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2532.76 3280 L 2524.76 3284 L 2526.76 3280 L 2524.76 3276 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><path d="M 1353.24 3540 L 2535 3540" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1347.24 3540 L 1355.24 3536 L 1353.24 3540 L 1355.24 3544 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1345" y="3490" width="476" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 474px; height: 1px; padding-top: 3510px; margin-left: 1347px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Return standardConfigSet (FlatFareConfigSet::class)</span></div></div></div></div></foreignObject><text x="1347" y="3514" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return standardConfigSet (FlatFareConfigSet::class)</text></switch></g></g><g><rect x="1355" y="3560" width="1016" height="410" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1014px; height: 1px; padding-top: 3765px; margin-left: 1357px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Retrieve estStandardConfigSet (FlatFareConfigSet::class) from Cache via </span></div><div><span style="background-color: initial;"><font style="font-size: 20px;"><b style="color: rgb(33, 48, 255);">DYNAMIC_PRICING:FLAT_FARE:EST_STANDARD_CONFIG_SET</b><font color="#2130ff"> or</font><br /><font color="#2130ff" style=""><b>DYNAMIC_PRICING:COMMON:FLAT_FARE:EST_STANDARD_CONFIG_SET </b>i</font>f the fomer is not available</font></span></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - prefixKey</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - peakHoursRates</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - midnightHoursRates</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - singleConfigs</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - flatdownRate</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - tier1Fare</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - tier2Fare</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - estimateRateConfig</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - durationUnitConfig</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - durationRateConfig</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - maxFlatFareCap</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - multiStopSurcharge<br /> - locationSurchargeConfigList<br /> - holidayList<br /> - bookingFeeList<br /> - additionalChargeList<br /> - eventSurgeAddressConfigList</font></div></div></div></div></foreignObject><text x="1357" y="3769" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve estStandardConfigSet (FlatFareConfigSet::class) from Cache via...</text></switch></g></g><g><path d="M 1345 3980 L 2526.76 3980" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2532.76 3980 L 2524.76 3984 L 2526.76 3980 L 2524.76 3976 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="3463.5" y="4002" width="20" height="180" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 2571 4030 L 3442.76 4030" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 3448.76 4030 L 3440.76 4034 L 3442.76 4030 L 3440.76 4026 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="2571" y="3980" width="330" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 328px; height: 1px; padding-top: 4000px; margin-left: 2573px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Retrieve Data via getValue() function</span></div></div></div></div></foreignObject><text x="2573" y="4004" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve Data via getValue() function</text></switch></g></g><g><rect x="4871" y="4060" width="20" height="70" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 3495 4070 L 4852.76 4070" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 4858.76 4070 L 4850.76 4074 L 4852.76 4070 L 4850.76 4066 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="3495" y="4020" width="540" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 538px; height: 1px; padding-top: 4040px; margin-left: 3497px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Retrieve data using redisTemplate.opsForValue() function</span></div></div></div></div></foreignObject><text x="3497" y="4044" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve data using redisTemplate.opsForValue() function</text></switch></g></g><g><path d="M 3503.24 4120 L 4861 4120" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 3497.24 4120 L 3505.24 4116 L 3503.24 4120 L 3505.24 4124 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="3495" y="4075" width="620" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 618px; height: 1px; padding-top: 4095px; margin-left: 3497px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Return estStandardConfigSet (FlatFareConfigSet::class)</span></div></div></div></div></foreignObject><text x="3497" y="4099" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return estStandardConfigSet (FlatFareConfigSet::class)</text></switch></g></g><g><path d="M 2579.24 4167.97 L 3451 4168" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2573.24 4167.97 L 2581.24 4163.97 L 2579.24 4167.97 L 2581.24 4171.97 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="2571" y="4115" width="290" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 288px; height: 1px; padding-top: 4135px; margin-left: 2573px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Return estStandardConfigSet (FlatFareConfigSet::class)</span></div></div></div></div></foreignObject><text x="2573" y="4139" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return estStandardConfigSet (FlatFareConfigSet::...</text></switch></g></g><g><path d="M 1353.24 4210 L 2535 4210" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1347.24 4210 L 1355.24 4206 L 1353.24 4210 L 1355.24 4214 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1345" y="4160" width="576" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 574px; height: 1px; padding-top: 4180px; margin-left: 1347px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Return estStandardConfigSet (FlatFareConfigSet::class)</span></div></div></div></div></foreignObject><text x="1347" y="4184" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return estStandardConfigSet (FlatFareConfigSet::class)</text></switch></g></g><g><rect x="1355" y="4250" width="1016" height="440" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1014px; height: 1px; padding-top: 4470px; margin-left: 1357px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Retrieve fareTypeConfigSet(DynamicPricingConfigSet::class) from Cache via </span></div><div><span style="background-color: initial;"><font style="font-size: 20px;"><b style="color: rgb(33, 48, 255);">DYNAMIC_PRICING:FLAT_FARE:FARE_TYPE_CONFIG_SET</b><font color="#2130ff"> or</font><br /><font color="#2130ff" style=""><b>DYNAMIC_PRICING:COMMON:FLAT_FARE:FARE_TYPE_CONFIG_SET </b>i</font>f the fomer is not available</font></span></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - desurgeMaxCapConfigList<br /> - minSurgeAmountConfigList<br /> - minCapConfigList<br /> - maxCapConfigList<br /> - surgeBufferConfigList<br /> - flagDownConfigList<br /> - durationRateConfigList<br /> - tier!PriceMultiplierConfigList<br /> - tier1StartDestConfigList<br /> - tier1EndDestConfigList<br /> - tier2PriceMultiplierConfigList<br /> - tier2STartDestConfigList<br /> - teir2EndDestConfigList<br /> - bookingFee<br /> - hourlySurcharge<br /> - holidayList<br /> -multiStopSurcharge<br /> - eventSurgeAddressConfigList<br /> - locationSurchargeConfigList</font></div></div></div></div></foreignObject><text x="1357" y="4474" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve fareTypeConfigSet(DynamicPricingConfigSet::class) from Cache via...</text></switch></g></g><g><path d="M 1345 4698 L 2526.76 4698" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2532.76 4698 L 2524.76 4702 L 2526.76 4698 L 2524.76 4694 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="3462.5" y="4712" width="20" height="180" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 2571 4740 L 3442.76 4740" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 3448.76 4740 L 3440.76 4744 L 3442.76 4740 L 3440.76 4736 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="2571" y="4690" width="340" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 338px; height: 1px; padding-top: 4710px; margin-left: 2573px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Retrieve Data via getValue() function</span></div></div></div></div></foreignObject><text x="2573" y="4714" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve Data via getValue() function</text></switch></g></g><g><rect x="4870" y="4770" width="20" height="70" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 3494 4780 L 4852.76 4780" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 4858.76 4780 L 4850.76 4784 L 4852.76 4780 L 4850.76 4776 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="3494" y="4730" width="540" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 538px; height: 1px; padding-top: 4750px; margin-left: 3496px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Retrieve data using redisTemplate.opsForValue() function</span></div></div></div></div></foreignObject><text x="3496" y="4754" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve data using redisTemplate.opsForValue() function</text></switch></g></g><g><path d="M 3502.24 4830 L 4861 4830" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 3496.24 4830 L 3504.24 4826 L 3502.24 4830 L 3504.24 4834 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="3494" y="4785" width="620" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 618px; height: 1px; padding-top: 4805px; margin-left: 3496px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Return fareTypeConfigSet (DynamicPricingConfigSet::class)</span></div></div></div></div></foreignObject><text x="3496" y="4809" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return fareTypeConfigSet (DynamicPricingConfigSet::class)</text></switch></g></g><g><path d="M 2579.24 4877.97 L 3451 4878" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2573.24 4877.97 L 2581.24 4873.97 L 2579.24 4877.97 L 2581.24 4881.97 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="2571" y="4825" width="310" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 308px; height: 1px; padding-top: 4845px; margin-left: 2573px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="font-size: 20px;">Return fareTypeConfigSet (DynamicPricingConfigSet::class)</span></div></div></div></foreignObject><text x="2573" y="4849" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return fareTypeConfigSet (DynamicPricingConfigSet::...</text></switch></g></g><g><path d="M 1353.24 4900 L 2535 4900" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1347.24 4900 L 1355.24 4896 L 1353.24 4900 L 1355.24 4904 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1345" y="4850" width="576" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 574px; height: 1px; padding-top: 4870px; margin-left: 1347px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="font-size: 20px;">Return fareTypeConfigSet (DynamicPricingConfigSet::class)</span></div></div></div></foreignObject><text x="1347" y="4874" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return fareTypeConfigSet (DynamicPricingConfigSet::class)</text></switch></g></g><g><rect x="1355" y="4940" width="1016" height="100" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1014px; height: 1px; padding-top: 4990px; margin-left: 1357px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Retrieve holidayList (flatFareHoliday::class) from Cache via </span></div><div><span style="background-color: initial;"><font style="font-size: 20px;"><b style="color: rgb(33, 48, 255);">DYNAMIC_PRICING:COMPANY_HOLIDAY</b></font></span></div><div style="font-size: 16px;"><font style="font-size: 16px;"> - date</font></div></div></div></div></foreignObject><text x="1357" y="4994" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve holidayList (flatFareHoliday::class) from Cache via...</text></switch></g></g><g><path d="M 1345 5040 L 2526.76 5040" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2532.76 5040 L 2524.76 5044 L 2526.76 5040 L 2524.76 5036 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="3461.5" y="5052" width="20" height="180" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 2571 5080 L 3442.76 5080" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 3448.76 5080 L 3440.76 5084 L 3442.76 5080 L 3440.76 5076 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="2571" y="5030" width="370" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 368px; height: 1px; padding-top: 5050px; margin-left: 2573px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Retrieve Data via getListValue() function</span></div></div></div></div></foreignObject><text x="2573" y="5054" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve Data via getListValue() function</text></switch></g></g><g><rect x="4869" y="5110" width="20" height="70" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 3493 5120 L 4852.76 5120" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 4858.76 5120 L 4850.76 5124 L 4852.76 5120 L 4850.76 5116 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="3493" y="5070" width="540" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 538px; height: 1px; padding-top: 5090px; margin-left: 3495px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Retrieve data using redisTemplate.opsForList() function</span></div></div></div></div></foreignObject><text x="3495" y="5094" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve data using redisTemplate.opsForList() function</text></switch></g></g><g><path d="M 3501.24 5170 L 4851 5170" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 3495.24 5170 L 3503.24 5166 L 3501.24 5170 L 3503.24 5174 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="3493" y="5125" width="620" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 618px; height: 1px; padding-top: 5145px; margin-left: 3495px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Return List&lt;FlatFareHoliday&gt;</span></div></div></div></div></foreignObject><text x="3495" y="5149" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return List&lt;FlatFareHoliday&gt;</text></switch></g></g><g><path d="M 2579.24 5217.97 L 3451 5218" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2573.24 5217.97 L 2581.24 5213.97 L 2579.24 5217.97 L 2581.24 5221.97 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="2571" y="5165" width="290" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 288px; height: 1px; padding-top: 5185px; margin-left: 2573px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="font-size: 20px;">Return List&lt;FlatFareHoliday&gt;</span></div></div></div></foreignObject><text x="2573" y="5189" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return List&lt;FlatFareHoliday&gt;</text></switch></g></g><g><path d="M 1353.24 5260 L 2535 5260" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1347.24 5260 L 1355.24 5256 L 1353.24 5260 L 1355.24 5264 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1345" y="5210" width="576" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 574px; height: 1px; padding-top: 5230px; margin-left: 1347px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="font-size: 20px;">Return List&lt;FlatFareHoliday&gt;</span></div></div></div></foreignObject><text x="1347" y="5234" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return List&lt;FlatFareHoliday&gt;</text></switch></g></g><g><rect x="2871" y="250" width="300" height="60" rx="9" ry="9" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 298px; height: 1px; padding-top: 280px; margin-left: 2872px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">LocationSurchargeServiceImpl</font></div></div></div></foreignObject><text x="3021" y="284" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">LocationSurchargeServiceImpl</text></switch></g></g><g><path d="M 3001 8450 L 2991 335" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><rect x="2989" y="5538" width="20" height="552" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 1341 5566 L 2972.76 5566" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2978.76 5566 L 2970.76 5570 L 2972.76 5566 L 2970.76 5562 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1345" y="5280" width="836" height="270" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 834px; height: 1px; padding-top: 5415px; margin-left: 1347px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="font-size: 20px;">Get List&lt;LocationSurchargeConfig&gt; via getLocationSurchargeConfigList method</span><div style="font-size: 16px;"><span style=""> - fareType<br /> - applicableDays<br /> - startTime<br /> - endTime<br /> - chargeBy<br /> - surchargeValue<br /> - locationId<br /> - locationName<br /> addressRef<br /> - zoneId<br /> - productId<br /> - dayIndicator</span></div></div></div></div></foreignObject><text x="1347" y="5419" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Get List&lt;LocationSurchargeConfig&gt; via getLocationSurchargeConfigList method...</text></switch></g></g><g><path d="M 2560.3 7579 L 2547.75 6040" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><rect x="2536" y="5611" width="20" height="429" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 2981 5766 L 2579.24 5766" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2573.24 5766 L 2581.24 5762 L 2579.24 5766 L 2581.24 5770 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="2571" y="5626" width="370" height="120" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 368px; height: 1px; padding-top: 5686px; margin-left: 2573px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="font-size: 20px;">Retrieve locationSurcharge config list via the following key:<br /><br /><font color="#2130ff"><b>DYNAMIC_PRICING:LOC_SURC:{DAY_OF_WEEK}:{ADDRESS_PART}</b></font></span></div></div></div></foreignObject><text x="2573" y="5690" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve locationSurcharge config list via the following key:...</text></switch></g></g><g><path d="M 2561 6028 L 2972.76 6028" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2978.76 6028 L 2970.76 6032 L 2972.76 6028 L 2970.76 6024 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="2571" y="5978" width="350" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 348px; height: 1px; padding-top: 5998px; margin-left: 2573px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="font-size: 20px;">Return List&lt;LocationSurchargeConfig&gt;</span></div></div></div></foreignObject><text x="2573" y="6002" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return List&lt;LocationSurchargeConfig&gt;</text></switch></g></g><g><path d="M 3471 8440 L 3472.21 5960" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><rect x="3462.25" y="5802" width="20" height="158" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 2571 5838.17 L 3442.76 5838.17" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 3448.76 5838.17 L 3440.76 5842.17 L 3442.76 5838.17 L 3440.76 5834.17 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="2571" y="5788.17" width="370" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 368px; height: 1px; padding-top: 5808px; margin-left: 2573px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Retrieve Data via getListValue() function</span></div></div></div></div></foreignObject><text x="2573" y="5812" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve Data via getListValue() function</text></switch></g></g><g><rect x="4871" y="5870" width="20" height="70" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 3495 5880 L 4852.76 5889.94" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 4858.76 5889.98 L 4850.73 5893.92 L 4852.76 5889.94 L 4850.79 5885.93 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="3495" y="5830" width="540" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 538px; height: 1px; padding-top: 5850px; margin-left: 3497px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Retrieve data using redisTemplate.opsForList() function</span></div></div></div></div></foreignObject><text x="3497" y="5854" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve data using redisTemplate.opsForList() function</text></switch></g></g><g><path d="M 3503.24 5930 L 4861 5930" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 3497.24 5930 L 3505.24 5926 L 3503.24 5930 L 3505.24 5934 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="3495" y="5885" width="620" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 618px; height: 1px; padding-top: 5905px; margin-left: 3497px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Return List&lt;LocationSurchargeConfig&gt;</span></div></div></div></div></foreignObject><text x="3497" y="5909" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return List&lt;LocationSurchargeConfig&gt;</text></switch></g></g><g><path d="M 2569.24 5950 L 3441 5950.03" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2563.24 5950 L 2571.24 5946 L 2569.24 5950 L 2571.24 5954 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="2571" y="5900" width="620" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 618px; height: 1px; padding-top: 5920px; margin-left: 2573px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Return List&lt;LocationSurchargeConfig&gt;</span></div></div></div></div></foreignObject><text x="2573" y="5924" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return List&lt;LocationSurchargeConfig&gt;</text></switch></g></g><g><rect x="3721" y="250" width="210" height="60" rx="9" ry="9" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 208px; height: 1px; padding-top: 280px; margin-left: 3722px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">FareServiceAdapter</font></div></div></div></foreignObject><text x="3826" y="284" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">FareServiceAdapter</text></switch></g></g><g><rect x="4081" y="250" width="210" height="60" rx="9" ry="9" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 208px; height: 1px; padding-top: 280px; margin-left: 4082px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">FareOutboundAdapter</font></div></div></div></foreignObject><text x="4186" y="284" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">FareOutboundAdapter</text></switch></g></g><g><path d="M 3825.99 6810 L 3825.52 339" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><path d="M 4200.91 6319 L 4191.29 337" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><rect x="3816" y="6271" width="20" height="199" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 1341 6283 L 3802.76 6289.98" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 3808.76 6289.99 L 3800.75 6293.97 L 3802.76 6289.98 L 3800.78 6285.97 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><path d="M 1349.24 6055 L 2983 6055" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1343.24 6055 L 1351.24 6051 L 1349.24 6055 L 1351.24 6059 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1345" y="6010" width="350" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 348px; height: 1px; padding-top: 6030px; margin-left: 1347px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="font-size: 20px;">Return List&lt;LocationSurchargeConfig&gt;</span></div></div></div></foreignObject><text x="1347" y="6034" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return List&lt;LocationSurchargeConfig&gt;</text></switch></g></g><g><rect x="1345" y="6160" width="836" height="110" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 834px; height: 1px; padding-top: 6215px; margin-left: 1347px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="font-size: 20px;">Get BookingFeeList configuration via getBookingFeeByList method</span><div style="font-size: 16px;"><span style=""> - vehicleTypeId<br /> - productId<br /> - flatFareType<br /> - bookingFee</span></div></div></div></div></foreignObject><text x="1347" y="6219" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Get BookingFeeList configuration via getBookingFeeByList method...</text></switch></g></g><g><path d="M 4191 8437 L 4200.73 6430" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><rect x="4191" y="6319" width="20" height="111" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 3841 6329 L 4172.76 6329.98" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 4178.76 6329.99 L 4170.75 6333.97 L 4172.76 6329.98 L 4170.78 6325.97 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="3845" y="6280" width="270" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 268px; height: 1px; padding-top: 6300px; margin-left: 3847px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Retrieve BookingFeeList via getBookingFeeByList function</span></div></div></div></div></foreignObject><text x="3847" y="6304" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve BookingFeeList via getBookingFeeByLi...</text></switch></g></g><g><path d="M 5531 8440 L 5521.17 6412" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><rect x="5511" y="6342" width="20" height="70" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 4221 6360 L 5492.76 6360" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 5498.76 6360 L 5490.76 6364 L 5492.76 6360 L 5490.76 6356 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="4221" y="6310" width="640" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 638px; height: 1px; padding-top: 6330px; margin-left: 4223px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Retrieve BookingFeeList via <b><font color="#2130ff">/v1.0/fares/booking-fee-list</font></b> endpoint</span></div></div></div></div></foreignObject><text x="4223" y="6334" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve BookingFeeList via /v1.0/fares/booking-fee-list endpoint</text></switch></g></g><g><path d="M 4229.24 6400 L 5501 6400" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 4223.24 6400 L 4231.24 6396 L 4229.24 6400 L 4231.24 6404 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="4221" y="6357" width="640" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 638px; height: 1px; padding-top: 6377px; margin-left: 4223px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Return BookingFeeList</span></div></div></div></div></foreignObject><text x="4223" y="6381" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return BookingFeeList</text></switch></g></g><g><path d="M 3853.24 6420.02 L 4185 6421" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 3847.24 6420.01 L 3855.25 6416.03 L 3853.24 6420.02 L 3855.22 6424.03 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="3845" y="6373" width="236" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 234px; height: 1px; padding-top: 6393px; margin-left: 3847px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Return BookingFeeList</span></div></div></div></div></foreignObject><text x="3847" y="6397" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return BookingFeeList</text></switch></g></g><g><path d="M 1349.24 6440.02 L 3811 6447" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1343.24 6440.01 L 1351.25 6436.03 L 1349.24 6440.02 L 1351.22 6444.03 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1345" y="6397" width="236" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 234px; height: 1px; padding-top: 6417px; margin-left: 1347px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Return BookingFeeList</span></div></div></div></div></foreignObject><text x="1347" y="6421" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return BookingFeeList</text></switch></g></g><g><path d="M 1281 1120 L 1276 1120 Q 1271 1120 1271 1130 L 1271 1240 Q 1271 1250 1266 1250 L 1263.5 1250 Q 1261 1250 1266 1250 L 1268.5 1250 Q 1271 1250 1271 1260 L 1271 1370 Q 1271 1380 1276 1380 L 1281 1380" fill="none" stroke="#82b366" stroke-width="4" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="951" y="1215" width="272" height="60" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 270px; height: 1px; padding-top: 1245px; margin-left: 953px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 24px;" color="#006600">Get Route Information from me-address-service</font></div></div></div></foreignObject><text x="953" y="1249" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Get Route Information from me-address-service</text></switch></g></g><g><path d="M 1281 1390 L 1276 1390 Q 1271 1390 1271 1400 L 1271 1470 Q 1271 1480 1266 1480 L 1263.5 1480 Q 1261 1480 1266 1480 L 1268.5 1480 Q 1271 1480 1271 1490 L 1271 1560 Q 1271 1570 1276 1570 L 1281 1570" fill="none" stroke="#82b366" stroke-width="4" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="951" y="1450" width="272" height="60" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 270px; height: 1px; padding-top: 1480px; margin-left: 953px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 24px;" color="#006600">Generate Unique FareId</font></div></div></div></foreignObject><text x="953" y="1484" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Generate Unique FareId</text></switch></g></g><g><path d="M 1281 1590 L 1276 1590 Q 1271 1590 1271 1600 L 1271 1850 Q 1271 1860 1266 1860 L 1263.5 1860 Q 1261 1860 1266 1860 L 1268.5 1860 Q 1271 1860 1271 1870 L 1271 2120 Q 1271 2130 1276 2130 L 1281 2130" fill="none" stroke="#82b366" stroke-width="4" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="961" y="1820" width="272" height="60" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 270px; height: 1px; padding-top: 1850px; margin-left: 963px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 24px;" color="#006600">Retrieve CommonConfigSet from Redis Cache</font></div></div></div></foreignObject><text x="963" y="1854" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve CommonConfigSet from Redis Cache</text></switch></g></g><g><path d="M 1281 2150 L 1276 2150 Q 1271 2150 1271 2160 L 1271 2490 Q 1271 2500 1266 2500 L 1263.5 2500 Q 1261 2500 1266 2500 L 1268.5 2500 Q 1271 2500 1271 2510 L 1271 2840 Q 1271 2850 1276 2850 L 1281 2850" fill="none" stroke="#82b366" stroke-width="4" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="941" y="2470" width="272" height="60" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 270px; height: 1px; padding-top: 2500px; margin-left: 943px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 24px;" color="#006600">Retrieve LimoConfiguration from Redis Cache</font></div></div></div></foreignObject><text x="943" y="2504" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve LimoConfiguration from Redis Cache</text></switch></g></g><g><path d="M 1281 2860 L 1276 2860 Q 1271 2860 1271 2870 L 1271 3200 Q 1271 3210 1266 3210 L 1263.5 3210 Q 1261 3210 1266 3210 L 1268.5 3210 Q 1271 3210 1271 3220 L 1271 3550 Q 1271 3560 1276 3560 L 1281 3560" fill="none" stroke="#82b366" stroke-width="4" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="951" y="3190" width="272" height="60" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 270px; height: 1px; padding-top: 3220px; margin-left: 953px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 24px;" color="#006600">Retrieve StandardConfiguration from Redis Cache</font></div></div></div></foreignObject><text x="953" y="3224" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve StandardConfiguration from Redis Cac...</text></switch></g></g><g><path d="M 1281 3570 L 1276 3570 Q 1271 3570 1271 3580 L 1271 3890 Q 1271 3900 1266 3900 L 1263.5 3900 Q 1261 3900 1266 3900 L 1268.5 3900 Q 1271 3900 1271 3910 L 1271 4220 Q 1271 4230 1276 4230 L 1281 4230" fill="none" stroke="#82b366" stroke-width="4" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="941" y="3870" width="272" height="90" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 270px; height: 1px; padding-top: 3915px; margin-left: 943px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 24px;" color="#006600">Retrieve Estimated StandardConfiguration from Redis Cache</font></div></div></div></foreignObject><text x="943" y="3919" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve Estimated StandardConfiguration from...</text></switch></g></g><g><path d="M 1281 4240 L 1276 4240 Q 1271 4240 1271 4250 L 1271 4575 Q 1271 4585 1266 4585 L 1263.5 4585 Q 1261 4585 1266 4585 L 1268.5 4585 Q 1271 4585 1271 4595 L 1271 4920 Q 1271 4930 1276 4930 L 1281 4930" fill="none" stroke="#82b366" stroke-width="4" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="941" y="4550" width="272" height="90" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 270px; height: 1px; padding-top: 4595px; margin-left: 943px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 24px;" color="#006600">Retrieve FareType Configuration from Redis Cache</font></div></div></div></foreignObject><text x="943" y="4599" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve FareType Configuration from Redis Ca...</text></switch></g></g><g><path d="M 1281 4940 L 1276 4940 Q 1271 4940 1271 4950 L 1271 5095 Q 1271 5105 1266 5105 L 1263.5 5105 Q 1261 5105 1266 5105 L 1268.5 5105 Q 1271 5105 1271 5115 L 1271 5260 Q 1271 5270 1276 5270 L 1281 5270" fill="none" stroke="#82b366" stroke-width="4" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="941" y="5075" width="272" height="90" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 270px; height: 1px; padding-top: 5120px; margin-left: 943px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 24px;" color="#006600">Retrieve Holiday Surcharge from Redis Cache</font></div></div></div></foreignObject><text x="943" y="5124" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve Holiday Surcharge from Redis Cache</text></switch></g></g><g><path d="M 1281 5281 L 1276 5281 Q 1271 5281 1271 5291 L 1271 5670.5 Q 1271 5680.5 1266 5680.5 L 1263.5 5680.5 Q 1261 5680.5 1266 5680.5 L 1268.5 5680.5 Q 1271 5680.5 1271 5690.5 L 1271 6070 Q 1271 6080 1276 6080 L 1281 6080" fill="none" stroke="#82b366" stroke-width="4" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="951" y="5641" width="272" height="90" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 270px; height: 1px; padding-top: 5686px; margin-left: 953px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 24px;" color="#006600">Retrieve Location Surcharge from Redis Cache</font></div></div></div></foreignObject><text x="953" y="5690" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve Location Surcharge from Redis Cache</text></switch></g></g><g><path d="M 1281 6090 L 1276 6090 Q 1271 6090 1271 6100 L 1271 6265 Q 1271 6275 1266 6275 L 1263.5 6275 Q 1261 6275 1266 6275 L 1268.5 6275 Q 1271 6275 1271 6285 L 1271 6450 Q 1271 6460 1276 6460 L 1281 6460" fill="none" stroke="#82b366" stroke-width="4" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="961" y="6230" width="272" height="90" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 270px; height: 1px; padding-top: 6275px; margin-left: 963px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 24px;" color="#006600">Retrieve BookingFee Configuration from me-fare-service</font></div></div></div></foreignObject><text x="963" y="6279" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve BookingFee Configuration from me-far...</text></switch></g></g><g><path d="M 1281 870 L 1276 870 Q 1271 870 1271 880 L 1271 980 Q 1271 990 1266 990 L 1263.5 990 Q 1261 990 1266 990 L 1268.5 990 Q 1271 990 1271 1000 L 1271 1100 Q 1271 1110 1276 1110 L 1281 1110" fill="none" stroke="#82b366" stroke-width="4" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="969" y="970" width="272" height="60" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 270px; height: 1px; padding-top: 1000px; margin-left: 971px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 24px;" color="#006600">Generate Unique TripId</font></div></div></div></foreignObject><text x="971" y="1004" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Generate Unique TripId</text></switch></g></g><g><path d="M 1336 6829 L 3797.76 6835.98" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 3803.76 6835.99 L 3795.75 6839.97 L 3797.76 6835.98 L 3795.78 6831.97 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1345" y="6480.5" width="626" height="339.5" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 624px; height: 1px; padding-top: 6650px; margin-left: 1347px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="font-size: 20px;">Get PlatformFeeList configuration via getPlatformFeeByList method</span><div style="font-size: 16px;"><span style=""> - id<br /> - bookingChannel<br /> - vehicleGroupId<br /> - productId<br /> - platformFeeApplicability<br /> - remarks<br /> - platformFeeThresholdLimit<br /> - lowerPlatformFee<br /> - upperPlatformFee<br /> - effectiveFrom<br /> - effectiveTo<br /> - deleted<br /> - createdDate<br /> - createdBy<br /> - updatedDate<br /> - updatedBy</span></div></div></div></div></foreignObject><text x="1347" y="6654" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Get PlatformFeeList configuration via getPlatformFeeByList method...</text></switch></g></g><g><path d="M 3825.23 8439 L 3825.95 7009" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><rect x="3816" y="6810" width="20" height="199" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><rect x="4191" y="6849" width="20" height="111" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 3841 6859 L 4172.76 6859.98" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 4178.76 6859.99 L 4170.75 6863.97 L 4172.76 6859.98 L 4170.78 6855.97 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="3845" y="6810" width="270" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 268px; height: 1px; padding-top: 6830px; margin-left: 3847px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Retrieve PlatformFeeList via getPlatformFeeByList function</span></div></div></div></div></foreignObject><text x="3847" y="6834" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve PlatformFeeList via getPlatformFeeBy...</text></switch></g></g><g><rect x="5511" y="6872" width="20" height="70" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 4221 6890 L 5492.76 6890" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 5498.76 6890 L 5490.76 6894 L 5492.76 6890 L 5490.76 6886 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="4221" y="6840" width="790" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 788px; height: 1px; padding-top: 6860px; margin-left: 4223px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Retrieve PlatformFeeList via <font color="#2130ff"><b>/v1.0/fares/getPlatformFeeByList</b></font> endpoint</span></div></div></div></div></foreignObject><text x="4223" y="6864" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve PlatformFeeList via /v1.0/fares/getPlatformFeeByList endpoint</text></switch></g></g><g><path d="M 4229.24 6930 L 5501 6930" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 4223.24 6930 L 4231.24 6926 L 4229.24 6930 L 4231.24 6934 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="4221" y="6887" width="640" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 638px; height: 1px; padding-top: 6907px; margin-left: 4223px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Return PlatformFeeList</span></div></div></div></div></foreignObject><text x="4223" y="6911" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return PlatformFeeList</text></switch></g></g><g><path d="M 3853.24 6950.02 L 4185 6951" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 3847.24 6950.01 L 3855.25 6946.03 L 3853.24 6950.02 L 3855.22 6954.03 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="3845" y="6903" width="236" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 234px; height: 1px; padding-top: 6923px; margin-left: 3847px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Return PlatformFeeList</span></div></div></div></div></foreignObject><text x="3847" y="6927" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return PlatformFeeList</text></switch></g></g><g><path d="M 1349.24 6977.02 L 3811 6984" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1343.24 6977.01 L 1351.25 6973.03 L 1349.24 6977.02 L 1351.22 6981.03 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1345" y="6934" width="236" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 234px; height: 1px; padding-top: 6954px; margin-left: 1347px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Return BookingFeeList</span></div></div></div></div></foreignObject><text x="1347" y="6958" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return BookingFeeList</text></switch></g></g><g><path d="M 1281 6479 L 1276 6479 Q 1271 6479 1271 6489 L 1271 6724.5 Q 1271 6734.5 1266 6734.5 L 1263.5 6734.5 Q 1261 6734.5 1266 6734.5 L 1268.5 6734.5 Q 1271 6734.5 1271 6744.5 L 1271 6980 Q 1271 6990 1276 6990 L 1281 6990" fill="none" stroke="#82b366" stroke-width="4" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="951" y="6689.5" width="272" height="90" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 270px; height: 1px; padding-top: 6735px; margin-left: 953px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 24px;" color="#006600">Retrieve PlatformFee Configuration from me-fare-service</font></div></div></div></foreignObject><text x="953" y="6738" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve PlatformFee Configuration from me-fa...</text></switch></g></g><g><path d="M 1337 7171.5 L 3798.76 7178.48" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 3804.76 7178.49 L 3796.75 7182.47 L 3798.76 7178.48 L 3796.78 7174.47 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1346" y="7009" width="775" height="151" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 773px; height: 1px; padding-top: 7085px; margin-left: 1348px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="font-size: 20px;">Get AdditionalChargeFeeConfigList via getAdditionalChargeFeeConfigMap method</span><div style="font-size: 16px;"><span style=""> - chargeId<br /> - chargeType<br /> - chargeKey<br /> - chargeValue<br /> - chargeDescription<br /> - chargeFormula</span></div></div></div></div></foreignObject><text x="1348" y="7088" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Get AdditionalChargeFeeConfigList via getAdditionalChargeFeeConfigMap method...</text></switch></g></g><g><rect x="3816" y="7170" width="20" height="199" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><rect x="4191" y="7209" width="20" height="111" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 3841 7219 L 4172.76 7219.98" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 4178.76 7219.99 L 4170.75 7223.97 L 4172.76 7219.98 L 4170.78 7215.97 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="3845" y="7109" width="326" height="100" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 324px; height: 1px; padding-top: 7159px; margin-left: 3847px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Retrieve AdditionalChargeFeeConfigList via getAdditionalChargeFeeConfigMap function</span></div></div></div></div></foreignObject><text x="3847" y="7163" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve AdditionalChargeFeeConfigList via getAddition...</text></switch></g></g><g><rect x="5511" y="7232" width="20" height="70" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 4221 7250 L 5492.76 7250" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 5498.76 7250 L 5490.76 7254 L 5492.76 7250 L 5490.76 7246 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="4221" y="7200" width="930" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 928px; height: 1px; padding-top: 7220px; margin-left: 4223px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Retrieve AdditionalChargeFeeConfigList via<font color="#2130ff"><b> /v1.0/fares/addtional-charge-fees</b></font> endpoint</span></div></div></div></div></foreignObject><text x="4223" y="7224" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve AdditionalChargeFeeConfigList via /v1.0/fares/addtional-charge-fees endpoint</text></switch></g></g><g><path d="M 4229.24 7290 L 5501 7290" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 4223.24 7290 L 4231.24 7286 L 4229.24 7290 L 4231.24 7294 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="4221" y="7247" width="640" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 638px; height: 1px; padding-top: 7267px; margin-left: 4223px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Return PlatformFeeList</span></div></div></div></div></foreignObject><text x="4223" y="7271" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return PlatformFeeList</text></switch></g></g><g><path d="M 3853.24 7310.02 L 4185 7311" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 3847.24 7310.01 L 3855.25 7306.03 L 3853.24 7310.02 L 3855.22 7314.03 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="3845" y="7263" width="346" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 344px; height: 1px; padding-top: 7283px; margin-left: 3847px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Return AdditionalChargeFeeConfigList</span></div></div></div></div></foreignObject><text x="3847" y="7287" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return AdditionalChargeFeeConfigList</text></switch></g></g><g><path d="M 1349.24 7333.02 L 3811 7340" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1343.24 7333.01 L 1351.25 7329.03 L 1349.24 7333.02 L 1351.22 7337.03 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1345" y="7280" width="346" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 344px; height: 1px; padding-top: 7300px; margin-left: 1347px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Return AdditionalChargeFeeConfigList</span></div></div></div></div></foreignObject><text x="1347" y="7304" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return AdditionalChargeFeeConfigList</text></switch></g></g><g><path d="M 1291 7390 L 1286 7390 Q 1281 7390 1281 7400 L 1281 7690 Q 1281 7700 1276 7700 L 1273.5 7700 Q 1271 7700 1276 7700 L 1278.5 7700 Q 1281 7700 1281 7710 L 1281 8000 Q 1281 8010 1286 8010 L 1291 8010" fill="none" stroke="#82b366" stroke-width="4" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="951" y="7130" width="272" height="120" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 270px; height: 1px; padding-top: 7190px; margin-left: 953px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 24px;" color="#006600">Retrieve AdditionalChargeFee Configuration from me-fare-service</font></div></div></div></foreignObject><text x="953" y="7194" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve AdditionalChargeFee Configuration fr...</text></switch></g></g><g><path d="M 1341 7380 L 1371 7380 L 1371 7510 L 1349.24 7510" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1343.24 7510 L 1351.24 7506 L 1349.24 7510 L 1351.24 7514 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1380" y="7380" width="406" height="130" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 404px; height: 1px; padding-top: 7445px; margin-left: 1382px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Calculate the estimated flatfare for each vehicleTypeId specified in the request.</span></div><div><span style="font-size: 20px;">This includes dynamic-pricing, surcharges and optional driver fees. The results are then prepared as fare breakdowns.</span></div></div></div></div></foreignObject><text x="1382" y="7449" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Calculate the estimated flatfare for each vehicleTypeId specified i...</text></switch></g></g><g><path d="M 2561 7770 L 2561 7750" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><rect x="2551" y="7579" width="20" height="171" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 1341 7613 L 2532.76 7613" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2538.76 7613 L 2530.76 7617 L 2532.76 7613 L 2530.76 7609 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1346" y="7540" width="552" height="60" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 550px; height: 1px; padding-top: 7570px; margin-left: 1348px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Cache FareBreakdown using the following key: <font color="#2130ff">DYNAMIC_PRICING:BREAKDOWN:{fareId}-{vehicleTypeId} </font>via setValue function call.</span></div></div></div></div></foreignObject><text x="1348" y="7574" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Cache FareBreakdown using the following key: DYNAMIC_PRICING:BREAKDOWN:{fareId}-{vehicleType...</text></switch></g></g><g><rect x="3462.25" y="7613" width="20" height="127" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 2580.5 7660 L 3452.26 7660" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 3458.26 7660 L 3450.26 7664 L 3452.26 7660 L 3450.26 7656 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="2580.5" y="7610" width="370" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 368px; height: 1px; padding-top: 7630px; margin-left: 2583px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Set value for key using setValue function</span></div></div></div></div></foreignObject><text x="2583" y="7634" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Set value for key using setValue function</text></switch></g></g><g><rect x="4871" y="7690" width="20" height="50" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 3495 7700 L 4853.76 7700" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 4859.76 7700 L 4851.76 7704 L 4853.76 7700 L 4851.76 7696 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="3495" y="7650" width="540" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 538px; height: 1px; padding-top: 7670px; margin-left: 3497px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Set data using redisTemplate.setValue() function</span></div></div></div></div></foreignObject><text x="3497" y="7674" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Set data using redisTemplate.setValue() function</text></switch></g></g><g><path d="M 1341 7793 L 2532.76 7793" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2538.76 7793 L 2530.76 7797 L 2532.76 7793 L 2530.76 7789 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1346" y="7720" width="552" height="60" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 550px; height: 1px; padding-top: 7750px; margin-left: 1348px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Cache MultiFare using the following key: <font color="#2130ff">DYNAMIC_PRICING:MULTI_FARE:{fareId} </font>via setValue function call.</span></div></div></div></div></foreignObject><text x="1348" y="7754" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Cache MultiFare using the following key: DYNAMIC_PRICING:MULTI_FARE:{fareId} via setValue fu...</text></switch></g></g><g><path d="M 2561 8440 L 2561 7941" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><rect x="2551" y="7770" width="20" height="171" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><rect x="3461.25" y="7773" width="20" height="127" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 2579.5 7820 L 3451.26 7820" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 3457.26 7820 L 3449.26 7824 L 3451.26 7820 L 3449.26 7816 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="2579.5" y="7770" width="370" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 368px; height: 1px; padding-top: 7790px; margin-left: 2582px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Set value for key using setValue function</span></div></div></div></div></foreignObject><text x="2582" y="7794" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Set value for key using setValue function</text></switch></g></g><g><rect x="4870" y="7850" width="20" height="50" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 3494 7860 L 4852.76 7860" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 4858.76 7860 L 4850.76 7864 L 4852.76 7860 L 4850.76 7856 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="3494" y="7810" width="540" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 538px; height: 1px; padding-top: 7830px; margin-left: 3496px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Set data using redisTemplate.setValue() function</span></div></div></div></div></foreignObject><text x="3496" y="7834" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Set data using redisTemplate.setValue() function</text></switch></g></g><g><path d="M 1341 8003 L 2532.76 8003" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2538.76 8003 L 2530.76 8007 L 2532.76 8003 L 2530.76 7999 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1346" y="7930" width="585" height="60" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 583px; height: 1px; padding-top: 7960px; margin-left: 1348px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Cache Route using the following key: <font color="#2130ff">DYNAMIC_PRICING:ROUTE:{tripId} </font>via setValue function call.</span></div></div></div></div></foreignObject><text x="1348" y="7964" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Cache Route using the following key: DYNAMIC_PRICING:ROUTE:{tripId} via setValue function call.</text></switch></g></g><g><rect x="2551" y="7980" width="20" height="171" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><rect x="3462.25" y="7983" width="20" height="127" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 2580.5 8030 L 3452.26 8030" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 3458.26 8030 L 3450.26 8034 L 3452.26 8030 L 3450.26 8026 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="2580.5" y="7980" width="370" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 368px; height: 1px; padding-top: 8000px; margin-left: 2583px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Set value for key using setValue function</span></div></div></div></div></foreignObject><text x="2583" y="8004" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Set value for key using setValue function</text></switch></g></g><g><rect x="4871" y="8060" width="20" height="50" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 3495 8070 L 4853.76 8070" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 4859.76 8070 L 4851.76 8074 L 4853.76 8070 L 4851.76 8066 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="3495" y="8020" width="540" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 538px; height: 1px; padding-top: 8040px; margin-left: 3497px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Set data using redisTemplate.setValue() function</span></div></div></div></div></foreignObject><text x="3497" y="8044" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Set data using redisTemplate.setValue() function</text></switch></g></g><g><path d="M 1291 7010 L 1286 7010 Q 1281 7010 1281 7020 L 1281 7180 Q 1281 7190 1276 7190 L 1273.5 7190 Q 1271 7190 1276 7190 L 1278.5 7190 Q 1281 7190 1281 7200 L 1281 7360 Q 1281 7370 1286 7370 L 1291 7370" fill="none" stroke="#82b366" stroke-width="4" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="941" y="7640" width="272" height="120" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 270px; height: 1px; padding-top: 7700px; margin-left: 943px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 24px;" color="#006600">Loop through each vehicleTypeId and cache the following info:</font><div><font color="#006600"><span style="font-size: 24px;"> - fareBreakdown<br /> - multiFare<br /> - route</span></font></div></div></div></div></foreignObject><text x="943" y="7704" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Loop through each vehicleTypeId and cache the...</text></switch></g></g><g><path d="M 1291 8130 L 829.24 8130" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 823.24 8130 L 831.24 8126 L 829.24 8130 L 831.24 8134 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="831" y="8065" width="370" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 368px; height: 1px; padding-top: 8085px; margin-left: 833px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div><span style="font-size: 20px;">Return MultiFareResponse</span></div></div></div></div></foreignObject><text x="833" y="8089" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return MultiFareResponse</text></switch></g></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>
<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than draw.io -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="4038px" height="3659px" viewBox="-0.5 -0.5 4038 3659" content="&lt;mxfile host=&quot;Electron&quot; modified=&quot;2025-01-01T15:41:50.323Z&quot; agent=&quot;Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.4.8 Chrome/124.0.6367.207 Electron/30.0.6 Safari/537.36&quot; etag=&quot;PwxYPq-z6xYW2EUpiz5v&quot; version=&quot;24.4.8&quot; type=&quot;device&quot;&gt;&#10;  &lt;diagram name=&quot;Page-1&quot; id=&quot;Ki1UnvLfCiUZ55iQNjq-&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;3772&quot; dy=&quot;2342&quot; grid=&quot;1&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;1169&quot; pageHeight=&quot;827&quot; background=&quot;#ffffff&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-1&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;ParameterConfigController&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;labelBackgroundColor=none;fillColor=#dae8fc;strokeColor=#6c8ebf;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;200&quot; y=&quot;-327&quot; width=&quot;270&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-2&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;-470&quot; y=&quot;-247&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;3560&quot; y=&quot;-247&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-3&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;FareTpeConfigServiceImpl&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;labelBackgroundColor=none;fillColor=#d5e8d4;strokeColor=#82b366;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;710&quot; y=&quot;-327&quot; width=&quot;270&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-10&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;exitX=0.5;exitY=0;exitDx=0;exitDy=0;&quot; parent=&quot;1&quot; source=&quot;-IJH7Msd9MsLSg7A33z7-9&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;320&quot; y=&quot;-217&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;320&quot; y=&quot;-237&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-11&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; parent=&quot;1&quot; target=&quot;-IJH7Msd9MsLSg7A33z7-9&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;315&quot; y=&quot;3070&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;320&quot; y=&quot;170&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-9&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;305&quot; y=&quot;500&quot; width=&quot;20&quot; height=&quot;2490&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-12&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;-415&quot; y=&quot;520&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;295&quot; y=&quot;520&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-13&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Update/Insert fare-type-config via POST /v1.0/pricing/param-config/fare-type-config endpoint.&amp;amp;nbsp;This triggers insertOrUpdateFareTypeConfig function.&amp;lt;br&amp;gt;The expected request body format is as follows:&amp;lt;/font&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;{&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp; &amp;quot;fareType&amp;quot;: &amp;amp;lt;string&amp;amp;gt;,&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp; &amp;quot;userChange&amp;quot;: &amp;amp;lt;string&amp;amp;gt;,&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp; &amp;quot;configRequest&amp;quot;: {&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp; &amp;amp;nbsp; &amp;quot;value&amp;quot;: &amp;amp;lt;number&amp;amp;gt;,&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp; &amp;amp;nbsp; &amp;quot;day&amp;quot;: &amp;amp;lt;string&amp;amp;gt;,&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp; &amp;amp;nbsp; &amp;quot;hour&amp;quot;: &amp;amp;lt;string&amp;amp;gt;,&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp; &amp;amp;nbsp; &amp;quot;effectiveDate&amp;quot;: &amp;amp;lt;datetime&amp;amp;gt;,&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp; &amp;amp;nbsp; &amp;quot;effectiveDateTo&amp;quot;: &amp;amp;lt;datetime&amp;amp;gt;,&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;amp;nbsp; &amp;amp;nbsp; &amp;quot;defaultPercent&amp;quot;: &amp;amp;lt;number&amp;amp;gt;&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;}&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;br&amp;gt;&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;where &amp;quot;fareType&amp;quot; can be one of the following values:&amp;lt;/font&amp;gt;&amp;lt;div style=&amp;quot;&amp;quot;&amp;gt;&amp;lt;div style=&amp;quot;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;br&amp;gt;&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;span style=&amp;quot;background-color: initial;&amp;quot;&amp;gt;- DYNP_BOOKING_FEE&amp;lt;/span&amp;gt;&amp;lt;br&amp;gt;&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;&amp;lt;div style=&amp;quot;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;- DYNP_DESURGE_MAX_CAP&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;- DYNP_DURATION_RATE&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;- DYNP_FLAG_DOWN_RATE&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;- DYNP_MAX_CAP&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;- DYNP_MIN_CAP&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;- DYNP_MIN_SURGE_AMOUNT&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;- DYNP_PEAK_MIDNIGHT_HOUR_RATE&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;- DYNP_SURGE_BUFFER&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;- DYNP_TIER_1_END_DIST&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;- DYNP_TIER_1_PRICE_MULTIPLIER&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;- DYNP_TIER_1_START_DIST&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;- DYNP_TIER_2_END_DIST&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;- DYNP_TIER_2_PRICE_MULTIPLIER&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;- DYNP_TIER_2_START_DIST&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;&amp;quot;&amp;gt;&amp;lt;br&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-410&quot; y=&quot;-130&quot; width=&quot;720&quot; height=&quot;660&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-14&quot; value=&quot;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Developer&amp;lt;/span&amp;gt;&quot; style=&quot;shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;labelBackgroundColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-445&quot; y=&quot;-347&quot; width=&quot;30&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-15&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;-435&quot; y=&quot;-217&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;-435&quot; y=&quot;-237&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-17&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; parent=&quot;1&quot; target=&quot;-IJH7Msd9MsLSg7A33z7-16&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;-435&quot; y=&quot;3070&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;-435&quot; y=&quot;170&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-16&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-445&quot; y=&quot;-210&quot; width=&quot;20&quot; height=&quot;3220&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-19&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;850&quot; y=&quot;3080&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;850&quot; y=&quot;-244&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-20&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;CacheService&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;labelBackgroundColor=none;fillColor=#dae8fc;strokeColor=#6c8ebf;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1169&quot; y=&quot;-327&quot; width=&quot;291&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-21&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; parent=&quot;1&quot; edge=&quot;1&quot; source=&quot;PHQ-aainbhki9ms9CzPA-73&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1310&quot; y=&quot;2970&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1310&quot; y=&quot;-240&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-23&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;870&quot; y=&quot;620&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2460&quot; y=&quot;620&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-31&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; parent=&quot;1&quot; edge=&quot;1&quot; source=&quot;PHQ-aainbhki9ms9CzPA-63&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1730&quot; y=&quot;2530&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1720&quot; y=&quot;-240&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-54&quot; value=&quot;Redis&quot; style=&quot;shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fontSize=20;labelBackgroundColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2020&quot; y=&quot;-357&quot; width=&quot;120&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;1gH6ChYx6o4yCL0K8Lsm-1&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;RedisService&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;labelBackgroundColor=none;fillColor=#dae8fc;strokeColor=#6c8ebf;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1574.5&quot; y=&quot;-327&quot; width=&quot;291&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;1gH6ChYx6o4yCL0K8Lsm-2&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; parent=&quot;1&quot; edge=&quot;1&quot; source=&quot;PHQ-aainbhki9ms9CzPA-67&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2075&quot; y=&quot;2530&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2080&quot; y=&quot;-230&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;1gH6ChYx6o4yCL0K8Lsm-8&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Retrieve existing entries by filtering using &amp;lt;b&amp;gt;getParamConfigByListFareType&amp;lt;/b&amp;gt;( fareType ) method&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;870&quot; y=&quot;570&quot; width=&quot;925&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;HITWuKsuAOjbjixdF0Fy-31&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;FareTpeConfigRepositoryImpl&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;labelBackgroundColor=none;fillColor=#dae8fc;strokeColor=#6c8ebf;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2350&quot; y=&quot;-327&quot; width=&quot;291&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;HITWuKsuAOjbjixdF0Fy-32&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; parent=&quot;1&quot; source=&quot;uWdqOPnzoMICUYkgqbc4-1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2475&quot; y=&quot;2530&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2480&quot; y=&quot;-240&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;HITWuKsuAOjbjixdF0Fy-35&quot; value=&quot;&amp;lt;span style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;FareTypeConfigJpaRepository&amp;lt;/span&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;labelBackgroundColor=none;fillColor=#dae8fc;strokeColor=#6c8ebf;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2750&quot; y=&quot;-327&quot; width=&quot;291&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;HITWuKsuAOjbjixdF0Fy-37&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; parent=&quot;1&quot; source=&quot;uWdqOPnzoMICUYkgqbc4-12&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2880&quot; y=&quot;2510&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2880&quot; y=&quot;-240&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;HITWuKsuAOjbjixdF0Fy-44&quot; value=&quot;Postgres&quot; style=&quot;shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fontSize=20;labelBackgroundColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3260&quot; y=&quot;-357&quot; width=&quot;120&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;HITWuKsuAOjbjixdF0Fy-45&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; parent=&quot;1&quot; edge=&quot;1&quot; source=&quot;PHQ-aainbhki9ms9CzPA-15&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;3320&quot; y=&quot;2520&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;3319.17&quot; y=&quot;-230&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;uWdqOPnzoMICUYkgqbc4-2&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; parent=&quot;1&quot; target=&quot;uWdqOPnzoMICUYkgqbc4-1&quot; edge=&quot;1&quot; source=&quot;PHQ-aainbhki9ms9CzPA-9&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2475&quot; y=&quot;2530&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2480&quot; y=&quot;-240&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;uWdqOPnzoMICUYkgqbc4-1&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2470&quot; y=&quot;590&quot; width=&quot;20&quot; height=&quot;330&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;uWdqOPnzoMICUYkgqbc4-11&quot; value=&quot;&quot; style=&quot;group&quot; parent=&quot;1&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-430&quot; y=&quot;-577&quot; width=&quot;240&quot; height=&quot;147&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;uWdqOPnzoMICUYkgqbc4-9&quot; value=&quot;&amp;lt;span style=&amp;quot;color: rgba(0, 0, 0, 0); font-family: monospace; font-size: 0px; text-align: start; text-wrap: nowrap;&amp;quot;&amp;gt;%3CmxGraphModel%3E%3Croot%3E%3CmxCell%20id%3D%220%22%2F%3E%3CmxCell%20id%3D%221%22%20parent%3D%220%22%2F%3E%3CmxCell%20id%3D%222%22%20value%3D%22%22%20style%3D%22group%3BlabelBackgroundColor%3Dnone%3B%22%20vertex%3D%221%22%20connectable%3D%220%22%20parent%3D%221%22%3E%3CmxGeometry%20x%3D%22-430%22%20y%3D%22-577%22%20width%3D%22240%22%20height%3D%22110%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%223%22%20value%3D%22%26lt%3Bfont%20style%3D%26quot%3Bfont-size%3A%2020px%3B%26quot%3B%26gt%3BLEGEND%26lt%3B%2Ffont%26gt%3B%22%20style%3D%22text%3Bhtml%3D1%3Balign%3Dleft%3BverticalAlign%3Dmiddle%3BwhiteSpace%3Dwrap%3Brounded%3D0%3BlabelBackgroundColor%3Dnone%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20width%3D%2290%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%224%22%20value%3D%22%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BlabelBackgroundColor%3Dnone%3BfillColor%3D%23dae8fc%3BstrokeColor%3D%236c8ebf%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20y%3D%2240%22%20width%3D%2240%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%225%22%20value%3D%22%26lt%3Bfont%20style%3D%26quot%3Bfont-size%3A%2016px%3B%26quot%3B%26gt%3BTechFramework%20Classes%26lt%3B%2Ffont%26gt%3B%22%20style%3D%22text%3Bhtml%3D1%3Balign%3Dleft%3BverticalAlign%3Dmiddle%3BwhiteSpace%3Dwrap%3Brounded%3D0%3BlabelBackgroundColor%3Dnone%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%2250%22%20y%3D%2240%22%20width%3D%22190%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%226%22%20value%3D%22%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BlabelBackgroundColor%3Dnone%3BfillColor%3D%23d5e8d4%3BstrokeColor%3D%2382b366%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20y%3D%2280%22%20width%3D%2240%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%227%22%20value%3D%22%26lt%3Bfont%20style%3D%26quot%3Bfont-size%3A%2016px%3B%26quot%3B%26gt%3BApplication%20Classes%26lt%3B%2Ffont%26gt%3B%22%20style%3D%22text%3Bhtml%3D1%3Balign%3Dleft%3BverticalAlign%3Dmiddle%3BwhiteSpace%3Dwrap%3Brounded%3D0%3BlabelBackgroundColor%3Dnone%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%2250%22%20y%3D%2280%22%20width%3D%22160%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3C%2Froot%3E%3C%2FmxGraphModel%3E&amp;lt;/span&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;labelBackgroundColor=none;container=0;&quot; parent=&quot;uWdqOPnzoMICUYkgqbc4-11&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry y=&quot;117&quot; width=&quot;40&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;uWdqOPnzoMICUYkgqbc4-10&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;External Components&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;container=0;&quot; parent=&quot;uWdqOPnzoMICUYkgqbc4-11&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;50&quot; y=&quot;117&quot; width=&quot;160&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-4&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;LEGEND&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;container=0;&quot; parent=&quot;uWdqOPnzoMICUYkgqbc4-11&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;90&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-5&quot; value=&quot;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;labelBackgroundColor=none;fillColor=#dae8fc;strokeColor=#6c8ebf;container=0;&quot; parent=&quot;uWdqOPnzoMICUYkgqbc4-11&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry y=&quot;40&quot; width=&quot;40&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-6&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;TechFramework Classes&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;container=0;&quot; parent=&quot;uWdqOPnzoMICUYkgqbc4-11&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;50&quot; y=&quot;40&quot; width=&quot;190&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-7&quot; value=&quot;&amp;lt;span style=&amp;quot;color: rgba(0, 0, 0, 0); font-family: monospace; font-size: 0px; text-align: start; text-wrap: nowrap;&amp;quot;&amp;gt;%3CmxGraphModel%3E%3Croot%3E%3CmxCell%20id%3D%220%22%2F%3E%3CmxCell%20id%3D%221%22%20parent%3D%220%22%2F%3E%3CmxCell%20id%3D%222%22%20value%3D%22%22%20style%3D%22group%3BlabelBackgroundColor%3Dnone%3B%22%20vertex%3D%221%22%20connectable%3D%220%22%20parent%3D%221%22%3E%3CmxGeometry%20x%3D%22-430%22%20y%3D%22-577%22%20width%3D%22240%22%20height%3D%22110%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%223%22%20value%3D%22%26lt%3Bfont%20style%3D%26quot%3Bfont-size%3A%2020px%3B%26quot%3B%26gt%3BLEGEND%26lt%3B%2Ffont%26gt%3B%22%20style%3D%22text%3Bhtml%3D1%3Balign%3Dleft%3BverticalAlign%3Dmiddle%3BwhiteSpace%3Dwrap%3Brounded%3D0%3BlabelBackgroundColor%3Dnone%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20width%3D%2290%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%224%22%20value%3D%22%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BlabelBackgroundColor%3Dnone%3BfillColor%3D%23dae8fc%3BstrokeColor%3D%236c8ebf%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20y%3D%2240%22%20width%3D%2240%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%225%22%20value%3D%22%26lt%3Bfont%20style%3D%26quot%3Bfont-size%3A%2016px%3B%26quot%3B%26gt%3BTechFramework%20Classes%26lt%3B%2Ffont%26gt%3B%22%20style%3D%22text%3Bhtml%3D1%3Balign%3Dleft%3BverticalAlign%3Dmiddle%3BwhiteSpace%3Dwrap%3Brounded%3D0%3BlabelBackgroundColor%3Dnone%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%2250%22%20y%3D%2240%22%20width%3D%22190%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%226%22%20value%3D%22%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BlabelBackgroundColor%3Dnone%3BfillColor%3D%23d5e8d4%3BstrokeColor%3D%2382b366%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20y%3D%2280%22%20width%3D%2240%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%227%22%20value%3D%22%26lt%3Bfont%20style%3D%26quot%3Bfont-size%3A%2016px%3B%26quot%3B%26gt%3BApplication%20Classes%26lt%3B%2Ffont%26gt%3B%22%20style%3D%22text%3Bhtml%3D1%3Balign%3Dleft%3BverticalAlign%3Dmiddle%3BwhiteSpace%3Dwrap%3Brounded%3D0%3BlabelBackgroundColor%3Dnone%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%2250%22%20y%3D%2280%22%20width%3D%22160%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3C%2Froot%3E%3C%2FmxGraphModel%3E&amp;lt;/span&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;labelBackgroundColor=none;fillColor=#d5e8d4;strokeColor=#82b366;container=0;&quot; parent=&quot;uWdqOPnzoMICUYkgqbc4-11&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry y=&quot;80&quot; width=&quot;40&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-IJH7Msd9MsLSg7A33z7-8&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 16px;&amp;quot;&amp;gt;Application Classes&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;container=0;&quot; parent=&quot;uWdqOPnzoMICUYkgqbc4-11&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;50&quot; y=&quot;80&quot; width=&quot;160&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;uWdqOPnzoMICUYkgqbc4-13&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; parent=&quot;1&quot; target=&quot;uWdqOPnzoMICUYkgqbc4-12&quot; edge=&quot;1&quot; source=&quot;PHQ-aainbhki9ms9CzPA-11&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2880&quot; y=&quot;2510&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2880&quot; y=&quot;-240&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;uWdqOPnzoMICUYkgqbc4-12&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2870&quot; y=&quot;651&quot; width=&quot;20&quot; height=&quot;220&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;uWdqOPnzoMICUYkgqbc4-14&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2500&quot; y=&quot;680&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2860&quot; y=&quot;680&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;uWdqOPnzoMICUYkgqbc4-15&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Retrieve existing entries via getParamConfigByListFareType method&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2500&quot; y=&quot;580&quot; width=&quot;330&quot; height=&quot;90&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;uWdqOPnzoMICUYkgqbc4-16&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3310&quot; y=&quot;680&quot; width=&quot;20&quot; height=&quot;150&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;uWdqOPnzoMICUYkgqbc4-17&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2900&quot; y=&quot;710&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;3300&quot; y=&quot;710&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;uWdqOPnzoMICUYkgqbc4-18&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Retrieve records from &amp;lt;font color=&amp;quot;#2130ff&amp;quot;&amp;gt;&amp;lt;b&amp;gt;fare_type_conf&amp;lt;/b&amp;gt;&amp;amp;nbsp;&amp;lt;/font&amp;gt;table&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2900&quot; y=&quot;651&quot; width=&quot;340&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;uWdqOPnzoMICUYkgqbc4-19&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;startArrow=classic;startFill=1;endFill=0;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2900&quot; y=&quot;810&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;3300&quot; y=&quot;810&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;uWdqOPnzoMICUYkgqbc4-20&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return records&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2905&quot; y=&quot;760&quot; width=&quot;195&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-1&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2500&quot; y=&quot;860&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2865&quot; y=&quot;860&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-2&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return records&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2500&quot; y=&quot;810&quot; width=&quot;160&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-3&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;870&quot; y=&quot;900&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2460&quot; y=&quot;899.9999999999999&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-4&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return records&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;850&quot; width=&quot;165&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-5&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;870&quot; y=&quot;1010&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;875&quot; y=&quot;950&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;940&quot; y=&quot;1010&quot; /&gt;&#10;              &lt;mxPoint x=&quot;940&quot; y=&quot;950&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-6&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Check if there is existing records&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;950&quot; y=&quot;960&quot; width=&quot;165&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-7&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;870&quot; y=&quot;1150&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2460&quot; y=&quot;1150&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-8&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;If no record exist, then create a new one by calling the &amp;lt;b&amp;gt;createFareTypeConfig &amp;lt;/b&amp;gt;method&amp;lt;/font&amp;gt;&amp;lt;div&amp;gt;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;If record exists, then perform an update.&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;1100&quot; width=&quot;815&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-10&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;PHQ-aainbhki9ms9CzPA-38&quot; target=&quot;PHQ-aainbhki9ms9CzPA-9&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2475&quot; y=&quot;2530&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2480&quot; y=&quot;920&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-9&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2470&quot; y=&quot;1130&quot; width=&quot;20&quot; height=&quot;210&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-12&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;PHQ-aainbhki9ms9CzPA-40&quot; target=&quot;PHQ-aainbhki9ms9CzPA-11&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2880&quot; y=&quot;2510&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2880&quot; y=&quot;871&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-11&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2870&quot; y=&quot;1168&quot; width=&quot;20&quot; height=&quot;152&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-13&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2500&quot; y=&quot;1190&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2860&quot; y=&quot;1190&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-14&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Save new/updated record&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2500&quot; y=&quot;1160&quot; width=&quot;240&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-16&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; target=&quot;PHQ-aainbhki9ms9CzPA-15&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;3320&quot; y=&quot;3070&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;3319.17&quot; y=&quot;-230&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-15&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3310&quot; y=&quot;1210&quot; width=&quot;20&quot; height=&quot;80&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-17&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2900&quot; y=&quot;1219&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;3300&quot; y=&quot;1219&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-18&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Save record to&amp;amp;nbsp;&amp;lt;font color=&amp;quot;#2130ff&amp;quot;&amp;gt;&amp;lt;b&amp;gt;fare_type_conf&amp;lt;/b&amp;gt;&amp;amp;nbsp;&amp;lt;/font&amp;gt;table&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2900&quot; y=&quot;1160&quot; width=&quot;340&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-19&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2900&quot; y=&quot;1274.09&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;3300&quot; y=&quot;1274.09&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-20&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return newly created/updated record&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2905&quot; y=&quot;1224.09&quot; width=&quot;345&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-21&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2500&quot; y=&quot;1298.18&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2865&quot; y=&quot;1298.18&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-22&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return newly created/updated record&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2500&quot; y=&quot;1248.18&quot; width=&quot;340&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-23&quot; value=&quot;&quot; style=&quot;shape=curlyBracket;whiteSpace=wrap;html=1;rounded=1;labelPosition=left;verticalLabelPosition=middle;align=right;verticalAlign=middle;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=4;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;790&quot; y=&quot;570&quot; width=&quot;20&quot; height=&quot;470&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-24&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 24px;&amp;quot;&amp;gt;Check for existing record&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;fontSize=24;fontColor=#006600;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;500&quot; y=&quot;810&quot; width=&quot;270&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-27&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;870&quot; y=&quot;1330&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2460&quot; y=&quot;1330&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-28&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return newly created/updated record to signify success&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;870&quot; y=&quot;1290&quot; width=&quot;510&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-29&quot; value=&quot;&quot; style=&quot;shape=curlyBracket;whiteSpace=wrap;html=1;rounded=1;labelPosition=left;verticalLabelPosition=middle;align=right;verticalAlign=middle;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=4;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;790&quot; y=&quot;1070&quot; width=&quot;20&quot; height=&quot;300&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-30&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 24px;&amp;quot;&amp;gt;Create new record if there is no existing record.&amp;lt;/font&amp;gt;&amp;lt;div&amp;gt;&amp;lt;font style=&amp;quot;font-size: 24px;&amp;quot;&amp;gt;.&amp;amp;nbsp;&amp;lt;/font&amp;gt;&amp;lt;div&amp;gt;&amp;lt;font style=&amp;quot;font-size: 24px;&amp;quot;&amp;gt;Perform update if there is an existing record&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;fontSize=24;fontColor=#006600;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;405&quot; y=&quot;1130&quot; width=&quot;370&quot; height=&quot;165&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-33&quot; value=&quot;Perform loading of fareTypeConfig&amp;lt;div&amp;gt;into cache&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;fontSize=24;fontColor=#006600;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;405&quot; y=&quot;1670&quot; width=&quot;375&quot; height=&quot;90&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-34&quot; value=&quot;&quot; style=&quot;shape=curlyBracket;whiteSpace=wrap;html=1;rounded=1;labelPosition=left;verticalLabelPosition=middle;align=right;verticalAlign=middle;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=4;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;790&quot; y=&quot;1410&quot; width=&quot;20&quot; height=&quot;610&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-35&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;870&quot; y=&quot;1440&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2460&quot; y=&quot;1440&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-36&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Retrieve all fareType entries via &amp;lt;b&amp;gt;getFareTypeConfigs &amp;lt;/b&amp;gt;method&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;870&quot; y=&quot;1400&quot; width=&quot;575&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-39&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; target=&quot;PHQ-aainbhki9ms9CzPA-38&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2480&quot; y=&quot;3080&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2480&quot; y=&quot;1340&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-38&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2470&quot; y=&quot;1413&quot; width=&quot;20&quot; height=&quot;247&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-41&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; target=&quot;PHQ-aainbhki9ms9CzPA-40&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2880&quot; y=&quot;3080&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2880&quot; y=&quot;1320&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-40&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2870&quot; y=&quot;1456&quot; width=&quot;20&quot; height=&quot;184&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-42&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2500&quot; y=&quot;1480&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2860&quot; y=&quot;1480&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-43&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Retrieve all records using the &amp;lt;b&amp;gt;getFareTypeConfig&amp;lt;/b&amp;gt; method&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2500&quot; y=&quot;1430&quot; width=&quot;330&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-44&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;3310&quot; y=&quot;1489&quot; width=&quot;20&quot; height=&quot;121&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-45&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2900&quot; y=&quot;1519&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;3300&quot; y=&quot;1519&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-46&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Retrieve all records from &amp;lt;font color=&amp;quot;#2130ff&amp;quot;&amp;gt;&amp;lt;b&amp;gt;fare_type_conf&amp;lt;/b&amp;gt;&amp;amp;nbsp;&amp;lt;/font&amp;gt;table&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2900&quot; y=&quot;1460&quot; width=&quot;340&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-47&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2900&quot; y=&quot;1588.0900000000001&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;3300&quot; y=&quot;1588.0900000000001&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-48&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return records&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2905&quot; y=&quot;1538.0900000000001&quot; width=&quot;195&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-49&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2500&quot; y=&quot;1620.0000000000002&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2865&quot; y=&quot;1620.0000000000002&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-50&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return records&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2500&quot; y=&quot;1570.0000000000002&quot; width=&quot;160&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-51&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;870&quot; y=&quot;1640&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2460&quot; y=&quot;1640&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-52&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return records&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;870&quot; y=&quot;1590&quot; width=&quot;165&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-53&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;870&quot; y=&quot;1760&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;875&quot; y=&quot;1700&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;940&quot; y=&quot;1760&quot; /&gt;&#10;              &lt;mxPoint x=&quot;940&quot; y=&quot;1700&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-54&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Sort entries into a hashmap with key as &amp;lt;b&amp;gt;&amp;lt;font color=&amp;quot;#2130ff&amp;quot;&amp;gt;DYNAMIC_PRCING:FARE_TYPE:{FARE_TYPE}:{DAY}&amp;lt;/font&amp;gt;&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;955&quot; y=&quot;1690&quot; width=&quot;355&quot; height=&quot;70&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-55&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1300&quot; y=&quot;1871&quot; width=&quot;20&quot; height=&quot;139&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-56&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;837&quot; y=&quot;540&quot; width=&quot;20&quot; height=&quot;2420&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-57&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;340&quot; y=&quot;555&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;830&quot; y=&quot;555&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-58&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Trigger insertOrUpdateFareTypeConfig method&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;335&quot; y=&quot;515&quot; width=&quot;465&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-61&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;870&quot; y=&quot;1900&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1290&quot; y=&quot;1900&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-62&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Save records to cache by calling setListValue method&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;870&quot; y=&quot;1840&quot; width=&quot;350&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-64&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;PHQ-aainbhki9ms9CzPA-96&quot; target=&quot;PHQ-aainbhki9ms9CzPA-63&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1730&quot; y=&quot;3080&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1720&quot; y=&quot;-240&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-63&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1720&quot; y=&quot;1920&quot; width=&quot;20&quot; height=&quot;70&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-65&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1332&quot; y=&quot;1940&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1710&quot; y=&quot;1940&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-66&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Save records to cache by calling &amp;lt;b&amp;gt;setListValue &amp;lt;/b&amp;gt;method&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1330&quot; y=&quot;1880&quot; width=&quot;350&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-68&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;PHQ-aainbhki9ms9CzPA-82&quot; target=&quot;PHQ-aainbhki9ms9CzPA-67&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2080&quot; y=&quot;2950&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2080&quot; y=&quot;-230&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-67&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2070&quot; y=&quot;1950&quot; width=&quot;20&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-69&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1745&quot; y=&quot;1964.57&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2060&quot; y=&quot;1965&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-70&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Utilizes &amp;lt;b&amp;gt;redisTemplate.opsForList().rightPushAll&amp;lt;/b&amp;gt; method to save records to cache&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1740&quot; y=&quot;1900&quot; width=&quot;380&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-71&quot; value=&quot;Perform reloading of fareTypeConfig&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;fontSize=24;fontColor=#006600;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;405&quot; y=&quot;2300&quot; width=&quot;375&quot; height=&quot;90&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-72&quot; value=&quot;&quot; style=&quot;shape=curlyBracket;whiteSpace=wrap;html=1;rounded=1;labelPosition=left;verticalLabelPosition=middle;align=right;verticalAlign=middle;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=4;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;790&quot; y=&quot;2040&quot; width=&quot;20&quot; height=&quot;850&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-74&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;PHQ-aainbhki9ms9CzPA-92&quot; target=&quot;PHQ-aainbhki9ms9CzPA-73&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1310&quot; y=&quot;2970&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1310&quot; y=&quot;-240&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-73&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1300&quot; y=&quot;2381&quot; width=&quot;20&quot; height=&quot;199&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-76&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;870&quot; y=&quot;2408&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1290&quot; y=&quot;2408&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-77&quot; value=&quot;&amp;lt;div style=&amp;quot;forced-color-adjust: none; color: rgb(0, 0, 0); font-family: Helvetica; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;&amp;quot;&amp;gt;&amp;lt;font size=&amp;quot;1&amp;quot; style=&amp;quot;forced-color-adjust: none;&amp;quot;&amp;gt;&amp;lt;span style=&amp;quot;forced-color-adjust: none; font-size: 20px;&amp;quot;&amp;gt;Retrieve values for all the following wildcard keys:&amp;lt;/span&amp;gt;&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;forced-color-adjust: none; font-family: Helvetica; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;forced-color-adjust: none; font-size: 16px;&amp;quot;&amp;gt;&amp;lt;span style=&amp;quot;forced-color-adjust: none;&amp;quot;&amp;gt;&amp;lt;br&amp;gt;&amp;lt;font color=&amp;quot;#2130ff&amp;quot;&amp;gt;- DYNAMIC_PRICING: FARE_TYPE:DYNP_BOOKING_FEE*&amp;lt;/font&amp;gt;&amp;lt;/span&amp;gt;&amp;lt;font color=&amp;quot;#2130ff&amp;quot;&amp;gt;&amp;lt;br style=&amp;quot;forced-color-adjust: none;&amp;quot;&amp;gt;&amp;lt;/font&amp;gt;&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;forced-color-adjust: none; font-family: Helvetica; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;&amp;quot;&amp;gt;&amp;lt;font color=&amp;quot;#2130ff&amp;quot; style=&amp;quot;forced-color-adjust: none; font-size: 16px;&amp;quot;&amp;gt;&amp;lt;div style=&amp;quot;forced-color-adjust: none;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;forced-color-adjust: none; font-size: 16px;&amp;quot;&amp;gt;-&amp;amp;nbsp;&amp;lt;/font&amp;gt;DYNAMIC_PRICING: FARE_TYPE:DYNP_DESURGE_MAX_CAP*&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;forced-color-adjust: none;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;forced-color-adjust: none; font-size: 16px;&amp;quot;&amp;gt;-&amp;amp;nbsp;&amp;lt;/font&amp;gt;DYNAMIC_PRICING: FARE_TYPE:DYNP_DURATION_RATE*&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;forced-color-adjust: none;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;forced-color-adjust: none; font-size: 16px;&amp;quot;&amp;gt;-&amp;amp;nbsp;&amp;lt;/font&amp;gt;DYNAMIC_PRICING: FARE_TYPE:DYNP_FLAG_DOWN_RATE*&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;forced-color-adjust: none;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;forced-color-adjust: none; font-size: 16px;&amp;quot;&amp;gt;-&amp;amp;nbsp;&amp;lt;/font&amp;gt;DYNAMIC_PRICING: FARE_TYPE:DYNP_MAX_CAP*&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;forced-color-adjust: none;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;forced-color-adjust: none; font-size: 16px;&amp;quot;&amp;gt;-&amp;amp;nbsp;&amp;lt;/font&amp;gt;DYNAMIC_PRICING: FARE_TYPE:DYNP_MIN_CAP*&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;forced-color-adjust: none;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;forced-color-adjust: none; font-size: 16px;&amp;quot;&amp;gt;-&amp;amp;nbsp;&amp;lt;/font&amp;gt;DYNAMIC_PRICING: FARE_TYPE:DYNP_MIN_SURGE_AMOUNT*&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;forced-color-adjust: none;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;forced-color-adjust: none; font-size: 16px;&amp;quot;&amp;gt;-&amp;amp;nbsp;&amp;lt;/font&amp;gt;DYNAMIC_PRICING: FARE_TYPE:DYNP_PEAK_MIDNIGHT_HOUR_RATE*&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;forced-color-adjust: none;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;forced-color-adjust: none; font-size: 16px;&amp;quot;&amp;gt;-&amp;amp;nbsp;&amp;lt;/font&amp;gt;DYNAMIC_PRICING: FARE_TYPE:DYNP_SURGE_BUFFER*&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;forced-color-adjust: none;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;forced-color-adjust: none; font-size: 16px;&amp;quot;&amp;gt;-&amp;amp;nbsp;&amp;lt;/font&amp;gt;DYNAMIC_PRICING: FARE_TYPE:DYNP_TIER_1_END_DIST*&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;forced-color-adjust: none;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;forced-color-adjust: none; font-size: 16px;&amp;quot;&amp;gt;-&amp;amp;nbsp;&amp;lt;/font&amp;gt;DYNAMIC_PRICING: FARE_TYPE:DYNP_TIER_1_PRICE_MULTIPLIER*&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;forced-color-adjust: none;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;forced-color-adjust: none; font-size: 16px;&amp;quot;&amp;gt;-&amp;amp;nbsp;&amp;lt;/font&amp;gt;DYNAMIC_PRICING: FARE_TYPE:DYNP_TIER_1_START_DIST*&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;forced-color-adjust: none;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;forced-color-adjust: none; font-size: 16px;&amp;quot;&amp;gt;-&amp;amp;nbsp;&amp;lt;/font&amp;gt;DYNAMIC_PRICING: FARE_TYPE:DYNP_TIER_2_END_DIST*&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;forced-color-adjust: none;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;forced-color-adjust: none; font-size: 16px;&amp;quot;&amp;gt;-&amp;amp;nbsp;&amp;lt;/font&amp;gt;DYNAMIC_PRICING: FARE_TYPE:DYNP_TIER_2_PRICE_MULTIPLIER*&amp;lt;/div&amp;gt;&amp;lt;div style=&amp;quot;forced-color-adjust: none;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;forced-color-adjust: none; font-size: 16px;&amp;quot;&amp;gt;-&amp;amp;nbsp;&amp;lt;/font&amp;gt;DYNAMIC_PRICING: FARE_TYPE:DYNP_TIER_2_START_DIST*&amp;lt;/div&amp;gt;&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;whiteSpace=wrap;html=1;fillColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;870&quot; y=&quot;2030&quot; width=&quot;600&quot; height=&quot;310&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-78&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1721&quot; y=&quot;2430&quot; width=&quot;20&quot; height=&quot;130&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-80&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1330&quot; y=&quot;2460&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1708&quot; y=&quot;2460&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-81&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Retrieve entries using &amp;lt;b&amp;gt;getKeysByPattern &amp;lt;/b&amp;gt;and &amp;lt;b&amp;gt;getMultiValueList &amp;lt;/b&amp;gt;methods&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1330&quot; y=&quot;2390&quot; width=&quot;350&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-83&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; target=&quot;PHQ-aainbhki9ms9CzPA-82&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2080&quot; y=&quot;3080&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2080&quot; y=&quot;1980&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-82&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2070&quot; y=&quot;2460&quot; width=&quot;20&quot; height=&quot;80&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-84&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1746&quot; y=&quot;2479.5699999999997&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2061&quot; y=&quot;2480&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-85&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Utilizes &amp;lt;b&amp;gt;redisTemplate.scan &amp;lt;/b&amp;gt;and&amp;amp;nbsp;&amp;lt;b&amp;gt;redisTemplate.opsForList&amp;lt;/b&amp;gt;&amp;amp;nbsp;methods to retrieve records from cache&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1754&quot; y=&quot;2381&quot; width=&quot;279&quot; height=&quot;85&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-86&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2060&quot; y=&quot;2530&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1750&quot; y=&quot;2530&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-87&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return records&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1754&quot; y=&quot;2481&quot; width=&quot;175&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-88&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1330&quot; y=&quot;2550&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1708&quot; y=&quot;2550&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-89&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return records&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1330&quot; y=&quot;2500&quot; width=&quot;175&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-90&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;strokeWidth=2;startArrow=classic;startFill=1;endFill=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;870&quot; y=&quot;2570&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1290&quot; y=&quot;2570&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-91&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return records&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;2520&quot; width=&quot;175&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-93&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; target=&quot;PHQ-aainbhki9ms9CzPA-92&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1310&quot; y=&quot;3080&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1310&quot; y=&quot;2580&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-92&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1300&quot; y=&quot;2732&quot; width=&quot;20&quot; height=&quot;199&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-94&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;870&quot; y=&quot;2746&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1290&quot; y=&quot;2746&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-95&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Save the same records as a combined list to the following keys:&amp;lt;/font&amp;gt;&amp;lt;div&amp;gt;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;&amp;lt;br&amp;gt;&amp;lt;/font&amp;gt;&amp;lt;div&amp;gt;&amp;lt;font color=&amp;quot;#2130ff&amp;quot; style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;- DYNAMIC_PRICING:COMMON:FARE_TYPE:FARE_TYPE_CONFIG_SET&amp;lt;br&amp;gt;- DYNAMIC_PRICING:FARE_TYPE:FARE_TYPE_CONFIG_SET&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div&amp;gt;&amp;lt;br&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div&amp;gt;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;This is done by calling the &amp;lt;b&amp;gt;setValue &amp;lt;/b&amp;gt;method&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;870&quot; y=&quot;2620&quot; width=&quot;770&quot; height=&quot;110&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-97&quot; value=&quot;&quot; style=&quot;endArrow=none;dashed=1;html=1;rounded=0;strokeWidth=2;labelBackgroundColor=none;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; target=&quot;PHQ-aainbhki9ms9CzPA-96&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1730&quot; y=&quot;3080&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1730&quot; y=&quot;1990&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-96&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1721&quot; y=&quot;2760&quot; width=&quot;20&quot; height=&quot;130&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-98&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1330&quot; y=&quot;2800&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1708&quot; y=&quot;2800&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-99&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Save records to cache by calling &amp;lt;b&amp;gt;setValue&amp;lt;/b&amp;gt; method&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1330&quot; y=&quot;2750&quot; width=&quot;350&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-100&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2070&quot; y=&quot;2800&quot; width=&quot;20&quot; height=&quot;80&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-101&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Save records to cache by calling &amp;lt;b&amp;gt;redisTemplate.opsForValue().set&amp;lt;/b&amp;gt;&amp;amp;nbsp;&amp;lt;/font&amp;gt;&amp;lt;div&amp;gt;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;method&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1754&quot; y=&quot;2760&quot; width=&quot;285&quot; height=&quot;90&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-102&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1749&quot; y=&quot;2860&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2064&quot; y=&quot;2860.43&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-103&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;830&quot; y=&quot;2920&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;332&quot; y=&quot;2920&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-104&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return newly created/updated config&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;347&quot; y=&quot;2880&quot; width=&quot;373&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-105&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;290&quot; y=&quot;2970&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;-420&quot; y=&quot;2970&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PHQ-aainbhki9ms9CzPA-106&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Return newly created/updated config&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-410&quot; y=&quot;2931&quot; width=&quot;373&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;&lt;/mxfile&gt;&#10;" style="background-color: rgb(255, 255, 255);"><defs/><rect fill="#ffffff" width="100%" height="100%" x="0" y="0"/><g><g><rect x="675" y="250" width="270" height="60" rx="9" ry="9" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 268px; height: 1px; padding-top: 280px; margin-left: 676px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">ParameterConfigController</font></div></div></div></foreignObject><text x="810" y="284" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">ParameterConfigController</text></switch></g></g><g><path d="M 5 330 L 4035 330" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/></g><g><rect x="1185" y="250" width="270" height="60" rx="9" ry="9" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 268px; height: 1px; padding-top: 280px; margin-left: 1186px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">FareTpeConfigServiceImpl</font></div></div></div></foreignObject><text x="1320" y="284" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">FareTpeConfigServiceImpl</text></switch></g></g><g><path d="M 790 1077 L 795 340" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><path d="M 790 3647 L 790 3567" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><rect x="780" y="1077" width="20" height="2490" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 60 1097 L 761.76 1097" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 767.76 1097 L 759.76 1101 L 761.76 1097 L 759.76 1093 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="65" y="447" width="720" height="660" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 718px; height: 1px; padding-top: 777px; margin-left: 67px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Update/Insert fare-type-config via POST /v1.0/pricing/param-config/fare-type-config endpoint. This triggers insertOrUpdateFareTypeConfig function.<br />The expected request body format is as follows:</font><div style="font-size: 16px;"><font style="font-size: 16px;">{</font></div><div style="font-size: 16px;"><font style="font-size: 16px;">  "fareType": &lt;string&gt;,</font></div><div style="font-size: 16px;"><font style="font-size: 16px;">  "userChange": &lt;string&gt;,</font></div><div style="font-size: 16px;"><font style="font-size: 16px;">  "configRequest": {</font></div><div style="font-size: 16px;"><font style="font-size: 16px;">    "value": &lt;number&gt;,</font></div><div style="font-size: 16px;"><font style="font-size: 16px;">    "day": &lt;string&gt;,</font></div><div style="font-size: 16px;"><font style="font-size: 16px;">    "hour": &lt;string&gt;,</font></div><div style="font-size: 16px;"><font style="font-size: 16px;">    "effectiveDate": &lt;datetime&gt;,</font></div><div style="font-size: 16px;"><font style="font-size: 16px;">    "effectiveDateTo": &lt;datetime&gt;,</font></div><div style="font-size: 16px;"><font style="font-size: 16px;">    "defaultPercent": &lt;number&gt;</font></div><div style="font-size: 16px;"><font style="font-size: 16px;">}</font></div><div style="font-size: 16px;"><font style="font-size: 16px;"><br /></font></div><div style="font-size: 16px;"><font style="font-size: 16px;">where "fareType" can be one of the following values:</font><div style=""><div style=""><font style="font-size: 16px;"><br /></font></div><div style=""><font style="font-size: 16px;"><span style="background-color: initial;">- DYNP_BOOKING_FEE</span><br /></font></div><div style=""><font style="font-size: 16px;"><div style=""><font style="font-size: 16px;">- DYNP_DESURGE_MAX_CAP</font></div><div style=""><font style="font-size: 16px;">- DYNP_DURATION_RATE</font></div><div style=""><font style="font-size: 16px;">- DYNP_FLAG_DOWN_RATE</font></div><div style=""><font style="font-size: 16px;">- DYNP_MAX_CAP</font></div><div style=""><font style="font-size: 16px;">- DYNP_MIN_CAP</font></div><div style=""><font style="font-size: 16px;">- DYNP_MIN_SURGE_AMOUNT</font></div><div style=""><font style="font-size: 16px;">- DYNP_PEAK_MIDNIGHT_HOUR_RATE</font></div><div style=""><font style="font-size: 16px;">- DYNP_SURGE_BUFFER</font></div><div style=""><font style="font-size: 16px;">- DYNP_TIER_1_END_DIST</font></div><div style=""><font style="font-size: 16px;">- DYNP_TIER_1_PRICE_MULTIPLIER</font></div><div style=""><font style="font-size: 16px;">- DYNP_TIER_1_START_DIST</font></div><div style=""><font style="font-size: 16px;">- DYNP_TIER_2_END_DIST</font></div><div style=""><font style="font-size: 16px;">- DYNP_TIER_2_PRICE_MULTIPLIER</font></div><div style=""><font style="font-size: 16px;">- DYNP_TIER_2_START_DIST</font></div><div style=""><br /></div></font></div></div></div></div></div></div></foreignObject><text x="67" y="781" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Update/Insert fare-type-config via POST /v1.0/pricing/param-config/fare-type-config endpoint. This triggers insertOrUpda...</text></switch></g></g><g><ellipse cx="45" cy="237.5" rx="7.5" ry="7.5" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><path d="M 45 245 L 45 270 M 45 250 L 30 250 M 45 250 L 60 250 M 45 270 L 30 290 M 45 270 L 60 290" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 297px; margin-left: 45px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;"><span style="font-size: 20px;">Developer</span></div></div></div></foreignObject><text x="45" y="309" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Devel...</text></switch></g></g><g><path d="M 40 360 L 40 340" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><path d="M 40 3647 L 40 3587" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><rect x="30" y="367" width="20" height="3220" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 1325 3657 L 1325 333" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><rect x="1644" y="250" width="291" height="60" rx="9" ry="9" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 289px; height: 1px; padding-top: 280px; margin-left: 1645px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">CacheService</font></div></div></div></foreignObject><text x="1790" y="284" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">CacheService</text></switch></g></g><g><path d="M 1785 2958 L 1785 337" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><path d="M 1345 1197 L 2926.76 1197" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2932.76 1197 L 2924.76 1201 L 2926.76 1197 L 2924.76 1193 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><path d="M 2204.84 2497 L 2195 337" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><path d="M 2495 235 C 2495 226.72 2521.86 220 2555 220 C 2570.91 220 2586.17 221.58 2597.43 224.39 C 2608.68 227.21 2615 231.02 2615 235 L 2615 305 C 2615 313.28 2588.14 320 2555 320 C 2521.86 320 2495 313.28 2495 305 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 2615 235 C 2615 243.28 2588.14 250 2555 250 C 2521.86 250 2495 243.28 2495 235" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 283px; margin-left: 2496px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Redis</div></div></div></foreignObject><text x="2555" y="289" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="20px" text-anchor="middle">Redis</text></switch></g></g><g><rect x="2049.5" y="250" width="291" height="60" rx="9" ry="9" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 289px; height: 1px; padding-top: 280px; margin-left: 2051px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">RedisService</font></div></div></div></foreignObject><text x="2195" y="284" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">RedisService</text></switch></g></g><g><path d="M 2555 2527 L 2555 347" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><rect x="1345" y="1147" width="925" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 923px; height: 1px; padding-top: 1162px; margin-left: 1347px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Retrieve existing entries by filtering using <b>getParamConfigByListFareType</b>( fareType ) method</font></div></div></div></foreignObject><text x="1347" y="1166" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve existing entries by filtering using getParamConfigByListFareType( fareType ) method</text></switch></g></g><g><rect x="2825" y="250" width="291" height="60" rx="9" ry="9" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 289px; height: 1px; padding-top: 280px; margin-left: 2826px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">FareTpeConfigRepositoryImpl</font></div></div></div></foreignObject><text x="2971" y="284" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">FareTpeConfigRepositoryImpl</text></switch></g></g><g><path d="M 2955 1167 L 2955 337" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><rect x="3225" y="250" width="291" height="60" rx="9" ry="9" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 289px; height: 1px; padding-top: 280px; margin-left: 3226px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="font-size: 20px;">FareTypeConfigJpaRepository</span></div></div></div></foreignObject><text x="3371" y="284" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">FareTypeConfigJpaRepository</text></switch></g></g><g><path d="M 3355 1228 L 3355 337" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><path d="M 3735 235 C 3735 226.72 3761.86 220 3795 220 C 3810.91 220 3826.17 221.58 3837.43 224.39 C 3848.68 227.21 3855 231.02 3855 235 L 3855 305 C 3855 313.28 3828.14 320 3795 320 C 3761.86 320 3735 313.28 3735 305 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 3855 235 C 3855 243.28 3828.14 250 3795 250 C 3761.86 250 3735 243.28 3735 235" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 283px; margin-left: 3736px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Postgres</div></div></div></foreignObject><text x="3795" y="289" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="20px" text-anchor="middle">Postgres</text></switch></g></g><g><path d="M 3794.98 1787 L 3794.17 347" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><path d="M 2955 1707 L 2955 1497" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><rect x="2945" y="1167" width="20" height="330" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g/><g><rect x="45" y="117" width="40" height="30" rx="4.5" ry="4.5" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 132px; margin-left: 46px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="color: rgba(0, 0, 0, 0); font-family: monospace; font-size: 0px; text-align: start; text-wrap: nowrap;">%3CmxGraphModel%3E%3Croot%3E%3CmxCell%20id%3D%220%22%2F%3E%3CmxCell%20id%3D%221%22%20parent%3D%220%22%2F%3E%3CmxCell%20id%3D%222%22%20value%3D%22%22%20style%3D%22group%3BlabelBackgroundColor%3Dnone%3B%22%20vertex%3D%221%22%20connectable%3D%220%22%20parent%3D%221%22%3E%3CmxGeometry%20x%3D%22-430%22%20y%3D%22-577%22%20width%3D%22240%22%20height%3D%22110%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%223%22%20value%3D%22%26lt%3Bfont%20style%3D%26quot%3Bfont-size%3A%2020px%3B%26quot%3B%26gt%3BLEGEND%26lt%3B%2Ffont%26gt%3B%22%20style%3D%22text%3Bhtml%3D1%3Balign%3Dleft%3BverticalAlign%3Dmiddle%3BwhiteSpace%3Dwrap%3Brounded%3D0%3BlabelBackgroundColor%3Dnone%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20width%3D%2290%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%224%22%20value%3D%22%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BlabelBackgroundColor%3Dnone%3BfillColor%3D%23dae8fc%3BstrokeColor%3D%236c8ebf%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20y%3D%2240%22%20width%3D%2240%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%225%22%20value%3D%22%26lt%3Bfont%20style%3D%26quot%3Bfont-size%3A%2016px%3B%26quot%3B%26gt%3BTechFramework%20Classes%26lt%3B%2Ffont%26gt%3B%22%20style%3D%22text%3Bhtml%3D1%3Balign%3Dleft%3BverticalAlign%3Dmiddle%3BwhiteSpace%3Dwrap%3Brounded%3D0%3BlabelBackgroundColor%3Dnone%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%2250%22%20y%3D%2240%22%20width%3D%22190%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%226%22%20value%3D%22%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BlabelBackgroundColor%3Dnone%3BfillColor%3D%23d5e8d4%3BstrokeColor%3D%2382b366%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20y%3D%2280%22%20width%3D%2240%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%227%22%20value%3D%22%26lt%3Bfont%20style%3D%26quot%3Bfont-size%3A%2016px%3B%26quot%3B%26gt%3BApplication%20Classes%26lt%3B%2Ffont%26gt%3B%22%20style%3D%22text%3Bhtml%3D1%3Balign%3Dleft%3BverticalAlign%3Dmiddle%3BwhiteSpace%3Dwrap%3Brounded%3D0%3BlabelBackgroundColor%3Dnone%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%2250%22%20y%3D%2280%22%20width%3D%22160%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3C%2Froot%3E%3C%2FmxGraphModel%3E</span></div></div></div></foreignObject><text x="65" y="136" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">%3CmxGr...</text></switch></g></g><g><rect x="95" y="117" width="160" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 158px; height: 1px; padding-top: 132px; margin-left: 97px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 16px;">External Components</font></div></div></div></foreignObject><text x="97" y="136" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">External Components</text></switch></g></g><g><rect x="45" y="0" width="90" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 88px; height: 1px; padding-top: 15px; margin-left: 47px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">LEGEND</font></div></div></div></foreignObject><text x="47" y="19" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">LEGEND</text></switch></g></g><g><rect x="45" y="40" width="40" height="30" rx="4.5" ry="4.5" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/></g><g><rect x="95" y="40" width="190" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 188px; height: 1px; padding-top: 55px; margin-left: 97px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 16px;">TechFramework Classes</font></div></div></div></foreignObject><text x="97" y="59" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">TechFramework Classes</text></switch></g></g><g><rect x="45" y="80" width="40" height="30" rx="4.5" ry="4.5" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 95px; margin-left: 46px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="color: rgba(0, 0, 0, 0); font-family: monospace; font-size: 0px; text-align: start; text-wrap: nowrap;">%3CmxGraphModel%3E%3Croot%3E%3CmxCell%20id%3D%220%22%2F%3E%3CmxCell%20id%3D%221%22%20parent%3D%220%22%2F%3E%3CmxCell%20id%3D%222%22%20value%3D%22%22%20style%3D%22group%3BlabelBackgroundColor%3Dnone%3B%22%20vertex%3D%221%22%20connectable%3D%220%22%20parent%3D%221%22%3E%3CmxGeometry%20x%3D%22-430%22%20y%3D%22-577%22%20width%3D%22240%22%20height%3D%22110%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%223%22%20value%3D%22%26lt%3Bfont%20style%3D%26quot%3Bfont-size%3A%2020px%3B%26quot%3B%26gt%3BLEGEND%26lt%3B%2Ffont%26gt%3B%22%20style%3D%22text%3Bhtml%3D1%3Balign%3Dleft%3BverticalAlign%3Dmiddle%3BwhiteSpace%3Dwrap%3Brounded%3D0%3BlabelBackgroundColor%3Dnone%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20width%3D%2290%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%224%22%20value%3D%22%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BlabelBackgroundColor%3Dnone%3BfillColor%3D%23dae8fc%3BstrokeColor%3D%236c8ebf%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20y%3D%2240%22%20width%3D%2240%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%225%22%20value%3D%22%26lt%3Bfont%20style%3D%26quot%3Bfont-size%3A%2016px%3B%26quot%3B%26gt%3BTechFramework%20Classes%26lt%3B%2Ffont%26gt%3B%22%20style%3D%22text%3Bhtml%3D1%3Balign%3Dleft%3BverticalAlign%3Dmiddle%3BwhiteSpace%3Dwrap%3Brounded%3D0%3BlabelBackgroundColor%3Dnone%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%2250%22%20y%3D%2240%22%20width%3D%22190%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%226%22%20value%3D%22%22%20style%3D%22rounded%3D1%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BlabelBackgroundColor%3Dnone%3BfillColor%3D%23d5e8d4%3BstrokeColor%3D%2382b366%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20y%3D%2280%22%20width%3D%2240%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3CmxCell%20id%3D%227%22%20value%3D%22%26lt%3Bfont%20style%3D%26quot%3Bfont-size%3A%2016px%3B%26quot%3B%26gt%3BApplication%20Classes%26lt%3B%2Ffont%26gt%3B%22%20style%3D%22text%3Bhtml%3D1%3Balign%3Dleft%3BverticalAlign%3Dmiddle%3BwhiteSpace%3Dwrap%3Brounded%3D0%3BlabelBackgroundColor%3Dnone%3B%22%20vertex%3D%221%22%20parent%3D%222%22%3E%3CmxGeometry%20x%3D%2250%22%20y%3D%2280%22%20width%3D%22160%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3C%2Froot%3E%3C%2FmxGraphModel%3E</span></div></div></div></foreignObject><text x="65" y="99" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">%3CmxGr...</text></switch></g></g><g><rect x="95" y="80" width="160" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 158px; height: 1px; padding-top: 95px; margin-left: 97px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 16px;">Application Classes</font></div></div></div></foreignObject><text x="97" y="99" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Application Classes</text></switch></g></g><g><path d="M 3355 1745 L 3355 1448" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><rect x="3345" y="1228" width="20" height="220" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 2975 1257 L 3326.76 1257" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 3332.76 1257 L 3324.76 1261 L 3326.76 1257 L 3324.76 1253 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="2975" y="1157" width="330" height="90" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 328px; height: 1px; padding-top: 1202px; margin-left: 2977px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Retrieve existing entries via getParamConfigByListFareType method</font></div></div></div></foreignObject><text x="2977" y="1206" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve existing entries via getParamConfigByListFareT...</text></switch></g></g><g><rect x="3785" y="1257" width="20" height="150" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 3375 1287 L 3766.76 1287" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 3772.76 1287 L 3764.76 1291 L 3766.76 1287 L 3764.76 1283 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="3375" y="1228" width="340" height="50" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 338px; height: 1px; padding-top: 1253px; margin-left: 3377px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Retrieve records from <font color="#2130ff"><b>fare_type_conf</b> </font>table</font></div></div></div></foreignObject><text x="3377" y="1257" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve records from fare_type_conf table</text></switch></g></g><g><path d="M 3383.24 1387 L 3775 1387" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 3377.24 1387 L 3385.24 1383 L 3383.24 1387 L 3385.24 1391 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="3380" y="1337" width="195" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 193px; height: 1px; padding-top: 1357px; margin-left: 3382px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Return records</font></div></div></div></foreignObject><text x="3382" y="1361" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return records</text></switch></g></g><g><path d="M 2983.24 1437 L 3340 1437" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2977.24 1437 L 2985.24 1433 L 2983.24 1437 L 2985.24 1441 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="2975" y="1387" width="160" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 158px; height: 1px; padding-top: 1407px; margin-left: 2977px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Return records</font></div></div></div></foreignObject><text x="2977" y="1411" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return records</text></switch></g></g><g><path d="M 1353.24 1477 L 2935 1477" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1347.24 1477 L 1355.24 1473 L 1353.24 1477 L 1355.24 1481 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1355" y="1427" width="165" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 163px; height: 1px; padding-top: 1442px; margin-left: 1357px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Return records</font></div></div></div></foreignObject><text x="1357" y="1446" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return records</text></switch></g></g><g><path d="M 1353.24 1587 L 1415 1587 L 1415 1527 L 1350 1527" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1347.24 1587 L 1355.24 1583 L 1353.24 1587 L 1355.24 1591 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1425" y="1537" width="165" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 163px; height: 1px; padding-top: 1552px; margin-left: 1427px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Check if there is existing records</font></div></div></div></foreignObject><text x="1427" y="1556" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Check if there is existing...</text></switch></g></g><g><path d="M 1345 1727 L 2926.76 1727" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2932.76 1727 L 2924.76 1731 L 2926.76 1727 L 2924.76 1723 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1355" y="1677" width="815" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 813px; height: 1px; padding-top: 1692px; margin-left: 1357px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">If no record exist, then create a new one by calling the <b>createFareTypeConfig </b>method</font><div><font style="font-size: 20px;">If record exists, then perform an update.</font></div></div></div></div></foreignObject><text x="1357" y="1696" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">If no record exist, then create a new one by calling the createFareTypeConfig method...</text></switch></g></g><g><path d="M 2955 1990 L 2955 1917" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><rect x="2945" y="1707" width="20" height="210" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 3355 2033 L 3355 1897" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><rect x="3345" y="1745" width="20" height="152" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 2975 1767 L 3326.76 1767" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 3332.76 1767 L 3324.76 1771 L 3326.76 1767 L 3324.76 1763 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="2975" y="1737" width="240" height="20" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 238px; height: 1px; padding-top: 1747px; margin-left: 2977px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Save new/updated record</font></div></div></div></foreignObject><text x="2977" y="1751" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Save new/updated record</text></switch></g></g><g><path d="M 3795 3647 L 3795 1867" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><rect x="3785" y="1787" width="20" height="80" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 3375 1796 L 3766.76 1796" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 3772.76 1796 L 3764.76 1800 L 3766.76 1796 L 3764.76 1792 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="3375" y="1737" width="340" height="50" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 338px; height: 1px; padding-top: 1762px; margin-left: 3377px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Save record to <font color="#2130ff"><b>fare_type_conf</b> </font>table</font></div></div></div></foreignObject><text x="3377" y="1766" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Save record to fare_type_conf table</text></switch></g></g><g><path d="M 3383.24 1851.09 L 3775 1851.09" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 3377.24 1851.09 L 3385.24 1847.09 L 3383.24 1851.09 L 3385.24 1855.09 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="3380" y="1801.09" width="345" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 343px; height: 1px; padding-top: 1821px; margin-left: 3382px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Return newly created/updated record</font></div></div></div></foreignObject><text x="3382" y="1825" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return newly created/updated record</text></switch></g></g><g><path d="M 2983.24 1875.18 L 3340 1875.18" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2977.24 1875.18 L 2985.24 1871.18 L 2983.24 1875.18 L 2985.24 1879.18 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="2975" y="1825.18" width="340" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 338px; height: 1px; padding-top: 1845px; margin-left: 2977px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Return newly created/updated record</font></div></div></div></foreignObject><text x="2977" y="1849" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return newly created/updated record</text></switch></g></g><g><path d="M 1285 1147 L 1280 1147 Q 1275 1147 1275 1157 L 1275 1372 Q 1275 1382 1270 1382 L 1267.5 1382 Q 1265 1382 1270 1382 L 1272.5 1382 Q 1275 1382 1275 1392 L 1275 1607 Q 1275 1617 1280 1617 L 1285 1617" fill="none" stroke="#82b366" stroke-width="4" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="975" y="1387" width="270" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 268px; height: 1px; padding-top: 1402px; margin-left: 977px;"><div data-drawio-colors="color: #006600; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 24px; font-family: Helvetica; color: rgb(0, 102, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 24px;">Check for existing record</font></div></div></div></foreignObject><text x="977" y="1409" fill="#006600" font-family="Helvetica" font-size="24px">Check for existing reco...</text></switch></g></g><g><path d="M 1353.24 1907 L 2935 1907" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1347.24 1907 L 1355.24 1903 L 1353.24 1907 L 1355.24 1911 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1345" y="1867" width="510" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 508px; height: 1px; padding-top: 1882px; margin-left: 1347px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Return newly created/updated record to signify success</font></div></div></div></foreignObject><text x="1347" y="1886" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return newly created/updated record to signify success</text></switch></g></g><g><path d="M 1285 1647 L 1280 1647 Q 1275 1647 1275 1657 L 1275 1787 Q 1275 1797 1270 1797 L 1267.5 1797 Q 1265 1797 1270 1797 L 1272.5 1797 Q 1275 1797 1275 1807 L 1275 1937 Q 1275 1947 1280 1947 L 1285 1947" fill="none" stroke="#82b366" stroke-width="4" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="880" y="1707" width="370" height="165" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 368px; height: 1px; padding-top: 1790px; margin-left: 882px;"><div data-drawio-colors="color: #006600; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 24px; font-family: Helvetica; color: rgb(0, 102, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 24px;">Create new record if there is no existing record.</font><div><font style="font-size: 24px;">. </font><div><font style="font-size: 24px;">Perform update if there is an existing record</font></div></div></div></div></div></foreignObject><text x="882" y="1797" fill="#006600" font-family="Helvetica" font-size="24px">Create new record if there is n...</text></switch></g></g><g><rect x="880" y="2247" width="375" height="90" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 373px; height: 1px; padding-top: 2292px; margin-left: 882px;"><div data-drawio-colors="color: #006600; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 24px; font-family: Helvetica; color: rgb(0, 102, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Perform loading of fareTypeConfig<div>into cache</div></div></div></div></foreignObject><text x="882" y="2299" fill="#006600" font-family="Helvetica" font-size="24px">Perform loading of fareTypeConf...</text></switch></g></g><g><path d="M 1285 1987 L 1280 1987 Q 1275 1987 1275 1997 L 1275 2282 Q 1275 2292 1270 2292 L 1267.5 2292 Q 1265 2292 1270 2292 L 1272.5 2292 Q 1275 2292 1275 2302 L 1275 2587 Q 1275 2597 1280 2597 L 1285 2597" fill="none" stroke="#82b366" stroke-width="4" stroke-miterlimit="10" pointer-events="all"/></g><g><path d="M 1345 2017 L 2926.76 2017" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2932.76 2017 L 2924.76 2021 L 2926.76 2017 L 2924.76 2013 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1345" y="1977" width="575" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 573px; height: 1px; padding-top: 1992px; margin-left: 1347px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Retrieve all fareType entries via <b>getFareTypeConfigs </b>method</font></div></div></div></foreignObject><text x="1347" y="1996" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve all fareType entries via getFareTypeConfigs method</text></switch></g></g><g><path d="M 2955 3657 L 2955 2237" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><rect x="2945" y="1990" width="20" height="247" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 3355 3657 L 3355 2217" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><rect x="3345" y="2033" width="20" height="184" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 2975 2057 L 3326.76 2057" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 3332.76 2057 L 3324.76 2061 L 3326.76 2057 L 3324.76 2053 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="2975" y="2007" width="330" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 328px; height: 1px; padding-top: 2022px; margin-left: 2977px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Retrieve all records using the <b>getFareTypeConfig</b> method</font></div></div></div></foreignObject><text x="2977" y="2026" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve all records using the getFareTypeConfig method</text></switch></g></g><g><rect x="3785" y="2066" width="20" height="121" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 3375 2096 L 3766.76 2096" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 3772.76 2096 L 3764.76 2100 L 3766.76 2096 L 3764.76 2092 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="3375" y="2037" width="340" height="50" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 338px; height: 1px; padding-top: 2062px; margin-left: 3377px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Retrieve all records from <font color="#2130ff"><b>fare_type_conf</b> </font>table</font></div></div></div></foreignObject><text x="3377" y="2066" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve all records from fare_type_conf table</text></switch></g></g><g><path d="M 3383.24 2165.09 L 3775 2165.09" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 3377.24 2165.09 L 3385.24 2161.09 L 3383.24 2165.09 L 3385.24 2169.09 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="3380" y="2115.09" width="195" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 193px; height: 1px; padding-top: 2135px; margin-left: 3382px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Return records</font></div></div></div></foreignObject><text x="3382" y="2139" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return records</text></switch></g></g><g><path d="M 2983.24 2197 L 3340 2197" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2977.24 2197 L 2985.24 2193 L 2983.24 2197 L 2985.24 2201 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="2975" y="2147" width="160" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 158px; height: 1px; padding-top: 2167px; margin-left: 2977px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Return records</font></div></div></div></foreignObject><text x="2977" y="2171" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return records</text></switch></g></g><g><path d="M 1353.24 2217 L 2935 2217" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1347.24 2217 L 1355.24 2213 L 1353.24 2217 L 1355.24 2221 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1345" y="2167" width="165" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 163px; height: 1px; padding-top: 2182px; margin-left: 1347px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Return records</font></div></div></div></foreignObject><text x="1347" y="2186" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return records</text></switch></g></g><g><path d="M 1353.24 2337 L 1415 2337 L 1415 2277 L 1350 2277" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1347.24 2337 L 1355.24 2333 L 1353.24 2337 L 1355.24 2341 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1430" y="2267" width="355" height="70" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 353px; height: 1px; padding-top: 2302px; margin-left: 1432px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Sort entries into a hashmap with key as <b><font color="#2130ff">DYNAMIC_PRCING:FARE_TYPE:{FARE_TYPE}:{DAY}</font></b></font></div></div></div></foreignObject><text x="1432" y="2306" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Sort entries into a hashmap with key as DYNAMIC_PRCING:FARE...</text></switch></g></g><g><rect x="1775" y="2448" width="20" height="139" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><rect x="1312" y="1117" width="20" height="2420" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 815 1132 L 1296.76 1132" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1302.76 1132 L 1294.76 1136 L 1296.76 1132 L 1294.76 1128 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="810" y="1092" width="465" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 463px; height: 1px; padding-top: 1107px; margin-left: 812px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Trigger insertOrUpdateFareTypeConfig method</font></div></div></div></foreignObject><text x="812" y="1111" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Trigger insertOrUpdateFareTypeConfig method</text></switch></g></g><g><path d="M 1345 2477 L 1756.76 2477" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1762.76 2477 L 1754.76 2481 L 1756.76 2477 L 1754.76 2473 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1345" y="2417" width="350" height="50" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 348px; height: 1px; padding-top: 2442px; margin-left: 1347px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Save records to cache by calling setListValue method</font></div></div></div></foreignObject><text x="1347" y="2446" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Save records to cache by calling setListValue method</text></switch></g></g><g><path d="M 2205.93 3337 L 2205.04 2567" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><rect x="2195" y="2497" width="20" height="70" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 1807 2517 L 2176.76 2517" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2182.76 2517 L 2174.76 2521 L 2176.76 2517 L 2174.76 2513 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1805" y="2457" width="350" height="50" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 348px; height: 1px; padding-top: 2482px; margin-left: 1807px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Save records to cache by calling <b>setListValue </b>method</font></div></div></div></foreignObject><text x="1807" y="2486" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Save records to cache by calling setListValue method</text></switch></g></g><g><path d="M 2555 3037 L 2555 2557" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><rect x="2545" y="2527" width="20" height="30" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 2220 2541.57 L 2526.76 2541.99" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2532.76 2542 L 2524.76 2545.99 L 2526.76 2541.99 L 2524.77 2537.99 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="2215" y="2477" width="380" height="50" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 378px; height: 1px; padding-top: 2502px; margin-left: 2217px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Utilizes <b>redisTemplate.opsForList().rightPushAll</b> method to save records to cache</font></div></div></div></foreignObject><text x="2217" y="2506" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Utilizes redisTemplate.opsForList().rightPushAll method to save...</text></switch></g></g><g><rect x="880" y="2877" width="375" height="90" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 373px; height: 1px; padding-top: 2922px; margin-left: 882px;"><div data-drawio-colors="color: #006600; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 24px; font-family: Helvetica; color: rgb(0, 102, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Perform reloading of fareTypeConfig</div></div></div></foreignObject><text x="882" y="2929" fill="#006600" font-family="Helvetica" font-size="24px">Perform reloading of fareTypeCo...</text></switch></g></g><g><path d="M 1285 2617 L 1280 2617 Q 1275 2617 1275 2627 L 1275 3032 Q 1275 3042 1270 3042 L 1267.5 3042 Q 1265 3042 1270 3042 L 1272.5 3042 Q 1275 3042 1275 3052 L 1275 3457 Q 1275 3467 1280 3467 L 1285 3467" fill="none" stroke="#82b366" stroke-width="4" stroke-miterlimit="10" pointer-events="all"/></g><g><path d="M 1785 3309 L 1785 3157" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><rect x="1775" y="2958" width="20" height="199" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 1345 2985 L 1756.76 2985" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1762.76 2985 L 1754.76 2989 L 1756.76 2985 L 1754.76 2981 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1345" y="2607" width="600" height="310" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 598px; height: 1px; padding-top: 2614px; margin-left: 1347px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div style="forced-color-adjust: none; color: rgb(0, 0, 0); font-family: Helvetica; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;"><font style="forced-color-adjust: none;" size="1"><span style="forced-color-adjust: none; font-size: 20px;">Retrieve values for all the following wildcard keys:</span></font></div><div style="forced-color-adjust: none; font-family: Helvetica; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;"><font style="forced-color-adjust: none; font-size: 16px;"><span style="forced-color-adjust: none;"><br /><font color="#2130ff">- DYNAMIC_PRICING: FARE_TYPE:DYNP_BOOKING_FEE*</font></span><font color="#2130ff"><br style="forced-color-adjust: none;" /></font></font></div><div style="forced-color-adjust: none; font-family: Helvetica; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;"><font style="forced-color-adjust: none; font-size: 16px;" color="#2130ff"><div style="forced-color-adjust: none;"><font style="forced-color-adjust: none; font-size: 16px;">- </font>DYNAMIC_PRICING: FARE_TYPE:DYNP_DESURGE_MAX_CAP*</div><div style="forced-color-adjust: none;"><font style="forced-color-adjust: none; font-size: 16px;">- </font>DYNAMIC_PRICING: FARE_TYPE:DYNP_DURATION_RATE*</div><div style="forced-color-adjust: none;"><font style="forced-color-adjust: none; font-size: 16px;">- </font>DYNAMIC_PRICING: FARE_TYPE:DYNP_FLAG_DOWN_RATE*</div><div style="forced-color-adjust: none;"><font style="forced-color-adjust: none; font-size: 16px;">- </font>DYNAMIC_PRICING: FARE_TYPE:DYNP_MAX_CAP*</div><div style="forced-color-adjust: none;"><font style="forced-color-adjust: none; font-size: 16px;">- </font>DYNAMIC_PRICING: FARE_TYPE:DYNP_MIN_CAP*</div><div style="forced-color-adjust: none;"><font style="forced-color-adjust: none; font-size: 16px;">- </font>DYNAMIC_PRICING: FARE_TYPE:DYNP_MIN_SURGE_AMOUNT*</div><div style="forced-color-adjust: none;"><font style="forced-color-adjust: none; font-size: 16px;">- </font>DYNAMIC_PRICING: FARE_TYPE:DYNP_PEAK_MIDNIGHT_HOUR_RATE*</div><div style="forced-color-adjust: none;"><font style="forced-color-adjust: none; font-size: 16px;">- </font>DYNAMIC_PRICING: FARE_TYPE:DYNP_SURGE_BUFFER*</div><div style="forced-color-adjust: none;"><font style="forced-color-adjust: none; font-size: 16px;">- </font>DYNAMIC_PRICING: FARE_TYPE:DYNP_TIER_1_END_DIST*</div><div style="forced-color-adjust: none;"><font style="forced-color-adjust: none; font-size: 16px;">- </font>DYNAMIC_PRICING: FARE_TYPE:DYNP_TIER_1_PRICE_MULTIPLIER*</div><div style="forced-color-adjust: none;"><font style="forced-color-adjust: none; font-size: 16px;">- </font>DYNAMIC_PRICING: FARE_TYPE:DYNP_TIER_1_START_DIST*</div><div style="forced-color-adjust: none;"><font style="forced-color-adjust: none; font-size: 16px;">- </font>DYNAMIC_PRICING: FARE_TYPE:DYNP_TIER_2_END_DIST*</div><div style="forced-color-adjust: none;"><font style="forced-color-adjust: none; font-size: 16px;">- </font>DYNAMIC_PRICING: FARE_TYPE:DYNP_TIER_2_PRICE_MULTIPLIER*</div><div style="forced-color-adjust: none;"><font style="forced-color-adjust: none; font-size: 16px;">- </font>DYNAMIC_PRICING: FARE_TYPE:DYNP_TIER_2_START_DIST*</div></font></div></div></div></div></foreignObject><text x="1347" y="2626" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve values for all the following wildcard keys:...</text></switch></g></g><g><rect x="2196" y="3007" width="20" height="130" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 1805 3037 L 2174.76 3037" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2180.76 3037 L 2172.76 3041 L 2174.76 3037 L 2172.76 3033 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1805" y="2967" width="350" height="60" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 348px; height: 1px; padding-top: 2997px; margin-left: 1807px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Retrieve entries using <b>getKeysByPattern </b>and <b>getMultiValueList </b>methods</font></div></div></div></foreignObject><text x="1807" y="3001" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Retrieve entries using getKeysByPattern and getMultiValueL...</text></switch></g></g><g><path d="M 2555 3657 L 2555 3117" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><rect x="2545" y="3037" width="20" height="80" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 2221 3056.57 L 2527.76 3056.99" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2533.76 3057 L 2525.76 3060.99 L 2527.76 3056.99 L 2525.77 3052.99 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="2229" y="2958" width="279" height="85" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 277px; height: 1px; padding-top: 3001px; margin-left: 2231px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Utilizes <b>redisTemplate.scan </b>and <b>redisTemplate.opsForList</b> methods to retrieve records from cache</font></div></div></div></foreignObject><text x="2231" y="3004" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Utilizes redisTemplate.scan and redisTemplate....</text></switch></g></g><g><path d="M 2535 3107 L 2233.24 3107" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2227.24 3107 L 2235.24 3103 L 2233.24 3107 L 2235.24 3111 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="2229" y="3058" width="175" height="50" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 173px; height: 1px; padding-top: 3083px; margin-left: 2231px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Return records</font></div></div></div></foreignObject><text x="2231" y="3087" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return records</text></switch></g></g><g><path d="M 1813.24 3127 L 2183 3127" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1807.24 3127 L 1815.24 3123 L 1813.24 3127 L 1815.24 3131 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1805" y="3077" width="175" height="50" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 173px; height: 1px; padding-top: 3102px; margin-left: 1807px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Return records</font></div></div></div></foreignObject><text x="1807" y="3106" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return records</text></switch></g></g><g><path d="M 1353.24 3147 L 1765 3147" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1347.24 3147 L 1355.24 3143 L 1353.24 3147 L 1355.24 3151 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1355" y="3097" width="175" height="50" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 173px; height: 1px; padding-top: 3122px; margin-left: 1357px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Return records</font></div></div></div></foreignObject><text x="1357" y="3126" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return records</text></switch></g></g><g><path d="M 1785 3657 L 1785 3508" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><rect x="1775" y="3309" width="20" height="199" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 1345 3323 L 1756.76 3323" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1762.76 3323 L 1754.76 3327 L 1756.76 3323 L 1754.76 3319 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1345" y="3197" width="770" height="110" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 768px; height: 1px; padding-top: 3252px; margin-left: 1347px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Save the same records as a combined list to the following keys:</font><div><font style="font-size: 20px;"><br /></font><div><font style="font-size: 20px;" color="#2130ff"><b>- DYNAMIC_PRICING:COMMON:FARE_TYPE:FARE_TYPE_CONFIG_SET<br />- DYNAMIC_PRICING:FARE_TYPE:FARE_TYPE_CONFIG_SET</b></font></div></div><div><br /></div><div><font style="font-size: 20px;">This is done by calling the <b>setValue </b>method</font></div></div></div></div></foreignObject><text x="1347" y="3256" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Save the same records as a combined list to the following keys:...</text></switch></g></g><g><path d="M 2205 3657 L 2205.75 3467" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/></g><g><rect x="2196" y="3337" width="20" height="130" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><path d="M 1805 3377 L 2174.76 3377" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2180.76 3377 L 2172.76 3381 L 2174.76 3377 L 2172.76 3373 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1805" y="3327" width="350" height="50" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 348px; height: 1px; padding-top: 3352px; margin-left: 1807px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Save records to cache by calling <b>setValue</b> method</font></div></div></div></foreignObject><text x="1807" y="3356" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Save records to cache by calling setValue method</text></switch></g></g><g><rect x="2545" y="3377" width="20" height="80" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><rect x="2229" y="3337" width="285" height="90" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 283px; height: 1px; padding-top: 3382px; margin-left: 2231px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Save records to cache by calling <b>redisTemplate.opsForValue().set</b> </font><div><font style="font-size: 20px;">method</font></div></div></div></div></foreignObject><text x="2231" y="3386" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Save records to cache by calling redisTemplate....</text></switch></g></g><g><path d="M 2224 3437 L 2530.76 3437.42" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2536.76 3437.43 L 2528.76 3441.42 L 2530.76 3437.42 L 2528.77 3433.42 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><path d="M 1305 3497 L 815.24 3497" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 809.24 3497 L 817.24 3493 L 815.24 3497 L 817.24 3501 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="822" y="3457" width="373" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 371px; height: 1px; padding-top: 3472px; margin-left: 824px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Return newly created/updated config</font></div></div></div></foreignObject><text x="824" y="3476" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return newly created/updated config</text></switch></g></g><g><path d="M 765 3547 L 63.24 3547" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 57.24 3547 L 65.24 3543 L 63.24 3547 L 65.24 3551 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="65" y="3508" width="373" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 371px; height: 1px; padding-top: 3523px; margin-left: 67px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Return newly created/updated config</font></div></div></div></foreignObject><text x="67" y="3527" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Return newly created/updated config</text></switch></g></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>
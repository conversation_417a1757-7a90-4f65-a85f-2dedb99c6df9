<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than draw.io -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="2382px" height="1435px" viewBox="-0.5 -0.5 2382 1435" content="&lt;mxfile host=&quot;Electron&quot; modified=&quot;2025-01-01T03:42:06.879Z&quot; agent=&quot;Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.4.8 Chrome/124.0.6367.207 Electron/30.0.6 Safari/537.36&quot; etag=&quot;BXSKD68UDu3G9HR6JnJO&quot; version=&quot;24.4.8&quot; type=&quot;device&quot;&gt;&#10;  &lt;diagram name=&quot;Page-1&quot; id=&quot;CPw34ZfNydPqV-QizAOX&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;5632&quot; dy=&quot;2597&quot; grid=&quot;1&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;1169&quot; pageHeight=&quot;827&quot; background=&quot;#ffffff&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;fEwdM1FGp-Ck77g_qzQm-1&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-400&quot; y=&quot;767&quot; width=&quot;2380&quot; height=&quot;1433&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;ioPOVzLXJ8FifJzRJOXt-2&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-250&quot; y=&quot;1600&quot; width=&quot;640&quot; height=&quot;490&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;4pYb1pB9OZO8X2TulYxU-1&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;ngp-dcp-pax-svc&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;verticalAlign=top;strokeWidth=2;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;140&quot; y=&quot;1280.5&quot; width=&quot;250&quot; height=&quot;70&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;4pYb1pB9OZO8X2TulYxU-2&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;PaxApp&amp;lt;/font&amp;gt;&quot; style=&quot;outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.mobile_client;fillColor=#D2D3D3;gradientColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-240&quot; y=&quot;1284&quot; width=&quot;40.5&quot; height=&quot;63&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;4pYb1pB9OZO8X2TulYxU-4&quot; value=&quot;1.1 Trigger GetFare&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-120&quot; y=&quot;1270&quot; width=&quot;180&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;4pYb1pB9OZO8X2TulYxU-5&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;ngp-me-dynamicpricing-svc&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;verticalAlign=top;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=16;strokeWidth=2;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;980&quot; y=&quot;1140&quot; width=&quot;270&quot; height=&quot;420&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;4pYb1pB9OZO8X2TulYxU-6&quot; value=&quot;&quot; style=&quot;shape=flexArrow;endArrow=classic;html=1;rounded=0;strokeWidth=2;fillColor=#dae8fc;strokeColor=#6c8ebf;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;-190&quot; y=&quot;1315&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;130&quot; y=&quot;1315&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;4pYb1pB9OZO8X2TulYxU-7&quot; value=&quot;&quot; style=&quot;shape=flexArrow;endArrow=classic;html=1;rounded=0;strokeWidth=2;fillColor=#dae8fc;strokeColor=#6c8ebf;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;410&quot; y=&quot;1325&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;960&quot; y=&quot;1325&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;4pYb1pB9OZO8X2TulYxU-8&quot; value=&quot;1.2. Get Multi-Fare&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;420&quot; y=&quot;1284&quot; width=&quot;180&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;4pYb1pB9OZO8X2TulYxU-9&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;ngp-me-address-svc&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;verticalAlign=top;strokeWidth=2;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;970&quot; y=&quot;840&quot; width=&quot;280&quot; height=&quot;90&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;4pYb1pB9OZO8X2TulYxU-10&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;ngp-me-fleetanalytics-svc&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;verticalAlign=top;strokeWidth=2;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;980&quot; y=&quot;1750&quot; width=&quot;285&quot; height=&quot;80&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;4pYb1pB9OZO8X2TulYxU-12&quot; value=&quot;&quot; style=&quot;shape=flexArrow;endArrow=classic;html=1;rounded=0;strokeWidth=2;fillColor=#dae8fc;strokeColor=#6c8ebf;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1109.5&quot; y=&quot;1130&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1109.5&quot; y=&quot;949&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;4pYb1pB9OZO8X2TulYxU-13&quot; value=&quot;1.3. Retrieve Addresses and Route&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1130&quot; y=&quot;1020&quot; width=&quot;320&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;4pYb1pB9OZO8X2TulYxU-14&quot; value=&quot;&quot; style=&quot;shape=flexArrow;endArrow=classic;html=1;rounded=0;strokeWidth=2;fillColor=#dae8fc;strokeColor=#6c8ebf;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1110&quot; y=&quot;1570&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1110&quot; y=&quot;1730&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;4pYb1pB9OZO8X2TulYxU-15&quot; value=&quot;1.4. Retrieve Demand/Supply statisticcs&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1140&quot; y=&quot;1640&quot; width=&quot;320&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;4pYb1pB9OZO8X2TulYxU-16&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;postgres&amp;lt;/font&amp;gt;&quot; style=&quot;shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;strokeWidth=2;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1420&quot; y=&quot;1370&quot; width=&quot;110&quot; height=&quot;89.5&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;4pYb1pB9OZO8X2TulYxU-17&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;redis&amp;lt;/font&amp;gt;&quot; style=&quot;shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;strokeWidth=2;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1420&quot; y=&quot;1470.5&quot; width=&quot;110&quot; height=&quot;89.5&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;4pYb1pB9OZO8X2TulYxU-18&quot; value=&quot;&quot; style=&quot;shape=flexArrow;endArrow=classic;startArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;100&quot; height=&quot;100&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1260&quot; y=&quot;1520&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1410&quot; y=&quot;1520&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;4pYb1pB9OZO8X2TulYxU-19&quot; value=&quot;&quot; style=&quot;shape=flexArrow;endArrow=classic;startArrow=classic;html=1;rounded=0;strokeWidth=2;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;100&quot; height=&quot;100&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1260&quot; y=&quot;1421&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1410&quot; y=&quot;1421&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;4pYb1pB9OZO8X2TulYxU-20&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;ngp-me-fare-service&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;verticalAlign=top;strokeWidth=2;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1570&quot; y=&quot;1220&quot; width=&quot;250&quot; height=&quot;160&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;4pYb1pB9OZO8X2TulYxU-21&quot; value=&quot;&quot; style=&quot;shape=flexArrow;endArrow=classic;html=1;rounded=0;strokeWidth=2;fillColor=#dae8fc;strokeColor=#6c8ebf;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1262&quot; y=&quot;1325&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1562&quot; y=&quot;1325&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;4pYb1pB9OZO8X2TulYxU-22&quot; value=&quot;1.5 Get static configurable components of fare (bookingFee, platformFee etc)&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1265&quot; y=&quot;1220&quot; width=&quot;290&quot; height=&quot;90&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;hJVZ83a7ZiN5PTLr_TfX-2&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;ngp-common-cms-svc&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;verticalAlign=top;strokeWidth=2;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;140&quot; y=&quot;860&quot; width=&quot;250&quot; height=&quot;70&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;hJVZ83a7ZiN5PTLr_TfX-3&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;User&amp;lt;/font&amp;gt;&quot; style=&quot;shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-230&quot; y=&quot;850&quot; width=&quot;30&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;hJVZ83a7ZiN5PTLr_TfX-4&quot; value=&quot;&quot; style=&quot;shape=flexArrow;endArrow=classic;html=1;rounded=0;strokeWidth=2;fillColor=#d5e8d4;strokeColor=#82b366;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;-170&quot; y=&quot;900&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;130&quot; y=&quot;900&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;hJVZ83a7ZiN5PTLr_TfX-5&quot; value=&quot;2.1 Update configurations&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-140&quot; y=&quot;850&quot; width=&quot;230&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;hJVZ83a7ZiN5PTLr_TfX-7&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;ngp.common.cms.refresh_setting&amp;lt;br&amp;gt;(Kafka)&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fillColor=#6a00ff;fontColor=#ffffff;strokeColor=#3700CC;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;570&quot; y=&quot;870&quot; width=&quot;340&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;hJVZ83a7ZiN5PTLr_TfX-9&quot; value=&quot;&quot; style=&quot;shape=flexArrow;endArrow=classic;html=1;rounded=0;strokeWidth=2;fillColor=#d5e8d4;strokeColor=#82b366;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;400&quot; y=&quot;894.29&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;560&quot; y=&quot;894&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;hJVZ83a7ZiN5PTLr_TfX-10&quot; value=&quot;2.2 Publish&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;410&quot; y=&quot;850&quot; width=&quot;130&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;hJVZ83a7ZiN5PTLr_TfX-11&quot; value=&quot;&quot; style=&quot;shape=flexArrow;endArrow=classic;html=1;rounded=0;strokeWidth=2;fillColor=#d5e8d4;strokeColor=#82b366;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;760&quot; y=&quot;950&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;950&quot; y=&quot;1190&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;760&quot; y=&quot;1190&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;hJVZ83a7ZiN5PTLr_TfX-12&quot; value=&quot;2.3 Consume&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;780&quot; y=&quot;1040&quot; width=&quot;130&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;ioPOVzLXJ8FifJzRJOXt-1&quot; value=&quot;&quot; style=&quot;shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,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;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-230&quot; y=&quot;1613&quot; width=&quot;56&quot; height=&quot;57&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;ioPOVzLXJ8FifJzRJOXt-3&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;AWS Event Bridge Scheduler&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-160&quot; y=&quot;1620&quot; width=&quot;280&quot; height=&quot;43&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;ioPOVzLXJ8FifJzRJOXt-4&quot; value=&quot;&quot; style=&quot;shape=flexArrow;endArrow=classic;html=1;rounded=0;strokeWidth=2;fillColor=#ffe6cc;strokeColor=#d79b00;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;400&quot; y=&quot;1720&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;970&quot; y=&quot;1500&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;910&quot; y=&quot;1720&quot; /&gt;&#10;              &lt;mxPoint x=&quot;910&quot; y=&quot;1500&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;ioPOVzLXJ8FifJzRJOXt-5&quot; value=&quot;3.1 Trigger update of surge pricing via ngp-me-r2dynamicpricing-upd-surge-lambda-cron-schedule at a rate of once every 2 minutes via the following url:&amp;lt;div&amp;gt;&amp;lt;br&amp;gt;&amp;lt;font color=&amp;quot;#2130ff&amp;quot;&amp;gt;/v2.0/pricing/update-dynamic-surge&amp;lt;/font&amp;gt;&amp;lt;br&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;410&quot; y=&quot;1550&quot; width=&quot;480&quot; height=&quot;175&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;80whLcPnr4gwUMUrk-Kn-1&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;ngp.me-r2dynamicpricing-upd-surge-lambda-cron-schedule (2min)&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-230&quot; y=&quot;1690&quot; width=&quot;600&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;&lt;/mxfile&gt;&#10;" style="background-color: rgb(255, 255, 255);"><defs/><rect fill="#ffffff" width="100%" height="100%" x="0" y="0"/><g><g><rect x="0" y="0" width="2380" height="1433" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><rect x="150" y="833" width="640" height="490" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><rect x="540" y="513.5" width="250" height="70" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 248px; height: 1px; padding-top: 521px; margin-left: 541px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">ngp-dcp-pax-svc</font></div></div></div></foreignObject><text x="665" y="533" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">ngp-dcp-pax-svc</text></switch></g></g><g><path d="M 163.48 580 C 161.63 580 160 578.42 160 576.5 L 160 520.48 C 160 518.48 161.61 517 163.36 517 L 197.01 517 C 198.78 517 200.5 518.43 200.5 520.56 L 200.5 576.56 C 200.5 578.51 198.9 580 197.16 580 Z" fill="#d2d3d3" stroke="none" pointer-events="all"/><path d="M 197.14 577.19 C 198.52 577.19 200.5 575.92 200.5 573.72 L 200.5 576.56 C 200.5 578.49 198.9 580 197.16 580 L 163.48 580 C 161.63 580 160 578.42 160 576.5 L 160 573.66 C 160 575.83 161.72 577.19 163.3 577.19 Z" fill-opacity="0.3" fill="#000000" stroke="none" pointer-events="all"/><rect x="160" y="517" width="0" height="0" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/><path d="M 170 525.29 C 168.82 525.29 167.81 524.28 167.81 522.99 C 167.81 521.47 169.14 520.81 170.03 520.81 L 190.26 520.81 C 191.64 520.81 192.47 522.04 192.47 523.02 C 192.47 524.31 191.46 525.29 190.26 525.29 Z M 166.05 565.09 C 165.06 565.09 163.83 564.34 163.83 562.96 L 163.83 531.32 C 163.83 529.81 165.13 529.1 166.11 529.1 L 194.34 529.1 C 195.48 529.1 196.58 529.93 196.58 531.26 L 196.58 562.99 C 196.58 564.06 195.53 565.09 194.33 565.09 Z M 180.12 575.62 C 178.5 575.62 176.16 574.4 176.16 571.54 C 176.16 569.5 177.96 567.78 180.11 567.78 C 182.54 567.78 184.15 569.76 184.15 571.63 C 184.15 573.43 182.52 575.62 180.12 575.62 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 587px; margin-left: 180px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;"><font style="font-size: 20px;">PaxApp</font></div></div></div></foreignObject><text x="180" y="599" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">PaxApp</text></switch></g></g><g><rect x="280" y="503" width="180" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 178px; height: 1px; padding-top: 518px; margin-left: 282px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">1.1 Trigger GetFare</div></div></div></foreignObject><text x="282" y="524" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="20px">1.1 Trigger GetFare</text></switch></g></g><g><rect x="1380" y="373" width="270" height="420" fill="#e1d5e7" stroke="#9673a6" stroke-width="2" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 268px; height: 1px; padding-top: 380px; margin-left: 1381px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">ngp-me-dynamicpricing-svc</font></div></div></div></foreignObject><text x="1515" y="396" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">ngp-me-dynamicpricing-svc</text></switch></g></g><g><path d="M 211 553.5 L 211 542.5 L 509 542.5 L 509 531.5 L 529 548 L 509 564.5 L 509 553.5 Z" fill="#dae8fc" stroke="#6c8ebf" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><path d="M 811 563.5 L 811 552.5 L 1339 552.5 L 1339 541.5 L 1359 558 L 1339 574.5 L 1339 563.5 Z" fill="#dae8fc" stroke="#6c8ebf" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="820" y="517" width="180" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 178px; height: 1px; padding-top: 532px; margin-left: 822px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">1.2. Get Multi-Fare</div></div></div></foreignObject><text x="822" y="538" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="20px">1.2. Get Multi-Fare</text></switch></g></g><g><rect x="1370" y="73" width="280" height="90" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 278px; height: 1px; padding-top: 80px; margin-left: 1371px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">ngp-me-address-svc</font></div></div></div></foreignObject><text x="1510" y="92" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">ngp-me-address-svc</text></switch></g></g><g><rect x="1380" y="983" width="285" height="80" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 283px; height: 1px; padding-top: 990px; margin-left: 1381px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">ngp-me-fleetanalytics-svc</font></div></div></div></foreignObject><text x="1523" y="1002" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">ngp-me-fleetanalytics-svc</text></switch></g></g><g><path d="M 1515 362 L 1504 362 L 1504 203 L 1493 203 L 1509.5 183 L 1526 203 L 1515 203 Z" fill="#dae8fc" stroke="#6c8ebf" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1530" y="253" width="320" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 318px; height: 1px; padding-top: 268px; margin-left: 1532px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">1.3. Retrieve Addresses and Route</div></div></div></foreignObject><text x="1532" y="274" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="20px">1.3. Retrieve Addresses and Route</text></switch></g></g><g><path d="M 1504.5 804 L 1515.5 804 L 1515.5 942 L 1526.5 942 L 1510 962 L 1493.5 942 L 1504.5 942 Z" fill="#dae8fc" stroke="#6c8ebf" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1540" y="873" width="320" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 318px; height: 1px; padding-top: 888px; margin-left: 1542px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">1.4. Retrieve Demand/Supply statisticcs</div></div></div></foreignObject><text x="1542" y="894" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="20px">1.4. Retrieve Demand/Supply stat...</text></switch></g></g><g><path d="M 1820 618 C 1820 609.72 1844.62 603 1875 603 C 1889.59 603 1903.58 604.58 1913.89 607.39 C 1924.21 610.21 1930 614.02 1930 618 L 1930 677.5 C 1930 685.78 1905.38 692.5 1875 692.5 C 1844.62 692.5 1820 685.78 1820 677.5 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 1930 618 C 1930 626.28 1905.38 633 1875 633 C 1844.62 633 1820 626.28 1820 618" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 108px; height: 1px; padding-top: 661px; margin-left: 1821px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">postgres</font></div></div></div></foreignObject><text x="1875" y="664" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">postgres</text></switch></g></g><g><path d="M 1820 718.5 C 1820 710.22 1844.62 703.5 1875 703.5 C 1889.59 703.5 1903.58 705.08 1913.89 707.89 C 1924.21 710.71 1930 714.52 1930 718.5 L 1930 778 C 1930 786.28 1905.38 793 1875 793 C 1844.62 793 1820 786.28 1820 778 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 1930 718.5 C 1930 726.78 1905.38 733.5 1875 733.5 C 1844.62 733.5 1820 726.78 1820 718.5" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 108px; height: 1px; padding-top: 761px; margin-left: 1821px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">redis</font></div></div></div></foreignObject><text x="1875" y="765" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">redis</text></switch></g></g><g><path d="M 1681 758.5 L 1681 769.5 L 1661 753 L 1681 736.5 L 1681 747.5 L 1789 747.5 L 1789 736.5 L 1809 753 L 1789 769.5 L 1789 758.5 Z" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><path d="M 1681 659.5 L 1681 670.5 L 1661 654 L 1681 637.5 L 1681 648.5 L 1789 648.5 L 1789 637.5 L 1809 654 L 1789 670.5 L 1789 659.5 Z" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1970" y="453" width="250" height="160" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 248px; height: 1px; padding-top: 460px; margin-left: 1971px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">ngp-me-fare-service</font></div></div></div></foreignObject><text x="2095" y="472" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">ngp-me-fare-service</text></switch></g></g><g><path d="M 1663 563.5 L 1663 552.5 L 1941 552.5 L 1941 541.5 L 1961 558 L 1941 574.5 L 1941 563.5 Z" fill="#dae8fc" stroke="#6c8ebf" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="1665" y="453" width="290" height="90" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 288px; height: 1px; padding-top: 498px; margin-left: 1667px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">1.5 Get static configurable components of fare (bookingFee, platformFee etc)</div></div></div></foreignObject><text x="1667" y="504" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="20px">1.5 Get static configurable c...</text></switch></g></g><g><rect x="540" y="93" width="250" height="70" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 248px; height: 1px; padding-top: 100px; margin-left: 541px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">ngp-common-cms-svc</font></div></div></div></foreignObject><text x="665" y="112" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">ngp-common-cms-svc</text></switch></g></g><g><ellipse cx="185" cy="90.5" rx="7.500000000000001" ry="7.500000000000001" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><path d="M 185 98 L 185 123 M 185 103 L 170 103 M 185 103 L 200 103 M 185 123 L 170 143 M 185 123 L 200 143" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 150px; margin-left: 185px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;"><font style="font-size: 20px;">User</font></div></div></div></foreignObject><text x="185" y="162" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">User</text></switch></g></g><g><path d="M 231 138.5 L 231 127.5 L 509 127.5 L 509 116.5 L 529 133 L 509 149.5 L 509 138.5 Z" fill="#d5e8d4" stroke="#82b366" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="260" y="83" width="230" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 228px; height: 1px; padding-top: 98px; margin-left: 262px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">2.1 Update configurations</div></div></div></foreignObject><text x="262" y="104" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="20px">2.1 Update configuratio...</text></switch></g></g><g><rect x="970" y="103" width="340" height="60" rx="9" ry="9" fill="#6a00ff" stroke="#3700cc" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 338px; height: 1px; padding-top: 133px; margin-left: 971px;"><div data-drawio-colors="color: #ffffff; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">ngp.common.cms.refresh_setting<br />(Kafka)</font></div></div></div></foreignObject><text x="1140" y="137" fill="#ffffff" font-family="Helvetica" font-size="12px" text-anchor="middle">ngp.common.cms.refresh_setting...</text></switch></g></g><g><path d="M 801.01 132.79 L 800.99 121.79 L 938.99 121.54 L 938.97 110.54 L 959 127 L 939.03 143.54 L 939.01 132.54 Z" fill="#d5e8d4" stroke="#82b366" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="810" y="83" width="130" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 128px; height: 1px; padding-top: 98px; margin-left: 812px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">2.2 Publish</div></div></div></foreignObject><text x="812" y="104" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="20px">2.2 Publish</text></switch></g></g><g><path d="M 1154.5 184 L 1165.5 184 L 1165.5 417.5 L 1329 417.5 L 1329 406.5 L 1349 423 L 1329 439.5 L 1329 428.5 L 1154.5 428.5 Z" fill="#d5e8d4" stroke="#82b366" stroke-width="2" stroke-miterlimit="1.42" pointer-events="all"/><path d="M 1329 417.5 L 1329 406.5 L 1349 423 L 1329 439.5 L 1329 428.5" fill="none" stroke="#82b366" stroke-width="2" stroke-miterlimit="4" pointer-events="all"/></g><g><rect x="1180" y="273" width="130" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 128px; height: 1px; padding-top: 288px; margin-left: 1182px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">2.3 Consume</div></div></div></foreignObject><text x="1182" y="294" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="20px">2.3 Consume</text></switch></g></g><g><image x="169.5" y="845.5" width="56" height="57" xlink:href="data:image/png;base64,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" preserveAspectRatio="none"/></g><g><rect x="240" y="853" width="280" height="43" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 278px; height: 1px; padding-top: 875px; margin-left: 242px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">AWS Event Bridge Scheduler</font></div></div></div></foreignObject><text x="242" y="878" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">AWS Event Bridge Scheduler</text></switch></g></g><g><path d="M 801 958.5 L 801 947.5 L 1304.5 947.5 L 1304.5 727.5 L 1349 727.5 L 1349 716.5 L 1369 733 L 1349 749.5 L 1349 738.5 L 1315.5 738.5 L 1315.5 958.5 Z" fill="#ffe6cc" stroke="#d79b00" stroke-width="2" stroke-miterlimit="1.42" pointer-events="all"/><path d="M 1349 727.5 L 1349 716.5 L 1369 733 L 1349 749.5 L 1349 738.5" fill="none" stroke="#d79b00" stroke-width="2" stroke-miterlimit="4" pointer-events="all"/></g><g><rect x="810" y="783" width="480" height="175" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 478px; height: 1px; padding-top: 871px; margin-left: 812px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">3.1 Trigger update of surge pricing via ngp-me-r2dynamicpricing-upd-surge-lambda-cron-schedule at a rate of once every 2 minutes via the following url:<div><br /><font color="#2130ff">/v2.0/pricing/update-dynamic-surge</font><br /></div></div></div></div></foreignObject><text x="812" y="877" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="20px">3.1 Trigger update of surge pricing via ngp-me-r...</text></switch></g></g><g><rect x="170" y="923" width="600" height="50" rx="7.5" ry="7.5" fill="#ffe6cc" stroke="#d79b00" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 598px; height: 1px; padding-top: 948px; margin-left: 171px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">ngp.me-r2dynamicpricing-upd-surge-lambda-cron-schedule (2min)</font></div></div></div></foreignObject><text x="470" y="952" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">ngp.me-r2dynamicpricing-upd-surge-lambda-cron-schedule (2min)</text></switch></g></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>
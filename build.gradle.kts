plugins {
  java
  jacoco
  id("com.diffplug.spotless") version "6.18.0"
}

// ================= Defining the group and version of the project =================
group = "com.cdg.pmg.ngp.me.dynamicpricing"

version = "0.0.1-SNAPSHOT"

java.sourceCompatibility = JavaVersion.VERSION_17

// ================= Definition of repositories =================
repositories {
  mavenCentral()
  mavenLocal()
}

// ================= Application of plugins to all projects =================
spotless {
  // Format Kotlin Gradle files
  kotlinGradle {
    target("*.gradle.kts", "**/*.gradle.kts")
    ktfmt()
  }

  // Format Java files
  java {
    target("**/*.java")
    toggleOffOn()
    removeUnusedImports()
    importOrder()
    googleJavaFormat()
    endWithNewline()
    indentWithSpaces(2)
    replace("SonarQube Comments", "// NOSONAR", "//NOSONAR")
  }
}

// Make sure spotlessApply runs before build
tasks.named("build") { dependsOn("spotlessApply") }

// ================= Jacoco Settings ================
jacoco { toolVersion = "0.8.8" }

// ================= Definition of showDirs Task =================
// When the "showDirs" task is executed, it will log the relative paths of the project's root
// directory
// to the base directory for project reports and the directory for test results.
// This custom task is useful for quickly checking the relative paths of specific directories within
// the
// project without having to navigate through the file system manually.
tasks.register("showDirs") {
  val rootDir = project.rootDir
  val reportsDir = project.reporting.baseDirectory
  val testResultsDir = project.java.testResultsDir

  doLast {
    logger.quiet(rootDir.toPath().relativize(reportsDir.get().asFile.toPath()).toString())
    logger.quiet(rootDir.toPath().relativize(testResultsDir.get().asFile.toPath()).toString())
  }
}

subprojects {
  apply(plugin = "java")
  apply(plugin = "jacoco")
  apply(plugin = "com.diffplug.spotless")

  // Configure spotless for each subproject
  spotless {
    java {
      target("**/*.java")
      toggleOffOn()
      removeUnusedImports()
      importOrder()
      googleJavaFormat()
      endWithNewline()
      indentWithSpaces(2)
      replace("SonarQube Comments", "// NOSONAR", "//NOSONAR")
    }
  }

  // Make spotlessApply run before build in each subproject
  tasks.named("build") { dependsOn("spotlessApply") }
}

// sonar {
//  properties {
//    property("sonar.projectKey", "com.cdg.pmg.ngp.me.dynamicpricing")
//    property("sonar.projectName", "ngp-me-dynamicpricing-svc")
//    property("sonar.java.binaries", "**/build/classes")
//    property("sonar.java.sources", "./techframework/src, ./application/src, ./domain/src")
//    property("sonar.tests", "junit")
//    property("sonar.dynamicAnalysis", "reuseReports")
//    property("sonar.junit.reportsPath", "**/build/test-results/test/TEST-*.xml")
//    property("sonar.cpd.exclusions", "**/inbound/dtos/**, **/entities/**")
//    property("sonar.test.exclusions", "**/test/**")
//    property(
//        "sonar.coverage.exclusions",
//        "**/aggregateroots/*, **/entities/*, **/dtos/**, **/dto/*, **/domain/**, **/constant/*,
// **/restful/apis/*, **/adapter/configs/*, **/aop/exceptions/*, **/validator/*, " +
//            ", **/configs/properties/*,  **/restful/validator/*,
// **/springdatajpa/entities/custom/*," +
//            "**/techframework/infra/adapter/properties/*," +
//            "**/application/ports/inbound/commands/*")
//    property("sonar.exclusions", "techframework/build/**")
//  }
// }

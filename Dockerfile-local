# syntax=docker/dockerfile:1

# Build stage
FROM gradle:8.4-jdk17 AS build

WORKDIR /app

# Copy the project files
COPY . .

# Run Gradle build to create a fat JAR (skip both unit tests and integration tests)
RUN gradle clean bootJar -x test -x integrationTest

# List the contents of the build directory to verify the JAR file was generated
RUN ls -la techframework/build/libs/

# Check the manifest of the JAR file
RUN jar tf techframework/build/libs/techframework-0.0.1-SNAPSHOT.jar | grep -i TechFrameworkApplication
RUN jar xf techframework/build/libs/techframework-0.0.1-SNAPSHOT.jar META-INF/MANIFEST.MF
RUN cat META-INF/MANIFEST.MF

# Main image
FROM openjdk:17-jdk-slim AS deploy

ARG profile=default

ENV APP_HOME /app
ENV APP_USER appuser
ENV JAVA_OPTS="-Dspring.profiles.active=$profile -XX:+UseG1GC -XX:+UseStringDeduplication -XX:-UseContainerSupport"

WORKDIR $APP_HOME

# Copy the built executable JAR file from the build stage
COPY --from=build /app/techframework/build/libs/techframework-0.0.1-SNAPSHOT.jar ./service.jar

# Create a non-root user
RUN adduser --disabled-password --gecos "" $APP_USER
RUN chown -R $APP_USER $APP_HOME
USER $APP_USER

EXPOSE 8080

# Run the executable JAR with Spring Boot
ENTRYPOINT ["sh", "-c", "java ${JAVA_OPTS} -jar service.jar --spring.profiles.active=$profile"]
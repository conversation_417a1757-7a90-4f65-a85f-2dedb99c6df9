package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.DynamicPricingConfigSet;
import com.cdg.pmg.ngp.me.dynamicpricing.commands.FareTypeConfigCommand;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FareTypeConfig;
import java.text.ParseException;
import java.util.List;
import java.util.Map;
import java.util.Set;

/** The interface fare type config service. */
public interface FareTypeConfigService {
  /** Load all fare type config to cache. */
  void loadFareTypeConfig();

  /** Load all fare type config in to a set */
  void reloadFareTypeConfigSet();

  /**
   * Get param config by set fare type
   *
   * @param listFareType Set fare type
   * @return List<FareTypeConfig> List fare type config
   */
  List<FareTypeConfig> getParamConfigsByListFareType(final Set<String> listFareType);

  /**
   * Insert/Update fare type config
   *
   * @param fareTypeConfigCommand Fare type config command
   * @return FareTypeConfig Fare type config
   */
  FareTypeConfig insertOrUpdateFareTypeConfig(final FareTypeConfigCommand fareTypeConfigCommand)
      throws ParseException;

  /**
   * Insert/Update fare type config
   *
   * @param key String
   * @return FareTypeConfig Fare type config
   */
  Map<String, List<FareTypeConfig>> getConfigs(String key);

  /**
   * Insert/Update fare type config
   *
   * @return FareTypeConfig Fare type config
   */
  DynamicPricingConfigSet getDynamicPricingConfigSet();
}

package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CMSConfigItem implements Serializable {
  @Serial private static final long serialVersionUID = 1L;

  private Long id;
  private String service;
  private String key;
  private String value;
  private String description;
  private String createdBy;
  private String updatedBy;
  private String createdDate;
  private String updatedDate;
}

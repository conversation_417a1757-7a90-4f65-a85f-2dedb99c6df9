package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class EstimatedFareRequest implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  private String countryCode;
  private String mobile;
  private String jobType;
  private String pickupAddressRef;
  private double pickupAddressLat;
  private double pickupAddressLng;
  private String pickupZoneId;
  private String destAddressRef;
  private double destAddressLat;
  private double destAddressLng;
  private String destZoneId;
  private String intermediateAddrRef;
  private double intermediateAddrLat;
  private double intermediateAddrLng;
  private String intermediateZoneId;
  private List<Integer> vehTypeIDs;
}

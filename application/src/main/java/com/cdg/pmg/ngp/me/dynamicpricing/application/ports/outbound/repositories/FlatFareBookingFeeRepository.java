package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.FlatFareBookingFeeConfig;
import java.util.List;

/** The interface flat fare booking fee repository. */
public interface FlatFareBookingFeeRepository {
  /**
   * Gets flat fare booking fee configuration.
   *
   * @return List of FlatFareBookingFeeConfig
   */
  List<FlatFareBookingFeeConfig> getFlatFareBookingFeeConfigs();
}

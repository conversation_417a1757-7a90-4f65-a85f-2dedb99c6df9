package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.BookingFeeRequest;
import java.time.OffsetDateTime;

public class BookingFareUtils {
  private BookingFareUtils() {}
  /**
   * Creates a BookingFeeRequest object with the provided parameters.
   *
   * @param vehicleGroupId The vehicle group ID.
   * @param fareType The fare type.
   * @param isHoliday A boolean indicating whether the request date is a holiday.
   * @param requestDate The date of the request.
   * @param productId The product ID.
   * @param jobType The job type.
   * @return A BookingFeeRequest object built with the provided parameters.
   */
  public static BookingFeeRequest createBookingFareRequest(
      final String vehicleGroupId,
      final String fareType,
      final boolean isHoliday,
      final OffsetDateTime requestDate,
      final String productId,
      final String jobType) {
    return BookingFeeRequest.builder()
        .jobType(jobType)
        .isHoliday(isHoliday)
        .vehicleTypeId(vehicleGroupId)
        .productId(productId)
        .requestDate(requestDate)
        .flatFareType(fareType)
        .build();
  }
}

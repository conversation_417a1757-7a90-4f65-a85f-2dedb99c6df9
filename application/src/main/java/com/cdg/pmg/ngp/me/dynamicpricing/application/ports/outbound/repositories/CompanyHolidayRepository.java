package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories;

import java.util.List;

/** The interface company holiday repository. */
public interface CompanyHolidayRepository {
  /**
   * Gets holiday configuration.
   *
   * @return
   */
  List<String> getCompanyHolidays();

  /**
   * Gets holiday by current date.
   *
   * @return
   */
  String getCurrentHoliday();

  /**
   * Get public holiday using Sing timezone
   *
   * @return public holiday time
   */
  String getCurrentHolidayWithSingTimezone();
}

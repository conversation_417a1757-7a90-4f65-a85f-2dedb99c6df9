package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services;

import java.util.Date;
import java.util.List;
import java.util.Map;

/** The interface Cache service. */
public interface DynpConfigCacheService {

  Map<String, String> getMapConfigsFromCache(String pattern);

  List<Map<String, String>> getMultiMapConfigsFromCache(String pattern);

  /**
   * Retrieves a combined map of configurations from cache based on a specified key pattern. This
   * method fetches all cache keys that match the given pattern, and then it combines the results
   * into a single map. There are two scenarios for the data structure in the cache: either as a
   * JSON map and as a single value string.
   *
   * @param key The key pattern used to search in the cache.
   * @return A Map of combined results where each entry consists of a configuration key and its
   *     value. The key is either the original key from the cache or an extracted part of the key,
   *     and the value is the corresponding configuration data.
   * @apiNote This method logs and handles parsing errors, adding problematic keys to a separate
   *     list. It uses helper methods like 'isJson', 'extractKeyPart', and 'unquote' for processing.
   *     The method 'handleParsingErrors' is called if there are any parsing errors.
   */
  Map<String, String> getMultiMapConfigsAndSingleValueFromCache(final String key);

  <T> List<T> getListObjectConfigFromCache(String key, Class<T> type);

  List<Double> getListDoubleConfigsFromCache(String key, String configName);

  /**
   * Get Map of List Object
   *
   * @param pattern key pattern
   * @param valueType class of value
   * @return map of list object
   * @param <V> class of value
   */
  <V> Map<String, List<V>> getMapOfListObject(String pattern, Class<V> valueType);

  /**
   * Add fare type hour rate (dynp tier, single config) config to cache.
   *
   * @param key key of cache
   * @param configs list of flat fare configs
   */
  <K, V> void addMapConfigsToCache(String key, Map<K, V> configs);

  /**
   * Add configs list to cache.
   *
   * @param key key of cache
   * @param configs list of configs
   */
  <T> void addListConfigsToCache(String key, List<T> configs);

  /**
   * Gets list value in cache by key.
   *
   * @param <T> Value Class
   * @param key of cache
   * @return the list of value
   */
  public <T> List<T> getListValue(String key, Class<T> typeOfList);

  boolean isHoliday(Date date);

  <T> List<T> getListLocSurcharge(String key, Class<T> type);

  <T> T getObjectValueByPattern(String pattern, Class<T> type);

  <T> T getValue(final String key, Class<T> valueType);
}

package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.FareTypeConfig;

/** The interface fare breakdown detail repository. */
public interface FareBreakdownDetailRepository {

  /**
   * Create fare type config
   *
   * @param fareTypeConfig Fare type config
   * @return FareTypeConfig Fare type config
   */
  FareTypeConfig createFareTypeConfig(final FareTypeConfig fareTypeConfig);
}

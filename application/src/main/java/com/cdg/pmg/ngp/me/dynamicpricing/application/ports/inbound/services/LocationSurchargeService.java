package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareHoliday;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.LocationSurchargeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.LocationSurchargeConfigRequest;
import java.util.Date;
import java.util.List;

public interface LocationSurchargeService {
  /** Load all location surcharge config to cache. */
  void loadAllLocationSurchargeConfigs();

  LocationSurchargeConfig getLocationSurchargeConfig(
      LocationSurchargeConfigRequest locationSurchargeConfigRequest);

  List<LocationSurchargeConfig> getLocationSurchargeConfigList(
      Date reqDate,
      String pickupAddrRef,
      String intermediateAddrRef,
      String dropOffAddrRef,
      List<FlatFareHoliday> holidayList);
}

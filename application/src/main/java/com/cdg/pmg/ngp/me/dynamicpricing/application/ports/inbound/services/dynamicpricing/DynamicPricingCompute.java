package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.dynamicpricing;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareVO;

public interface DynamicPricingCompute {

  /**
   * Calculates the dynamic pricing booking fee
   *
   * @param flatFareVO the FlatFareVo object containing the necessary data for the calculation
   */
  void calBookingFee(FlatFareVO flatFareVO);

  /**
   * Calculates the dynamic pricing flag down rate
   *
   * @param flatFareVO the FlatFareVo object containing the necessary data for the calculation
   */
  void calFlagDown(FlatFareVO flatFareVO);

  /**
   * Calculates the dynamic pricing tier 1 distance fare
   *
   * @param flatFareVO the FlatFareVo object containing the necessary data for the calculation
   */
  void calTier1Fare(FlatFareVO flatFareVO);

  /**
   * Calculates the dynamic pricing tier 2 distance fare
   *
   * @param flatFareVO the FlatFareVo object containing the necessary data for the calculation
   */
  void calTier2Fare(FlatFareVO flatFareVO);

  /**
   * Calculates the dynamic pricing wait time fare
   *
   * @param flatFareVO the FlatFareVo object containing the necessary data for the calculation
   */
  void calWaitTimeFare(FlatFareVO flatFareVO);

  /**
   * Calculates the dynamic pricing hourly surcharge
   *
   * @param flatFareVO the FlatFareVo object containing the necessary data for the calculation
   */
  void calHourlySurcharge(FlatFareVO flatFareVO);

  /**
   * Calculates the dynamic pricing after applying surge fare
   *
   * @param flatFareVO the FlatFareVo object containing the necessary data for the calculation
   */
  void updateAfterApplyingSurgeFare(FlatFareVO flatFareVO);

  /**
   * Set dynamic min max for total fare
   *
   * @param flatFareVO the FlatFareVo object containing the necessary data for the calculation
   */
  void setDynpMinMaxForTotalFare(FlatFareVO flatFareVO);

  /**
   * Calculate Multi Dest Surcharge
   *
   * @param flatfareVO the FlatFareVo object containing the necessary data for the calculation
   */
  void calMultiDestSurcharge(FlatFareVO flatfareVO);

  /**
   * Calculates Location Surcharge
   *
   * @param flatFareVO the FlatFareVo object containing the necessary data for the calculation
   */
  void calLocationSurcharge(FlatFareVO flatFareVO);

  /**
   * Calculates Event Surcharge
   *
   * @param flatFareVO the FlatFareVo object containing the necessary data for the calculation
   */
  void calEventSurcharge(FlatFareVO flatFareVO);
}

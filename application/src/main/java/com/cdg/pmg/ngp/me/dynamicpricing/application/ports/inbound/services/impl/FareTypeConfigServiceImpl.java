package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import static com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant.*;
import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.INVALID_END_DATE;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.DynamicPricingConfigSet;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.mapper.FareTypeConfigAppMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.FareTypeConfigService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.FareTypeConfigRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.CacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.commands.FareTypeConfigCommand;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FareTypeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FareTypeConfigCacheEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.BadRequestException;
import com.cdg.pmg.ngp.me.dynamicpricing.utils.CollectionUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.text.ParseException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@AllArgsConstructor
@ServiceComponent
@Slf4j
public class FareTypeConfigServiceImpl implements FareTypeConfigService {
  private static final String FARE_TYPE_KEY_CACHE_PREFIX =
      RedisKeyConstant.DYNAMIC_PRICING
          .concat(RedisKeyConstant.COLON)
          .concat(RedisKeyConstant.FARE_TYPE)
          .concat(RedisKeyConstant.COLON);

  private final FareTypeConfigRepository fareTypeConfigRepository;
  private final FareTypeConfigAppMapper fareTypeConfigAppMapper;
  private final CacheService cacheService;
  private final ObjectMapper objectMapper;

  @Override
  public void loadFareTypeConfig() {

    List<FareTypeConfig> configs = fareTypeConfigRepository.getFareTypeConfigs();
    final var fareTypeConfigCache = new FareTypeConfigCacheEntity();

    if (CollectionUtils.isEmpty(configs)) {
      log.info("Can't load fare type configs. The fare type config is empty!");
      return;
    }
    configs.forEach(item -> item.setCacheUpdateDate(new Date()));

    for (FareTypeConfig config : configs) {
      final Map<String, List<FareTypeConfig>> fareTypeConfigMapByDay =
          this.getListConfigsByDay(config, fareTypeConfigCache.getFareTypeConfigMapByDay());
      fareTypeConfigCache.setFareTypeConfigMapByDay(fareTypeConfigMapByDay);
    }

    this.putFareTypeConfigsToCache(fareTypeConfigCache);
    log.info("Loaded fare type configs to cache complete!");
  }

  @Override
  public List<FareTypeConfig> getParamConfigsByListFareType(final Set<String> listFareType) {
    Set<String> listKey = new HashSet<>();
    for (String key : listFareType) {
      listKey.addAll(
          cacheService.getKeysByPattern(key, RedisKeyConstant.WILDCARD, RedisKeyConstant.WILDCARD));
    }

    List<List<FareTypeConfig>> listMapConfigFromCache =
        cacheService.getMultiValueList(listKey, FareTypeConfig.class);

    if (CollectionUtils.isEmpty(listMapConfigFromCache)) {
      return fareTypeConfigRepository.getParamConfigByListFareType(listFareType);
    }

    List<FareTypeConfig> listFareTypeConfigFromCache = new ArrayList<>();
    listMapConfigFromCache.forEach(
        list -> {
          List<FareTypeConfig> listFareTypeConverted =
              objectMapper.convertValue(list, new TypeReference<>() {});
          listFareTypeConfigFromCache.addAll(listFareTypeConverted);
        });

    return new ArrayList<>(listFareTypeConfigFromCache);
  }

  @Override
  public FareTypeConfig insertOrUpdateFareTypeConfig(
      final FareTypeConfigCommand fareTypeConfigCommand) throws ParseException {
    FareTypeConfig fareTypeConfig =
        fareTypeConfigAppMapper.mapFareTypeConfigCommandToFareTypeConfig(fareTypeConfigCommand);

    if (fareTypeConfig.getEndDate().isBefore(fareTypeConfig.getStartDate())) {
      log.error("Invalid end date");
      throw new BadRequestException(INVALID_END_DATE.getMessage(), INVALID_END_DATE.getErrorCode());
    }

    Set<String> listFareType = new HashSet<>();
    listFareType.add(fareTypeConfig.getFareType());

    List<FareTypeConfig> fareTypeConfigList =
        fareTypeConfigRepository.getParamConfigByListFareType(listFareType);

    fareTypeConfigList =
        fareTypeConfigList.stream()
            .filter(item -> !checkExistingFareTypeConfig(item, fareTypeConfig))
            .toList();
    if (CollectionUtils.isEmpty(fareTypeConfigList)) {
      FareTypeConfig createdFareTypeConfig =
          fareTypeConfigRepository.createFareTypeConfig(fareTypeConfig);
      this.loadFareTypeConfig();
      this.reloadFareTypeConfigSet();
      return createdFareTypeConfig;
    }
    FareTypeConfig updatedFareTypeConfig =
        fareTypeConfigRepository.updateFareTypeConfig(fareTypeConfig);
    this.loadFareTypeConfig();
    this.reloadFareTypeConfigSet();
    return updatedFareTypeConfig;
  }

  private void putFareTypeConfigsToCache(final FareTypeConfigCacheEntity entity) {
    if (Objects.isNull(entity)) {
      return;
    }
    final Map<String, List<FareTypeConfig>> configs = entity.getFareTypeConfigMapByDay();
    this.pushConfigListToCacheByKey(configs);
  }

  private Map<String, List<FareTypeConfig>> getListConfigsByDay(
      final FareTypeConfig config, Map<String, List<FareTypeConfig>> configMap) {
    final var key =
        FARE_TYPE_KEY_CACHE_PREFIX
            .concat(config.getFareType())
            .concat(RedisKeyConstant.COLON)
            .concat(config.getDay());
    if (Objects.isNull(configMap)) {
      configMap = new TreeMap<>();
    }
    final List<FareTypeConfig> listConfigByDay =
        Optional.ofNullable(configMap.get(key)).orElse(new ArrayList<>());
    listConfigByDay.add(config);
    configMap.put(key, listConfigByDay);
    return configMap;
  }

  private void pushConfigListToCacheByKey(final Map<String, List<FareTypeConfig>> configsMap) {
    if (Objects.nonNull(configsMap)) {
      configsMap.forEach(
          (key, value) -> {
            cacheService.deleteByKey(key);
            cacheService.setListValue(key, value);
          });
    }
  }

  private boolean checkExistingFareTypeConfig(
      FareTypeConfig fareTypeConfigInput, FareTypeConfig fareTypeConfig) {
    String newFareType = fareTypeConfigInput.getFareType();
    String newDay = fareTypeConfigInput.getDay();
    String newHour = fareTypeConfigInput.getHour();
    LocalDate newStartDate = fareTypeConfigInput.getStartDate();
    LocalDate newEndDate = fareTypeConfigInput.getEndDate();

    String oldFareType = fareTypeConfig.getFareType();
    String oldDay = fareTypeConfig.getDay();
    String oldHour = fareTypeConfig.getHour();
    LocalDate oldStartDate = fareTypeConfig.getStartDate();
    LocalDate oldEndDate = fareTypeConfig.getEndDate();

    return !Objects.equals(newFareType, oldFareType)
        || !Objects.equals(newDay, oldDay)
        || !Objects.equals(newHour, oldHour)
        || !Objects.equals(newStartDate, oldStartDate)
        || !Objects.equals(newEndDate, oldEndDate);
  }

  @Override
  public Map<String, List<FareTypeConfig>> getConfigs(String key) {
    try {
      var listCachedKeys = cacheService.getKeysByPattern(key);
      var listCachedValues = cacheService.getMultiValueList(listCachedKeys, FareTypeConfig.class);
      var keyToFareTypeConfigsMap = new HashMap<String, List<FareTypeConfig>>();
      int index = 0;
      for (String keyValue : listCachedKeys) {
        var listFareTypeConfigs = new ArrayList<>(listCachedValues.get(index));
        for (FareTypeConfig config : listFareTypeConfigs) {
          if (Objects.isNull(config.getUpdatedBy())) {
            config.setUpdatedDate(config.getCreatedDate());
          }
        }
        listFareTypeConfigs.sort(Comparator.comparing(FareTypeConfig::getUpdatedDate).reversed());
        keyToFareTypeConfigsMap.put(keyValue, new ArrayList<>(listFareTypeConfigs));
        index++;
      }
      return keyToFareTypeConfigsMap;
    } catch (Exception e) {
      log.error(e.getMessage());
    }
    return new HashMap<>();
  }

  @Override
  public void reloadFareTypeConfigSet() {
    final DynamicPricingConfigSet fareTypeConfigSet = this.getDynamicPricingConfigSet();
    cacheService.setValue(CACHE_KEY_FARE_TYPE_CONFIG_SET, fareTypeConfigSet);
  }

  @Override
  public DynamicPricingConfigSet getDynamicPricingConfigSet() {
    var bookingFeeConfigList =
        getConfigs(
            DYNAMIC_PRICING_FARE_TYPE_PREFIX
                + RedisKeyConstant.DYNP_BOOKING_FEE
                + RedisKeyConstant.WILDCARD);
    var flagDownConfigList =
        getConfigs(
            DYNAMIC_PRICING_FARE_TYPE_PREFIX
                + RedisKeyConstant.DYNP_FLAG_DOWN_RATE
                + RedisKeyConstant.WILDCARD);
    var tier1StartDistConfigList =
        getConfigs(
            DYNAMIC_PRICING_FARE_TYPE_PREFIX
                + RedisKeyConstant.DYNP_TIER_1_START_DIST
                + RedisKeyConstant.WILDCARD);
    var tier1EndDistConfigList =
        getConfigs(
            DYNAMIC_PRICING_FARE_TYPE_PREFIX
                + RedisKeyConstant.DYNP_TIER_1_END_DIST
                + RedisKeyConstant.WILDCARD);
    var tier1PriceMultiplierConfigList =
        getConfigs(
            DYNAMIC_PRICING_FARE_TYPE_PREFIX
                + RedisKeyConstant.DYNP_TIER_1_PRICE_MULTIPLIER
                + RedisKeyConstant.WILDCARD);
    var tier2StartDistConfigList =
        getConfigs(
            DYNAMIC_PRICING_FARE_TYPE_PREFIX
                + RedisKeyConstant.DYNP_TIER_2_START_DIST
                + RedisKeyConstant.WILDCARD);
    var tier2EndDistConfigList =
        getConfigs(
            DYNAMIC_PRICING_FARE_TYPE_PREFIX
                + RedisKeyConstant.DYNP_TIER_2_END_DIST
                + RedisKeyConstant.WILDCARD);
    var tier2PriceMultiplierConfigList =
        getConfigs(
            DYNAMIC_PRICING_FARE_TYPE_PREFIX
                + RedisKeyConstant.DYNP_TIER_2_PRICE_MULTIPLIER
                + RedisKeyConstant.WILDCARD);
    var durationRateConfigList =
        getConfigs(
            DYNAMIC_PRICING_FARE_TYPE_PREFIX
                + RedisKeyConstant.DYNP_DURATION_RATE
                + RedisKeyConstant.WILDCARD);
    var hourlySurchargeConfigList =
        getConfigs(
            DYNAMIC_PRICING_FARE_TYPE_PREFIX
                + RedisKeyConstant.DYNP_PEAK_MIDNIGHT_HOUR_RATE
                + RedisKeyConstant.WILDCARD);
    var dynpSurgeBuffer =
        getConfigs(
            DYNAMIC_PRICING_FARE_TYPE_PREFIX
                + RedisKeyConstant.DYNP_SURGE_BUFFER
                + RedisKeyConstant.WILDCARD);
    var desurgeMaxCap =
        getConfigs(
            DYNAMIC_PRICING_FARE_TYPE_PREFIX
                + RedisKeyConstant.DYNP_DESURGE_MAX_CAP
                + RedisKeyConstant.WILDCARD);
    var minSurgeAmount =
        getConfigs(
            DYNAMIC_PRICING_FARE_TYPE_PREFIX
                + RedisKeyConstant.DYNP_MIN_SURGE_AMOUNT
                + RedisKeyConstant.WILDCARD);
    var minCap =
        getConfigs(
            DYNAMIC_PRICING_FARE_TYPE_PREFIX
                + RedisKeyConstant.DYNP_MIN_CAP
                + RedisKeyConstant.WILDCARD);
    var maxCap =
        getConfigs(
            DYNAMIC_PRICING_FARE_TYPE_PREFIX
                + RedisKeyConstant.DYNP_MAX_CAP
                + RedisKeyConstant.WILDCARD);

    return DynamicPricingConfigSet.builder()
        .surgeBufferConfigList(dynpSurgeBuffer)
        .desurgeMaxCapConfigList(desurgeMaxCap)
        .minSurgeAmountConfigList(minSurgeAmount)
        .minCapConfigList(minCap)
        .maxCapConfigList(maxCap)
        .flagDownConfigList(flagDownConfigList)
        .tier1StartDestConfigList(tier1StartDistConfigList)
        .tier1EndDestConfigList(tier1EndDistConfigList)
        .tier1PriceMultiplierConfigList(tier1PriceMultiplierConfigList)
        .tier2StartDestConfigList(tier2StartDistConfigList)
        .tier2EndDestConfigList(tier2EndDistConfigList)
        .tier2PriceMultiplierConfigList(tier2PriceMultiplierConfigList)
        .durationRateConfigList(durationRateConfigList)
        .hourlySurcharge(hourlySurchargeConfigList)
        .bookingFee(bookingFeeConfigList)
        .build();
  }
}

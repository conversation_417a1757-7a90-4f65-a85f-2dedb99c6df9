package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/** The type Error. */
@Getter
@Setter
@Builder
public class Error implements Serializable {
  @Serial private static final long serialVersionUID = 7816610691460267759L;
  private String message;
  private Long code;

  @JsonInclude(JsonInclude.Include.NON_NULL)
  private List<ErrorDetail> errors;

  /** The type Error detail. */
  @Getter
  @Setter
  @Builder
  @JsonInclude(JsonInclude.Include.NON_NULL)
  public static class ErrorDetail implements Serializable {
    @Serial private static final long serialVersionUID = -9102851913623515134L;
    private String domain;
    private String reason;
    private String field;
    private String message;
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.ConfigurationEffectiveEntity;
import java.time.Instant;
import java.util.List;
import lombok.experimental.UtilityClass;

@UtilityClass
public class ConfigurationVersionUtils {

  public static boolean isEffective(ConfigurationEffectiveEntity regionVersion, Instant now) {
    if (regionVersion.getEffectiveFrom() == null) {
      return false;
    }
    return !now.isBefore(regionVersion.getEffectiveFrom())
        && (regionVersion.getEffectiveTo() == null || now.isBefore(regionVersion.getEffectiveTo()));
  }

  /**
   * Updates the {@code isInUse} status of each {@link ConfigurationEffectiveEntity} in the provided
   * list based on the given time.
   *
   * <p>Rules:
   *
   * <ul>
   *   <li>If there is a version where {@code effectiveFrom <= now < effectiveTo} (or {@code
   *       effectiveTo == null}), that version is marked as {@code isInUse = true}.
   *   <li>If no such version exists, the most recent expired version (i.e., with the latest {@code
   *       effectiveTo} before {@code now}) is marked as {@code isInUse = true}.
   *   <li>All other versions are marked as {@code isInUse = false}.
   * </ul>
   *
   * @param domains the list of {@link ConfigurationEffectiveEntity} records to update; must not be
   *     {@code null}, but may be empty
   */
  public static void updateIsInUse(List<? extends ConfigurationEffectiveEntity> domains) {
    if (domains == null || domains.isEmpty()) {
      return;
    }

    Instant now = Instant.now();

    ConfigurationEffectiveEntity bestPast = null;
    ConfigurationEffectiveEntity currentMatch = null;

    for (ConfigurationEffectiveEntity d : domains) {
      d.setIsInUse(false);

      if (isEffective(d, now)) {
        currentMatch = d;
        break;
      }

      if (d.getEffectiveTo() != null && now.isAfter(d.getEffectiveTo())) {
        if (bestPast == null || d.getEffectiveTo().isAfter(bestPast.getEffectiveTo())) {
          bestPast = d;
        }
      }
    }

    if (currentMatch != null) {
      currentMatch.setIsInUse(true);
    } else if (bestPast != null) {
      bestPast.setIsInUse(true);
    }
  }
}

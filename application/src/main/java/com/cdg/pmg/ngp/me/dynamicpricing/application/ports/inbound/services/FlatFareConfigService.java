package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.CommonConfigSet;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.DriverSurgeLevelIndication;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.EventSurgeAddressConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareConfigSet;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareHoliday;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.LocationSurchargeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.FareTypeEnum;
import java.util.List;

/** The interface flat fare config service. */
public interface FlatFareConfigService {
  /** Load all flat fare configuration to cache. */
  void loadAllFlatFareConfig();

  /**
   * Collect configs set by fareType (Eg: EST_LIVE_TRAFFIC, EST_LIMO_LIVE_TRAFFIC)
   *
   * @param fareType @FareTypeEnum
   * @return configs set
   */
  FlatFareConfigSet collectFlatFareConfigSet(FareTypeEnum fareType);

  /**
   * Collect common configs set (Eg: limoFlatFareVehIds, vehGrpShowMeterOnly, vehGrpShowFFOnly...)
   *
   * @return configs set
   */
  CommonConfigSet collectCommonConfigSet();

  /**
   * Get flat fare additional surcharge configs
   *
   * @return list Double
   */
  List<Double> getFlatFareAdditionalSurchargeConfig();

  /**
   * Get location surcharge configs
   *
   * @param key key config
   * @return list location surcharge configs
   */
  List<LocationSurchargeConfig> getLocationSurchargeConfig(String key);

  /**
   * Get list driver surge configs
   *
   * @return list driver surge configs
   */
  List<DriverSurgeLevelIndication> getListDriverSurgeConfig();

  /**
   * Get list holiday configs
   *
   * @return list holiday configs
   */
  List<FlatFareHoliday> getListHoliday();

  /**
   * Get string value by key
   *
   * @param key key
   * @return string value
   */
  String getStringValueByKey(String key);

  List<EventSurgeAddressConfig> getListEventSurgeAddressConfig();
}

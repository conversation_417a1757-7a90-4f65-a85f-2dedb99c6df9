package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.PricingRangeConfigEntity;
import java.util.List;

public interface PricingRangeConfigRepository {
  /**
   * Get pricing range config
   *
   * @return List<PricingRangeConfigEntity> List pricing range config entity
   */
  List<PricingRangeConfigEntity> getPricingRangeConfigs();

  /**
   * Insert pricing range config
   *
   * @param pricingRangeConfigEntity Pricing range config entity
   * @return PricingRangeConfigEntity Pricing range config entity
   */
  PricingRangeConfigEntity insertPricingRangeConfig(
      final PricingRangeConfigEntity pricingRangeConfigEntity);

  /**
   * Update pricing range config
   *
   * @param newPricingRange Pricing range config entity
   * @param oldPricingRange Pricing range config entity
   * @return PricingRangeConfigEntity Pricing range config entity
   */
  PricingRangeConfigEntity updatePricingRangeConfig(
      final PricingRangeConfigEntity newPricingRange,
      final PricingRangeConfigEntity oldPricingRange);

  /**
   * Get pricing range configs by day and hour
   *
   * @param day Day of config
   * @param hour Hour of config
   * @return List<PricingRangeConfigEntity> List pricing range config entity
   */
  List<PricingRangeConfigEntity> getPricingRangeConfigsByDayAndHour(
      final String day, final String hour);
}

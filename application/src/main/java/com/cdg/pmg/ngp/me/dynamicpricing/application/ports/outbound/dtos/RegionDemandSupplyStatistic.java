package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

@Data
public class RegionDemandSupplyStatistic implements Serializable {
  @Serial private static final long serialVersionUID = 8017521171666836978L;

  private Long regionId;
  private Integer comfortRideDemand;
  private Integer meterDemand;
  private Integer comfortRideUnmetDemand;
  private Integer meterUnmetDemand;
}

package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.FareBreakdownDetailEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.RouteInfo;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.SearchFareBreakdownResponse;

/** The interface flat fare breakdown repository */
public interface FlatFareBreakDownRepository {

  /**
   * check bookingId is existed
   *
   * @param bookingId fare id
   * @return boolean
   */
  boolean isExisted(final String bookingId);

  /**
   * Create fare type config
   *
   * @param fareBreakdownDetailEntity fare breakdown data
   */
  void createFareBreakdown(final FareBreakdownDetailEntity fareBreakdownDetailEntity);

  /**
   * Gets generated route by trip id.
   *
   * @param tripId the trip id
   * @return the generated route by trip id
   */
  RouteInfo getGeneratedRouteByTripId(final String tripId);

  /**
   * Search fare breakdown by fareId, tripId, bookingId.
   *
   * @param fareId the fare id
   * @param tripId the trip id
   * @param bookingId the booking id
   * @return the flat fare breakdown
   */
  SearchFareBreakdownResponse searchFareBreakdown(String fareId, String tripId, String bookingId);
}

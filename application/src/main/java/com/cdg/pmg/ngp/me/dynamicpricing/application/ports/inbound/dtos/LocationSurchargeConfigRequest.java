package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos;

import java.time.OffsetDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LocationSurchargeConfigRequest {
  private String chargeBy;
  private String addressRef;
  private OffsetDateTime requestDate;
  private String productId;
}

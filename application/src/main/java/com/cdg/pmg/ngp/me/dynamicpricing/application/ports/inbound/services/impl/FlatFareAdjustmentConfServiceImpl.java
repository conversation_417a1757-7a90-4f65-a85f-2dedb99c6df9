package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.FlatFareAdjustmentConfService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.FlatFareAdjustmentConfRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.CacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FlatFareAdjustmentConfEntity;
import java.util.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@AllArgsConstructor
@ServiceComponent
@Slf4j
public class FlatFareAdjustmentConfServiceImpl implements FlatFareAdjustmentConfService {
  private static final String FARE_FARE_ADJUSTMENT_KEY_CACHE =
      RedisKeyConstant.DYNAMIC_PRICING
          .concat(RedisKeyConstant.COLON)
          .concat(
              RedisKeyConstant.FLAT_FARE
                  .concat(RedisKeyConstant.COLON)
                  .concat(RedisKeyConstant.FLAT_FARE_ADJUSTMENT));

  private final FlatFareAdjustmentConfRepository flatFareAdjustmentConfRepository;
  private final CacheService cacheService;

  @Override
  public void loadFlatFareAdjustmentConf() {
    cacheService.deleteByKey(FARE_FARE_ADJUSTMENT_KEY_CACHE);
    cacheService.setListValue(
        FARE_FARE_ADJUSTMENT_KEY_CACHE,
        flatFareAdjustmentConfRepository.getFlatFareAdjustmentConf());
    log.info("Loaded FareFareAdjustmentConf to cache complete!");
  }

  @Override
  public List<FlatFareAdjustmentConfEntity> getFlatFareAdjustmentConf() {
    return cacheService.getListValue(
        FARE_FARE_ADJUSTMENT_KEY_CACHE, FlatFareAdjustmentConfEntity.class);
  }
}

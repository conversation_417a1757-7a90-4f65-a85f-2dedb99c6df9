package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories;

import com.cdg.pmg.ngp.me.dynamicpricing.queries.PricingRangeCalDemandSurgeQueryResponse;
import java.util.List;

/** The interface pricing range repository. */
public interface PricingRangeRepository {
  /**
   * Gets dynp pricing range to calculate demand surge
   *
   * @return list PricingRangeCalDemandSurgeQueryResponse
   */
  List<PricingRangeCalDemandSurgeQueryResponse> getDynpConfigForDemandSurge(boolean isHoliday);

  /**
   * @param isHoliday is this day holiday
   * @return list PricingRangeCalDemandSurgeQueryResponse
   */
  List<PricingRangeCalDemandSurgeQueryResponse> getDynpConfigForDemandSurgeV2(boolean isHoliday);
}

package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdditionalChargeFeeConfigResponse implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  private Integer chargeId;
  private String chargeType;
  private String chargeKey;
  private Double chargeValue;
  private String chargeDescription;
  private String chargeFormula;
}

package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.mapper;

import com.cdg.pmg.ngp.me.dynamicpricing.commands.FareTypeConfigCommand;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.CommonConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FareTypeConfig;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

@Mapper
public interface FareTypeConfigAppMapper {

  @Named("convertStringToLocalDate")
  static LocalDate convertStringToLocalDate(String string) {
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(CommonConstant.DD_MM_YYYY);
    return LocalDate.parse(string, formatter);
  }

  @Named("round")
  default double round(double value, int places) {
    if (places < 0) throw new IllegalArgumentException();

    BigDecimal bd = new BigDecimal(Double.toString(value));
    bd = bd.setScale(places, RoundingMode.HALF_UP);
    return bd.doubleValue();
  }

  /**
   * Map FareTypeConfigCommand to FareTypeConfig
   *
   * @param source Fare type config command
   * @return FareTypeConfig Fare type config
   */
  @Mapping(target = "defaultFixed", expression = "java(round(source.getDefaultFixed(), 2))")
  @Mapping(target = "defaultPercent", expression = "java(round(source.getDefaultPercent(), 2))")
  @Mapping(
      target = "startDate",
      source = "source.startDate",
      qualifiedByName = "convertStringToLocalDate")
  @Mapping(
      target = "endDate",
      source = "source.endDate",
      qualifiedByName = "convertStringToLocalDate")
  @Mapping(target = "createdBy", source = "source.userChange")
  @Mapping(target = "updatedBy", source = "source.userChange")
  FareTypeConfig mapFareTypeConfigCommandToFareTypeConfig(FareTypeConfigCommand source)
      throws ParseException;
}

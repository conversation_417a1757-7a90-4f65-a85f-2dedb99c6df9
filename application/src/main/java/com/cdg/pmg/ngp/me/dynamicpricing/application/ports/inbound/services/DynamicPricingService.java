package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.MultiFareResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.additionalcharge.AdditionalChargeFeeData;
import com.cdg.pmg.ngp.me.dynamicpricing.commands.StoreFareBreakdownCommandRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.commands.StoreFareBreakdownCommandResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.*;
import com.cdg.pmg.ngp.me.dynamicpricing.queries.MultiFareRequestQuery;
import java.util.List;

/** Interface of dynamic pricing service */
public interface DynamicPricingService {

  /**
   * Get estimated fare
   *
   * @param multiFareRequest request
   * @return response estimated fare
   */
  MultiFareResponse getMultiFare(MultiFareRequestQuery multiFareRequest);

  /**
   * Get estimated fare detail for testing
   *
   * @param fareId request
   * @param vehicleTypeId request
   *               
   * @return response estimated fare
   */
  FareBreakdownDetailEntity getMultiFareDetail(String fareId, Integer vehicleTypeId);

  /**
   * Get multi fare by fare id
   *
   * @param fareId fare id
   * @return response multi fare by fare Id
   */
  MultiFareResponse getMultiFareByFareId(final String fareId);

  /**
   * Store Fare Breakdown Detail
   *
   * @param queryRequest request
   * @return status store fare breakdown command response
   */
  StoreFareBreakdownCommandResponse storeFareBreakdownDetail(
      StoreFareBreakdownCommandRequest queryRequest);

  /**
   * Validate fare boolean.
   *
   * @param validateFareRequest the validate fare request
   * @return the boolean
   */
  boolean validateFare(ValidateFareEntity validateFareRequest);

  /**
   * Gets generated route by trip id.
   *
   * @param tripId the trip id
   * @return the generated route by trip id
   */
  GeneratedRouteEntity getGeneratedRouteByTripId(final String tripId);

  /**
   * Search fare breakdown
   *
   * @param request search request
   * @return SearchFareBreakdownResponse
   */
  SearchFareBreakdownResponse searchFareBreakdown(SearchFareBreakdownRequestEntity request);

  /**
   * List AdditionalChargeDriverFeeData by fareId ,vehTypeId and productTypeId.
   *
   * @param fareId fare id
   * @param vehTypeId vehicle type id
   * @param productTypeId product type id (also known as pdtId)
   * @return List of AdditionalChargeDriverFeeData
   */
  List<AdditionalChargeFeeData> getAdditionalChargeFeesByCondition(
      String fareId, Integer vehTypeId, String productTypeId);
}

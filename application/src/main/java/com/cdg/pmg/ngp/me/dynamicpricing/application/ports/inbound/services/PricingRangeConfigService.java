package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services;

import com.cdg.pmg.ngp.me.dynamicpricing.commands.PricingRangeConfigCommand;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.PricingRangeConfigEntity;
import java.util.List;

public interface PricingRangeConfigService {
  /**
   * Get pricing range configs
   *
   * @return List<PricingRangeConfigEntity> List pricing range config entity
   */
  List<PricingRangeConfigEntity> getPricingRangeConfigs();

  /**
   * Insert/Update pricing range config
   *
   * @param pricingRangeConfigCommand Pricing range config command
   * @return PricingRangeConfigEntity Pricing range config entity
   */
  PricingRangeConfigEntity insertOrUpdatePricingRangeConfig(
      final PricingRangeConfigCommand pricingRangeConfigCommand);
}

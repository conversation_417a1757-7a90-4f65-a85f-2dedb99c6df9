package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import static com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.ConfigurationKeyUtils.extractKeyPart;
import static com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.StringUtils.isJson;
import static com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.StringUtils.unquote;
import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.ERROR_TO_PARSING_JSON;
import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.GET_LIST_DOUBLE_CONFIGS_ERROR;
import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.PARSING_HOLIDAY_ERROR;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareHoliday;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.DynpConfigCacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.CacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.DomainException;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.InternalServerException;
import com.cdg.pmg.ngp.me.dynamicpricing.utils.CollectionUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@ServiceComponent
@Slf4j
public class DynpConfigCacheServiceImpl implements DynpConfigCacheService {

  private final CacheService cacheService;
  private final ObjectMapper objectMapper;

  @Override
  public <K, V> void addMapConfigsToCache(String key, Map<K, V> configs) {
    if (Objects.nonNull(key)) {
      cacheService.setValue(key, configs);
    }
  }

  @Override
  public Map<String, String> getMapConfigsFromCache(String pattern) {
    var keyResultList = cacheService.getKeysByPattern(pattern);
    var key = keyResultList.stream().findFirst().orElse("");
    return cacheService.getMapValue(key, String.class, String.class);
  }

  @Override
  public List<Map<String, String>> getMultiMapConfigsFromCache(String key) {
    Set<String> keyResultList = cacheService.getKeysByPattern(key);
    TreeSet<String> keyResultTreeSet = new TreeSet<>(keyResultList);
    return cacheService.getMultiValueMap(keyResultTreeSet, String.class, String.class);
  }

  @Override
  public Map<String, String> getMultiMapConfigsAndSingleValueFromCache(final String key) {
    Map<String, String> combinedResult = new HashMap<>();
    Set<String> keyCacheList = cacheService.getKeysByPattern(key);
    List<String> keyErrorList = new ArrayList<>();
    for (String keyCache : keyCacheList) {
      final String dataByKey = cacheService.getStringValue(keyCache);
      if (Objects.nonNull(dataByKey)) {
        if (isJson(dataByKey)) {
          try {
            Map<String, String> mapData = objectMapper.readValue(dataByKey, Map.class);
            combinedResult.putAll(mapData);
          } catch (JsonProcessingException e) {
            log.error("Error parsing JSON for key: " + keyCache, e);
            keyErrorList.add(keyCache);
          }
        } else {
          String extractedKey = extractKeyPart(keyCache);
          combinedResult.put(extractedKey, unquote(dataByKey));
        }
      }
    }
    if (!keyErrorList.isEmpty()) {
      handleParsingErrors(keyErrorList);
    }
    return combinedResult;
  }

  @Override
  public List<Double> getListDoubleConfigsFromCache(String key, String configName) {
    List<Double> outputList = new ArrayList<>();
    try {
      Set<String> keyResultList = cacheService.getKeysByPattern(key);
      TreeSet<String> keyResultTreeSet = new TreeSet<>(keyResultList);
      var listMapResult =
          cacheService.getMultiValueMap(keyResultTreeSet, String.class, String.class);

      for (int i = 0; i < listMapResult.size(); i++) {
        var itemValue = listMapResult.get(i).get(configName + "_" + i);
        outputList.add(Double.parseDouble(itemValue));
      }
    } catch (ClassCastException e) {
      log.error("Error at getListDoubleConfigsFromCache:", e);
      throw new DomainException(
          GET_LIST_DOUBLE_CONFIGS_ERROR.getMessage(), GET_LIST_DOUBLE_CONFIGS_ERROR.getErrorCode());
    }
    return outputList;
  }

  @Override
  public <T> List<T> getListObjectConfigFromCache(String pattern, Class<T> type) {
    var keyResultList = cacheService.getKeysByPattern(pattern);
    var key = keyResultList.stream().findFirst().orElse("");
    return cacheService.getListValue(key, type);
  }

  @Override
  public <V> Map<String, List<V>> getMapOfListObject(String pattern, Class<V> valueType) {
    Set<String> keyResultSet = cacheService.getKeysByPattern(pattern);
    Map<String, List<V>> mapOfListObject = new HashMap<>();
    keyResultSet.forEach(
        item -> {
          List<V> listValue = cacheService.getListValue(item, valueType);
          mapOfListObject.put(item, listValue);
        });
    return mapOfListObject;
  }

  @Override
  public <T> void addListConfigsToCache(String key, List<T> configs) {
    if (Objects.nonNull(key) && Objects.nonNull(configs) && !CollectionUtils.isEmpty(configs)) {
      cacheService.setValue(key, configs);
    }
  }

  public <T> List<T> getListLocSurcharge(String key, Class<T> type) {
    return cacheService.getListValue(key, type);
  }

  @Override
  public <T> List<T> getListValue(String key, Class<T> typeOfList) {
    return cacheService.getListValue(key, typeOfList);
  }

  @Override
  public boolean isHoliday(Date date) {
    String key = "*" + "COMPANY_HOLIDAY";
    var listHoliday = getListObjectConfigFromCache(key, FlatFareHoliday.class);
    return listHoliday.stream()
        .anyMatch(
            publicHoliday -> {
              try {
                return publicHoliday.isPublicHoliday(date);
              } catch (ParseException e) {
                throw new InternalServerException(
                    PARSING_HOLIDAY_ERROR.getMessage(), PARSING_HOLIDAY_ERROR.getErrorCode());
              }
            });
  }

  @Override
  public <T> T getObjectValueByPattern(String pattern, Class<T> type) {
    final Set<String> keyResultList = cacheService.getKeysByPattern(pattern);
    final String key = keyResultList.stream().findFirst().orElse("");
    return cacheService.getValue(key, type);
  }

  @Override
  public <T> T getValue(final String key, Class<T> type) {
    return cacheService.getValue(key, type);
  }

  /**
   * Handles the process of aggregating and reporting parsing errors for cache keys. This method is
   * called when there are errors in parsing JSON data from the cache. It creates a detailed error
   * message for each problematic key and then throws a single aggregated exception containing all
   * these details. This approach ensures that all parsing errors are reported together, making it
   * easier to diagnose and resolve multiple issues in one go.
   *
   * @param keyErrorList A list of cache keys for which JSON parsing errors occurred.
   * @throws InternalServerException An exception that encapsulates all parsing error details. This
   *     exception is thrown to indicate that there was a server-side problem in processing the
   *     cache data, specifically related to JSON parsing.
   * @apiNote The method uses a StringBuilder to construct a detailed error message, appending each
   *     problematic key with a new line for clarity. The `InternalServerException` is then thrown
   *     with this aggregated message and a constant error message identifier.
   */
  private void handleParsingErrors(List<String> keyErrorList) {
    StringBuilder errorDetails =
        new StringBuilder("Errors occurred while parsing JSON data for the following keys:\n");
    for (String keyError : keyErrorList) {
      errorDetails.append(keyError).append("\n");
    }
    throw new InternalServerException(
        errorDetails.toString(), ERROR_TO_PARSING_JSON.getErrorCode());
  }
}

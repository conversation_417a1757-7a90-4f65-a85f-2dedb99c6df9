package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.ConfigKeyValueConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.NewPricingModelConfigChangeEntity;
import java.util.List;

public interface NewPricingModelConfigChangeRepository {
  void saveAllPricingConfigModelChanges(
      List<NewPricingModelConfigChangeEntity> newPricingModelConfigChangeEntities);

  void saveAllPropertyChanges(List<ConfigKeyValueConfigEntity> keyValueConfigs);
}

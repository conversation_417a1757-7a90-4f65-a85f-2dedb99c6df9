package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils;

import static com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.DateUtils.*;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.BookARideConfigsConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.DynamicPricingTimeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FareUploadConfiguration;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.HourRateConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.VGProductFareBean;
import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

@UtilityClass
@Slf4j
public class BookARideFareCalculationUtils {
  /**
   * Configures the flat fare for a given product and vehicle group based on a prefix, date, and
   * whether surge pricing is required. Updates the VGProductFareBean with various fare components
   * such as flag-down rate, tier rates, wait time fare, etc.
   *
   * @param prefix Prefix used to identify fare configuration keys.
   * @param fareUploadConfiguration Configuration details for fare calculation.
   * @param productId The product ID for which fare is being calculated.
   * @param date The date used in fare calculation.
   * @param vehicleGroupId The vehicle group ID.
   * @param isSurgeRequired Boolean indicating if surge pricing should be applied.
   */
  public static void setFlatFareConfig(
      String prefix,
      FareUploadConfiguration fareUploadConfiguration,
      final String productId,
      final Date date,
      final int vehicleGroupId,
      final boolean isSurgeRequired) {

    Map<String, String> flatFareConfMap = fareUploadConfiguration.getFlatFareConfig();
    Map<Integer, Map<String, VGProductFareBean>> productVGFareBeanMap =
        fareUploadConfiguration.getVgProductFareMap();
    Map<String, VGProductFareBean> productFareBeanMap = productVGFareBeanMap.get(vehicleGroupId);
    if (productFareBeanMap == null) {
      productFareBeanMap = new HashMap<>();
    }
    VGProductFareBean vGProductFareBean =
        initializeOrGetProductFareBean(productFareBeanMap, productId);
    productFareBeanMap.put(productId, vGProductFareBean);
    productVGFareBeanMap.put(vehicleGroupId, productFareBeanMap);
    populateFareBean(prefix, flatFareConfMap, vGProductFareBean, isSurgeRequired);

    if (prefix.contains(BookARideConfigsConstant.DYNAMIC_PRICING_PREFIX)) {
      vGProductFareBean.setDpMinFareCap(
          Double.parseDouble(
              flatFareConfMap.getOrDefault(BookARideConfigsConstant.DYNAMIC_PRICING_MIN_CAP, "0")));

      if (vGProductFareBean.isPeakHour()) {
        reCalculate(
            fareUploadConfiguration,
            Double.parseDouble(
                flatFareConfMap.getOrDefault(
                    BookARideConfigsConstant.DYNAMIC_PRICING_PEAK_HOUR_RATE, "0")),
            productId,
            vehicleGroupId,
            prefix);
      } else if (vGProductFareBean.isMidNight()) {
        reCalculate(
            fareUploadConfiguration,
            Double.parseDouble(
                flatFareConfMap.getOrDefault(
                    BookARideConfigsConstant.DYNAMIC_PRICING_MID_NIGHT_RATE, "0")),
            productId,
            vehicleGroupId,
            prefix);
      }

    } else {
      String prefixForFareEstimate = prefix;
      if (BookARideConfigsConstant.COMFORT_RIDE_PRODUCT.equalsIgnoreCase(productId)) {
        prefixForFareEstimate = "";
      }
      vGProductFareBean.setEstimatedFareLF(
          Double.parseDouble(
              flatFareConfMap.getOrDefault(
                  prefixForFareEstimate + BookARideConfigsConstant.TOTAL_FARE_ESTIMATE_LF, "0")));

      vGProductFareBean.setEstimatedFareRT(
          Double.parseDouble(
              flatFareConfMap.getOrDefault(
                  prefixForFareEstimate + BookARideConfigsConstant.TOTAL_FARE_ESTIMATE_RT, "0")));
      setPeakOrMidNightRt(
          date, fareUploadConfiguration, FlatfareConstants.PEAK_HOUR, productId, vehicleGroupId);
      setPeakOrMidNightRt(
          date, fareUploadConfiguration, FlatfareConstants.MIDNIGHT, productId, vehicleGroupId);
    }
  }

  /**
   * Sets the dynamic pricing fare configuration if dynamic pricing is enabled for the given vehicle
   * group and product ID. It considers the time of the request to determine if dynamic pricing
   * applies.
   *
   * @param date The current date used to check dynamic pricing applicability.
   * @param fareUploadConfiguration Configuration details for fare calculation.
   * @param productId The product ID for which fare is being calculated.
   * @param vehicleGroupId The vehicle group ID.
   */
  public static void setDpFareConfig(
      final Date date,
      final FareUploadConfiguration fareUploadConfiguration,
      final String productId,
      final int vehicleGroupId) {
    if (BookARideConfigsConstant.ENABLED.equalsIgnoreCase(
            fareUploadConfiguration
                .getFlatFareConfig()
                .get(BookARideConfigsConstant.DYNAMIC_PRICING_ENABLED))
        && fareUploadConfiguration
            .getFlatFareConfig()
            .get(BookARideConfigsConstant.DYNAMIC_PRICING_VEH_GROUP_IDS)
            .contains(String.valueOf(vehicleGroupId))) {
      Map<String, DynamicPricingTimeConfig> dynamicPriceSchedulerBeansMap =
          fareUploadConfiguration.getDynamicPricingTimeConfig();
      if (dynamicPriceSchedulerBeansMap != null) {
        Long requestTime = date.getTime();
        boolean isAvailable =
            dynamicPriceSchedulerBeansMap.entrySet().stream()
                .anyMatch(
                    p ->
                        p.getValue().getStartTime() != 0
                            && p.getValue().getEndTime() != 0
                            && p.getValue().getStartTime() <= requestTime
                            && p.getValue().getEndTime() >= requestTime);
        if (isAvailable) {
          log.info("setDpFareConfig {}", isAvailable);
          setFlatFareConfig(
              BookARideConfigsConstant.DYNAMIC_PRICING_PREFIX,
              fareUploadConfiguration,
              productId,
              date,
              vehicleGroupId,
              true);
        }
      }
    }
  }

  /**
   * Recalculates the fare based on a given rate for peak hour or midnight adjustments. Updates the
   * VGProductFareBean with recalculated values for flag down, tier fares, and wait time fare.
   *
   * @param fareUploadConfiguration Configuration details for fare calculation.
   * @param rate The rate used for recalculating the fare.
   * @param productId The product ID for which fare is being recalculated.
   * @param vehicleGroupId The vehicle group ID.
   * @param prefix Indicates if the recalculation is for peak hour or midnight.
   */
  public static void reCalculate(
      final FareUploadConfiguration fareUploadConfiguration,
      final double rate,
      final String productId,
      final int vehicleGroupId,
      final String prefix) {
    VGProductFareBean vGProductFareBean =
        fareUploadConfiguration.getVgProductFareMap().get(vehicleGroupId).get(productId);
    BigDecimal rateInBD = BigDecimal.valueOf(rate);
    if (rateInBD.compareTo(BigDecimal.ZERO) != 0) {
      if (prefix.equalsIgnoreCase(FlatfareConstants.PEAK_HOUR)) {
        vGProductFareBean.setPeakHour(true);
      } else if (prefix.equalsIgnoreCase(FlatfareConstants.MIDNIGHT)) {
        vGProductFareBean.setMidNight(true);
      }
      vGProductFareBean.setFlagDown(
          vGProductFareBean.getFlagDown() + (vGProductFareBean.getFlagDown() * rate));
      vGProductFareBean.setTier1Fare(
          vGProductFareBean.getTier1Fare() + (vGProductFareBean.getTier1Fare() * rate));
      vGProductFareBean.setTier2Fare(
          vGProductFareBean.getTier2Fare() + (vGProductFareBean.getTier2Fare() * rate));
      vGProductFareBean.setWaitTimeFare(
          vGProductFareBean.getWaitTimeFare() + (vGProductFareBean.getWaitTimeFare() * rate));
    }
  }

  /**
   * Sets the peak hour or midnight rate for a given product and vehicle group. Determines if the
   * current request falls within peak hours or midnight hours and recalculates the fare
   * accordingly.
   *
   * @param reqDate The date and time of the fare request.
   * @param fareUploadConfiguration Configuration details for fare calculation.
   * @param prefix Indicates if the rate setting is for peak hours or midnight.
   * @param productId The product ID for which fare is being calculated.
   * @param vehicleGroupId The vehicle group ID.
   */
  public static void setPeakOrMidNightRt(
      final Date reqDate,
      final FareUploadConfiguration fareUploadConfiguration,
      final String prefix,
      final String productId,
      final int vehicleGroupId) {
    List<HourRateConfig> hourRateConfigs;
    if (prefix.equalsIgnoreCase(FlatfareConstants.PEAK_HOUR)) {
      hourRateConfigs = new ArrayList<>(fareUploadConfiguration.getPeakHourRateConfig().values());
    } else {
      hourRateConfigs = new ArrayList<>(fareUploadConfiguration.getMidNightRateConfig().values());
    }
    List<String> holidaysList = fareUploadConfiguration.getHolidayConfig();
    LocalTime reqTime = convertToLocalTime(reqDate);

    log.debug("Hour Rate Beans {}", hourRateConfigs);
    double rate;

    if (DateUtils.isHolidaySingTime(reqDate, DateUtils.mapToListFlatFareHoliday(holidaysList))) {
      rate =
          hourRateConfigs.stream()
              .filter(
                  p ->
                      p.getDaysOfWeekIncluded().contains(BookARideConfigsConstant.HOL)
                          && isBetween(reqTime, p.getStartTime(), p.getEndTime().minusMinutes(15)))
              .mapToDouble(HourRateConfig::getRate)
              .sum();
    } else {
      rate =
          hourRateConfigs.stream()
              .filter(
                  p ->
                      p.getDaysOfWeekIncluded().contains(toDayOfWeekShortUpperCase(reqDate))
                          && isBetween(reqTime, p.getStartTime(), p.getEndTime().minusMinutes(15)))
              .mapToDouble(HourRateConfig::getRate)
              .sum();
    }
    if (prefix.equalsIgnoreCase(FlatfareConstants.PEAK_HOUR)) {
      log.info("Peak hour rate {}", rate);
    } else {
      log.info("Mid-night hour rate {}", rate);
    }
    reCalculate(fareUploadConfiguration, rate, productId, vehicleGroupId, prefix);
  }

  /**
   * This method populates a VGProductFareBean object with fare details from a given configuration
   * map. The prefix parameter is used to retrieve the correct fare details from the configuration
   * map. The isSurgeRequired parameter is used to set the surgeRequired property of the
   * VGProductFareBean.
   *
   * @param prefix The prefix used to retrieve fare details from the configuration map.
   * @param flatFareConfMap The configuration map containing fare details.
   * @param vGProductFareBean The VGProductFareBean object to be populated with fare details.
   * @param isSurgeRequired A boolean indicating whether surge pricing is required.
   */
  public static void populateFareBean(
      String prefix,
      Map<String, String> flatFareConfMap,
      VGProductFareBean vGProductFareBean,
      boolean isSurgeRequired) {
    vGProductFareBean.setFlagDown(
        Double.parseDouble(
            flatFareConfMap.getOrDefault(prefix + BookARideConfigsConstant.FLAG_DOWN_RATE, "0")));

    vGProductFareBean.setTier1Fare(
        Double.parseDouble(
            flatFareConfMap.getOrDefault(
                prefix + BookARideConfigsConstant.TIER_1_PER_COUNT_FARE, "0")));

    vGProductFareBean.setTier2Fare(
        Double.parseDouble(
            flatFareConfMap.getOrDefault(
                prefix + BookARideConfigsConstant.TIER_2_PER_COUNT_FARE, "0")));

    vGProductFareBean.setTier1PerCountMeter(
        Double.parseDouble(
            flatFareConfMap.getOrDefault(
                prefix + BookARideConfigsConstant.TIER_1_PER_COUNT_METER, "0")));

    vGProductFareBean.setTier2PerCountMeter(
        Double.parseDouble(
            flatFareConfMap.getOrDefault(
                prefix + BookARideConfigsConstant.TIER_2_PER_COUNT_METER, "0")));

    vGProductFareBean.setWaitTimeFare(
        Double.parseDouble(
            flatFareConfMap.getOrDefault(
                prefix + BookARideConfigsConstant.DURATION_TIME_RATE, "0")));

    vGProductFareBean.setTier1Start(
        Double.parseDouble(
            flatFareConfMap.getOrDefault(
                prefix + BookARideConfigsConstant.TIER_1_START_DIST, "0")));

    vGProductFareBean.setTier2Start(
        Double.parseDouble(
            flatFareConfMap.getOrDefault(
                prefix + BookARideConfigsConstant.TIER_2_START_DIST, "0")));

    vGProductFareBean.setTier1End(
        Double.parseDouble(
            flatFareConfMap.getOrDefault(prefix + BookARideConfigsConstant.TIER_1_END_DIST, "0")));

    vGProductFareBean.setTier2End(
        Double.parseDouble(
            flatFareConfMap.getOrDefault(prefix + BookARideConfigsConstant.TIER_2_END_DIST, "0")));

    vGProductFareBean.setSurgeRequired(isSurgeRequired);
  }

  /**
   * This method initializes a VGProductFareBean object or retrieves it from a given product fare
   * bean map. The productId parameter is used to retrieve the VGProductFareBean from the map. If
   * the map is null or does not contain a VGProductFareBean for the given productId, a new
   * VGProductFareBean is created.
   *
   * @param productFareBeanMap The map containing product fare beans.
   * @param productId The id of the product for which the VGProductFareBean is to be retrieved or
   *     created.
   * @return The initialized or retrieved VGProductFareBean.
   */
  public static VGProductFareBean initializeOrGetProductFareBean(
      Map<String, VGProductFareBean> productFareBeanMap, String productId) {
    VGProductFareBean vGProductFareBean;
    vGProductFareBean = productFareBeanMap.get(productId);
    if (vGProductFareBean == null) {
      vGProductFareBean = VGProductFareBean.builder().build();
    }
    return vGProductFareBean;
  }
}

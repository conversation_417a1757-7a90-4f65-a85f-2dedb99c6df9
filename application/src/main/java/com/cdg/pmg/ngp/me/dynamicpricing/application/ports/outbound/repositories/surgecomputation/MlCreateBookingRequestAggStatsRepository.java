package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.MlCreateBookingRequestAggStatsEntity;
import java.time.Instant;
import java.util.List;

/**
 * Repository interface for ML Create Booking Request Aggregation Statistics. Provides methods to
 * interact with the ml_create_booking_request_agg_stats table.
 */
public interface MlCreateBookingRequestAggStatsRepository {

  /**
   * Save a ML Create Booking Request Aggregation Statistics entity.
   *
   * @param entity the entity to save
   * @return the saved entity
   */
  MlCreateBookingRequestAggStatsEntity save(MlCreateBookingRequestAggStatsEntity entity);

  /**
   * Get fare ids in a specific time range, use closed-open interval -> [start,end)
   *
   * @param startTime the start time, include
   * @param endTime the end time, exclude
   * @return a list of fare ids
   */
  List<String> getBookingIdsInTimeRange(Instant startTime, Instant endTime);
}

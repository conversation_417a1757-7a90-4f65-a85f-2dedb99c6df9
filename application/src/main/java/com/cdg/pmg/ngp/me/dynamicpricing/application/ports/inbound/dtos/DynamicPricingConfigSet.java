package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.FareTypeConfig;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DynamicPricingConfigSet {
  private Map<String, List<FareTypeConfig>> desurgeMaxCapConfigList;
  private Map<String, List<FareTypeConfig>> minSurgeAmountConfigList;
  private Map<String, List<FareTypeConfig>> minCapConfigList;
  private Map<String, List<FareTypeConfig>> maxCapConfigList;
  private Map<String, List<FareTypeConfig>> surgeBufferConfigList;
  private Map<String, List<FareTypeConfig>> flagDownConfigList;
  private Map<String, List<FareTypeConfig>> durationRateConfigList;
  private Map<String, List<FareTypeConfig>> tier1PriceMultiplierConfigList;
  private Map<String, List<FareTypeConfig>> tier1StartDestConfigList;
  private Map<String, List<FareTypeConfig>> tier1EndDestConfigList;
  private Map<String, List<FareTypeConfig>> tier2PriceMultiplierConfigList;
  private Map<String, List<FareTypeConfig>> tier2StartDestConfigList;
  private Map<String, List<FareTypeConfig>> tier2EndDestConfigList;
  private Map<String, List<FareTypeConfig>> bookingFee;
  private Map<String, List<FareTypeConfig>> hourlySurcharge;
  private List<FlatFareHoliday> holidayList;
  private String multiStopSurcharge;
  private List<EventSurgeAddressConfig> eventSurgeAddressConfigList;
  private List<LocationSurchargeConfig> locationSurchargeConfigList;
}

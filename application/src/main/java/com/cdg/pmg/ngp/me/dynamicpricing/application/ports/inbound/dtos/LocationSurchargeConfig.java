package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos;

import java.time.LocalTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LocationSurchargeConfig {

  private String fareType;
  private String applicableDays;
  private LocalTime startTime;
  private LocalTime endTime;
  private String chargeBy;
  private Double surchargeValue;
  private Integer locationId;
  private String locationName;
  private String addressRef;
  private String zoneId;
  private String productId;
  private String dayIndicator;

  @Override
  public String toString() {
    return "LocConfig{"
        + "fareType='"
        + fareType
        + '\''
        + ", applicableDays='"
        + applicableDays
        + '\''
        + ", startTime="
        + startTime
        + ", endTime="
        + endTime
        + ", chargeBy='"
        + chargeBy
        + '\''
        + ", surchargeValue="
        + surchargeValue
        + ", locationId="
        + locationId
        + ", locationName='"
        + locationName
        + '\''
        + ", addressRef='"
        + addressRef
        + '\''
        + ", zoneId='"
        + zoneId
        + '\''
        + ", productId='"
        + productId
        + '\''
        + ", dayIndicator='"
        + dayIndicator
        + '\''
        + '}';
  }
}

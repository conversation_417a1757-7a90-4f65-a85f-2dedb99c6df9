package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.SurgeComputationModelApiLogEntity;
import java.util.List;

/** Repository interface for surge computation model API logs. */
public interface SurgeComputationModelApiLogRepository {

  /**
   * Save a new API log entry.
   *
   * @param entity the API log entity to save
   * @return the saved entity
   */
  SurgeComputationModelApiLogEntity save(SurgeComputationModelApiLogEntity entity);

  /**
   * Find API logs by model ID.
   *
   * @param modelId the model ID to search for
   * @return a list of API logs for the specified model
   */
  List<SurgeComputationModelApiLogEntity> findByModelId(Long modelId);
}

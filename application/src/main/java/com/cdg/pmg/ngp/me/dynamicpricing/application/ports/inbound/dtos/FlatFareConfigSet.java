package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.BookingFeeItem;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ServiceComponent
public class FlatFareConfigSet {
  private String prefixKey;
  private List<Map<String, String>> peakHoursRates;
  private List<Map<String, String>> midnightHoursRates;
  private String flagDownRate;
  private TierFare tier1Fare;
  private TierFare tier2Fare;
  private EstimateRateConfig estimateRateConfig;
  private String durationUnitConfig;
  private String durationRateConfig;
  private String maxFlatFareCap;
  private String multiStopSurcharge;
  private List<LocationSurchargeConfig> locationSurchargeConfigList;
  private List<FlatFareHoliday> holidayList;
  private List<BookingFeeItem> bookingFeeList;
  private List<Double> additionalChargeList;
  private List<EventSurgeAddressConfig> eventSurgeAddressConfigList;
}

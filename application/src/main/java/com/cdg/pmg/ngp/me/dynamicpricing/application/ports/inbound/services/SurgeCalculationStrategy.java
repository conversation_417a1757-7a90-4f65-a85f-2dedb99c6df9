package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.SurgeCalculationDto;

public interface SurgeCalculationStrategy {
  String V1 = "V1";
  String V2 = "V2";
  String V2POINT5 = "V2.5";
  String V3 = "V3";

  int calculate(SurgeCalculationDto surgeCalculationDto);

  String getVersion();

  default int clamp(int value, int min, int max) {
    return Math.max(min, Math.min(value, max));
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.mapper;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.LocationSurchargeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.LocationSurchargeConfigEntity;
import org.mapstruct.Mapper;

@Mapper
public interface LocationSurchargeMapper {
  LocationSurchargeConfigEntity mapToLocationSurchargeConfigEntity(LocationSurchargeConfig dto);
}

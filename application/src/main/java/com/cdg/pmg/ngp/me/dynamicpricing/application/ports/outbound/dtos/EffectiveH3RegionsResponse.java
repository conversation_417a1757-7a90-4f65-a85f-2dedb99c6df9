package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

@Data
public class EffectiveH3RegionsResponse implements Serializable {
  @Serial private static final long serialVersionUID = 2688726902866914286L;

  private String regionVersion;
  private LocalDateTime effectiveFrom;
  private LocalDateTime effectiveTo;
  private List<H3Region> regions;
}

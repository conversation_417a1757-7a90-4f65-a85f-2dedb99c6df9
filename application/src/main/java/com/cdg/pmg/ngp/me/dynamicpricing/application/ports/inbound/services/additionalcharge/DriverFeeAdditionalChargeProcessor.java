package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.additionalcharge;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareVOPart;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.additionalcharge.AdditionalChargeDriverFeeData;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.AdditionalChargeFeeConfigResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.InternalServerException;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;

@RequiredArgsConstructor
@ServiceComponent
@Slf4j
public class DriverFeeAdditionalChargeProcessor
    implements AdditionalChargeProcessor<FlatFareVOPart, AdditionalChargeDriverFeeData> {

  public static final String CHARGE_THRESHOLD = "charge_threshold";
  public static final String CHARGE_LOWER_VALUE = "charge_lower_value";
  public static final String CHARGE_UPPER_VALUE = "charge_upper_value";
  public static final String IS_COUNT_IN_TOTALFARE = "is_count_in_totalfare";

  @Override
  public Optional<AdditionalChargeDriverFeeData> calculateAdditionalCharge(
      FlatFareVOPart calculateParam,
      Map<String, List<AdditionalChargeFeeConfigResponse>> additionalChargeFeeConfigMap) {
    if (calculateParam == null) {
      log.warn("process calculateAdditionalCharge, calculateParam is null, now end.");
      return Optional.empty();
    } else if (null == additionalChargeFeeConfigMap || additionalChargeFeeConfigMap.isEmpty()) {
      log.warn(
          "process calculateAdditionalCharge, additionalChargeFeeConfigMap is null or empty, now end.");
      return Optional.empty();
    } else {
      try {
        /*
        1.Transfer "DRIVER_FEE" additional charge config list to map.
         */
        List<AdditionalChargeFeeConfigResponse> driverFeeConfigList =
            additionalChargeFeeConfigMap.get(getAdditionalChargeType());
        /*
        Below map, key is chargeKey, value is AdditionalChargeFeeConfigResponse.
         */
        Map<String, AdditionalChargeFeeConfigResponse> driverFeeKeyConfigMap = new HashMap<>();

        Optional.ofNullable(driverFeeConfigList)
            .ifPresentOrElse(
                driverFeeConfig ->
                    driverFeeKeyConfigMap.putAll(
                        driverFeeConfig.stream()
                            .collect(
                                Collectors.toMap(
                                    AdditionalChargeFeeConfigResponse::getChargeKey,
                                    Function.identity(),
                                    (oldData, newData) -> newData))),
                () ->
                    log.warn(
                        "There is no driver fee config from FareService for chargeType={}.",
                        getAdditionalChargeType()));
        /*
        2.If there is no "DRIVER_FEE" config get from fare-svc, will not do calculate ,return null.
         */
        if (driverFeeKeyConfigMap.isEmpty()) {
          log.warn("process calculateAdditionalCharge, driverFeeKeyConfigMap is empty, now end.");
          return Optional.empty();
        } else if (isSkipCalculate(driverFeeKeyConfigMap)) {
          /*
          3.If "DRIVER_FEE" config without "is_count_in_totalfare",or "is_count_in_totalfare"charge_formual is "false",
          will skip calculate and return null.
           */
          log.warn(
              "process calculateAdditionalCharge, is_count_in_totalfare is null or false, now skip and end.");
          return Optional.empty();
        } else {
          /*
          4.Calculate driverFee for totalFare、estimatedFareLF、estimatedFareRT,return the calculate result.
           */
          AdditionalChargeDriverFeeData additionalChargeDriverFeeData =
              AdditionalChargeDriverFeeData.builder()
                  .chargeId(driverFeeConfigList.get(0).getChargeId())
                  .chargeType(driverFeeConfigList.get(0).getChargeType())
                  .totalFareDriverFee(
                      calculateDriverFee(calculateParam.getTotalFare(), driverFeeKeyConfigMap))
                  .estimatedFareLFDriverFee(
                      calculateDriverFee(
                          calculateParam.getEstimatedFareLF(), driverFeeKeyConfigMap))
                  .estimatedFareRTDriverFee(
                      calculateDriverFee(
                          calculateParam.getEstimatedFareRT(), driverFeeKeyConfigMap))
                  .build();

          /*
          5.Set driver fee config data(charge_threshold,charge_lower_value,charge_upper_value)
          to the return AdditionalChargeDriverFeeData.
           */
          Optional.ofNullable(driverFeeKeyConfigMap.get(CHARGE_THRESHOLD))
              .ifPresentOrElse(
                  chargeThresholdConfig ->
                      additionalChargeDriverFeeData.setChargeThreshold(
                          chargeThresholdConfig.getChargeValue()),
                  () ->
                      log.warn("There is no driver fee charge_threshold config from FareService."));

          Optional.ofNullable(driverFeeKeyConfigMap.get(CHARGE_LOWER_VALUE))
              .ifPresentOrElse(
                  chargeLowerValue ->
                      additionalChargeDriverFeeData.setChargeLowerLimit(
                          chargeLowerValue.getChargeValue()),
                  () ->
                      log.warn(
                          "There is no driver fee charge_lower_value config from FareService."));

          Optional.ofNullable(driverFeeKeyConfigMap.get(CHARGE_UPPER_VALUE))
              .ifPresentOrElse(
                  chargeUpperValue ->
                      additionalChargeDriverFeeData.setChargeUpperLimit(
                          chargeUpperValue.getChargeValue()),
                  () ->
                      log.warn(
                          "There is no driver fee charge_upper_value config from FareService."));

          return Optional.of(additionalChargeDriverFeeData);
        }
      } catch (InternalServerException e) {
        log.error("Failed to get additional charge fee config and process failed.", e);
      }
      return Optional.empty();
    }
  }

  /**
   * Calculate driver fee. If originFare > charge_threshold, then return charge_upper_value, If
   * originFare <= charge_threshold, then return charge_lower_value.
   *
   * @param originFare origin fare
   * @param driverFeeConfigMap driver fee config use for calculate
   * @return fare added driver fee
   */
  private BigDecimal calculateDriverFee(
      BigDecimal originFare, Map<String, AdditionalChargeFeeConfigResponse> driverFeeConfigMap) {

    log.info(
        "Start to calculate driver fee originFare={} driverFeeConfigMap={}.",
        originFare,
        driverFeeConfigMap);
    if (ObjectUtils.isEmpty(originFare)) {
      return null;
    }
    // If any driver fee config is null , then will not calculate
    AdditionalChargeFeeConfigResponse chargeThresholdObject =
        driverFeeConfigMap.get(CHARGE_THRESHOLD);
    AdditionalChargeFeeConfigResponse chargeLowerValueObject =
        driverFeeConfigMap.get(CHARGE_LOWER_VALUE);
    AdditionalChargeFeeConfigResponse chargeUpperValueObject =
        driverFeeConfigMap.get(CHARGE_UPPER_VALUE);
    if (ObjectUtils.isEmpty(chargeThresholdObject)
        || ObjectUtils.isEmpty(chargeLowerValueObject)
        || ObjectUtils.isEmpty(chargeUpperValueObject)) {
      return null;
    }
    // If any driver fee config value is null , then will not calculate
    Double chargeThreshold = chargeThresholdObject.getChargeValue();
    Double chargeLowerValue = chargeLowerValueObject.getChargeValue();
    Double chargeUpperValue = chargeUpperValueObject.getChargeValue();
    if (ObjectUtils.isEmpty(chargeThreshold)
        || ObjectUtils.isEmpty(chargeLowerValue)
        || ObjectUtils.isEmpty(chargeUpperValue)) {
      return null;
    }
    // value > chargeThreshold ,driver fee = chargeUpperValue
    // value <= chargeThreshold ,driver fee = chargeLowerValue
    // when exception ,return null
    try {
      if (originFare.doubleValue() > chargeThreshold) {
        return BigDecimal.valueOf(chargeUpperValue);
      } else {
        return BigDecimal.valueOf(chargeLowerValue);
      }
    } catch (Exception e) {
      log.error("Calculate driver fee error, will return null.", e);
      return null;
    }
  }

  /**
   * Judge if is_count_in_totalfare equal "true", or this method will return true to skip DRIVER_FEE
   * calculate.
   *
   * @param driverFeeConfigMap DRIVER_FEE config map
   * @return if isCountInTotalFare
   */
  private boolean isSkipCalculate(
      Map<String, AdditionalChargeFeeConfigResponse> driverFeeConfigMap) {

    AdditionalChargeFeeConfigResponse isCountInTotalFare =
        driverFeeConfigMap.get(IS_COUNT_IN_TOTALFARE);

    if (ObjectUtils.isEmpty(isCountInTotalFare)) {
      return true;
    } else {
      return !Boolean.TRUE.toString().equalsIgnoreCase(isCountInTotalFare.getChargeFormula());
    }
  }

  public String getAdditionalChargeType() {
    // "DRIVER_FEE" type additional charge fee type.
    return "DRIVER_FEE";
  }
}

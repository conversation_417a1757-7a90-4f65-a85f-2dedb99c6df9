package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.flatfare;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareVO;

/** Interface flat fare compute formula */
public interface FlatFareCompute {

  /**
   * Calculate FlatDownRateFare
   *
   * @param vo flatFare object
   */
  void calFlatDownRateFare(FlatFareVO vo);

  /**
   * Calculate BookingFee
   *
   * @param vo flatFare object
   */
  void calBookingFee(FlatFareVO vo);

  /**
   * Calculate Tier1
   *
   * @param vo flatFare object
   */
  void calTier1(FlatFareVO vo);

  /**
   * Calculate Tier2
   *
   * @param vo flatFare object
   */
  void calTier2(FlatFareVO vo);

  /**
   * Calculate EttFare
   *
   * @param vo flatFare object
   */
  void calEttFare(FlatFareVO vo);

  /**
   * Calculate PeakHourCharges
   *
   * @param vo flatFare object
   */
  void calPeakHourCharges(FlatFareVO vo);

  /**
   * Calculate MidNightCharges
   *
   * @param vo flatFare object
   */
  void calMidNightCharges(FlatFareVO vo);

  /**
   * Calculate LocSurcharges
   *
   * @param vo flatFare object
   */
  void calLocSurcharges(FlatFareVO vo);

  /**
   * Calculate AdditionalSurCharge
   *
   * @param vo flatFare object
   */
  void calAdditionalSurCharge(FlatFareVO vo);

  /**
   * Calculate multi destination surcharge
   *
   * @param vo flatFare object
   */
  void calMultiDestSurcharge(FlatFareVO vo);

  /**
   * Init data
   *
   * @param vo flatFare object
   */
  void initVORequest(FlatFareVO vo);

  /**
   * Calculate BaseFare
   *
   * @param vo flatFare object
   */
  void calBaseFare(FlatFareVO vo);

  /**
   * Calculate TotalFareBeforeSurge
   *
   * @param vo flatFare object
   */
  void setTotalFareBeforeSurge(FlatFareVO vo);

  /**
   * Calculate EventSurgeAddrCharge
   *
   * @param vo flatFare object
   */
  void calEventSurgeAddrCharge(FlatFareVO vo);

  /**
   * Round To Fifty Cent
   *
   * @param vo flatFare object
   */
  void roundToFiftyCent(FlatFareVO vo);

  /**
   * Calculate EstimatedTotalFare
   *
   * @param vo flatFare object
   */
  void calEstimatedTotalFare(FlatFareVO vo);

  /**
   * Finalize Total Amount
   *
   * @param vo flatFare object
   */
  void finalizeTotalAmount(FlatFareVO vo);

  /**
   * Is Valid Flat fare
   *
   * @param vo flatFare object
   * @return boolean
   */
  boolean isValidFlatfare(FlatFareVO vo);

  /**
   * Get Fare Type
   *
   * @return fare type
   */
  String getFareType();
}

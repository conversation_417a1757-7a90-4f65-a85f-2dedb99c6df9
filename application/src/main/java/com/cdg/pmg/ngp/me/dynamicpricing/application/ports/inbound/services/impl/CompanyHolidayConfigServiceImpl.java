package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.CompanyHolidayConfigService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.CompanyHolidayRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.CacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.utils.CollectionUtils;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@AllArgsConstructor
@ServiceComponent
@Slf4j
public class CompanyHolidayConfigServiceImpl implements CompanyHolidayConfigService {
  private final CacheService cacheService;
  private final CompanyHolidayRepository companyHolidayRepository;
  private static final String COMPANY_HOLIDAY_KEY_CACHE =
      RedisKeyConstant.DYNAMIC_PRICING
          .concat(RedisKeyConstant.COLON)
          .concat(RedisKeyConstant.COMPANY_HOLIDAY);

  @Override
  public void loadCompanyHolidayConfig() {
    final List<String> configs = companyHolidayRepository.getCompanyHolidays();
    if (CollectionUtils.isEmpty(configs)) {
      log.info("Can't load company holiday configs. The company holiday config is empty!");
      return;
    }

    cacheService.deleteByKey(COMPANY_HOLIDAY_KEY_CACHE);
    cacheService.setListValue(COMPANY_HOLIDAY_KEY_CACHE, configs);
    log.info("Loaded company holiday configs to cache complete!");
  }
}

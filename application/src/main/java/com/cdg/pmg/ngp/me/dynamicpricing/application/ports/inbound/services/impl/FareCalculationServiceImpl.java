package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.FareCalculationService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.BookARideFareCalculationUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.BookARideConfigsConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FareUploadConfiguration;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Implementation of the {@link FareCalculationService} interface, providing functionality for
 * calculating fares for various transportation services. This class handles the intricacies of fare
 * calculation based on multiple parameters including date, time, vehicle group, and product types.
 * It supports different fare types such as flat fares and dynamic pricing, and adjusts fares based
 * on peak hours, midnight rates, and holidays.
 *
 * <p>The class employs various strategies to determine the appropriate fare by considering factors
 * like surge pricing, vehicle group-specific configurations, and special rates for holidays. It
 * also includes methods for recalculating fares under certain conditions, setting booking fees, and
 * determining tariff applicability.
 *
 * <p>Usage of this class involves initializing it with necessary configuration data and then
 * invoking its methods with specific ride details (like date, vehicle group, and product IDs) to
 * calculate the applicable fares.
 *
 * <p>This class is annotated with {@code @ServiceComponent} to denote its role in the application's
 * service layer, and it uses {@code @Slf4j} for logging. It is designed to be used as part of a
 * larger application managing transportation services, specifically in the context of dynamic
 * pricing.
 */
@AllArgsConstructor
@ServiceComponent
@Slf4j
public class FareCalculationServiceImpl implements FareCalculationService {

  /**
   * Calculates the fare based on the current date, fare upload configuration, and a list of product
   * IDs for each vehicle group. Iterates over each vehicle group and product ID to set the fare.
   *
   * @param currentDate The current date used in fare calculation.
   * @param fareUploadConfiguration Configuration details for fare calculation.
   * @param productList A map of vehicle group IDs to a list of product IDs.
   */
  @Override
  public void calculateFare(
      Date currentDate,
      FareUploadConfiguration fareUploadConfiguration,
      Map<String, List<String>> productList) {
    productList.forEach(
        (vehicleGroupId, productIdList) ->
            productIdList.forEach(
                productId ->
                    setFare(
                        currentDate,
                        fareUploadConfiguration,
                        productId,
                        Integer.parseInt(vehicleGroupId))));
  }
  /**
   * Sets the fare for a given product ID and vehicle group ID based on the current date and fare
   * upload configuration. This method determines the type of fare (flat or dynamic) based on the
   * product ID.
   *
   * @param currentDate The current date used in fare calculation.
   * @param fareUploadConfiguration Configuration details for fare calculation.
   * @param productId The product ID for which fare is being calculated.
   * @param vehicleGroupId The vehicle group ID associated with the product.
   */
  private void setFare(
      final Date currentDate,
      final FareUploadConfiguration fareUploadConfiguration,
      final String productId,
      final int vehicleGroupId) {
    if (BookARideConfigsConstant.COMFORT_RIDE_PRODUCT.equalsIgnoreCase(productId)) {
      BookARideFareCalculationUtils.setFlatFareConfig(
          BookARideConfigsConstant.LIVE_TRAFFIC_PREFIX,
          fareUploadConfiguration,
          productId,
          currentDate,
          vehicleGroupId,
          false);
    } else if (BookARideConfigsConstant.METER_FARE_PRODUCT.equalsIgnoreCase(productId)) {
      BookARideFareCalculationUtils.setFlatFareConfig(
          BookARideConfigsConstant.EST_LIVE_TRAFFIC_PREFIX,
          fareUploadConfiguration,
          productId,
          currentDate,
          vehicleGroupId,
          false);
    }
    if (BookARideConfigsConstant.COMFORT_RIDE_PRODUCT.equalsIgnoreCase(productId)) {
      BookARideFareCalculationUtils.setDpFareConfig(
          currentDate, fareUploadConfiguration, productId, vehicleGroupId);
    }
  }
}

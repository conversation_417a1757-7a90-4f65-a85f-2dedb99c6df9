package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.commands;

import java.time.OffsetDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NewPricingModelCommand {
  private Integer index;
  private String zoneId;
  private OffsetDateTime startDt;
  private OffsetDateTime endDt;
  private Integer additionalSurgeHigh;
  private String createdBy;
  private OffsetDateTime createdDt;
  private String updatedBy;
  private OffsetDateTime updatedDt;
  private Double k1;
  private Double k2;
  private Double k3;
  private Double k4;
  private Double k5;
  private Double k6;
  private Double k7;
  private Double k8;
  private Double surgeHighTierRate;
  private Double unmetRate1;
  private Double unmetRate2;
  private Double negativeDemandSupplyDownRate;
  private String zonePriceVersion;
}

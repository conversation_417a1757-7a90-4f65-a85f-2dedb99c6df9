package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticBasedConfigurationEffectiveCheckEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticBasedConfigurationVersionEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticRegionBasedConfigurationEntity;
import java.util.List;

/** Service interface for managing static region-based configurations. */
public interface StaticRegionBasedConfigurationService {

  /**
   * Get all static time-based configuration versions.
   *
   * @param modelId the ID of the surge computation model to get the versions for
   * @return a list of all versions
   */
  List<StaticBasedConfigurationVersionEntity> getStaticTimeBasedConfigurationVersions(Long modelId);

  /**
   * Creates a new static region-based configuration.
   *
   * @param configuration the configuration to create
   * @return the created configuration
   */
  List<StaticRegionBasedConfigurationEntity> batchCreateStaticRegionBasedConfigurations(
      List<StaticRegionBasedConfigurationEntity> configuration);

  /**
   * Gets all static region-based configurations.
   *
   * @param modelId the ID of the surge computation model to get the configurations for
   * @param version the version of the configuration to get
   * @return a list of all configurations
   */
  List<StaticRegionBasedConfigurationEntity> getStaticRegionBasedConfigurations(
      Long modelId, String version);

  /**
   * Gets a static region-based configuration by ID.
   *
   * @param id the ID of the configuration to get
   * @return the configuration, or null if not found
   */
  StaticRegionBasedConfigurationEntity getStaticRegionBasedConfigurationById(Long id);

  /**
   * Batch update or create static region-based configuration, will determine if it exists based on
   * name and effectiveFrom.
   *
   * @param modelId the modelId from the surge computation model
   * @param configurations a list of the configuration to update
   */
  void updateStaticRegionBasedConfiguration(
      Long modelId, List<StaticRegionBasedConfigurationEntity> configurations);

  /**
   * Check if the static region-based configuration out of date or close to expiration
   *
   * @param modelId the model id from table surge_computation_models
   * @return the effective check entity
   */
  StaticBasedConfigurationEffectiveCheckEntity effectiveCheck(Long modelId);
}

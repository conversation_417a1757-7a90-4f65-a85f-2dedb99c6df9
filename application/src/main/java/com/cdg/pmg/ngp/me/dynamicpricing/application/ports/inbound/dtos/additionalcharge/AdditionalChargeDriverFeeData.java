package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.additionalcharge;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * This bean contains two part of data 1. calculated additional charge fee for DRIVER_FEE scenario:
 * (1) totalFareDriverFee: driver fee of totalFare (2) estimatedFareLFDriverFee: driver fee of
 * estimatedFareLF (3) estimatedFareRTDriverFee: driver fee of estimatedFareRT
 *
 * <p>2. additional charge config for DRIVER_FEE scenario from ngp-me-fare-svc table
 * "additional_charge_fee_config" (1) chargeId (2) chargeType (3) chargeThreshold (4)
 * chargeUpperLimit (5) chargeLowerLimit
 *
 * <p>and additional charge config data
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdditionalChargeDriverFeeData implements Serializable {

  private static final long serialVersionUID = 1L;

  // chargeId from config data
  private Integer chargeId;

  // chargeType from config data
  private String chargeType;

  // chargeThreshold from config data
  private Double chargeThreshold;

  // chargeUpperLimit from config data
  private Double chargeUpperLimit;

  // chargeLowerLimit from config data
  private Double chargeLowerLimit;

  // Calculated driver fee for totalFare
  private BigDecimal totalFareDriverFee;

  // Calculated driver fee for estimatedFareLF
  private BigDecimal estimatedFareLFDriverFee;

  // Calculated driver fee for estimatedFareRT
  private BigDecimal estimatedFareRTDriverFee;
}

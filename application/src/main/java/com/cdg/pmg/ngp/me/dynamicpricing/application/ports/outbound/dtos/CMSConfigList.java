package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CMSConfigList implements Serializable {
  @Serial private static final long serialVersionUID = 1L;
  private int skip;
  private int limit;
  private int total;
  private List<CMSConfigItem> data;
}

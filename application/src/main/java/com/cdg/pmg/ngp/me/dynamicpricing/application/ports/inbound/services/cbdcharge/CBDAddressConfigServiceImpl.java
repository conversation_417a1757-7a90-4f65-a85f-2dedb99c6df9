package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.cbdcharge;

import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.UPDATE_CBD_ADDRESS_BAD_REQUEST;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.cbdcharge.CBDAddress;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.cbdcharge.LocReloadCache;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.cbdcharge.UpdateCBDAddress;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.SurchargeUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.CBDAddressConfigRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.CacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.LocationSurchargeConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.BadRequestException;
import com.cdg.pmg.ngp.me.dynamicpricing.utils.CollectionUtils;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;

@RequiredArgsConstructor
@ServiceComponent
@Slf4j
public class CBDAddressConfigServiceImpl implements CBDAddressConfigService {

  private static final String LOC_KEY_CACHE_PREFIX =
      RedisKeyConstant.DYNAMIC_PRICING
          .concat(RedisKeyConstant.COLON)
          .concat(RedisKeyConstant.LOC_SURC)
          .concat(RedisKeyConstant.COLON);

  private final CBDAddressConfigRepository cbdAddressConfigRepository;
  private final CacheService cacheService;

  @Override
  public void updateCBDAddress(UpdateCBDAddress updateCBDAddress) {
    final List<CBDAddress> requestList = updateCBDAddress.getCbdAddressList();
    if (CollectionUtils.isEmpty(requestList)) {
      throw new BadRequestException(
          UPDATE_CBD_ADDRESS_BAD_REQUEST.getMessage(),
          UPDATE_CBD_ADDRESS_BAD_REQUEST.getErrorCode());
    }
    processInsertCBDAddress(requestList);
    processDeleteCBDAddress(requestList);
    updateLocSurchargeConfigOnCache(
        updateCBDAddress.getCbdAddressList().stream().map(CBDAddress::getAddressRef).toList());
  }

  @Override
  public void reloadCache(LocReloadCache locReloadCache) {
    updateLocSurchargeConfigOnCache(locReloadCache.getAddressRefList());
  }

  public void updateLocSurchargeConfigOnCache(List<String> requestAddressRefs) {
    if (CollectionUtils.isEmpty(requestAddressRefs)) {
      log.info("[updateLocSurchargeConfigOnCache] addressRef list is empty!");
      return;
    }
    final String cbdAddressAddedString = String.join(", ", requestAddressRefs);

    List<LocationSurchargeConfigEntity> cbdConfigs =
        getConfigFromDBByAddressList(requestAddressRefs);

    if (CollectionUtils.isEmpty(cbdConfigs)) {
      log.info(
          "[updateLocSurchargeConfigOnCache] No config in DB for addressRefs={}",
          cbdAddressAddedString);
      return;
    }

    Map<String, Map<String, List<LocationSurchargeConfigEntity>>> configGroupByDayAndAddress =
        SurchargeUtils.groupConfigByDayInWeekThenAddress(cbdConfigs);

    List<String> daysOfWeek = List.of("MON", "TUE", "WED", "THU", "FRI", "SAT", "SUN", "HOL");
    requestAddressRefs.forEach(
        addressRef -> {
          for (String dayIndicator : daysOfWeek) {
            if (!configGroupByDayAndAddress.containsKey(dayIndicator)
                || !configGroupByDayAndAddress.get(dayIndicator).containsKey(addressRef)) {
              Map<String, List<LocationSurchargeConfigEntity>> currentConfigs =
                  configGroupByDayAndAddress.get(dayIndicator);
              currentConfigs.put(addressRef, new ArrayList<>());
              configGroupByDayAndAddress.put(dayIndicator, currentConfigs);
            }
          }
        });

    configGroupByDayAndAddress.forEach(
        (dayInWeek, addressRefMap) ->
            addressRefMap.forEach(
                (addressRef, newLocConfigInDB) -> {
                  String keyCache =
                      LOC_KEY_CACHE_PREFIX + dayInWeek + RedisKeyConstant.COLON + addressRef;
                  try {
                    cacheService.deleteByKey(keyCache);

                    if (CollectionUtils.isNotEmpty(newLocConfigInDB)) {
                      cacheService.setListValue(keyCache, new ArrayList<>(newLocConfigInDB));
                    } else {
                      log.info(
                          "[updateLocSurchargeConfigOnCache] No config for dayInWeek={}, addressRef={}, only delete",
                          dayInWeek,
                          addressRef);
                    }

                  } catch (Exception exception) {
                    log.error(
                        "[updateLocSurchargeConfigOnCache] Could not update cacheKey={}", keyCache);
                  }
                }));
    log.info(
        "[updateLocSurchargeConfigOnCache] Update Loc Config follow addressRef list successfully! {}",
        cbdAddressAddedString);
  }

  @Override
  public void addCBDConfigCache(List<String> cbdAddressAdded) {
    if (CollectionUtils.isEmpty(cbdAddressAdded)) {
      log.info("[addCBDAddressCache] Insert CBD address list is empty!");
      return;
    }
    final String cbdAddressAddedString = String.join(", ", cbdAddressAdded);

    List<LocationSurchargeConfigEntity> cbdConfigs = getConfigFromDBByAddressList(cbdAddressAdded);

    if (CollectionUtils.isEmpty(cbdConfigs)) {
      log.info("[addCBDAddressCache] No config in DB for addressRefs={}", cbdAddressAddedString);
      return;
    }

    Map<String, Map<String, List<LocationSurchargeConfigEntity>>> configGroupByDayAndAddress =
        SurchargeUtils.groupConfigByDayInWeekThenAddress(cbdConfigs);

    configGroupByDayAndAddress.forEach(
        (dayInWeek, addressRefMap) ->
            addressRefMap.forEach(
                (addressRef, configsNeedToAddToCache) -> {
                  String keyCache =
                      LOC_KEY_CACHE_PREFIX + dayInWeek + RedisKeyConstant.COLON + addressRef;

                  try {
                    List<LocationSurchargeConfigEntity> configOnCache =
                        cacheService.getListValue(keyCache, LocationSurchargeConfigEntity.class);

                    Set<LocationSurchargeConfigEntity> uniqueConfigOnCache =
                        new HashSet<>(configsNeedToAddToCache);
                    uniqueConfigOnCache.addAll(configOnCache);

                    cacheService.deleteByKey(keyCache);
                    cacheService.setListValue(keyCache, new ArrayList<>(uniqueConfigOnCache));
                  } catch (Exception exception) {
                    log.error("[addCBDAddressCache] Could not update cacheKey={}", keyCache);
                  }
                }));
    log.info(
        "[addCBDAddressCache] Insert CBD address list successfully! {}", cbdAddressAddedString);
  }

  @Override
  public void removeCBDConfigCache(List<String> cbdAddressAdded) {
    if (CollectionUtils.isEmpty(cbdAddressAdded)) {
      log.info("[removeCBDAddressCache] Insert CBD address list is empty!");
      return;
    }
    cbdAddressAdded.forEach(
        addressRef -> {
          String keyPattern =
              LOC_KEY_CACHE_PREFIX
                  .concat(RedisKeyConstant.WILDCARD)
                  .concat(RedisKeyConstant.COLON)
                  .concat(addressRef);

          Set<String> keySet = cacheService.getKeysByPattern(keyPattern);

          if (CollectionUtils.isNotEmpty(keySet)) {
            keySet.forEach(
                key -> {
                  try {
                    List<LocationSurchargeConfigEntity> configOnCache =
                        cacheService.getListValue(key, LocationSurchargeConfigEntity.class);

                    List<LocationSurchargeConfigEntity> configAfterFilter =
                        configOnCache.stream()
                            .filter(
                                config ->
                                    ObjectUtils.isNotEmpty(config.getLocationId())
                                        && config.getLocationId() != 1)
                            .toList();
                    cacheService.deleteByKey(key);
                    cacheService.setListValue(key, new ArrayList<>(configAfterFilter));
                  } catch (Exception exception) {
                    log.error("[removeCBDAddressCache] Could not remove cacheKey={}", keyPattern);
                  }
                });
          }
        });
    log.info(
        "[removeCBDAddressCache] Remove CBD address list successfully! {}",
        String.join(", ", cbdAddressAdded));
  }

  private void processInsertCBDAddress(List<CBDAddress> requestList) {
    final Set<String> insertCBDAddressList =
        requestList.stream()
            .filter(CBDAddress::isValidConfig)
            .filter(CBDAddress::isInsertable)
            .map(CBDAddress::getAddressRef)
            .collect(Collectors.toSet());
    if (CollectionUtils.isEmpty(insertCBDAddressList)) {
      log.info("[updateCBDAddress] Insert CBD address list is empty!");
      return;
    }
    final String listAddressString = String.join(", ", insertCBDAddressList);
    try {
      cbdAddressConfigRepository.insertCBDAddress(insertCBDAddressList);
      addCBDConfigCache(insertCBDAddressList.stream().toList());
      log.info("[updateCBDAddress] Insert CBD Address successful: {}", listAddressString);
    } catch (Exception e) {
      log.error("[updateCBDAddress] Insert CBD Address failed: {}", listAddressString, e);
    }
  }

  private void processDeleteCBDAddress(List<CBDAddress> requestList) {
    final Set<String> deleteCBDAddressList =
        requestList.stream()
            .filter(CBDAddress::isValidConfig)
            .filter(CBDAddress::isDeletable)
            .map(CBDAddress::getAddressRef)
            .collect(Collectors.toSet());
    if (CollectionUtils.isEmpty(deleteCBDAddressList)) {
      log.info("[updateCBDAddress] Delete CBD address list is empty!");
      return;
    }
    final String listAddressString = String.join(", ", deleteCBDAddressList);
    try {
      cbdAddressConfigRepository.deleteCBDAddress(deleteCBDAddressList);
      removeCBDConfigCache(deleteCBDAddressList.stream().toList());
      log.info("[updateCBDAddress] Delete CBD Address successful: {}", listAddressString);
    } catch (Exception e) {
      log.error("[updateCBDAddress] Delete CBD Address failed: {}", listAddressString, e);
    }
  }

  private List<LocationSurchargeConfigEntity> getConfigFromDBByAddressList(
      List<String> cbdAddressAdded) {
    List<LocationSurchargeConfigEntity> locConfigs = new ArrayList<>();
    final String cbdAddressAddedString = String.join(", ", cbdAddressAdded);
    try {
      locConfigs = cbdAddressConfigRepository.getLocConfigByAddressRefList(cbdAddressAdded);
    } catch (Exception exception) {
      log.error(
          "[addCBDAddressCache] Error while get configs from DB cbdAddress={} , exception={}",
          cbdAddressAddedString,
          exception.getMessage());
    }
    return locConfigs;
  }
}

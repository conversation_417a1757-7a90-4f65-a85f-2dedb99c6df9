package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.FlatFareAdjustmentConfEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.S2CellEntity;
import java.util.List;

/**
 * Repository handling S2 cells. This repository provides an abstraction layer for accessing S2 cell
 * data
 */
public interface FlatFareAdjustmentConfRepository {
  /**
   * Retrieves all available S2 cell entities from the data source. This method is typically used
   * for obtaining a comprehensive list of S2 cells, each represented by an {@link S2CellEntity}
   * object.
   *
   * @return a list of {@link S2CellEntity} objects
   */
  List<FlatFareAdjustmentConfEntity> getFlatFareAdjustmentConf();
}

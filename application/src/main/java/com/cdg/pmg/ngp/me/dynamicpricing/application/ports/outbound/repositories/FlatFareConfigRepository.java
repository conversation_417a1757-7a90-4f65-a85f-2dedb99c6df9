package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.FlatFareConfig;
import java.util.List;

/** The interface flat fare config repository. */
public interface FlatFareConfigRepository {

  /**
   * Gets all flat fare configuration.
   *
   * @return List of FlatFareConfig
   */
  List<FlatFareConfig> getAllFlatFareConfig();

  /**
   * Gets param value by unique param key configuration.
   *
   * @return value of is live traffic conf
   */
  List<String> getConfByParamkeyFlatFare(String paramKey);

  /**
   * Gets flat fare configuration by identify for demand surge.
   *
   * @return List<FlatFareConfig>
   */
  List<FlatFareConfig> getConfByIdentifyForCalDemandSurge(String identify);
}

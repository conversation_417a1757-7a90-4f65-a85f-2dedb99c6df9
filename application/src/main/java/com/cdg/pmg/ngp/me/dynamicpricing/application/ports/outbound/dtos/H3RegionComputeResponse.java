package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos;

import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class H3RegionComputeResponse extends H3Region {
  @Serial private static final long serialVersionUID = 2061687306608920235L;

  private String regionVersion;
  private Double lat;
  private Double lng;

  public String getLatLngMapKey() {
    return lat + ":" + lng;
  }
}

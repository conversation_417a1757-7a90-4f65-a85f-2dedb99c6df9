package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DemandSupplyStatisticsResponseV2 implements Serializable {
  @Serial private static final long serialVersionUID = 1L;
  private String zoneId;
  private Integer demand15;
  private Integer demand30;
  private Integer demand60;
  private Integer predictedDemand15;
  private Integer predictedDemand30;
  private Integer supply;
  private Integer excessDemand15;
  private Integer excessDemand30;
  private Integer previousDemand15;
  private Integer batchCounter;
  private Double unmet15;
  private Double previousUnmet15;
}

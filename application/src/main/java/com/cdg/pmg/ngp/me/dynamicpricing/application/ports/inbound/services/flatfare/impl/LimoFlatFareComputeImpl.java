package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.flatfare.impl;

import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.GET_BOOKING_FEE_FAILED;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareConfigSet;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareVO;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.FlatFareConfigService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.LocationSurchargeService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.CommonUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.DateUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.SurchargeUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.BookingFeeItem;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.FareService;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.InternalServerException;
import java.text.MessageFormat;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;

@ServiceComponent
@Slf4j
public class LimoFlatFareComputeImpl extends FlatFareComputeImpl {

  public LimoFlatFareComputeImpl(
      FlatFareConfigSet configSet,
      FlatFareConfigService configService,
      FareService fareService,
      LocationSurchargeService locationSurchargeService) {
    super(configSet, configService, fareService, locationSurchargeService);
  }

  @Override
  public void calBookingFee(FlatFareVO vo) {
    super.calBookingFee(vo);
    vo.setActualPDTFlatFee(vo.getBookingFee());

    // CALC BOOKING FEE FOR METER
    final Date reqDate = vo.getFlatFareRequest().getRequestDate();
    final Integer vehTypeIdReq = vo.getFlatFareRequest().getVehTypeId();
    final String productIdReq = vo.getPdtId();
    Optional<BookingFeeItem> bookingFee =
        configSet.getBookingFeeList().stream()
            .filter(
                bookingFeeItem ->
                    vehTypeIdReq.equals(bookingFeeItem.getVehicleTypeId())
                        && RedisKeyConstant.EST_LIVE_TRAFFIC.equalsIgnoreCase(
                            bookingFeeItem.getFlatFareType())
                        && FlatfareConstants.STD_PDT_ID.equalsIgnoreCase(
                            bookingFeeItem.getProductId()))
            .findFirst();
    if (bookingFee.isPresent()) {
      vo.setBookingFee(bookingFee.get().getBookingFee());
    } else {
      log.error("Failed to get booking fee");
      final String errorMsg =
          MessageFormat.format(
              GET_BOOKING_FEE_FAILED.getMessage(),
              vehTypeIdReq,
              vo.getFlatFareRequest().getVehTypeId(),
              productIdReq,
              vo.getFlatFareRequest().getJobType(),
              DateUtils.isHolidaySingTime(reqDate, configSet.getHolidayList()),
              reqDate.toInstant().atOffset(ZoneOffset.UTC));
      throw new InternalServerException(errorMsg, GET_BOOKING_FEE_FAILED.getErrorCode());
    }
  }

  @Override
  public void calLocSurcharges(FlatFareVO vo) {
    SurchargeUtils.calLocSurchargesForLimo(vo, configSet.getLocationSurchargeConfigList());
  }

  @Override
  public void initVORequest(FlatFareVO vo) {
    // do nothing
  }

  @Override
  public void finalizeTotalAmount(FlatFareVO vo) {
    log.info("Start finalizeTotalAmount with vehTypeId={}", vo.getFlatFareRequest().getVehTypeId());

    try {
      double maxFareCap = FlatfareConstants.DEFAULT_MAX_FLATFARE_CAP;
      final String maxFareCapConfig = configSet.getMaxFlatFareCap();
      if (maxFareCapConfig != null) {
        maxFareCap = Double.parseDouble(maxFareCapConfig);
      }
      if (vo.getActualPDTFlatFee() != null
          && vo.getActualPDTFlatFee() > FlatfareConstants.PRICE_DEFAULT) {
        if (vo.getActualPDTFlatFee() > maxFareCap) {
          vo.setTotalFare(CommonUtils.roundToTwoBD(maxFareCap));
        } else {
          vo.setTotalFare(CommonUtils.roundToTwoBD(vo.getActualPDTFlatFee()));
        }
      } else {
        vo.setTotalFare(CommonUtils.roundToTwoBD(FlatfareConstants.PRICE_DEFAULT));
      }
    } catch (NumberFormatException | NullPointerException e) {
      throw handleExceptionForBreakDownCalc(e, vo, "finalizeTotalAmount");
    }
  }

  @Override
  public boolean isValidFlatfare(FlatFareVO vo) {
    return (vo.getTotalFare().doubleValue() >= FlatfareConstants.MIN_TOTAL_FARE)
        && !vo.getPdtId().isEmpty();
  }
}

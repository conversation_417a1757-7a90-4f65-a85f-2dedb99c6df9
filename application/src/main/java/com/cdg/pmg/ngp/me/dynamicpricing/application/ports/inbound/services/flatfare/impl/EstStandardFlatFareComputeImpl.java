package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.flatfare.impl;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareConfigSet;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareVO;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.FlatFareConfigService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.LocationSurchargeService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.SurchargeUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.FareService;
import lombok.extern.slf4j.Slf4j;

@ServiceComponent
@Slf4j
public class EstStandardFlatFareComputeImpl extends FlatFareComputeImpl {

  public EstStandardFlatFareComputeImpl(
      FlatFareConfigSet configSet,
      FlatFareConfigService configService,
      FareService fareService,
      LocationSurchargeService locationSurchargeService) {
    super(configSet, configService, fareService, locationSurchargeService);
  }

  @Override
  public void calLocSurcharges(FlatFareVO vo) {
    SurchargeUtils.calLocSurchargesForEstStandard(vo, configSet.getLocationSurchargeConfigList());
  }

  @Override
  public void finalizeTotalAmount(FlatFareVO vo) {
    /* do nothing */
  }

  @Override
  public void calAdditionalSurCharge(FlatFareVO vo) {
    log.info(
        "Start calAdditionalSurCharge with vehTypeId={}", vo.getFlatFareRequest().getVehTypeId());
    vo.setAdditionalSurcharge(FlatfareConstants.EST_STANDARD_FLATFARE_DEFAULT_ADDITIONAL_SURCHARGE);
  }

  @Override
  public void initVORequest(FlatFareVO vo) {
    vo.setPdtId(FlatfareConstants.STD_PDT_ID);
  }

  @Override
  public boolean isValidFlatfare(FlatFareVO vo) {
    return vo.getTotalFare().doubleValue() >= FlatfareConstants.MIN_TOTAL_FARE;
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.MlGetFareRequestAggStatsEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.RegionGetFareCountEntity;

import java.time.Instant;
import java.util.List;

/**
 * Repository interface for ML Get Fare Request Aggregation Statistics. Provides methods to interact
 * with the ml_get_fare_request_agg_stats table.
 */
public interface MlGetFareRequestAggStatsRepository {

  /**
   * Batch save ML Get Fare Request Aggregation Statistics entities.
   *
   * @param entities a list of {@link MlGetFareRequestAggStatsEntity} to save
   */
  void saveAll(List<MlGetFareRequestAggStatsEntity> entities);

  /**
   * List all ML Get Fare Request Aggregation Statistics entities by a specific time.
   * 
   * @param time the time to search for
   * @return a list of {@link RegionGetFareCountEntity}
   */
  List<RegionGetFareCountEntity> listAllByTimeRange(Instant time);
}

package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DemandSupplyStatisticsResponse implements Serializable {
  @Serial private static final long serialVersionUID = 1L;
  private String zoneId;
  private Integer recentDemand;
  private Integer previousDemand;
  private Integer predictedDemand;
  private Integer supply;
  private Integer batchCounter;
  private Integer excessDemand;
}

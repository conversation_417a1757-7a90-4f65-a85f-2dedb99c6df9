package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticBasedConfigurationEffectiveCheckEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticBasedConfigurationVersionEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticTimeBasedConfigurationEntity;
import java.time.Instant;
import java.util.List;
import java.util.Optional;

public interface StaticTimeBasedConfigurationRepository {

  /**
   * Get all versions of static time-based configurations.
   *
   * @return a list of all versions
   */
  List<StaticBasedConfigurationVersionEntity> findAllVersions();

  StaticTimeBasedConfigurationEntity save(StaticTimeBasedConfigurationEntity entity);

  StaticTimeBasedConfigurationEntity update(StaticTimeBasedConfigurationEntity entity);

  List<StaticTimeBasedConfigurationEntity> findAllByVersion(String version);

  Optional<StaticTimeBasedConfigurationEntity> findById(Long id);

  List<StaticTimeBasedConfigurationEntity> findByEffectiveTimeRange(Instant effectiveTime);

  StaticBasedConfigurationEffectiveCheckEntity effectiveCheck();

  void deleteById(Long id);

  /**
   * Batch create static time-based configurations.
   *
   * @param configurations the list of configurations to create
   * @return the list of created configurations
   */
  List<StaticTimeBasedConfigurationEntity> batchCreateTimeRegionBasedConfigurations(
      List<StaticTimeBasedConfigurationEntity> configurations);
}

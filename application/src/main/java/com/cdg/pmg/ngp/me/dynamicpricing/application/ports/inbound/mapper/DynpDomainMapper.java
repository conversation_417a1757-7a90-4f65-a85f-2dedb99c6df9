package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.mapper;

import com.cdg.pmg.ngp.me.dynamicpricing.commands.StoreFareBreakdownCommandRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.GeneratedRouteEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.MultiFareRequestEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.RouteInfo;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.StoreFareBreakdownRequestEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.queries.MultiFareRequestQuery;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/** The interface Dynp domain mapper. */
@Mapper
public interface DynpDomainMapper {

  /**
   * Map to est fare request entity est fare request entity.
   *
   * @param requestQuery the request query
   * @return the est fare request entity
   */
  MultiFareRequestEntity mapToEstFareRequestEntity(MultiFareRequestQuery requestQuery);

  /**
   * Map to store fare breakdown request entity store fare breakdown request entity.
   *
   * @param requestQuery the request query
   * @return the store fare breakdown request entity
   */
  StoreFareBreakdownRequestEntity mapToStoreFareBreakdownRequestEntity(
      StoreFareBreakdownCommandRequest requestQuery);

  /**
   * Map route info to generated route entity generated route entity.
   *
   * @param source the source
   * @return the generated route entity
   */
  @Mapping(target = "pickupPoint.lat", source = "source.pickupAddressLat")
  @Mapping(target = "pickupPoint.lng", source = "source.pickupAddressLng")
  @Mapping(target = "intermediatePoint.lat", source = "source.intermediateAddrLat")
  @Mapping(target = "intermediatePoint.lng", source = "source.intermediateAddrLng")
  @Mapping(target = "destinationPoint.lat", source = "source.destAddressLat")
  @Mapping(target = "destinationPoint.lng", source = "source.destAddressLng")
  @Mapping(target = "distance", source = "source.routingDistance")
  @Mapping(target = "duration", source = "source.ett")
  GeneratedRouteEntity mapRouteInfoToGeneratedRouteEntity(RouteInfo source);
}

package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.mapper.PricingRangeConfigAppMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.PricingRangeConfigService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.PricingRangeConfigRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.commands.PricingRangeConfigCommand;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.PricingRangeConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.utils.CollectionUtils;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ServiceComponent
@RequiredArgsConstructor
@Slf4j
public class PricingRangeConfigServiceImpl implements PricingRangeConfigService {
  private final PricingRangeConfigRepository pricingRangeConfigRepository;
  private final PricingRangeConfigAppMapper pricingRangeConfigAppMapper;

  @Override
  public List<PricingRangeConfigEntity> getPricingRangeConfigs() {
    return pricingRangeConfigRepository.getPricingRangeConfigs();
  }

  @Override
  public PricingRangeConfigEntity insertOrUpdatePricingRangeConfig(
      final PricingRangeConfigCommand pricingRangeConfigCommand) {
    PricingRangeConfigEntity pricingRangeConfigEntity =
        pricingRangeConfigAppMapper.mapPricingRangeConfigCommandToPricingRangeConfigEntity(
            pricingRangeConfigCommand);

    List<PricingRangeConfigEntity> pricingRangeConfigsByDayAndHour =
        pricingRangeConfigRepository.getPricingRangeConfigsByDayAndHour(
            pricingRangeConfigEntity.getDay(), pricingRangeConfigEntity.getHour());
    if (CollectionUtils.isEmpty(pricingRangeConfigsByDayAndHour)) {
      return pricingRangeConfigRepository.insertPricingRangeConfig(pricingRangeConfigEntity);
    }
    return pricingRangeConfigRepository.updatePricingRangeConfig(
        pricingRangeConfigEntity, pricingRangeConfigsByDayAndHour.get(0));
  }
}

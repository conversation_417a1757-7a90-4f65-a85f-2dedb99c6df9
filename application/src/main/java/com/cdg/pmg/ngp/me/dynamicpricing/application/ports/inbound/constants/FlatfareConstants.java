package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants;

import java.io.Serial;
import java.io.Serializable;

public class FlatfareConstants implements Serializable {
  public static final String JOB_TYPE_CJ = "CJ";
  @Serial private static final long serialVersionUID = 1L;
  public static final String LIVE_TRAFFIC_DYNAMIC_PRICE = "LIVE_TRAFFIC_DYNAMIC_PRICE";
  public static final String LIVE_TRAFFIC_CAL_METHOD = "LIVE_TRAFFIC";
  public static final String ADDITIONAL_CHARGE_TYPE = "ADDITIONAL_CHARGE";
  public static final Double EST_STANDARD_FLATFARE_DEFAULT_ADDITIONAL_SURCHARGE = 0.0;
  public static final String STANDARD_DEFAULT_ZONE_ID = "00";
  public static final Double PRICE_DEFAULT = 0.0;
  public static final Long PLATFORM_FEE_ID_DEFAULT = 0L;
  public static final String FLATFARE_BOOKING_FEE_TYPE = "FLATFARE_BOOKING_FEE";
  public static final String PEAK_HOUR_TYPE = "PEAK_HOUR";
  public static final String MIDNIGHT_TYPE = "MID_NIGHT";

  public static final String MAX_FLATFARE_CAP = "MAX_FLATFARE_CAP";

  public static final String EVENT_SURGE_ADDRESS = "EVENT_SURGE_ADDRESS";
  public static final String SINGLE_CONFIGS = "SINGLE_CONFIGS";
  public static final String WILDCARD = "*";
  public static final String FLAT_FARE_DOTS = "FLAT_FARE:";
  public static final String HYPHEN = "-";
  public static final Double MIN_DISTANCE_KM = -0.1;
  public static final long MIN_ETT = -1;
  public static final Double MAX_DISTANCE_KM = 1000.0;
  public static final String ROUTE = "ROUTE";
  public static final Integer FIVE_MINUTES = 5;
  public static final Integer TEN_MINUTES = 10;
  public static final Integer FIFTEEN_MINUTES = 15;
  // down to 2 to testing
  public static final Integer MIN_TOTAL_FARE = 0;

  /***
   * For Normal Rate
   ***/
  public static final String COLON = ":";

  public static final String MON = "MON";
  public static final String TUE = "TUE";
  public static final String WED = "WED";
  public static final String THU = "THU";
  public static final String FRI = "FRI";
  public static final String SAT = "SAT";
  public static final String SUN = "SUN";
  public static final String HOL = "HOL";

  public static final String LOC_SURC_KEY_PREFIX = "DYNAMIC_PRICING:LOC_SURC:";
  public static final String FLAG_DOWN_RATE = "FLAG_DOWN_RATE";
  public static final String TIER_1 = "TIER_1_";
  public static final String TIER_2 = "TIER_2_";

  public static final String DRIVER_SURGE_LEVEL_INDICATION_TYPE = "DRIVER_SURGE_LEVEL_INDICATION";
  public static final String DRV_EFFECT_FROM_TS = "_DRV_EFFECT_FROM_TS";
  public static final String DRV_EFFECT_TO_TS = "_DRV_EFFECT_TO_TS";
  public static final String DRV_SURGE_COLOR_HEX = "_DRV_SURGE_COLOR_HEX";
  public static final String DRV_SURGE_LEVEL = "_DRV_SURGE_LEVEL";
  public static final String DRV_SURGE_PERC_FROM = "_DRV_SURGE_PERC_FROM";
  public static final String DRV_SURGE_PERC_TO = "_DRV_SURGE_PERC_TO";
  public static final String ETT_FARE_TYPE = "ETT_FARE_TYPE";
  public static final String COMPANY_HOLIDAY = "COMPANY_HOLIDAY";

  public static final String DYNAMIC_PRICING = "DYNAMIC_PRICING";
  public static final String FLAT_FARE = "FLAT_FARE";
  public static final String CACHE_TIMER_MINS_MULTI_FLATFARE = "CACHE_TIMER_MINS_MULTI_FLATFARE";
  public static final int CACHE_TIMER_MINS_MULTI_FLATFARE_DEFAULT = 8;
  public static final String CACHE_TIMER_MINS_BREAKDOWN_FLATFARE =
      "CACHE_TIMER_MINS_BREAKDOWN_FLATFARE";
  public static final int CACHE_TIMER_MINS_BREAKDOWN_FLATFARE_DEFAULT = 8;
  public static final Double PLATFORM_FEE_DEFAULT = 0.0;

  public enum TIER {
    PER_COUNT_FARE,
    PER_COUNT_METER,
    START_DISTANCE,
    END_DISTANCE
  }

  public static final String DURATION_TIME_UNIT = "DURATION_UNIT";
  public static final String DURATION_TIME_RATE = "DURATION_RATE";
  public static final String ETT_UNIT = "ETT_UNIT";

  public static final String PEAK_HOUR = "PEAK_HOUR_";
  public static final String PEAK_HOUR_RT = "PEAK_HOUR_RATE_";
  public static final String PEAK_HOUR_DAYS = "PEAK_HOUR_DAYS_";
  public static final String PEAK_HOUR_START_TIME = "PEAK_HOUR_START_TIME_";
  public static final String PEAK_HOUR_END_TIME = "PEAK_HOUR_END_TIME_";
  public static final String PEAK_HOUR_STEPUP_5MINS = "PEAK_HOUR_STEPUP_5mins_";
  public static final String PEAK_HOUR_STEPUP_10MINS = "PEAK_HOUR_STEPUP_10mins_";
  public static final String PEAK_HOUR_STEPUP_15MINS = "PEAK_HOUR_STEPUP_15mins_";
  public static final String PEAK_HOUR_STEPDOWN_5MINS = "PEAK_HOUR_STEPDOWN_5mins_";
  public static final String PEAK_HOUR_STEPDOWN_10MINS = "PEAK_HOUR_STEPDOWN_10mins_";
  public static final String PEAK_HOUR_STEPDOWN_15MINS = "PEAK_HOUR_STEPDOWN_15mins_";

  public static final String MIDNIGHT = "MID_NIGHT_";
  public static final String MIDNIGHT_RT = "MID_NIGHT_RATE_";
  public static final String MIDNIGHT_DAYS = "MID_NIGHT_DAYS_";
  public static final String MIDNIGHT_START_TIME = "MID_NIGHT_START_TIME_";
  public static final String MIDNIGHT_END_TIME = "MID_NIGHT_END_TIME_";
  public static final String MID_NIGHT_STEPUP_5MINS = "MID_NIGHT_STEPUP_5mins_";
  public static final String MID_NIGHT_STEPUP_10MINS = "MID_NIGHT_STEPUP_10mins_";
  public static final String MID_NIGHT_STEPUP_15MINS = "MID_NIGHT_STEPUP_15mins_";
  public static final String MID_NIGHT_STEPDOWN_5MINS = "MID_NIGHT_STEPDOWN_5mins_";
  public static final String MID_NIGHT_STEPDOWN_10MINS = "MID_NIGHT_STEPDOWN_10mins_";
  public static final String MID_NIGHT_STEPDOWN_15MINS = "MID_NIGHT_STEPDOWN_15mins_";

  public static final String NORMAL_TAXI_TYPE = "NORMAL";

  /** Address Surge Event Address */
  public static final String EVENT_SURGE_ADDR = "EVENT_SURGE_ADDR_";

  public static final String EVENT_SURGE_ADDR_DAYS = "EVENT_SURGE_ADDR_DAYS_";
  public static final String EVENT_SURGE_ADDR_MONTHS = "EVENT_SURGE_ADDR_MONTHS_";
  public static final String EVENT_SURGE_ADDR_START_TIME = "EVENT_SURGE_ADDR_START_TIME_";
  public static final String EVENT_SURGE_ADDR_END_TIME = "EVENT_SURGE_ADDR_END_TIME_";
  public static final String EVENT_SURGE_ADDR_CHARGETYPE = "EVENT_SURGE_ADDR_CHARGETYPE_";
  public static final String EVENT_SURGE_ADDR_CHARGEBY = "EVENT_SURGE_ADDR_CHARGEBY_";
  public static final String EVENT_SURGE_ADDR_CHARGEVAL = "EVENT_SURGE_ADDR_CHARGEVAL_";
  public static final String EVENT_SURGE_ADDR_APPC = "EVENT_SURGE_ADDR_APPC_";

  /** Address Surge Event Zone */
  public static final String EVENT_SURGE_ZONE = "EVENT_SURGE_ZONE_";

  public static final String EVENT_SURGE_ZONE_DAYS = "EVENT_SURGE_ZONE_DAYS_";
  public static final String EVENT_SURGE_ZONE_MONTHS = "EVENT_SURGE_ZONE_MONTHS_";
  public static final String EVENT_SURGE_ZONE_START_TIME = "EVENT_SURGE_ZONE_START_TIME_";
  public static final String EVENT_SURGE_ZONE_END_TIME = "EVENT_SURGE_ZONE_END_TIME_";
  public static final String EVENT_SURGE_ZONE_CHARGETYPE = "EVENT_SURGE_ZONE_CHARGETYPE_";
  public static final String EVENT_SURGE_ZONE_CHARGEBY = "EVENT_SURGE_ZONE_CHARGEBY_";
  public static final String EVENT_SURGE_ZONE_CHARGEVAL = "EVENT_SURGE_ZONE_CHARGEVAL_";
  public static final String EVENT_SURGE_ZONE_APPC = "EVENT_SURGE_ZONE_APPC_";

  /** Dyna Dos Adjument */
  public static final String DYNA_DOS_ADJUSTMENT = "_DYNA_DOS_ADJUSTMENT";

  public static final String DYNA_DOS_ADJUSTMENT_MONTHS = "_DYNA_DOS_ADJUSTMENT_MONTHS";
  public static final String DYNA_DOS_ADJUSTMENT_DAYS_M = "_DYNA_DOS_ADJUSTMENT_DAYS_M";
  public static final String DYNA_DOS_ADJUSTMENT_DAYS_W = "_DYNA_DOS_ADJUSTMENT_DAYS_W";
  public static final String DYNA_DOS_ADJUSTMENT_START_TIME = "_DYNA_DOS_ADJUSTMENT_START_TIME";
  public static final String DYNA_DOS_ADJUSTMENT_END_TIME = "_DYNA_DOS_ADJUSTMENT_END_TIME";
  public static final String DYNA_DOS_ADJUSTMENT_CHARGEBY = "_DYNA_DOS_ADJUSTMENT_CHARGEBY";
  public static final String DYNA_DOS_ADJUSTMENT_CHARGE_DOSMIN_C0 =
      "_DYNA_DOS_ADJUSTMENT_CHARGE_DOSMIN_C0";
  public static final String DYNA_DOS_ADJUSTMENT_CHARGE_DOSMAX_C0 =
      "_DYNA_DOS_ADJUSTMENT_CHARGE_DOSMAX_C0";
  public static final String DYNA_DOS_ADJUSTMENT_CHARGE_DOSVAL_C0 =
      "_DYNA_DOS_ADJUSTMENT_CHARGE_DOSVAL_C0";
  public static final String DYNA_DOS_ADJUSTMENT_CHARGE_DOSMIN_C1 =
      "_DYNA_DOS_ADJUSTMENT_CHARGE_DOSMIN_C1";
  public static final String DYNA_DOS_ADJUSTMENT_CHARGE_DOSMAX_C1 =
      "_DYNA_DOS_ADJUSTMENT_CHARGE_DOSMAX_C1";
  public static final String DYNA_DOS_ADJUSTMENT_CHARGE_DOSVAL_C1 =
      "_DYNA_DOS_ADJUSTMENT_CHARGE_DOSVAL_C1";
  public static final String DYNA_DOS_ADJUSTMENT_CHARGE_DOSMIN_C2 =
      "_DYNA_DOS_ADJUSTMENT_CHARGE_DOSMIN_C2";
  public static final String DYNA_DOS_ADJUSTMENT_CHARGE_DOSMAX_C2 =
      "_DYNA_DOS_ADJUSTMENT_CHARGE_DOSMAX_C2";
  public static final String DYNA_DOS_ADJUSTMENT_CHARGE_DOSVAL_C2 =
      "_DYNA_DOS_ADJUSTMENT_CHARGE_DOSVAL_C2";
  public static final String DYNA_DOS_ADJUSTMENT_CHARGE_DOSMIN_C3 =
      "_DYNA_DOS_ADJUSTMENT_CHARGE_DOSMIN_C3";
  public static final String DYNA_DOS_ADJUSTMENT_CHARGE_DOSMAX_C3 =
      "_DYNA_DOS_ADJUSTMENT_CHARGE_DOSMAX_C3";
  public static final String DYNA_DOS_ADJUSTMENT_CHARGE_DOSVAL_C3 =
      "_DYNA_DOS_ADJUSTMENT_CHARGE_DOSVAL_C3";
  public static final String DYNA_DOS_ADJUSTMENT_CHARGE_DOSMIN_C4 =
      "_DYNA_DOS_ADJUSTMENT_CHARGE_DOSMIN_C4";
  public static final String DYNA_DOS_ADJUSTMENT_CHARGE_DOSMAX_C4 =
      "_DYNA_DOS_ADJUSTMENT_CHARGE_DOSMAX_C4";
  public static final String DYNA_DOS_ADJUSTMENT_CHARGE_DOSVAL_C4 =
      "_DYNA_DOS_ADJUSTMENT_CHARGE_DOSVAL_C4";
  public static final String DYNA_DOS_ADJUSTMENT_CHARGE_DOSMIN_C5 =
      "_DYNA_DOS_ADJUSTMENT_CHARGE_DOSMIN_C5";
  public static final String DYNA_DOS_ADJUSTMENT_CHARGE_DOSMAX_C5 =
      "_DYNA_DOS_ADJUSTMENT_CHARGE_DOSMAX_C5";
  public static final String DYNA_DOS_ADJUSTMENT_CHARGE_DOSVAL_C5 =
      "_DYNA_DOS_ADJUSTMENT_CHARGE_DOSVAL_C5";
  public static final String DYNA_DOS_ADJUSTMENT_CHARGE_DOSMIN_C6 =
      "_DYNA_DOS_ADJUSTMENT_CHARGE_DOSMIN_C6";
  public static final String DYNA_DOS_ADJUSTMENT_CHARGE_DOSMAX_C6 =
      "_DYNA_DOS_ADJUSTMENT_CHARGE_DOSMAX_C6";
  public static final String DYNA_DOS_ADJUSTMENT_CHARGE_DOSVAL_C6 =
      "_DYNA_DOS_ADJUSTMENT_CHARGE_DOSVAL_C6";
  public static final String DYNA_DOS_ADJUSTMENT_CHARGE_DOSMIN_C7 =
      "_DYNA_DOS_ADJUSTMENT_CHARGE_DOSMIN_C7";
  public static final String DYNA_DOS_ADJUSTMENT_CHARGE_DOSMAX_C7 =
      "_DYNA_DOS_ADJUSTMENT_CHARGE_DOSMAX_C7";
  public static final String DYNA_DOS_ADJUSTMENT_CHARGE_DOSVAL_C7 =
      "_DYNA_DOS_ADJUSTMENT_CHARGE_DOSVAL_C7";
  public static final String DYNA_DOS_ADJUSTMENT_CHARGE_DOSMIN_C8 =
      "_DYNA_DOS_ADJUSTMENT_CHARGE_DOSMIN_C8";
  public static final String DYNA_DOS_ADJUSTMENT_CHARGE_DOSMAX_C8 =
      "_DYNA_DOS_ADJUSTMENT_CHARGE_DOSMAX_C8";
  public static final String DYNA_DOS_ADJUSTMENT_CHARGE_DOSVAL_C8 =
      "_DYNA_DOS_ADJUSTMENT_CHARGE_DOSVAL_C8";
  public static final String DYNA_DOS_ADJUSTMENT_CHARGE_DOSMIN_C9 =
      "_DYNA_DOS_ADJUSTMENT_CHARGE_DOSMIN_C9";
  public static final String DYNA_DOS_ADJUSTMENT_CHARGE_DOSMAX_C9 =
      "_DYNA_DOS_ADJUSTMENT_CHARGE_DOSMAX_C9";
  public static final String DYNA_DOS_ADJUSTMENT_CHARGE_DOSVAL_C9 =
      "_DYNA_DOS_ADJUSTMENT_CHARGE_DOSVAL_C9";

  public static final String ADDITIONAL_CHARGE = "ADDITIONAL_CHARGE_";

  /** CR0318048 fare adjustment Charge priority level* */
  public static final String ZONE_TARGETED_CHARGE_PRIORITY_LEVEL =
      "ZONE_TARGETED_CHARGE_PRIORITY_LEVEL";

  public static final String DYNA_DOS_FARE_CHARGE_PRIORITY_LEVEL =
      "DYNA_DOS_FARE_CHARGE_PRIORITY_LEVEL";
  public static final String ZONE_DOS_FARE_CHARGE_PRIORITY_LEVEL =
      "ZONE_DOS_FARE_CHARGE_PRIORITY_LEVEL";
  public static final String PAX_SURGE_INDICATOR_THRESHOLD = "PAX_SURGE_INDICATOR_THRESHOLD";
  public static final String PAX_SURGE_INDICATOR_THRESHOLD_0 = "PAX_SURGE_INDICATOR_THRESHOLD_0";

  /***
   * QI
   ****/
  public static final String QI_SINGLEROUTE_DOMAIN_URL = "QI_SINGLEROUTE_DOMAIN_URL";

  public static final String QI_MULTIROUTE_DOMAIN_URL = "QI_MULTIROUTE_DOMAIN_URL";
  public static final String MULTIROUTE_OPTION = "MULTIROUTE_OPTION";
  public static final String MULTIROUTE_BYPASS = "MULTIROUTE_BYPASS";
  public static final String MULTIROUTE_TRAVELINFO = "MULTIROUTE_TRAVELINFO";
  public static final String MULTIROUTE_DISABLETRAFFIC = "MULTIROUTE_DISABLETRAFFIC";
  public static final String ENABLE_QI_MULTIROUTE = "ENABLE_QI_MULTIROUTE";
  public static final String UPDATE_BREAKDOWN_DETAILS = "UPDATE_BREAKDOWN_DETAILS";
  public static final String DOS_LIFE_SPAN_UPDATE = "DOS_UPDATE_INTERVAL_MIN";
  public static final String LIVE_TRAFFIC_SCH_CHECK_SECS = "LIVE_TRAFFIC_SCH_CHECK_SECS";
  public static final String ERPLAT = "ERPLAT";
  public static final String ERPLNG = "ERPLNG";

  /***
   * FareType
   */
  public static final String LOC_SURC_TYPE = "LOC_SURC";

  public static final String EVENT_SURGE_TYPE = "EV_SURGE";

  public static final String ONLINE = "Service is On";
  public static final String OFFLINE = "Serivce is Offline";
  public static final String SUCCESS = "FLAT_FARE_SUCCESS_MSG";
  public static final String FAIL = "FLAT_FARE_FAIL_MSG";
  public static final String ERROR_RESP_CODE = "500";
  public static final String ERROR_RESP_MSG = "FLAT_FARE_ERROR_MSG";

  /***
   * Charge By
   ****/
  public static final String LOCATION_SURC_BY_PICKUP = "LOCATION_SURC_BY_PICKUP";

  public static final String LOCATION_SURC_BY_DEST = "LOCATION_SURC_BY_DEST";

  /***
   * Charge Type
   ****/
  public static final String PERCENTAGE = "PER";

  public static final String FIXED_AMOUNT = "FIX";

  /****
   * Booking Fare
   ****/
  public static final String TARIFF_PDT = "PDT";

  public static final String TARIFF_CJBOOKINGFEE = "CBKC";
  public static final String TARIFF_AJBOOKINGFEE = "ABKC";
  public static final String YES = "Y";
  public static final String NO = "N";
  public static final String GENERAL = "G";

  public static final String FLATFAREJSONKEY = "flatfare";
  public static final String CHARGE_BY_DEST = "DEST";
  public static final String CHARGE_BY_PICKUP = "PICKUP";

  public static final String WEEKENDS = "SAT,SUN";

  public static final String TOTAL_FARE_ESTIMATE_LF = "TOTAL_FARE_ESTIMATE_LF";
  public static final String TOTAL_FARE_ESTIMATE_RT = "TOTAL_FARE_ESTIMATE_RT";
  public static final String NORMAL_FLAT_FARE_VEH_IDS = "NORMAL_FLAT_FARE_VEH_IDS";
  public static final String LIMO_FLAT_FARE_VEH_IDS = "LIMO_FLAT_FARE_VEH_IDS";

  public static final String NORMAL_FLATFARE_PDT_ID = "FLAT-001";
  public static final String MAXI_FLATFARE_PDT_ID = "FFMAXI01";
  public static final String MERC_FLATFARE_PDT_ID = "FFMERC01";

  /***
   * From Changi to Any
   */
  public static final String GUA_PDT_ID = "GUA-001";

  /** From Any to Changi */
  public static final String GUD_PDT_ID = "GUD-001";

  /***
   * Any to Any
   */
  public static final String OWT_PDT_ID = "OWT-001";

  /***
   * For special Case handling
   ***/
  public static final String OTHER_FLATFARE_PDT_ID = "FLATOHTR-001";

  public static final String STD_PDT_ID = "STD001";

  public static final Integer MAXI_VEHICLE_ID = 1;
  public static final Integer MERC_VEHICLE_ID = 2;
  public static final Integer NORMAL_VEHICLE_ID = 3;

  public static final String CENTRAL_BUSINESS_DISTRICT = "Central Business District";
  public static final String MARINA_BAY_CRUISE_CENTER = "Marina Bay Cruise Center";
  public static final String CHANGI_AIRPORT = "Changi Airport";

  public static final String ENABLE_QI_ERP_CHARGE = "ENABLE_QI_ERP_CHARGE";
  public static final String IS_LIVE_TRAFFIC = "IS_LIVE_TRAFFIC";
  public static final String IS_NGINX_ADDRESS = "IS_NGINX_ADDRESS";
  public static final String DYNAMIC_PRICING_ENABLED = "DYNAMIC_PRICING_ENABLED";
  public static final String DYNAMIC_PRICING_ZONE_IDS = "DYNAMIC_PRICING_ZONE_IDS";
  public static final String DYNAMIC_PRICING_VEH_GROUP_IDS = "DYNAMIC_PRICING_VEH_GROUP_IDS";
  public static final String IS_SWRITER_REDIS_ENABLED = "IS_SWRITER_REDIS_ENABLED";
  public static final String LIVE_TRAFFIC_API_URL = "LIVE_TRAFFIC_API_URL";
  public static final String DYNAMIC_PRICING_API_URL = "DYNAMIC_PRICING_API_URL";
  public static final String DYNAMIC_PRICING_BOOKED_API_URL = "DYNAMIC_PRICING_BOOKED_API_URL";
  public static final String REDIS_SERV_IP_URL = "REDIS_SERV_IP";
  public static final String LIVE_TRAFFIC_API_TIMEOUT_CHECK = "LIVE_TRAFFIC_API_TIMEOUT_CHECK";
  public static final String INTERNAL_ERP_RATE = "INTERNAL_ERP_RATE";
  public static final String MAX_JOURNEY_DISTANCE_KM = "MAX_JOURNEY_DISTANCE_KM";
  public static final String CACHE_TIMER_MINS = "CACHE_TIMER_MINS";

  public static final String LIMO_EXTRA_MIDNIGHT_CHARGE = "LIMO_EXTRA_MIDNIGHT_CHARGE";

  /** Config Dynamic Start End Time* */
  public static final String DYNAMIC_PRICING_START_TIME = "_DYNAMIC_PRICING_START_TIME";

  public static final String DYNAMIC_PRICING_END_TIME = "_DYNAMIC_PRICING_END_TIME";

  /***Config PREFIX Sets***/
  public static final String LIVE_TRAFFIC_PREFIX = "LIVE_TRAFFIC_";

  public static final String WAIT_TIME_PREFIX = "WAIT_TIME_";
  public static final String EST_WAIT_TIME_PREFIX = "EST_WAIT_TIME_";
  public static final String EST_LIVE_TRAFFIC_PREFIX = "EST_LIVE_TRAFFIC_";
  public static final String EST_LIMO_WAIT_TIME_PREFIX = "EST_LIMO_WAIT_TIME_";
  public static final String EST_LIMO_LIVE_TRAFFIC_PREFIX = "EST_LIMO_LIVE_TRAFFIC_";
  public static final String WAIT_TIME_CAL_METHOD = "WAIT_TIME";
  public static final String WAIT_TIME_DYNAMIC_PRICE = "WAIT_TIME_DYNAMIC_PRICE";

  /** Show Meter Or Flatfare Only, if both are not meet, show both * */
  public static final String VEH_GRP_SHOW_FF_ONLY = "VEH_GRP_SHOW_FF_ONLY";

  public static final String VEH_GRP_SHOW_METER_ONLY = "VEH_GRP_SHOW_METER_ONLY";

  public static final String STANDARD_JOB_PREFIX = "CJ";
  public static final String ADVANCE_JOB_PREFIX = "AJ";

  /***TOC-12 Dynamic schedular interval config***/
  public static final String DYNAMIC_PRICING_SCHEDULER_INTERVAL =
      "DYNAMIC_PRICING_SCHEDULER_INTERVAL";

  /** TOC - 52 : Flatfare default Max Cap Constant declaration * */
  public static final Double DEFAULT_MAX_FLATFARE_CAP = 200.0;

  // CDG-1963 Address Sync changes values
  public static final String ADDRESS_SYNC_SCHEDULER_INTERVAL = "ADDRESS_SYNC_SCHEDULER_INTERVAL";
  public static final String ADDRESS_SYNC_FLAG_ENABLED = "ADDRESS_SYNC_FLAG_ENABLED";
  public static final String ADDRESS_SYNC_FLAG = "Y";
  public static final String ADDRESS_SYNC_PROCEDURE =
      "cn3objs.mspk_flatfare_interface.address_sync_flatfare";

  /*CDG-8173 ComfortRide base fare calculation changes*/
  public static final String EST_INTERNAL_ERP_RATE = "EST_INTERNAL_ERP_RATE";
  public static final String TARIFF_PDT_CJBOOKINGFEE = "PDT";
  public static final String LIMO_FLAT_FARE = "LimoFlatFare";
  public static final String STANDARD_FLAT_FARE = "StandardFlatFare";
  public static final String EST_STANDARD_FLAT_FARE = "EstStandardFlatFare";
}

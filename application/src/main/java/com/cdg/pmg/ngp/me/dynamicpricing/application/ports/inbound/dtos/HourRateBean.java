package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos;

import java.time.LocalTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HourRateBean {

  private String daysOfWeekIncluded;
  private LocalTime startTime;
  private LocalTime endTime;
  private Double stepUp5;
  private Double stepUp10;
  private Double stepUp15;
  private Double stepDown5;
  private Double stepDown10;
  private Double stepDown15;
  private Double rate;
}

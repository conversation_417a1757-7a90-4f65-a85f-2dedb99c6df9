package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.RegionRainfall;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public interface WeatherRetrievalService {

  List<RegionRainfall> getRainfall();

  default Map<Long, Double> getRainFallMap() {
    return getRainfall().stream()
        .collect(
            Collectors.toMap(
                RegionRainfall::getRegionId,
                RegionRainfall::getAverageIntensity,
                (oldValue, newValue) -> newValue));
  }
}

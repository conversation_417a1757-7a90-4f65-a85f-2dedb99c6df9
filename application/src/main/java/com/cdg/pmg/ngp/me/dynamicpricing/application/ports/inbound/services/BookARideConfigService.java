package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.DynamicSurgesEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FareUploadConfiguration;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.LocationSurchargeConfigEntity;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * BookARideConfigService is an interface that defines the contract for services dealing with the
 * configuration and retrieval of data in module book-a-ride.
 */
public interface BookARideConfigService {
  /**
   * Retrieves a list of string representations of holiday configurations. This method is used to
   * fetch the configured holidays
   *
   * @return List of Strings, each representing a holiday configuration.
   */
  List<String> getHolidayConfigs();

  /**
   * Fetches the dynamic surge pricing entities. This method is responsible for retrieving current
   * surge pricing
   *
   * @return List of DynamicSurgesEntity, each representing a surge pricing configuration.
   */
  List<DynamicSurgesEntity> getDynpSurges();

  /**
   * Retrieves a list of location-based surcharge configurations. This method considers the current
   * date and a list of holidays
   *
   * @param currentDate The date for which location surcharge configuration is requested.
   * @param holidayList A list of holidays to consider in the surcharge calculation.
   * @return List of LocationSurchargeConfig, each representing a location-based surcharge
   *     configuration.
   */
  List<LocationSurchargeConfigEntity> getLocationSurchargeConfigs(
      final Date currentDate, final List<String> holidayList);

  Map<String, String> getFlatFareConfigs();

  void getBookingFareConfigs(final FareUploadConfiguration fareUploadConfiguration, Date reqDate);
}

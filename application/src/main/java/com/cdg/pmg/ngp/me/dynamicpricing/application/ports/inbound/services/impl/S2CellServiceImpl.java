package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.S2CellService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.S2CellRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.CacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.S2CellEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.queries.S2CellListConfigQueryResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

@ServiceComponent
@Slf4j
@RequiredArgsConstructor
@Getter
@Setter
public class S2CellServiceImpl implements S2CellService {
  private static final String S2_CELL_KEY_CACHE_PREFIX =
      RedisKeyConstant.S2_CELL.concat(RedisKeyConstant.COLON);
  private final S2CellRepository s2CellRepository;
  private final CacheService cacheService;

  @Override
  @Transactional(readOnly = true)
  public S2CellListConfigQueryResponse getS2CellList() {
    log.info("Get S2CellList Configuration starting ...");
    List<S2CellEntity> s2Cells = new ArrayList<>();
    try {
      s2Cells = cacheService.getListValue(S2_CELL_KEY_CACHE_PREFIX, S2CellEntity.class);
    } catch (Exception e) {
      log.error("Failed to get S2Cell from cache", e);
    }
    if (Objects.isNull(s2Cells) || s2Cells.isEmpty()) {
      try {
        s2Cells = s2CellRepository.getAllS2Cell();
        cacheService.setValue(S2_CELL_KEY_CACHE_PREFIX, s2Cells);
      } catch (Exception e1) {
        log.error("Failed to get S2Cell from database", e1);
      }
    }
    return S2CellListConfigQueryResponse.builder().s2CellList(s2Cells).build();
  }

  @Override
  @Transactional(readOnly = true)
  public void fetchCacheS2Cell() {
    log.info("Fetch cache S2Cell starting ...");
    List<S2CellEntity> s2Cells = s2CellRepository.getAllS2Cell();
    cacheService.setValue(S2_CELL_KEY_CACHE_PREFIX, s2Cells);
    log.info("Fetch cache S2Cell with {} is completed", S2_CELL_KEY_CACHE_PREFIX);
  }
}

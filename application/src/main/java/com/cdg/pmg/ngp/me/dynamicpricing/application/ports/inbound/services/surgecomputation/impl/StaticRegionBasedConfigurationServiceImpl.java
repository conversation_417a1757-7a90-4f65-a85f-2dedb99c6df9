package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.impl;

import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.*;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.StaticRegionBasedConfigurationService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.ConfigurationVersionUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.surgecomputation.ModelRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.surgecomputation.StaticRegionBasedConfigurationRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.ModelEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticBasedConfigurationEffectiveCheckEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticBasedConfigurationVersionEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticRegionBasedConfigurationEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.BadRequestException;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.InternalServerException;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.NotFoundException;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Implementation of the StaticRegionBasedConfigurationService interface. This class provides the
 * implementation for managing static region-based configurations.
 */
@Slf4j
@ServiceComponent
@RequiredArgsConstructor
public class StaticRegionBasedConfigurationServiceImpl
    implements StaticRegionBasedConfigurationService {

  private final StaticRegionBasedConfigurationRepository configurationRepository;
  private final ModelRepository modelRepository;

  @Override
  public List<StaticBasedConfigurationVersionEntity> getStaticTimeBasedConfigurationVersions(
      Long modelId) {
    List<String> regionConfigNames =
        modelRepository
            .findById(modelId)
            .map(ModelEntity::getRegionBasedConfigNames)
            .orElseThrow(
                () ->
                    new NotFoundException(
                        NOT_FOUND_SURGE_COMPUTATION_MODEL.getMessage(),
                        NOT_FOUND_SURGE_COMPUTATION_MODEL.getErrorCode()));

    List<StaticBasedConfigurationVersionEntity> versions =
        configurationRepository.findAllVersionsByNames(regionConfigNames);

    ConfigurationVersionUtils.updateIsInUse(versions);
    return versions;
  }

  @Override
  public List<StaticRegionBasedConfigurationEntity> batchCreateStaticRegionBasedConfigurations(
      List<StaticRegionBasedConfigurationEntity> configurations) {
    checkVersionAndEffectivePeriod(configurations);

    try {
      return configurationRepository.batchCreateStaticRegionBasedConfigurations(configurations);
    } catch (Exception e) {
      String errorMessage = e.getMessage();
      if (errorMessage.contains("Overlapping effective date ranges")) {
        log.error(
            "[batchCreateStaticRegionBasedConfigurations] Error create region based configuration with overlapping effective date ranges: ",
            e);
        throw new BadRequestException(
            SURGE_COMPUTATION_MODEL_REGION_BASED_CONFIGURATION_OVERLAPPING_DATES_ERROR.getMessage(),
            SURGE_COMPUTATION_MODEL_REGION_BASED_CONFIGURATION_OVERLAPPING_DATES_ERROR
                .getErrorCode());
      }

      log.error(
          "[batchCreateStaticRegionBasedConfigurations] Error create region based configuration: ",
          e);
      throw new InternalServerException(
          SURGE_COMPUTATION_MODEL_REGION_BASED_CONFIGURATION_CREATE_ERROR.getMessage(),
          SURGE_COMPUTATION_MODEL_REGION_BASED_CONFIGURATION_CREATE_ERROR.getErrorCode());
    }
  }

  @Override
  public List<StaticRegionBasedConfigurationEntity> getStaticRegionBasedConfigurations(
      Long modelId, String version) {
    return modelRepository
        .findById(modelId)
        .map(
            model -> {
              List<String> regionConfigNames = model.getRegionBasedConfigNames();

              return regionConfigNames.isEmpty()
                  ? List.<StaticRegionBasedConfigurationEntity>of()
                  : configurationRepository.findAllByVersionAndNames(version, regionConfigNames);
            })
        .orElse(List.of());
  }

  @Override
  public StaticRegionBasedConfigurationEntity getStaticRegionBasedConfigurationById(Long id) {
    return configurationRepository.findById(id).orElse(null);
  }

  @Override
  public void updateStaticRegionBasedConfiguration(
      Long modelId, List<StaticRegionBasedConfigurationEntity> configurations) {
    checkVersionAndEffectivePeriod(configurations);

    List<String> regionConfigNames = checkAndGetRegionConfigNamesFromModel(modelId, configurations);

    try {
      Map<String, StaticRegionBasedConfigurationEntity> existingConfigMap =
          configurationRepository.findAllByNames(regionConfigNames).stream()
              .collect(
                  Collectors.toMap(
                      StaticRegionBasedConfigurationServiceImpl::buildMapKey,
                      config -> config,
                      (existing, replacement) -> existing));

      List<StaticRegionBasedConfigurationEntity> configsToCreate = new ArrayList<>();
      List<StaticRegionBasedConfigurationEntity> configsToUpdate = new ArrayList<>();
      for (StaticRegionBasedConfigurationEntity config : configurations) {
        String key = buildMapKey(config);
        StaticRegionBasedConfigurationEntity existingConfig = existingConfigMap.get(key);

        if (existingConfig != null) {
          config.setId(existingConfig.getId());
          configsToUpdate.add(config);
        } else {
          configsToCreate.add(config);
        }
      }

      configurationRepository.batchCreateOrUpdateStaticRegionBasedConfigurations(
          configsToCreate, configsToUpdate);
    } catch (Exception e) {
      String errorMessage = e.getMessage();
      if (errorMessage.contains("Overlapping effective date ranges")) {
        log.error(
            "[updateStaticRegionBasedConfiguration] Error update region based configuration with overlapping effective date ranges: ",
            e);
        throw new BadRequestException(
            SURGE_COMPUTATION_MODEL_REGION_BASED_CONFIGURATION_OVERLAPPING_DATES_ERROR.getMessage(),
            SURGE_COMPUTATION_MODEL_REGION_BASED_CONFIGURATION_OVERLAPPING_DATES_ERROR
                .getErrorCode());
      }

      log.error(
          "[updateStaticRegionBasedConfiguration] Error update region based configuration: ", e);
      throw new InternalServerException(
          SURGE_COMPUTATION_MODEL_REGION_BASED_CONFIGURATION_UPDATE_ERROR.getMessage(),
          SURGE_COMPUTATION_MODEL_REGION_BASED_CONFIGURATION_UPDATE_ERROR.getErrorCode());
    }
  }

  @Override
  public StaticBasedConfigurationEffectiveCheckEntity effectiveCheck(Long modelId) {
    return configurationRepository.effectiveCheck(modelId);
  }

  /**
   * Check if the configuration names in the request match the ones defined in the model, then
   * return the configuration names.
   *
   * @param modelId the modelId will use to find model from surge computation model
   * @param configurations the region based configurations from request
   * @return the configuration names
   * @throws NotFoundException if the surge computation model not found
   * @throws BadRequestException if the configuration names in the request are not match the ones
   *     defined in the model
   */
  private List<String> checkAndGetRegionConfigNamesFromModel(
      final Long modelId, final List<StaticRegionBasedConfigurationEntity> configurations) {
    List<String> regionConfigNames =
        modelRepository
            .findById(modelId)
            .map(ModelEntity::getRegionBasedConfigNames)
            .orElseThrow(
                () ->
                    new NotFoundException(
                        NOT_FOUND_SURGE_COMPUTATION_MODEL.getMessage(),
                        NOT_FOUND_SURGE_COMPUTATION_MODEL.getErrorCode()));

    Set<String> regionConfigNamesFromRequest =
        configurations.stream()
            .map(StaticRegionBasedConfigurationEntity::getName)
            .collect(Collectors.toSet());

    // Check if the configuration names in the request match the ones defined in the model
    if (!regionConfigNamesFromRequest.containsAll(regionConfigNames)) {
      throw new BadRequestException(
          SURGE_COMPUTATION_MODEL_REGION_BASED_CONFIGURATION_NAME_MISMATCH_ERROR.getMessage(),
          SURGE_COMPUTATION_MODEL_REGION_BASED_CONFIGURATION_NAME_MISMATCH_ERROR.getErrorCode());
    }
    return regionConfigNames;
  }

  private static String buildMapKey(final StaticRegionBasedConfigurationEntity config) {
    return config.getName()
        + ":"
        + config.getVersion()
        + ":"
        + config.getEffectiveFrom().toString();
  }

  /**
   * Check the version and effectiveFrom and effectiveTo are unique in the configuration list.
   *
   * @param configurations the list of configuration neet to be checked
   * @throws BadRequestException if the version and effectiveFrom and effectiveTo are not unique
   */
  private static void checkVersionAndEffectivePeriod(
      final List<StaticRegionBasedConfigurationEntity> configurations) {
    Set<String> checkSet =
        configurations.stream()
            .map(v -> v.getVersion() + ":" + v.getEffectiveFrom() + ":" + v.getEffectiveTo())
            .collect(Collectors.toSet());
    if (checkSet.size() != 1) {
      throw new BadRequestException(
          SURGE_COMPUTATION_MODEL_STATIC_BASED_CONFIGURATION_VERSION_NOT_SAME_ERROR.getMessage(),
          SURGE_COMPUTATION_MODEL_STATIC_BASED_CONFIGURATION_VERSION_NOT_SAME_ERROR.getErrorCode());
    }
  }
}

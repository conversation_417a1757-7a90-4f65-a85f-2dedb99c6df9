package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.mapper.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.H3RegionStatisticsEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.RegionDemandSupplyStatistic;
import java.util.Map;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

@Mapper
public interface H3RegionStatisticsMapper {

  @Named("mapWithRainfall")
  @Mapping(
      target = "averageIntensity",
      expression = "java(regionRainFallMap.getOrDefault(statistic.getRegionId(), 0.0))")
  H3RegionStatisticsEntity toStandardInputsEntity(
      RegionDemandSupplyStatistic statistic, Map<Long, Double> regionRainFallMap);
}

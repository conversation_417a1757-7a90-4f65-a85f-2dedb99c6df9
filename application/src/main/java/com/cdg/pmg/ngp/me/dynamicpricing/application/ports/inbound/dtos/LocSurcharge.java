package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LocSurcharge implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  private Integer locSurchargeId;
  private String chargeType;
  private String chargeBy;
  private Double amount;
  private String locationName;
}

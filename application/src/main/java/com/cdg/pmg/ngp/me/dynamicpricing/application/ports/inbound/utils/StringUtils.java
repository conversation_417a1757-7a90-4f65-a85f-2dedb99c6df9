package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class StringUtils {
  private StringUtils() {}

  /**
   * Removes leading and trailing double quotes from a given string. If the string starts and ends
   * with a double quote character ("), these quotes are removed. If the string is null, does not
   * start with a double quote, or does not end with a double quote, it is returned as is without
   * any modification.
   *
   * <p>This method is typically used to process strings where quotes are used to denote the
   * beginning and end of a value but are not part of the value itself.
   *
   * @param value The string from which to remove the quotes. It may be null.
   * @return The unquoted string. If the input string starts and ends with a double quote, these are
   *     removed. If the input string is null, does not start with a double quote, or does not end
   *     with a double quote, it is returned without changes.
   */
  public static String unquote(final String value) {
    if (value != null && value.startsWith("\"") && value.endsWith("\"")) {
      return value.substring(1, value.length() - 1);
    }
    return value;
  }

  /**
   * Checks if a given string is a valid JSON object format. This method determines if the string
   * represents a JSON object by checking if it starts with '{' and ends with '}'. The method also
   * considers leading and trailing whitespaces by trimming the string before performing these
   * checks.
   *
   * <p>Note that this method only checks for the basic structural pattern of a JSON object
   * (starting with '{' and ending with '}'), and does not validate the JSON content itself.
   *
   * @param value The string to be checked for JSON object format. It can be null.
   * @return {@code true} if the string is non-null, starts with '{' and ends with '}' after
   *     trimming whitespaces; {@code false} otherwise.
   */
  public static boolean isJson(final String value) {
    return value != null && value.trim().startsWith("{") && value.trim().endsWith("}");
  }
}

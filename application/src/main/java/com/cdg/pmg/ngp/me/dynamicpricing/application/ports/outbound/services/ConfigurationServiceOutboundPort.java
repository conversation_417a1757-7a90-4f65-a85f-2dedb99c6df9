package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.NewPricingModelConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.DynamicPricingSurgeConfig;
import java.util.List;
import java.util.Map;

public interface ConfigurationServiceOutboundPort {

  /**
   * Retrieves the New Pricing Model Config Entity from CMS.
   *
   * @return The New Pricing Model Config Entity obtained from the CMS.
   */
  List<NewPricingModelConfigEntity> getNewPricingModelConfigEntities(boolean validate);

  Map<String, String> loadBookARideConfigurations();

  int getTotalSizeNewPricingModelConfigEntities();

  DynamicPricingSurgeConfig getDynamicPricingSurgeConfig();
}

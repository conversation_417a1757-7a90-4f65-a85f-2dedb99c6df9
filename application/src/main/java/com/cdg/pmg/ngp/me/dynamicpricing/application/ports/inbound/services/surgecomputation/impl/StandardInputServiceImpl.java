package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.impl;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.H3RegionStatisticsEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.mapper.surgecomputation.H3RegionStatisticsMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.StandardInputService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.RegionDemandSupplyStatistic;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.FleetAnalyticService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.WeatherRetrievalService;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StandardInputEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ReflectionUtils;

@Slf4j
@ServiceComponent
@RequiredArgsConstructor
public class StandardInputServiceImpl implements StandardInputService {

  private final List<StandardInputEntity> standardInputs = new ArrayList<>();

  private final FleetAnalyticService fleetAnalyticService;
  private final WeatherRetrievalService weatherRetrievalService;
  private final H3RegionStatisticsMapper h3RegionStatisticsMapper;
  private final ObjectMapper objectMapper;

  @Override
  public List<StandardInputEntity> getStandardInputs() {
    return standardInputs;
  }

  @Override
  public Map<Long, Map<String, BigDecimal>> getStandardInputValueMap(List<String> bookingIds) {
    List<RegionDemandSupplyStatistic> demandSupplyStatistics =
        fleetAnalyticService.calculateRegionDemandSupplyStatistics(bookingIds);

    if (demandSupplyStatistics.isEmpty()) {
      log.warn("[getStandardInputValueMap] Demand supply statistics is empty");
      return Map.of();
    }

    Map<Long, Double> regionRainFallMap = weatherRetrievalService.getRainFallMap();

    return demandSupplyStatistics.stream()
        .map(v -> h3RegionStatisticsMapper.toStandardInputsEntity(v, regionRainFallMap))
        .collect(
            Collectors.toMap(
                H3RegionStatisticsEntity::getRegionId,
                this::toAttributeMap,
                (existing, replacement) -> replacement));
  }

  private Map<String, BigDecimal> toAttributeMap(H3RegionStatisticsEntity entity) {
    return objectMapper.convertValue(entity, new TypeReference<>() {});
  }

  @PostConstruct
  public void generateStandardInputs() {
    ReflectionUtils.doWithFields(
        H3RegionStatisticsEntity.class,
        field -> {
          JsonProperty propertyAnn = field.getAnnotation(JsonProperty.class);
          JsonPropertyDescription descriptionAnn =
              field.getAnnotation(JsonPropertyDescription.class);

          if (propertyAnn != null) {
            String name = propertyAnn.value().isEmpty() ? field.getName() : propertyAnn.value();
            String description = (descriptionAnn != null) ? descriptionAnn.value() : "";

            standardInputs.add(new StandardInputEntity(name, description));
          }
        });

    log.info("The current standard inputs: {}", standardInputs);
  }
}

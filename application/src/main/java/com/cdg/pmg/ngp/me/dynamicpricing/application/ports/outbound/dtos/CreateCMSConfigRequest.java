package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
public class CreateCMSConfigRequest extends CMSConfigRequest implements Serializable {
  @Serial private static final long serialVersionUID = 6964690958117534234L;

  private String service;
  private String key;
  private String description;
  private String createdBy;
}

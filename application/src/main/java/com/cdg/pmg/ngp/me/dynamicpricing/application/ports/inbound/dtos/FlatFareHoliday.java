package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.DateUtils;
import java.io.Serial;
import java.io.Serializable;
import java.text.ParseException;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FlatFareHoliday implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  private String date;

  public boolean isPublicHoliday(Date reqDate) throws ParseException {
    return DateUtils.toddMMyyyy(reqDate).equals(DateUtils.toddMMyyyy(this.date));
  }

  public boolean isPublicHoliday(String reqDateString) throws ParseException {
    return reqDateString.equals(DateUtils.toddMMyyyy(this.date));
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.DemandSupplyService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.DynamicSurgeService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.CommonUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.DpsProperties;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.CompanyHolidayRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.DynamicSurgeRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.DynpSurgeLogsRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.PricingRangeRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.CacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.DpsPropertiesService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.FleetAnalyticService;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.CommonConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.DynamicSurgesEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.utils.CollectionUtils;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

/**
 * Implementation of the {@link DemandSupplyService} interface for handling dynamic pricing
 * calculations based on real-time demand and supply data. This service integrates with various
 * outbound services, repositories, and caching mechanisms to compute and store surge pricing
 * configurations.
 *
 * <p>Key responsibilities of this class include:
 *
 * <ul>
 *   <li>Loading and caching dynamic surge configurations for pricing.
 *   <li>Calculating demand-supply-based surge values for both standard and NGP (Next Generation
 *       Pricing) flows.
 *   <li>Determining whether the current day is a company holiday to adjust surge calculations
 *       accordingly.
 *   <li>Interfacing with fleet analytics services to retrieve demand and supply statistics.
 *   <li>Storing and retrieving surge data from cache and backing repositories to ensure updated
 *       surge values are readily available.
 * </ul>
 *
 * <p>This class uses various injected dependencies to achieve its functionality:
 *
 * <ul>
 *   <li>{@link CacheService}: For caching surge configurations and related data.
 *   <li>{@link FleetAnalyticService}: For retrieving demand and supply statistics.
 *   <li>{@link CompanyHolidayRepository}: For determining holiday schedules impacting surge
 *       calculations.
 *   <li>{@link PricingRangeRepository}: For fetching dynamic pricing configurations.
 *   <li>{@link DynamicSurgeRepository}: For retrieving and updating surge configurations in
 *       persistent storage.
 *   <li>{@link DynpSurgeLogsRepository}: For logging changes to surge configurations.
 *   <li>{@link DpsPropertiesService}: For accessing configuration properties related to dynamic
 *       pricing.
 *   <li>{@link DynamicSurgeService}: For delegated calculation of demand-supply surges in NGP
 *       contexts.
 * </ul>
 *
 * <p>The class also leverages utility functions for date/time processing, collection handling, and
 * release version determination to adapt caching keys and logic as configurations evolve over
 * releases.
 */
@AllArgsConstructor
@ServiceComponent
@Slf4j
public class DemandSupplyServiceImpl implements DemandSupplyService {
  private CacheService cacheService;
  private FleetAnalyticService fleetAnalyticService;
  private CompanyHolidayRepository companyHolidayRepository;
  private PricingRangeRepository pricingRangeRepository;
  private DynamicSurgeRepository dynamicSurgeRepository;
  private DynpSurgeLogsRepository dynpSurgeLogsRepository;
  private DpsPropertiesService dpsPropertiesService;
  private DynamicSurgeService dynamicSurgeService;

  /**
   * Calculates and updates the demand-supply surge configurations by retrieving the latest dynamic
   * surge data from the repository and updating the cache. Although the main logic for
   * demand-supply calculations was previously implemented here, it now primarily refreshes the
   * cached surge data.
   *
   * <p>Steps involved:
   *
   * <ol>
   *   <li>Retrieve existing surge configurations from persistent storage.
   *   <li>Delete any outdated entries from the cache.
   *   <li>Populate the cache with the latest surge configurations.
   *   <li>Log completion of the process.
   * </ol>
   *
   * <p>This method ensures that subsequent requests for dynamic surge values can rely on the
   * updated cache for real-time calculations and lookups.
   */
  @Override
  public void calculateDemandSupplySurge() {
    log.info("Start calculating demand supply surge!");

    // update cache from dynp_surge
    final List<DynamicSurgesEntity> dynpSurgesFromCn3 = dynamicSurgeRepository.getDynpSurges();
    var message = String.format("[DYNP_SURGE] update dynpSurgesFromCn3 =%s", dynpSurgesFromCn3);
    log.info(message);

    cacheService.deleteByKey(CommonConstant.DYNP_SURGES_KEY_CACHE);
    cacheService.setListValue(CommonConstant.DYNP_SURGES_KEY_CACHE, dynpSurgesFromCn3);

    log.info("Cache DYNP_SURGES updated!");
    log.info("End of calculation of demand supply surge!");
  }

  /**
   * Retrieves the current list of dynamic surge configurations. The source of the data (cache or
   * persistent storage) depends on whether the configurations are found in cache. If the cache is
   * empty, it falls back to retrieving them from the repository.
   *
   * @return a {@link List} of {@link DynamicSurgesEntity} containing the current surge
   *     configurations.
   */
  @Override
  @Transactional(readOnly = true)
  public List<DynamicSurgesEntity> getDynpSurges() {
    final String dynpSurgeCacheKey = this.getDynpSurgesCacheKeyBaseOnReleaseVer();

    List<DynamicSurgesEntity> dynpSurges =
        cacheService.getListValue(dynpSurgeCacheKey, DynamicSurgesEntity.class);
    if (CollectionUtils.isEmpty(dynpSurges)) {
      log.info("Dynp surge from cache is empty!");
      return dynamicSurgeRepository.getDynpSurges();
    }
    return dynpSurges;
  }

  /**
   * Loads the dynamic surge configurations from the repository and stores them into the cache. If
   * the configurations are empty, it logs a warning and does not update the cache.
   */
  @Override
  @Transactional(readOnly = true)
  public void loadDynpSurgeConfigs() {
    final List<DynamicSurgesEntity> configs = dynamicSurgeRepository.getDynpSurges();
    if (CollectionUtils.isEmpty(configs)) {
      log.info("Can't load dynp surge configs. The dynp surge config is empty!");
      return;
    }
    cacheService.deleteByKey(CommonConstant.DYNP_SURGES_KEY_CACHE);
    cacheService.setListValue(CommonConstant.DYNP_SURGES_KEY_CACHE, configs);
    log.info("Loaded dynp surge configs to cache complete!");
  }

  /**
   * Loads the dynamic surge configurations from the repository and caches them. If no
   * configurations are available, it logs a warning and avoids updating the cache.
   */
  @Override
  @Transactional(readOnly = true)
  public void loadDynpSurgeNgpConfigs() {
    final List<DynamicSurgesEntity> configs = dynamicSurgeRepository.getNGPDynpSurges();
    if (CollectionUtils.isEmpty(configs)) {
      log.info("Can't load dynp surge NGP configs. The dynp surge config is empty!");
      return;
    }
    cacheService.deleteByKey(CommonConstant.DYNP_SURGES_NGP_KEY_CACHE);
    cacheService.setListValue(CommonConstant.DYNP_SURGES_NGP_KEY_CACHE, configs);
    log.info("Loaded dynp surge NGP configs to cache complete!");
  }

  /**
   * Delegates the calculation of NGP demand-supply surge values to the {@link DynamicSurgeService}.
   * This method acts as a wrapper to integrate the NGP surge calculations into the current
   * service's workflow.
   */
  @Override
  public void calculateDemandSupplySurgeNgp() {
    dynamicSurgeService.calculateDemandSupplySurge();
  }

  /**
   * Determines the appropriate cache key for retrieving dynamic surge configurations based on the
   * current release version. This allows for versioned caching strategies, ensuring that different
   * releases can maintain separate sets of surge data.
   *
   * @return a {@link String} representing the chosen cache key for dynamic surge configurations.
   */
  private String getDynpSurgesCacheKeyBaseOnReleaseVer() {
    final DpsProperties dpsProperties = dpsPropertiesService.getDpsProperties();
    final int releaseVer = CommonUtils.getReleaseVersion(dpsProperties);

    if (dpsProperties.isSurgeForceUsingR1()) {
      return CommonConstant.DYNP_SURGES_KEY_CACHE;
    }

    log.info("[Release Version] {}", releaseVer);
    return switch (releaseVer) {
      case CommonConstant.RELEASE_1 -> CommonConstant.DYNP_SURGES_KEY_CACHE;
      case CommonConstant.RELEASE_2 -> CommonConstant.DYNP_SURGES_NGP_KEY_CACHE;
      default -> CommonConstant.DYNP_SURGES_NGP_KEY_CACHE;
    };
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.impl;

import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.*;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.ModelService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.surgecomputation.ModelRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.surgecomputation.RegionModelDistributionRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.surgecomputation.StaticRegionBasedConfigurationRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.ModelEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.BadRequestException;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.InternalServerException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Implementation of the SurgeComputationModelService interface. This class provides the business
 * logic for managing surge computation models.
 */
@AllArgsConstructor
@ServiceComponent
@Slf4j
public class ModelServiceImpl implements ModelService {

  private final ModelRepository modelRepository;
  private final StaticRegionBasedConfigurationRepository staticRegionBasedConfigurationRepository;
  private final RegionModelDistributionRepository regionModelDistributionRepository;

  @Override
  public ModelEntity createSurgeComputationModel(ModelEntity request, String userId) {
    Instant now = Instant.now();
    request.setCreatedBy(userId);
    request.setCreatedDate(now);
    request.setUpdatedBy(userId);
    request.setUpdatedDate(now);

    try {
      return createSurgeComputationModel(request);
    } catch (Exception e) {
      String errorMessage = e.getMessage();
      if (errorMessage.contains("unique_model_name")) {
        log.error(
            "[createSurgeComputationModel] Error create surge computation model with duplicate name: {}",
            e.getMessage());
        throw new BadRequestException(
            SURGE_COMPUTATION_MODEL_DUPLICATE_NAME_ERROR.getMessage(),
            SURGE_COMPUTATION_MODEL_DUPLICATE_NAME_ERROR.getErrorCode());
      }

      log.error("[createSurgeComputationModel] Error create surge computation model: ", e);
      throw new InternalServerException(
          SURGE_COMPUTATION_MODEL_CREATE_ERROR.getMessage(),
          SURGE_COMPUTATION_MODEL_CREATE_ERROR.getErrorCode());
    }
  }

  @Override
  public ModelEntity createSurgeComputationModel(ModelEntity request) {
    return modelRepository.save(request);
  }

  @Override
  public List<ModelEntity> getAllSurgeComputationModels() {
    return modelRepository.findAll();
  }

  @Override
  public ModelEntity getSurgeComputationModelById(Long id) {
    return modelRepository.findById(id).orElse(null);
  }

  @Override
  public ModelEntity updateSurgeComputationModel(Long id, ModelEntity request, String userId) {
    // First fetch the existing entity
    ModelEntity existingEntity = getSurgeComputationModelById(id);
    if (existingEntity == null) {
      return null;
    }

    // Preserve audit fields
    request.setId(id);
    request.setCreatedBy(existingEntity.getCreatedBy());
    request.setCreatedDate(existingEntity.getCreatedDate());
    request.setUpdatedBy(userId);
    request.setUpdatedDate(Instant.now());

    try {
      return updateSurgeComputationModel(request);
    } catch (Exception e) {
      String errorMessage = e.getMessage();
      if (errorMessage.contains("unique_model_name")) {
        log.error(
            "[updateSurgeComputationModel] Error update surge computation model with duplicate name: {}",
            e.getMessage());
        throw new BadRequestException(
            SURGE_COMPUTATION_MODEL_DUPLICATE_NAME_ERROR.getMessage(),
            SURGE_COMPUTATION_MODEL_DUPLICATE_NAME_ERROR.getErrorCode());
      }

      log.error("[updateSurgeComputationModel] Error update surge computation model: ", e);
      throw new InternalServerException(
          SURGE_COMPUTATION_MODEL_UPDATE_ERROR.getMessage(),
          SURGE_COMPUTATION_MODEL_UPDATE_ERROR.getErrorCode());
    }
  }

  @Override
  public ModelEntity updateSurgeComputationModel(ModelEntity request) {
    Optional<ModelEntity> existingModel = modelRepository.findById(request.getId());

    if (existingModel.isEmpty()) {
      return null;
    }

    return modelRepository.save(request);
  }

  @Override
  public boolean deleteSurgeComputationModel(Long id, String userId) {
    Optional<ModelEntity> existingModel = modelRepository.findById(id);

    if (existingModel.isEmpty()) {
      return false;
    }

    // Check the model if used by distribution
    checkUsedOrNot(id);

    Instant now = Instant.now();
    ModelEntity model = existingModel.get();
    model.setUpdatedBy(userId);
    model.setUpdatedDate(now);

    deleteRelatedRegionBasedConfigurations(model, userId, now);
    modelRepository.save(model);
    modelRepository.deleteById(id);
    return true;
  }

  @Override
  public String getSurgeComputationModelName(final Long id) {
    return modelRepository.findModelName(id);
  }

  private void checkUsedOrNot(final Long modelId) {
    long distributionUsedCount = regionModelDistributionRepository.countByModelId(modelId);
    if (distributionUsedCount > 0) {
      log.warn(
          "[deleteSurgeComputationModel] Surge model is used by distribution, modelId: {}",
          modelId);
      throw new BadRequestException(
          SURGE_COMPUTATION_MODEL_DELETE_ERROR.getMessage(),
          SURGE_COMPUTATION_MODEL_DELETE_ERROR.getErrorCode());
    }
  }

  private void deleteRelatedRegionBasedConfigurations(
      final ModelEntity model, final String userId, final Instant now) {
    List<String> regionConfigNames = model.getRegionBasedConfigNames();

    if (regionConfigNames.isEmpty()) {
      return;
    }

    List<String> configNamesUsedByOtherModels =
        modelRepository.findConfigNamesUsedByOtherModels(regionConfigNames, model.getId());

    ArrayList<String> namesWillBeFilter = new ArrayList<>(regionConfigNames);
    namesWillBeFilter.removeAll(configNamesUsedByOtherModels);

    // All configs are used by other models, no need to delete
    if (namesWillBeFilter.isEmpty()) {
      return;
    }

    // Do update to trigger the audit fields update
    staticRegionBasedConfigurationRepository.updateAuditFields(namesWillBeFilter, userId, now);
    staticRegionBasedConfigurationRepository.deleteByNames(namesWillBeFilter);
  }
}

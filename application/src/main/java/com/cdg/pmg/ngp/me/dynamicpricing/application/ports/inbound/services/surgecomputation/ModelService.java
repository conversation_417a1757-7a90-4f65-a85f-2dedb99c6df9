package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.ModelEntity;
import java.util.List;

/**
 * Service interface for managing surge computation models. This interface defines the operations
 * that can be performed on surge computation models.
 */
public interface ModelService {

  /**
   * Creates a new surge computation model with audit information.
   *
   * @param request The request containing the model details
   * @param userId The ID of the user creating the model
   * @return The created surge computation model
   */
  ModelEntity createSurgeComputationModel(ModelEntity request, String userId);

  /**
   * Creates a new surge computation model.
   *
   * @param request The request containing the model details
   * @return The created surge computation model
   */
  ModelEntity createSurgeComputationModel(ModelEntity request);

  /**
   * Retrieves all surge computation models.
   *
   * @return A list of all surge computation models
   */
  List<ModelEntity> getAllSurgeComputationModels();

  /**
   * Retrieves a surge computation model by its ID.
   *
   * @param id The ID of the model to retrieve
   * @return The surge computation model, or null if not found
   */
  ModelEntity getSurgeComputationModelById(Long id);

  /**
   * Updates an existing surge computation model with preserved audit information.
   *
   * @param id The ID of the model to update
   * @param request The request containing the updated model details
   * @param userId The ID of the user updating the model
   * @return The updated surge computation model, or null if not found
   */
  ModelEntity updateSurgeComputationModel(Long id, ModelEntity request, String userId);

  /**
   * Updates an existing surge computation model.
   *
   * @param request The request containing the updated model details
   * @return The updated surge computation model, or null if not found
   */
  ModelEntity updateSurgeComputationModel(ModelEntity request);

  /**
   * Deletes a surge computation model, will also delete all related region based configurations.
   *
   * @param id The ID of the model to delete
   * @param userId The user id who doing delete
   * @return true if the model was deleted, false if it was not found
   */
  boolean deleteSurgeComputationModel(Long id, String userId);

  /**
   * Only retrieve the model name by id.
   *
   * @param id The ID of the model
   * @return The model name for the specific model id
   */
  String getSurgeComputationModelName(Long id);
}

package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PlatformFeeResponse implements Serializable {
  @Serial private static final long serialVersionUID = -7707491769528834690L;

  private Long id;
  private String bookingChannel;
  private Integer vehicleGroupId;
  private String productId;
  private String platformFeeApplicability;
  private String remarks;
  private Double platformFeeThresholdLimit;
  private Double lowerPlatformFee;
  private Double upperPlatformFee;
  private String effectiveFrom;
  private String effectiveTo;
  private Boolean deleted;
  private LocalDateTime createdDate;
  private String createdBy;
  private LocalDateTime updatedDate;
  private String updatedBy;
}

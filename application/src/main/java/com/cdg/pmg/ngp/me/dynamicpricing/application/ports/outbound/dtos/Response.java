package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.function.Function;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * The type Response.
 *
 * @param <T> the type parameter
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Response<T> {
  @JsonInclude(JsonInclude.Include.NON_NULL)
  private T data;

  @JsonInclude(JsonInclude.Include.NON_NULL)
  private OffsetDateTime timestamp;

  @JsonInclude(JsonInclude.Include.NON_NULL)
  private String traceId;

  @JsonInclude(JsonInclude.Include.NON_NULL)
  private Error error;

  private static DateTimeFormatter fmDateTime = DateTimeFormatter.ISO_OFFSET_DATE_TIME;

  /**
   * Of response.
   *
   * @param <T> the type parameter
   * @param data the data
   * @return the response
   */
  public static <T> Response<T> of(final T data) {
    return Response.<T>builder().data(data).timestamp(getCurrentTime()).build();
  }

  /**
   * Fail response.
   *
   * @param <T> the type parameter
   * @param message the message
   * @param code the code
   * @return the response
   */
  public static <T> Response<T> fail(String message, Long code) {
    return Response.<T>builder()
        .timestamp(getCurrentTime())
        .error(Error.builder().message(message).code(code).build())
        .build();
  }

  /**
   * Fail response.
   *
   * @param <T> the type parameter
   * @param message the message
   * @param code the code
   * @param errors the errors
   * @return the response
   */
  public static <T> Response<T> fail(String message, Long code, List<Error.ErrorDetail> errors) {
    return Response.<T>builder()
        .timestamp(getCurrentTime())
        .error(Error.builder().message(message).code(code).errors(errors).build())
        .build();
  }

  /**
   * Fail response.
   *
   * @param <T> the type parameter
   * @param data the data
   * @param message the message
   * @param code the code
   * @return the response
   */
  public static <T> Response<T> fail(final T data, String message, Long code) {
    return Response.<T>builder()
        .data(data)
        .timestamp(getCurrentTime())
        .error(Error.builder().message(message).code(code).build())
        .build();
  }

  /**
   * Fail response.
   *
   * @param <T> the type parameter
   * @param data the data
   * @param message the message
   * @param code the code
   * @param errors the errors
   * @return the response
   */
  public static <T> Response<T> fail(
      final T data, String message, Long code, List<Error.ErrorDetail> errors) {
    return Response.<T>builder()
        .data(data)
        .timestamp(getCurrentTime())
        .error(Error.builder().message(message).code(code).errors(errors).build())
        .build();
  }

  /**
   * Map response.
   *
   * @param <E> the type parameter
   * @param mapper the mapper
   * @return the response
   */
  public <E> Response<E> map(final Function<T, E> mapper) {
    var newContent = mapper.apply(data);
    return of(newContent);
  }

  private static OffsetDateTime getCurrentTime() {
    var currentDate = fmDateTime.format(ZonedDateTime.now().truncatedTo(ChronoUnit.SECONDS));
    return OffsetDateTime.parse(currentDate);
  }
}

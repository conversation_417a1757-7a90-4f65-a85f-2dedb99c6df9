package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.DynpConfigCacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.FlatFareBookingFeeConfigService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.FlatFareBookingFeeRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FlatFareBookingFeeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.utils.CollectionUtils;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@AllArgsConstructor
@ServiceComponent
@Slf4j
public class FlatFareBookingFeeConfigServiceImpl implements FlatFareBookingFeeConfigService {
  private final FlatFareBookingFeeRepository flatFareBookingFeeRepository;
  private final DynpConfigCacheService dynpConfigCacheService;

  @Override
  public void loadFlatFareBookingFeeConfig() {
    final List<FlatFareBookingFeeConfig> configs =
        flatFareBookingFeeRepository.getFlatFareBookingFeeConfigs();
    if (CollectionUtils.isEmpty(configs)) {
      log.info(
          "Can't load flat fare booking fee configs. The flat fare booking fee config is empty!");
      return;
    }
    final String keyCache =
        RedisKeyConstant.DYNAMIC_PRICING
            .concat(RedisKeyConstant.COLON)
            .concat(RedisKeyConstant.FLATFARE_BOOKING_FEE);
    dynpConfigCacheService.addListConfigsToCache(keyCache, configs);
    log.info("Loaded flat fare booking fee configs to cache complete!");
  }
}

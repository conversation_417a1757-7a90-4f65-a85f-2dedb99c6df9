package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.CacheUtilities;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.*;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.LocalCacheConstant;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/** The interface Address service */
public interface AddressService {

  /**
   * Get generated route
   *
   * @param generateRouteRequest request
   * @return route
   */
  GenerateRouteResponse getRoute(GenerateRouteRequest generateRouteRequest);

  /**
   * Get all effective h3 regions with version
   *
   * @return the effective h3 regions
   */
  Optional<EffectiveH3RegionsResponse> getEffectiveH3RegionWithVersion();

  /**
   * Get all effective h3 regions
   *
   * @return the effective h3 regions
   */
  default List<H3Region> getEffectiveH3Regions() {
    return getEffectiveH3RegionWithVersion()
        .map(EffectiveH3RegionsResponse::getRegions)
        .orElseGet(List::of);
  }

  /**
   * Get all effective h3 regions from local cache first, if expired, will get from address service
   *
   * <p>The local cache key is {@link LocalCacheConstant#CACHE_KEY_EFFECTIVE_H3_REGIONS}
   *
   * <p>The TTL is {@link LocalCacheConstant#EFFECTIVE_H3_REGIONS_CACHE_TTl_SECS}
   *
   * @return the effective h3 regions
   */
  default List<H3Region> getEffectiveH3RegionsFromCache() {
    return CacheUtilities.getOrLoad(
        LocalCacheConstant.CACHE_KEY_EFFECTIVE_H3_REGIONS,
        this::getEffectiveH3Regions,
        LocalCacheConstant.EFFECTIVE_H3_REGIONS_CACHE_TTl_SECS);
  }

  /**
   * Resolve h3 region by geographical coordinates
   *
   * @param request the geographical coordinates
   * @return the h3 regions response
   */
  List<H3RegionComputeResponse> resolveH3Region(List<H3RegionComputeRequest> request);

  /**
   * Resolve h3 region by geographical coordinates, convert to map, the key is lat:lng
   *
   * @param request the geographical coordinates
   * @return the h3 regions map
   */
  default Map<String, H3RegionComputeResponse> resolveH3RegionToMap(
      List<H3RegionComputeRequest> request) {
    return resolveH3Region(request).stream()
        .collect(
            Collectors.toMap(
                H3RegionComputeResponse::getLatLngMapKey,
                function -> function,
                (oldValue, newValue) -> newValue));
  }

  /**
   * Resolve h3 region by specified geographical coordinate
   *
   * @param request the specified geographical coordinate
   * @return the region id
   */
  default Optional<H3RegionComputeResponse> resolveH3Region(H3RegionComputeRequest request) {
    Map<String, H3RegionComputeResponse> regionMap = resolveH3RegionToMap(List.of(request));
    if (regionMap.isEmpty()) {
      return Optional.empty();
    }

    return Optional.ofNullable(regionMap.get(request.getLatLngMapKey()));
  }
}

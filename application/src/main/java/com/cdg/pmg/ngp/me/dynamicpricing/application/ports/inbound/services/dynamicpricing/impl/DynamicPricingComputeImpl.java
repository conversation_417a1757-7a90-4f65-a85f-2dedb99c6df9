package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.dynamicpricing.impl;

import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.CALC_EVENT_SURCHARGE_DYNP_ERROR;
import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.SETTING_MAXIMUM_CAP_ERROR;
import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.SETTING_MINIMUM_CAP_ERROR;
import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.SETTING_MIN_MAX_CAP_FOR_TOTAL_FARE_ERROR;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.DynamicPricingConfigSet;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.EventSurgeAddressConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareHoliday;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareVO;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.dynamicpricing.DynamicPricingCompute;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.CommonUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.DateUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.SurchargeUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.CommonConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FareTypeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.DomainException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ServiceComponent
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
public class DynamicPricingComputeImpl implements DynamicPricingCompute {
  protected DynamicPricingConfigSet configSet;

  @Override
  public void calBookingFee(final FlatFareVO flatFareVO) {
    var bookingFeeValue =
        calculateDynamicPricingConfigValue(
            flatFareVO,
            configSet.getBookingFee(),
            RedisKeyConstant.DYNP_BOOKING_FEE,
            RedisKeyConstant.DYNP_BOOKING_FEE + RedisKeyConstant.COLON,
            CommonConstant.DEFAULT_MULTIPLIER,
            configSet.getHolidayList());
    log.info("[DYNP_CALC] calBookingFee = {}", bookingFeeValue);
    flatFareVO.setBookingFee(bookingFeeValue);
  }

  @Override
  public void calFlagDown(final FlatFareVO flatFareVO) {
    var flagDownValue =
        calculateDynamicPricingConfigValue(
            flatFareVO,
            configSet.getFlagDownConfigList(),
            RedisKeyConstant.DYNP_FLAG_DOWN_RATE,
            RedisKeyConstant.DYNP_FLAG_DOWN_RATE + RedisKeyConstant.COLON,
            CommonConstant.DEFAULT_MULTIPLIER,
            configSet.getHolidayList());
    flatFareVO.setFlagDown(flagDownValue);
  }

  @Override
  public void calTier1Fare(final FlatFareVO flatFareVO) {
    double tier1EndDistValue =
        calculateDynamicPricingConfigValue(
            flatFareVO,
            configSet.getTier1EndDestConfigList(),
            RedisKeyConstant.DYNP_TIER_1_END_DIST,
            RedisKeyConstant.DYNP_TIER_1_END_DIST + RedisKeyConstant.COLON,
            CommonConstant.DEFAULT_MULTIPLIER,
            configSet.getHolidayList());
    double tier1EndDistInKm = tier1EndDistValue / CommonConstant.ONE_KILOMETER;
    double tier1PriceMultipler =
        calculateDynamicPricingConfigValue(
            flatFareVO,
            configSet.getTier1PriceMultiplierConfigList(),
            RedisKeyConstant.DYNP_TIER_1_PRICE_MULTIPLIER,
            RedisKeyConstant.DYNP_TIER_1_PRICE_MULTIPLIER + RedisKeyConstant.COLON,
            CommonConstant.DEFAULT_MULTIPLIER,
            configSet.getHolidayList());
    double routingDistanceInKm =
        CommonUtils.roundMeterToKm(flatFareVO.getFlatFareRequest().getRoutingDistance());
    double tier1Fare = tier1PriceMultipler * Math.min(routingDistanceInKm, tier1EndDistInKm);
    flatFareVO.setTier1Fare(tier1Fare);
  }

  @Override
  public void calTier2Fare(final FlatFareVO flatFareVO) {
    double tier2StartDistValue =
        calculateDynamicPricingConfigValue(
            flatFareVO,
            configSet.getTier2StartDestConfigList(),
            RedisKeyConstant.DYNP_TIER_2_START_DIST,
            RedisKeyConstant.DYNP_TIER_2_START_DIST + RedisKeyConstant.COLON,
            CommonConstant.DEFAULT_MULTIPLIER,
            configSet.getHolidayList());
    double tier2StartDistValueInKm = tier2StartDistValue / CommonConstant.ONE_KILOMETER;
    double tier2PriceMultipler =
        calculateDynamicPricingConfigValue(
            flatFareVO,
            configSet.getTier2PriceMultiplierConfigList(),
            RedisKeyConstant.DYNP_TIER_2_PRICE_MULTIPLIER,
            RedisKeyConstant.DYNP_TIER_2_PRICE_MULTIPLIER + RedisKeyConstant.COLON,
            CommonConstant.DEFAULT_MULTIPLIER,
            configSet.getHolidayList());
    double routingDistanceInKm =
        CommonUtils.roundMeterToKm(flatFareVO.getFlatFareRequest().getRoutingDistance());

    double tier2Fare =
        tier2PriceMultipler * Math.max(0, routingDistanceInKm - tier2StartDistValueInKm);
    flatFareVO.setTier2Fare(tier2Fare);
  }

  @Override
  public void calWaitTimeFare(final FlatFareVO flatFareVO) {
    double waitTimeValue =
        calculateDynamicPricingConfigValue(
            flatFareVO,
            configSet.getDurationRateConfigList(),
            RedisKeyConstant.DYNP_DURATION_RATE,
            RedisKeyConstant.DYNP_DURATION_RATE + RedisKeyConstant.COLON,
            (double) flatFareVO.getFlatFareRequest().getEtt() / CommonConstant.ONE_MINUTE,
            configSet.getHolidayList());
    log.info("[DYNP_CALC] calWaitTimeFare = {}", waitTimeValue);
    flatFareVO.setWaitTimeFare(waitTimeValue);
  }

  @Override
  public void calHourlySurcharge(final FlatFareVO flatFareVO) {
    double hourlySurchargeMultiplier =
        flatFareVO.getFlagDown()
            + flatFareVO.getTier1Fare()
            + flatFareVO.getTier2Fare()
            + flatFareVO.getWaitTimeFare();
    double hourlySurchargeValue =
        calculateDynamicPricingConfigValue(
            flatFareVO,
            configSet.getHourlySurcharge(),
            RedisKeyConstant.DYNP_PEAK_MIDNIGHT_HOUR_RATE,
            RedisKeyConstant.DYNP_PEAK_MIDNIGHT_HOUR_RATE + RedisKeyConstant.COLON,
            hourlySurchargeMultiplier,
            configSet.getHolidayList());
    log.info("[DYNP_CALC] calHourlySurcharge = {}", hourlySurchargeValue);
    flatFareVO.setHourlySurcharge(hourlySurchargeValue);
  }

  @Override
  public void updateAfterApplyingSurgeFare(final FlatFareVO flatFareVO) {
    log.info("Start validate desurge.");
    double beforeSurge = flatFareVO.getDpBaseFareForSurge();

    // Get config from cache
    var dynpSurgeBuffer =
        calculateDynamicPricingConfigValue(
            flatFareVO,
            configSet.getSurgeBufferConfigList(),
            RedisKeyConstant.DYNP_SURGE_BUFFER,
            RedisKeyConstant.DYNP_SURGE_BUFFER + RedisKeyConstant.COLON,
            flatFareVO.getDpFareAfterSurge(),
            configSet.getHolidayList());
    var maxDesurgeAmount =
        calculateDynamicPricingConfigValue(
            flatFareVO,
            configSet.getDesurgeMaxCapConfigList(),
            RedisKeyConstant.DYNP_DESURGE_MAX_CAP,
            RedisKeyConstant.DYNP_DESURGE_MAX_CAP + RedisKeyConstant.COLON,
            CommonConstant.DEFAULT_MULTIPLIER,
            configSet.getHolidayList());
    var minSurgeAmount =
        calculateDynamicPricingConfigValue(
            flatFareVO,
            configSet.getMinSurgeAmountConfigList(),
            RedisKeyConstant.DYNP_MIN_SURGE_AMOUNT,
            RedisKeyConstant.DYNP_MIN_SURGE_AMOUNT + RedisKeyConstant.COLON,
            CommonConstant.DEFAULT_MULTIPLIER,
            configSet.getHolidayList());
    double price = flatFareVO.getDpFareAfterSurge() + dynpSurgeBuffer;

    // Check max desurge
    if (flatFareVO.getMeteredBaseFare() - price > maxDesurgeAmount) {
      price = flatFareVO.getMeteredBaseFare() - maxDesurgeAmount;
    }

    // Adding event surcharge to dynamic price
    if (price
            - (beforeSurge
                + flatFareVO.getTotalLocSurCharge()
                + flatFareVO.getTotalEventSurCharge())
        < minSurgeAmount) {
      price =
          beforeSurge
              + minSurgeAmount
              + flatFareVO.getTotalLocSurCharge()
              + flatFareVO.getTotalEventSurCharge();
      flatFareVO.setDpAplydSurgeAmt(minSurgeAmount);
    } else {
      flatFareVO.setDpAplydSurgeAmt(
          price
              - (beforeSurge
                  + flatFareVO.getTotalLocSurCharge()
                  + flatFareVO.getTotalEventSurCharge()));
    }

    price += flatFareVO.getMultiDestSurcharge();

    // Set final value
    flatFareVO.setDpFinalFare(price);
    price = CommonUtils.roundTo(price, 1);
    flatFareVO.setTotalFare(CommonUtils.roundToTwoBD(price));
    log.info("End validate desurge.");
  }

  @Override
  public void setDynpMinMaxForTotalFare(final FlatFareVO flatFareVO) {
    log.info("Start set min max for total fare.");

    if (flatFareVO.getTotalFare() != null) {
      setDynpMinCapForTotalFare(flatFareVO);
      setDynpMaxCapForTotalFare(flatFareVO);
      log.info("End set min max for total fare.");
    } else {
      log.error(
          "Exception occurred while setting minimum/maximum cap for dynamic pricing total fare : {}",
          flatFareVO);
      throw new DomainException(
          SETTING_MIN_MAX_CAP_FOR_TOTAL_FARE_ERROR.getMessage(),
          SETTING_MIN_MAX_CAP_FOR_TOTAL_FARE_ERROR.getErrorCode());
    }
  }

  @Override
  public void calMultiDestSurcharge(FlatFareVO flatfareVO) {
    if (Objects.nonNull(flatfareVO.getFlatFareRequest().getIntermediateAddrRef())) {
      final String multiDestSurchargeConfig = configSet.getMultiStopSurcharge();
      try {
        double multiDestSurcharge = Double.parseDouble(multiDestSurchargeConfig);
        flatfareVO.setMultiDestSurcharge(multiDestSurcharge);
      } catch (NumberFormatException | NullPointerException e) {
        log.error("Error while parsing multi stop surcharge config : {}", multiDestSurchargeConfig);
        flatfareVO.setMultiDestSurcharge(FlatfareConstants.PRICE_DEFAULT);
      }
    }
  }

  @Override
  public void calLocationSurcharge(FlatFareVO flatFareVO) {
    SurchargeUtils.calLocSurchargesForStandard(
        flatFareVO, configSet.getLocationSurchargeConfigList());
  }

  @Override
  public void calEventSurcharge(FlatFareVO flatFareVO) {
    log.info(
        "Start calEventSurgeAddrCharge with vehTypeId={}",
        flatFareVO.getFlatFareRequest().getVehTypeId());
    try {
      final double totalFareBeforeSurge =
          flatFareVO.getFlagDown()
              + flatFareVO.getTier1Fare()
              + flatFareVO.getTier2Fare()
              + flatFareVO.getWaitTimeFare()
              + flatFareVO.getHourlySurcharge()
              + flatFareVO.getBookingFee()
              + flatFareVO.getMultiDestSurcharge()
              + flatFareVO.getTotalLocSurCharge();

      final Date reqDate = flatFareVO.getFlatFareRequest().getRequestDate();
      final String pickUpRef = flatFareVO.getFlatFareRequest().getOriginAddressRef();
      final String intermediateRef = flatFareVO.getFlatFareRequest().getIntermediateAddrRef();
      final String dropOffRef = flatFareVO.getFlatFareRequest().getDestAddressRef();

      List<EventSurgeAddressConfig> eventSurgeZoneConfigs =
          configSet.getEventSurgeAddressConfigList();
      eventSurgeZoneConfigs =
          eventSurgeZoneConfigs.stream()
              .filter(
                  configItem ->
                      SurchargeUtils.isEventConfigMatchingDayAndMonthAndTimeEffective(
                          configItem, reqDate))
              .toList();

      double eventSurgeAddrCharge = 0.0d;

      for (EventSurgeAddressConfig configItem : eventSurgeZoneConfigs) {
        if (SurchargeUtils.isAddressRefMatchingWithConfigUsingFixAmountAndChangeByPickUp(
                pickUpRef, configItem)
            || SurchargeUtils.isAddressRefMatchingWithConfigUsingFixAmountAndChangeByDestination(
                dropOffRef, configItem)
            || SurchargeUtils.isAddressRefMatchingWithConfigUsingFixAmountAndChangeByDestination(
                intermediateRef, configItem)) {
          eventSurgeAddrCharge += configItem.getChargeVal();
        } else if (SurchargeUtils.isAddressRefMatchingWithConfigUsingPercentageAndChangeByPickUp(
                pickUpRef, configItem)
            || SurchargeUtils.isAddressRefMatchingWithConfigUsingPercentageAndChangeByDestination(
                dropOffRef, configItem)
            || SurchargeUtils.isAddressRefMatchingWithConfigUsingPercentageAndChangeByDestination(
                intermediateRef, configItem)) {
          eventSurgeAddrCharge += totalFareBeforeSurge * configItem.getChargeVal();
        }
      }

      if (eventSurgeAddrCharge != 0) {
        eventSurgeAddrCharge = CommonUtils.roundToTwo(eventSurgeAddrCharge);
        flatFareVO.getEventSurgeCharge().add(eventSurgeAddrCharge);
      }

    } catch (Exception e) {
      log.error(
          "Exception occurred while calculating event surge address charge : {}", e.getMessage());
      throw new DomainException(
          CALC_EVENT_SURCHARGE_DYNP_ERROR.getMessage(),
          CALC_EVENT_SURCHARGE_DYNP_ERROR.getErrorCode());
    }
  }

  private void setDynpMinCapForTotalFare(final FlatFareVO flatFareVO) {
    try {
      double minCap =
          calculateDynamicPricingConfigValue(
              flatFareVO,
              configSet.getMinCapConfigList(),
              RedisKeyConstant.DYNP_MIN_CAP,
              RedisKeyConstant.DYNP_MIN_CAP + RedisKeyConstant.COLON,
              CommonConstant.DEFAULT_MULTIPLIER,
              configSet.getHolidayList());
      if (flatFareVO.getTotalFare().doubleValue() < minCap) {
        flatFareVO.setTotalFare(CommonUtils.roundToTwoBD(minCap));
        flatFareVO.setDpFinalFare(minCap);
      }
    } catch (Exception e) {
      log.error(
          "Exception occurred while setting Minimum cap for dynamic pricing total fare : {}",
          flatFareVO.getTotalFare());
      throw new DomainException(
          SETTING_MINIMUM_CAP_ERROR.getMessage(), SETTING_MINIMUM_CAP_ERROR.getErrorCode());
    }
  }

  private void setDynpMaxCapForTotalFare(final FlatFareVO flatFareVO) {
    try {
      double maxCap =
          calculateDynamicPricingConfigValue(
              flatFareVO,
              configSet.getMaxCapConfigList(),
              RedisKeyConstant.DYNP_MAX_CAP,
              RedisKeyConstant.DYNP_MAX_CAP + RedisKeyConstant.COLON,
              CommonConstant.DEFAULT_MULTIPLIER,
              configSet.getHolidayList());
      if (flatFareVO.getTotalFare().doubleValue() > maxCap) {
        flatFareVO.setTotalFare(CommonUtils.roundToTwoBD(maxCap));
        flatFareVO.setDpFinalFare(maxCap);
      }
    } catch (Exception e) {
      log.error(
          "Exception occurred while setting Maximum cap for dynamic pricing total fare : {}",
          flatFareVO.getTotalFare());
      throw new DomainException(
          SETTING_MAXIMUM_CAP_ERROR.getMessage(), SETTING_MAXIMUM_CAP_ERROR.getErrorCode());
    }
  }

  /**
   * Gets value of config by formula
   *
   * @param flatFareVO object FlatFareVo used to calculate
   * @param configMap map of config lists
   * @param confName config name
   * @param prefix prefix of config
   * @param multiplier multiplier for formula
   * @return value of config after being calculated
   */
  double calculateDynamicPricingConfigValue(
      FlatFareVO flatFareVO,
      Map<String, List<FareTypeConfig>> configMap,
      String confName,
      String prefix,
      Double multiplier,
      List<FlatFareHoliday> flatFareHolidays) {
    // Get request date
    Date reqDate = flatFareVO.getFlatFareRequest().getRequestDate();
    String dayInWeekReq = DateUtils.toDayOfWeekShortUpperCase(reqDate);
    Stream<FareTypeConfig> stream = Stream.empty();
    // Check ìf request date is holiday date
    if (DateUtils.isHolidaySingTime(reqDate, flatFareHolidays)) {
      // Add config stream to stream (holiday, requested day, all days)
      stream = addValuesToStream(stream, configMap, prefix, "HOL");
      stream = addValuesToStream(stream, configMap, prefix, dayInWeekReq);
      stream = addValuesToStream(stream, configMap, prefix, "ALL");
    } else {
      // Add config stream to stream (requested day, all days)
      stream = addValuesToStream(stream, configMap, prefix, dayInWeekReq);
      stream = addValuesToStream(stream, configMap, prefix, "ALL");
    }
    // Filter for valid config
    FareTypeConfig fareTypeConfig =
        stream
            .filter(config -> isValidConfig(config, confName, flatFareHolidays))
            .findFirst()
            .orElse(null);

    log.info("[DYNP_CALC] Get {} config after filter: {}", confName, fareTypeConfig);

    if (fareTypeConfig != null) {
      // Check vehGrp in config and request vehGrp then set value to use for formula
      getVehGrpMapping(fareTypeConfig, flatFareVO.getFlatFareRequest().getVehTypeId());
      double fixValue = fareTypeConfig.getFixedValue() == null ? 0 : fareTypeConfig.getFixedValue();
      double percentValue =
          fareTypeConfig.getPercentValue() == null ? 0 : fareTypeConfig.getPercentValue();
      double multiplierValue = multiplier == null ? 0d : multiplier;
      // Return formula calculating for dynamic pricing config
      return fixValue + percentValue * multiplierValue;
    }
    // Return default if there is no valid config
    return CommonConstant.DEFAULT_DYNAMIC_PRICING_CONFIG_VALUE;
  }

  /**
   * Check validity of config
   *
   * @param config object FareTypeConfig to check
   * @param confName config name
   * @return validity of config (true/false)
   */
  boolean isValidConfig(
      final FareTypeConfig config,
      final String confName,
      final List<FlatFareHoliday> flatFareHolidays) {
    Date currentDate = new Date();
    // Check if current date is between effective date of config
    boolean isValidConfig =
        config.getFareType().equals(confName)
            && DateUtils.isBetween(currentDate, config.getStartDate(), config.getEndDate());
    // Get current hour and check if config is used for current hour
    boolean isValidHour =
        RedisKeyConstant.ALL.equals(config.getHour())
            || Double.compare(
                    Double.parseDouble(config.getHour()), DateUtils.getHourOfDate(currentDate))
                == 0;

    // Check if current date is holiday date
    boolean isHoliday = DateUtils.isHolidaySingTime(currentDate, flatFareHolidays);
    boolean isValidHol = isHoliday && RedisKeyConstant.HOL.equals(config.getDay());
    // Check if config day is applied for holiday / current day / all days
    boolean isValidDay =
        isValidHol
            || (!RedisKeyConstant.ALL.equals(config.getDay())
                && config.getDay().equals(DateUtils.toDayOfWeekShortUpperCase(currentDate)))
            || RedisKeyConstant.ALL.equals(config.getDay());

    return isValidConfig && isValidDay && isValidHour;
  }

  void getVehGrpMapping(FareTypeConfig config, Integer vehGrp) {
    if (vehGrp == null || config.getVehGrp() == null || !vehGrp.equals(config.getVehGrp())) {
      config.setFixedValue(config.getDefaultFixed());
      config.setPercentValue(config.getDefaultPercent());
      config.setVehGrp(vehGrp);
    }
  }

  Stream<FareTypeConfig> addValuesToStream(
      Stream<FareTypeConfig> stream,
      Map<String, List<FareTypeConfig>> map,
      final String confName,
      final String date) {
    if (map.get(
            RedisKeyConstant.DYNAMIC_PRICING
                + RedisKeyConstant.COLON
                + RedisKeyConstant.FARE_TYPE
                + RedisKeyConstant.COLON
                + confName
                + date)
        != null) {
      stream =
          Stream.concat(
              stream,
              map
                  .get(
                      RedisKeyConstant.DYNAMIC_PRICING
                          + RedisKeyConstant.COLON
                          + RedisKeyConstant.FARE_TYPE
                          + RedisKeyConstant.COLON
                          + confName
                          + date)
                  .stream());
    }
    return stream;
  }
}

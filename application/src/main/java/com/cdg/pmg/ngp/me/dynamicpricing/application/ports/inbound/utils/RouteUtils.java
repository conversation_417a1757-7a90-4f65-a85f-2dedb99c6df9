package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils;

import static com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants.MIN_DISTANCE_KM;
import static com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants.MIN_ETT;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.RouteInfo;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.GenerateRouteRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.GenerateRouteResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.LatLng;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.MultiFareRequestEntity;
import java.util.List;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;

@Slf4j
@UtilityClass
public class RouteUtils {

  public static GenerateRouteRequest createGenerateRouteRequest(
      final MultiFareRequestEntity request, final String tripId) {
    final LatLng pickupPoint =
        LatLng.builder()
            .latitude(request.getPickupAddressLat())
            .longitude(request.getPickupAddressLng())
            .build();
    final LatLng dropOffPoint =
        LatLng.builder()
            .latitude(request.getDestAddressLat())
            .longitude(request.getDestAddressLng())
            .build();
    var stream =
        GenerateRouteRequest.builder().tripId(tripId).origin(pickupPoint).destination(dropOffPoint);
    if (request.getIntermediateAddrRef() != null) {
      final List<LatLng> intermediatePoint =
          List.of(
              LatLng.builder()
                  .latitude(request.getIntermediateAddrLat())
                  .longitude(request.getIntermediateAddrLng())
                  .build());
      stream.intermediates(intermediatePoint);
    }
    return stream.build();
  }

  public boolean isRouteResponseEmpty(final GenerateRouteResponse route) {
    return ObjectUtils.isEmpty(route);
  }

  public boolean isRouteResponseEttDistInvalid(final GenerateRouteResponse route) {
    try {
      final long ett = Long.parseLong(route.getDuration());
      final long distanceMeters = route.getDistanceMeters();
      if (ett > MIN_ETT && distanceMeters > MIN_DISTANCE_KM) {
        log.info("Valid route ett={} distanceMeters={}", ett, distanceMeters);
        return Boolean.FALSE;
      }
    } catch (NumberFormatException exception) {
      log.error("Get route error when parse ett errorMessage = {}", exception.getMessage());
      return Boolean.TRUE;
    }
    return Boolean.TRUE;
  }

  public RouteInfo createRouteInfo(
      final MultiFareRequestEntity estFareRequest,
      final GenerateRouteResponse route,
      final String tripId) {
    var stream =
        RouteInfo.builder()
            .tripId(tripId)
            .pickupAddressRef(estFareRequest.getPickupAddressRef())
            .pickupAddressLat(estFareRequest.getPickupAddressLat())
            .pickupAddressLng(estFareRequest.getPickupAddressLng())
            .pickupZoneId(estFareRequest.getPickupZoneId())
            .destAddressRef(estFareRequest.getDestAddressRef())
            .destAddressLat(estFareRequest.getDestAddressLat())
            .destAddressLng(estFareRequest.getDestAddressLng())
            .destZoneId(estFareRequest.getDestZoneId())
            .routingDistance(route.getDistanceMeters())
            .ett(Long.parseLong(route.getDuration()))
            .encodedPolyline(route.getEncodedPolyline());
    if (estFareRequest.getIntermediateAddrRef() != null) {
      stream
          .intermediateAddrRef(estFareRequest.getIntermediateAddrRef())
          .intermediateAddrLat(estFareRequest.getIntermediateAddrLat())
          .intermediateAddrLng(estFareRequest.getIntermediateAddrLng())
          .intermediateZoneId(estFareRequest.getIntermediateZoneId());
    }

    return stream.build();
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.impl.StandardInputServiceImpl;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

/**
 * This class contains all the live inputs. If you need to add new live input, please add {@link
 * JsonProperty} and {@link JsonPropertyDescription} annotation to the property so that it can be
 * parsed to standard inputs
 *
 * @see StandardInputServiceImpl#generateStandardInputs
 * @see StandardInputServiceImpl#getStandardInputs()
 */
@Data
public class H3RegionStatisticsEntity implements Serializable {
  @Serial private static final long serialVersionUID = 3275747501438564310L;

  @JsonIgnore private Long regionId;

  @JsonProperty(value = "ComfortRideDemand")
  @JsonPropertyDescription("ComfortRide Demand by region")
  private Integer comfortRideDemand;

  @JsonProperty(value = "MeterDemand")
  @JsonPropertyDescription("Meter Demand by region")
  private Integer meterDemand;

  @JsonProperty(value = "ComfortRideUnmetDemand")
  @JsonPropertyDescription("ComfortRide Unmet Demand by region")
  private Integer comfortRideUnmetDemand;

  @JsonProperty(value = "MeterUnmetDemand")
  @JsonPropertyDescription("Meter Unmet Demand by region")
  private Integer meterUnmetDemand;

  @JsonProperty(value = "AverageIntensity")
  @JsonPropertyDescription("Rainfall intensity by region")
  private Double averageIntensity;
}

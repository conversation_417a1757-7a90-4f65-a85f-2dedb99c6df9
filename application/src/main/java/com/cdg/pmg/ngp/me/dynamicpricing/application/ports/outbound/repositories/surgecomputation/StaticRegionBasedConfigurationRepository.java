package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticBasedConfigurationEffectiveCheckEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticBasedConfigurationVersionEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticRegionBasedConfigurationEntity;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/** Repository interface for static region-based configurations. */
public interface StaticRegionBasedConfigurationRepository {

  /**
   * Get all versions of static time-based configurations.
   *
   * @param names a list of the configuration name
   * @return a list of all versions
   */
  List<StaticBasedConfigurationVersionEntity> findAllVersionsByNames(List<String> names);

  StaticRegionBasedConfigurationEntity save(StaticRegionBasedConfigurationEntity entity);

  List<StaticRegionBasedConfigurationEntity> findAllByVersionAndNames(
      String version, List<String> names);

  List<StaticRegionBasedConfigurationEntity> findAllByNames(List<String> names);

  Optional<StaticRegionBasedConfigurationEntity> findById(Long id);

  List<StaticRegionBasedConfigurationEntity> findByEffectiveTimeRange(
      Long modelId, Instant effectiveTime);

  /**
   * Find a map of static region-based configurations by effective time range. The format of the map
   * is:
   *
   * <p>{name, {regionId, StaticRegionBasedConfigurationEntity>}}
   *
   * @param modelId the model id from surge_computation_models
   * @param effectiveTime the time at which the configurations should be effective
   * @return a map of configurations
   */
  default Map<String, Map<Long, String>> findMapByEffectiveTimeRange(
      Long modelId, Instant effectiveTime) {
    return findByEffectiveTimeRange(modelId, effectiveTime).stream()
        .collect(
            Collectors.toMap(
                StaticRegionBasedConfigurationEntity::getName,
                entity ->
                    entity.getRegionValues().stream()
                        .collect(
                            Collectors.toMap(
                                StaticRegionBasedConfigurationEntity.RegionValue::getRegionId,
                                StaticRegionBasedConfigurationEntity.RegionValue::getValue,
                                (oldValue, newValue) -> newValue))));
  }

  /**
   * Update audit fields for a list of configurations.
   *
   * @param configNames a list of configuration names
   * @param updatedBy the user who updated the configurations
   * @param updatedDate the date when the configurations were updated
   */
  void updateAuditFields(List<String> configNames, String updatedBy, Instant updatedDate);

  /**
   * Delete configurations by names
   *
   * @param configNames a list of configuration names
   */
  void deleteByNames(List<String> configNames);

  /**
   * Batch create static region-based configurations.
   *
   * @param configurations the list of configurations to create
   * @return the list of created configurations
   */
  List<StaticRegionBasedConfigurationEntity> batchCreateStaticRegionBasedConfigurations(
      List<StaticRegionBasedConfigurationEntity> configurations);

  /**
   * Batch create or update static region-based configurations.
   *
   * @param configsToCreate the list of configurations to create
   * @param configsToUpdate the list of configurations to update
   */
  void batchCreateOrUpdateStaticRegionBasedConfigurations(
      List<StaticRegionBasedConfigurationEntity> configsToCreate,
      List<StaticRegionBasedConfigurationEntity> configsToUpdate);

  StaticBasedConfigurationEffectiveCheckEntity effectiveCheck(Long modelId);
}

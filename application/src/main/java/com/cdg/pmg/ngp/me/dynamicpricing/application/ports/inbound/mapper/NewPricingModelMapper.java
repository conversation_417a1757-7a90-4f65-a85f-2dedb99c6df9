package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.mapper;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.commands.NewPricingModelCommand;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.CMSConfigList;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.NewPricingModelConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.DomainException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Slf4j
@AllArgsConstructor
@ServiceComponent
@Mapper
public abstract class NewPricingModelMapper {

  public abstract NewPricingModelConfigEntity mapToNewPricingModelConfigEntity(
      NewPricingModelCommand newPricingModelCommand);

  public List<NewPricingModelConfigEntity> mapToNewPricingModelConfigEntities(
      ObjectMapper objectMapper, CMSConfigList cmsConfigList) {
    return Optional.ofNullable(cmsConfigList.getData()).stream()
        .flatMap(Collection::stream)
        .map(
            x -> {
              NewPricingModelConfigEntity parse = parse(objectMapper, x.getValue());
              if (parse != null) {
                parse.setId(x.getId());
              }
              return parse;
            })
        .filter(Objects::nonNull)
        .toList();
  }

  public NewPricingModelConfigEntity parse(ObjectMapper objectMapper, String jsonValue) {
    try {
      return objectMapper.readValue(jsonValue, NewPricingModelConfigEntity.class);
    } catch (JsonProcessingException e) {
      log.error("Error parse json", e);
      return null;
    }
  }

  protected boolean nonNull(Object object) {
    return Objects.nonNull(object);
  }

  public String toJson(
      ObjectMapper objectMapper, NewPricingModelConfigEntity newPricingModelConfigEntity) {
    try {
      return objectMapper.writeValueAsString(newPricingModelConfigEntity);
    } catch (JsonProcessingException e) {
      log.error("Error serialize NewPricingModelConfigEntity", e);
      throw new DomainException(
          ErrorEnum.JSON_PARSE_ERROR.getMessage(), ErrorEnum.JSON_PARSE_ERROR.getErrorCode());
    }
  }

  @Mapping(
      source = "source.zoneId",
      target = "target.zoneId",
      conditionExpression = "java(nonNull(source.getZoneId()))",
      defaultExpression = "java(target.getZoneId())")
  @Mapping(
      source = "source.startDt",
      target = "target.startDt",
      conditionExpression = "java(nonNull(source.getStartDt()))",
      defaultExpression = "java(target.getStartDt())")
  @Mapping(
      source = "source.endDt",
      target = "target.endDt",
      conditionExpression = "java(nonNull(source.getEndDt()))",
      defaultExpression = "java(target.getEndDt())")
  @Mapping(
      source = "source.additionalSurgeHigh",
      target = "target.additionalSurgeHigh",
      conditionExpression = "java(nonNull(source.getAdditionalSurgeHigh()))",
      defaultExpression = "java(target.getAdditionalSurgeHigh())")
  @Mapping(
      source = "source.surgeHighTierRate",
      target = "target.surgeHighTierRate",
      conditionExpression = "java(nonNull(source.getSurgeHighTierRate()))",
      defaultExpression = "java(target.getSurgeHighTierRate())")
  @Mapping(
      source = "source.unmetRate1",
      target = "target.unmetRate1",
      conditionExpression = "java(nonNull(source.getUnmetRate1()))",
      defaultExpression = "java(target.getUnmetRate1())")
  @Mapping(
      source = "source.unmetRate2",
      target = "target.unmetRate2",
      conditionExpression = "java(nonNull(source.getUnmetRate2()))",
      defaultExpression = "java(target.getUnmetRate2())")
  @Mapping(
      source = "source.negativeDemandSupplyDownRate",
      target = "target.negativeDemandSupplyDownRate",
      conditionExpression = "java(nonNull(source.getNegativeDemandSupplyDownRate()))",
      defaultExpression = "java(target.getNegativeDemandSupplyDownRate())")
  @Mapping(
      source = "source.createdBy",
      target = "target.createdBy",
      conditionExpression = "java(nonNull(source.getCreatedBy()))",
      defaultExpression = "java(target.getCreatedBy())")
  @Mapping(
      source = "source.createdDt",
      target = "target.createdDt",
      conditionExpression = "java(nonNull(source.getCreatedDt()))",
      defaultExpression = "java(target.getCreatedDt())")
  @Mapping(
      source = "source.updatedBy",
      target = "target.updatedBy",
      conditionExpression = "java(nonNull(source.getUpdatedBy()))",
      defaultExpression = "java(target.getUpdatedBy())")
  @Mapping(
      source = "source.updatedDt",
      target = "target.updatedDt",
      conditionExpression = "java(nonNull(source.getUpdatedDt()))",
      defaultExpression = "java(target.getUpdatedDt())")
  @Mapping(
      source = "source.k1",
      target = "target.k1",
      conditionExpression = "java(nonNull(source.getK1()))",
      defaultExpression = "java(target.getK1())")
  @Mapping(
      source = "source.k2",
      target = "target.k2",
      conditionExpression = "java(nonNull(source.getK2()))",
      defaultExpression = "java(target.getK2())")
  @Mapping(
      source = "source.k3",
      target = "target.k3",
      conditionExpression = "java(nonNull(source.getK3()))",
      defaultExpression = "java(target.getK3())")
  @Mapping(
      source = "source.k4",
      target = "target.k4",
      conditionExpression = "java(nonNull(source.getK4()))",
      defaultExpression = "java(target.getK4())")
  @Mapping(
      source = "source.k5",
      target = "target.k5",
      conditionExpression = "java(nonNull(source.getK5()))",
      defaultExpression = "java(target.getK5())")
  @Mapping(
      source = "source.k6",
      target = "target.k6",
      conditionExpression = "java(nonNull(source.getK6()))",
      defaultExpression = "java(target.getK6())")
  @Mapping(
      source = "source.k7",
      target = "target.k7",
      conditionExpression = "java(nonNull(source.getK7()))",
      defaultExpression = "java(target.getK7())")
  @Mapping(
      source = "source.k8",
      target = "target.k8",
      conditionExpression = "java(nonNull(source.getK8()))",
      defaultExpression = "java(target.getK8())")
  @Mapping(
      source = "source.zonePriceVersion",
      target = "target.zonePriceVersion",
      conditionExpression = "java(nonNull(source.getZonePriceVersion()))",
      defaultExpression = "java(target.getZonePriceVersion())")
  public abstract void mergeNewPricingModel(
      @MappingTarget NewPricingModelConfigEntity target, NewPricingModelCommand source);
}

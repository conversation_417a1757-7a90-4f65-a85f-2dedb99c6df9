package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import lombok.Getter;
import lombok.experimental.UtilityClass;

@UtilityClass
public class CacheUtilities {

  // Global cache: key -> value wrapper
  private static final Cache<String, CacheValueWrapper> CACHE =
      CacheBuilder.newBuilder()
          .maximumSize(1000) // Max entries
          .build();

  private static final long DEFAULT_TTL_SECONDS = TimeUnit.MINUTES.toSeconds(10); // Default 10 min

  /**
   * Put value into cache with custom TTL (in seconds).
   *
   * @param key cache key
   * @param value cache value
   * @param ttlSeconds expiration time in seconds
   */
  public static void put(String key, Object value, long ttlSeconds) {
    if (Objects.nonNull(key) && Objects.nonNull(value) && ttlSeconds > 0) {
      CACHE.put(key, new CacheValueWrapper(value, ttlSeconds));
    }
  }

  /**
   * Put value with default TTL.
   *
   * @param key cache key
   * @param value cache value
   */
  public static void put(String key, Object value) {
    put(key, value, DEFAULT_TTL_SECONDS);
  }

  /**
   * Get cached value, or null if absent/expired.
   *
   * @param key cache key
   * @param <T> expected type
   * @return cached value or null
   */
  @SuppressWarnings("unchecked")
  public static <T> T get(String key) {
    CacheValueWrapper wrapper = CACHE.getIfPresent(key);
    if (wrapper == null || wrapper.isExpired()) {
      CACHE.invalidate(key);
      return null;
    }
    return (T) wrapper.getValue();
  }

  /**
   * Get from cache or load using supplier if absent/expired.
   *
   * @param key cache key
   * @param supplier data loader
   * @param ttlSeconds expiration time in seconds
   * @param <T> expected type
   * @return cached or newly loaded value
   */
  public static <T> T getOrLoad(String key, Supplier<T> supplier, long ttlSeconds) {
    T value = get(key);
    if (value == null) {
      value = supplier.get();
      if (value != null) {
        put(key, value, ttlSeconds);
      }
    }
    return value;
  }

  /**
   * Remove specific key.
   *
   * @param key cache key
   */
  public static void invalidate(String key) {
    CACHE.invalidate(key);
  }

  // Inner wrapper to hold value + expiration time
  private static class CacheValueWrapper {
    @Getter private final Object value;
    private final long expireAtMillis;

    CacheValueWrapper(Object value, long ttlSeconds) {
      this.value = value;
      this.expireAtMillis = System.currentTimeMillis() + (ttlSeconds * 1000);
    }

    boolean isExpired() {
      return System.currentTimeMillis() > expireAtMillis;
    }
  }
}

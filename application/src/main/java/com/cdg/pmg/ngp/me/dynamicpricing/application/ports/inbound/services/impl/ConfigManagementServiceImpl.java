package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import static com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants.FRI;
import static com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants.HOL;
import static com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants.LOC_SURC_KEY_PREFIX;
import static com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants.MON;
import static com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants.SAT;
import static com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants.SUN;
import static com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants.THU;
import static com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants.TUE;
import static com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants.WED;
import static com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant.*;
import static com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant.COLON;
import static com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant.COMPANY_HOLIDAY;
import static com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant.DYNAMIC_PRICING;
import static com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant.DYNP_SURGES;
import static com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant.FARE_TYPE;
import static com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant.FLAT_FARE;
import static com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant.LOC_SURC;
import static com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant.WILDCARD;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.CommonConfigSet;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.DynamicPricingConfigSet;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareConfigSet;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.LocationSurchargeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.cbdcharge.LocReloadCache;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.*;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.cbdcharge.CBDAddressConfigService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.CacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.DynamicSurgesEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.FareTypeEnum;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.BadRequestException;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.InternalServerException;
import com.cdg.pmg.ngp.me.dynamicpricing.utils.CollectionUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@AllArgsConstructor
@ServiceComponent
@Slf4j
public class ConfigManagementServiceImpl implements ConfigManagementService {
  private FlatFareConfigService flatFareConfigService;
  private FareTypeConfigService fareTypeConfigService;
  private CompanyHolidayConfigService companyHolidayConfigService;
  private LocationSurchargeService locationSurchargeService;
  private DemandSupplyService demandSupplyService;
  private CBDAddressConfigService cbdAddressConfigService;
  private CacheService cacheService;
  private FlatFareAdjustmentConfService flatFareAdjustmentConfService;

  @Override
  public String reloadAllConfig() {
    log.info("[Reload Config] Start load all configs to cache!");
    ExecutorService executor = Executors.newFixedThreadPool(5);
    List<Future<String>> futures = new ArrayList<>();

    futures.add(executor.submit(this::reloadFlatFareConfig));
    futures.add(executor.submit(this::reloadFareTypeConfig));
    futures.add(executor.submit(this::reloadCompanyHolidayConfig));
    futures.add(executor.submit(this::reloadDynpSurgeConfig));
    futures.add(executor.submit(this::reloadLocationSurchargeConfig));

    executor.shutdown();

    StringBuilder responseMsg = new StringBuilder();

    try {
      if (!executor.awaitTermination(5, TimeUnit.MINUTES)) {
        // timeout
        executor.shutdownNow();
        responseMsg.append("Timeout occurred.\n");
      }

      for (Future<String> future : futures) {
        responseMsg.append(future.get()).append(" | "); // Get the result from each method
      }

    } catch (InterruptedException | ExecutionException e) {
      log.error("[Reload Config] Got error {}", e.getMessage());
      throw new InternalServerException(
          ErrorEnum.RELOAD_ALL_CONFIG_FAIL.getMessage(),
          ErrorEnum.RELOAD_ALL_CONFIG_FAIL.getErrorCode());
    }
    log.info("[Reload Config] End load all configs to cache!");
    return responseMsg.toString();
  }

  @Override
  public String reloadFlatFareConfig() {
    try {
      log.info("[Reload Config] Start reload flat fare configs to cache");
      flatFareConfigService.loadAllFlatFareConfig();
      setupCommonConfigSet();
      setupFlatFareConfigSet();
      log.info("[Reload Config] End reload flat fare configs to cache");
      return "Reload Flat Fare config successfully";
    } catch (Exception exception) {
      log.error("[Reload Config] Error reloading flat fare configs", exception);
      return "Reload Flat Fare configs failed due to " + exception.getMessage();
    }
  }

  @Override
  public String reloadFareTypeConfig() {
    try {
      log.info("[Reload Config] Start reload fare type configs to cache");
      fareTypeConfigService.loadFareTypeConfig();
      fareTypeConfigService.reloadFareTypeConfigSet();
      log.info("[Reload Config] End reload fare type configs to cache");
      return "Reload Fare Type config successfully";
    } catch (Exception exception) {
      log.error("[Reload Config] Error reloading fare type configs", exception);
      return "Reload Fare Type configs failed due to " + exception.getMessage();
    }
  }

  @Override
  public String reloadFareFareAdjustmentConfig() {
    try {
      log.info(
          "[reloadFareFareAdjustmentConfig] Start reload reloadFareFareAdjustmentConfig to cache");
      flatFareAdjustmentConfService.loadFlatFareAdjustmentConf();

      return "Reload reloadFareFareAdjustmentConfig successfully";
    } catch (Exception exception) {
      log.error("[reloadFareFareAdjustmentConfig] Error reloading fare type configs", exception);
      return "Reload reloadFareFareAdjustmentConfig failed due to " + exception.getMessage();
    }
  }

  @Override
  public String reloadCompanyHolidayConfig() {
    try {
      log.info("[Reload Config] Start reload company holiday configs to cache");
      companyHolidayConfigService.loadCompanyHolidayConfig();
      log.info("[Reload Config] End reload company holiday configs to cache");
      return "Reload Company Holiday config successfully";
    } catch (Exception exception) {
      log.error("[Reload Config] Error reloading company holiday configs", exception);
      return "Reload Company Holiday config failed due to " + exception.getMessage();
    }
  }

  @Override
  public String reloadLocationSurchargeConfig() {
    try {
      log.info("[Reload Config] Start reload location surcharge configs to cache");
      locationSurchargeService.loadAllLocationSurchargeConfigs();
      log.info("[Reload Config] End reload location surcharge configs to cache");
      return "Reload Location Surcharge config successfully";
    } catch (Exception exception) {
      log.error("[Reload Config] Error reloading location surcharge configs", exception);
      return "Reload Location Surcharge config failed due to " + exception.getMessage();
    }
  }

  @Override
  public String reloadDynpSurgeConfig() {
    try {
      log.info("[Reload Config] Start reload dynamic surge configs to cache");
      demandSupplyService.loadDynpSurgeConfigs();
      demandSupplyService.loadDynpSurgeNgpConfigs();
      log.info("[Reload Config] End reload dynamic surge configs to cache");
      return "Reload Dynamic Surge config successfully";
    } catch (Exception exception) {
      log.error("[Reload Config] Error reloading dynamic surge R1 configs", exception);
      return "Reload Dynamic Surge config failed due to " + exception.getMessage();
    }
  }

  @Override
  public void setupFlatFareConfigSet() {
    FlatFareConfigSet limoConfigSet =
        flatFareConfigService.collectFlatFareConfigSet(FareTypeEnum.EST_LIMO_LIVE_TRAFFIC);
    cacheService.setValue(CACHE_KEY_LIMO_CONFIG_SET, limoConfigSet);

    FlatFareConfigSet estStandardConfigSet =
        flatFareConfigService.collectFlatFareConfigSet(FareTypeEnum.EST_LIVE_TRAFFIC);
    cacheService.setValue(CACHE_KEY_EST_STANDARD_CONFIG_SET, estStandardConfigSet);

    log.info("Setup flat fare config set to cache completed!");
  }

  @Override
  public void setupCommonConfigSet() {
    log.info("Start setup common config set to cache!");
    CommonConfigSet commonConfigSet = flatFareConfigService.collectCommonConfigSet();
    cacheService.setValue(CACHE_KEY_COMMON_CONFIG_SET, commonConfigSet);

    log.info("Setup common config set to cache completed! KEY:{}", CACHE_KEY_COMMON_CONFIG_SET);
  }

  @Override
  public void checkEmptyAndReloadConfigs() {
    checkThenReloadIfEmptyFlatFareConfigs();
    checkThenReloadIfEmptyFareTypeConfigs();
    checkThenReloadIfEmptyCompanyHolidayConfigs();
    checkThenReloadIfEmptyDynpSurgeConfigs();
    checkThenReloadIfEmptyLocSurchargeConfigs();
  }

  @Override
  public List<LocationSurchargeConfig> getAllLocationSurchargeConfig(final String dayInWeek) {
    List<LocationSurchargeConfig> locationSurchargeConfigs;
    locationSurchargeConfigs =
        switch (dayInWeek) {
          case MON -> flatFareConfigService.getLocationSurchargeConfig(LOC_SURC_KEY_PREFIX + MON);
          case TUE -> flatFareConfigService.getLocationSurchargeConfig(LOC_SURC_KEY_PREFIX + TUE);
          case WED -> flatFareConfigService.getLocationSurchargeConfig(LOC_SURC_KEY_PREFIX + WED);
          case THU -> flatFareConfigService.getLocationSurchargeConfig(LOC_SURC_KEY_PREFIX + THU);
          case FRI -> flatFareConfigService.getLocationSurchargeConfig(LOC_SURC_KEY_PREFIX + FRI);
          case SAT -> flatFareConfigService.getLocationSurchargeConfig(LOC_SURC_KEY_PREFIX + SAT);
          case SUN -> flatFareConfigService.getLocationSurchargeConfig(LOC_SURC_KEY_PREFIX + SUN);
          case HOL -> flatFareConfigService.getLocationSurchargeConfig(LOC_SURC_KEY_PREFIX + HOL);
          default -> throw new BadRequestException("Not found dayInWeek", 500L);
        };

    return locationSurchargeConfigs;
  }

  @Override
  public List<DynamicSurgesEntity> getAllDOSSurgeCache() {
    return demandSupplyService.getDynpSurges();
  }

  @Override
  public DynamicPricingConfigSet getAllFareTypeConfigCache() {
    return cacheService.getValue(CACHE_KEY_FARE_TYPE_CONFIG_SET, DynamicPricingConfigSet.class);
  }

  @Override
  public void reloadCBDAddressConfig(LocReloadCache locReloadCache) {
    try {
      log.info("[Reload Config] Start reload CBD Address configs to cache");
      cbdAddressConfigService.reloadCache(locReloadCache);
      log.info("[Reload Config] End reload CBD Address configs to cache");
    } catch (Exception exception) {
      log.error("[Reload Config] Error reloading CBD Address configs cache", exception);
      throw new InternalServerException(
          ErrorEnum.RELOAD_CBD_CONFIG_CACHE_FAIL.getMessage(),
          ErrorEnum.RELOAD_CBD_CONFIG_CACHE_FAIL.getErrorCode());
    }
  }

  private void checkThenReloadIfEmptyFlatFareConfigs() {
    final String flatFareCacheKeyPattern =
        DYNAMIC_PRICING.concat(COLON).concat(FLAT_FARE).concat(COLON).concat(WILDCARD);
    final Set<String> listFlatFareCacheKey = cacheService.getKeysByPattern(flatFareCacheKeyPattern);
    if (CollectionUtils.isEmpty(listFlatFareCacheKey)) {
      flatFareConfigService.loadAllFlatFareConfig();
    }
  }

  private void checkThenReloadIfEmptyFareTypeConfigs() {
    final String fareTypeCacheKeyPattern =
        DYNAMIC_PRICING.concat(COLON).concat(FARE_TYPE).concat(COLON).concat(WILDCARD);
    final Set<String> listFareTypeCacheKey = cacheService.getKeysByPattern(fareTypeCacheKeyPattern);
    if (CollectionUtils.isEmpty(listFareTypeCacheKey)) {
      fareTypeConfigService.loadFareTypeConfig();
    }
  }

  private void checkThenReloadIfEmptyCompanyHolidayConfigs() {
    final String companyHolidayCacheKeyPattern =
        DYNAMIC_PRICING.concat(COLON).concat(COMPANY_HOLIDAY).concat(WILDCARD);
    final Set<String> listCompanyHolidayCacheKey =
        cacheService.getKeysByPattern(companyHolidayCacheKeyPattern);
    if (CollectionUtils.isEmpty(listCompanyHolidayCacheKey)) {
      companyHolidayConfigService.loadCompanyHolidayConfig();
    }
  }

  private void checkThenReloadIfEmptyLocSurchargeConfigs() {
    final String locSurchargeCacheKeyPattern =
        DYNAMIC_PRICING.concat(COLON).concat(LOC_SURC).concat(COLON).concat(WILDCARD);
    var listLocSurchargeCacheKey = cacheService.getKeysByPattern(locSurchargeCacheKeyPattern);
    if (CollectionUtils.isEmpty(listLocSurchargeCacheKey)) {
      locationSurchargeService.loadAllLocationSurchargeConfigs();
    }
  }

  private void checkThenReloadIfEmptyDynpSurgeConfigs() {
    final String dynpSurgeCacheKeyPattern =
        DYNAMIC_PRICING.concat(COLON).concat(DYNP_SURGES).concat(WILDCARD);
    var listDynpSurgeCacheKey = cacheService.getKeysByPattern(dynpSurgeCacheKeyPattern);
    if (CollectionUtils.isEmpty(listDynpSurgeCacheKey)) {
      demandSupplyService.loadDynpSurgeConfigs();
    }
  }
}

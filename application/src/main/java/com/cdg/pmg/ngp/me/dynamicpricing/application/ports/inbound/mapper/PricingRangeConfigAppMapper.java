package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.mapper;

import com.cdg.pmg.ngp.me.dynamicpricing.commands.PricingRangeConfigCommand;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.PricingRangeConfigEntity;
import java.math.BigDecimal;
import java.math.RoundingMode;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

@Mapper
public interface PricingRangeConfigAppMapper {
  @Named("round")
  default double round(double value, int places) {
    if (places < 0) throw new IllegalArgumentException();

    BigDecimal bd = new BigDecimal(Double.toString(value));
    bd = bd.setScale(places, RoundingMode.HALF_UP);
    return bd.doubleValue();
  }

  /**
   * Map PricingRangeConfigCommand to PricingRangeConfigEntity
   *
   * @param source Pricing range config command
   * @return PricingRangeConfigEntity Pricing range config entity
   */
  @Mapping(target = "startPrice", expression = "java(round(source.getStartPrice(), 2))")
  @Mapping(target = "endPrice", expression = "java(round(source.getEndPrice(), 2))")
  @Mapping(target = "stepPositive", expression = "java(round(source.getStepPositive(), 2))")
  @Mapping(target = "stepNegative", expression = "java(round(source.getStepNegative(), 2))")
  PricingRangeConfigEntity mapPricingRangeConfigCommandToPricingRangeConfigEntity(
      PricingRangeConfigCommand source);
}

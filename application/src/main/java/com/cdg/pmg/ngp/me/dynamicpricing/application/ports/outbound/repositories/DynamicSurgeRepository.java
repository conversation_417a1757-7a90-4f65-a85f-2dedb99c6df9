package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.DynamicSurgesEntity;
import java.util.List;

public interface DynamicSurgeRepository {

  /**
   * Get list dynamic surges config
   *
   * @return List dynamic surge
   */
  List<DynamicSurgesEntity> getDynpSurges();

  /**
   * For release 1 purpose, we will use table dynp_surge_ngp table dynp_surge will upstream from CN3
   */
  void updateDynpSurges(List<DynamicSurgesEntity> entities);

  List<DynamicSurgesEntity> getNGPDynpSurges();

  void removeInvalidDynSurges();

  /**
   * Update dynp_surge_ngp table for release 2
   *
   * @param entities list of dyn surge entities
   */
  void updateDynpSurgesV2(List<DynamicSurgesEntity> entities);
}

package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class FlatFareBreakdown implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  private String fareId;
  private String bookingId;
  private String tripId;
  private long routingDistance;
  private long ett;
  private String encodedPolyline;
  private String calMethod;
  private String pickupAddressRef;
  private double pickupAddressLat;
  private double pickupAddressLng;
  private String pickupZoneId;
  private String destAddressRef;
  private double destAddressLat;
  private double destAddressLng;
  private String destZoneId;
  private String intermediateAddrRef;
  private double intermediateAddrLat;
  private double intermediateAddrLng;
  private String intermediateZoneId;
  private Date requestDate;
  private double flagDownRate;
  private double tier1Fare;
  private double tier2Fare;
  private double waitTimeFare;
  private double peakHrFare;
  private double midNightFare;
  private double hourlySurcharge;
  private double bookingFee;
  private double locSurcharge;
  private double eventSurcharge;
  private double additionalSurcharge;
  private Double dpSurgePercent;
  private Double dpSurgeAmount;
  private Double dpAppliedSurgeAmount;
  private Double dpBaseFareForSurge;
  private Double dpFinalFare;
  private Integer dpBatchKey;
  private double meteredBaseFare;
  private BigDecimal estimatedFareLF;
  private BigDecimal estimatedFareRT;
  private Long flatPlatformFeeId;
  private Double flatPlatformFee;
  private Long meterPlatformFeeId;
  private Double meterPlatformFeeLower;
  private Double meterPlatformFeeUpper;
}

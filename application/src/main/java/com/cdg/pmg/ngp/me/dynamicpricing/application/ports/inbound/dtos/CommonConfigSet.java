package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ServiceComponent
public class CommonConfigSet {
  private String limoFlatFareVehIds;
  private String estLimoFlatFareVehIds;
  private String estFareVehIds;
  private String dynamicPricingVehIds;
  private String advanceVehIds;
  private String vehGrpShowMeterOnly;
  private String vehGrpShowFFOnly;
  private String cacheTimerMinsMultiFlatFare;
  private String cacheTimerMinsBreakdownFlatFare;
  private List<DriverSurgeLevelIndication> driverSurgeLevelIndications;
  private String surgeIndicatorThreshold;
  private String surgeIndicatorThresholdZero;
}

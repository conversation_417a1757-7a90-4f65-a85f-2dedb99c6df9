package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.HashObjectService;

@ServiceComponent
public class HashObjectServiceImpl implements HashObjectService {
  @Override
  public String hashObject(Object object) {
    return String.valueOf(object.hashCode());
  }
}

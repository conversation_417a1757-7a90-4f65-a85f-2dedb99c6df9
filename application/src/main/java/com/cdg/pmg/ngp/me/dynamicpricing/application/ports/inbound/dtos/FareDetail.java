package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.FareBreakdownDetailEntity;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FareDetail implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  private MultiFareResponse multiFareResponse;
  private FareBreakdownDetailEntity fareBreakdown;
}

package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.cbdcharge;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.cbdcharge.LocReloadCache;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.cbdcharge.UpdateCBDAddress;
import java.util.List;

public interface CBDAddressConfigService {

  /**
   * Update CBD Address
   *
   * @param updateCBDAddress request
   */
  void updateCBDAddress(UpdateCBDAddress updateCBDAddress);

  void reloadCache(LocReloadCache locReloadCache);

  void addCBDConfigCache(List<String> cbdAddressAdded);

  void removeCBDConfigCache(List<String> cbdAddressAdded);
}

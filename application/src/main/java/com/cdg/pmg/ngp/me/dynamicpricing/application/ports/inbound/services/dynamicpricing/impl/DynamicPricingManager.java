package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.dynamicpricing.impl;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.DynamicPricingConfigSet;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareVO;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.DemandSupplyService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.dynamicpricing.DynamicPricingCompute;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.CommonConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.DynamicSurgesEntity;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;

@ServiceComponent
@Slf4j
public class DynamicPricingManager {
  private final DemandSupplyService demandSupplyService;

  public DynamicPricingManager(DemandSupplyService demandSupplyService) {
    this.demandSupplyService = demandSupplyService;
  }

  public void computeTotalFare(FlatFareVO flatFareVO, DynamicPricingConfigSet fareTypeConfigSet) {

    final DynamicPricingCompute dynpComputeInf = new DynamicPricingComputeImpl(fareTypeConfigSet);

    dynpComputeInf.calFlagDown(flatFareVO);
    dynpComputeInf.calTier1Fare(flatFareVO);
    dynpComputeInf.calTier2Fare(flatFareVO);
    dynpComputeInf.calWaitTimeFare(flatFareVO);
    dynpComputeInf.calHourlySurcharge(flatFareVO);
    dynpComputeInf.calLocationSurcharge(flatFareVO);
    dynpComputeInf.calBookingFee(flatFareVO);
    dynpComputeInf.calMultiDestSurcharge(flatFareVO);
    dynpComputeInf.calEventSurcharge(flatFareVO);

    var dpBaseFareForSurge =
        flatFareVO.getFlagDown()
            + flatFareVO.getTier1Fare()
            + flatFareVO.getTier2Fare()
            + flatFareVO.getWaitTimeFare()
            + flatFareVO.getHourlySurcharge()
            + flatFareVO.getBookingFee();
    flatFareVO.setDpBaseFareForSurge(dpBaseFareForSurge);

    List<DynamicSurgesEntity> listDynamicSurges = demandSupplyService.getDynpSurges();
    log.info("[DYNP_SURGE] calculate dynamic surge config {}", listDynamicSurges);
    DynamicSurgesEntity dynamicSurgeObject =
        listDynamicSurges.stream()
            .filter(
                dynamicSurge ->
                    dynamicSurge
                        .getZoneId()
                        .equals(flatFareVO.getFlatFareRequest().getOriginZoneId()))
            .findFirst()
            .orElse(null);

    final double surgeValue =
        Objects.nonNull(dynamicSurgeObject)
            ? dynamicSurgeObject.getSurge()
            : CommonConstant.DEFAULT_SURGE;
    final Integer batchKey =
        Objects.nonNull(dynamicSurgeObject) ? dynamicSurgeObject.getBatchKey() : null;
    flatFareVO.setDpSurgePercent(surgeValue);
    flatFareVO.setBatchKey(batchKey);

    final double surgePercent = surgeValue / 100;
    final double dpAfterAddingSurge = flatFareVO.getDpBaseFareForSurge() * (1 + surgePercent);
    final double dpSurgeAmount = dpAfterAddingSurge - flatFareVO.getDpBaseFareForSurge();
    flatFareVO.setDpSurgeAmt(dpSurgeAmount);
    final double dpFareAfterSurge =
        dpAfterAddingSurge
            + flatFareVO.getTotalLocSurCharge()
            + flatFareVO.getTotalEventSurCharge();

    flatFareVO.setPricePerKm(surgePercent);
    flatFareVO.setDpFareAfterSurge(dpFareAfterSurge);
    flatFareVO.setCalculated(Boolean.TRUE);
  }

  public void validateDesurge(FlatFareVO flatFareVO, DynamicPricingConfigSet fareTypeConfigSet) {
    final DynamicPricingCompute dynpComputeInf = new DynamicPricingComputeImpl(fareTypeConfigSet);
    dynpComputeInf.updateAfterApplyingSurgeFare(flatFareVO);
  }

  public void setLimitMinMax(FlatFareVO flatFareVO, DynamicPricingConfigSet fareTypeConfigSet) {
    final DynamicPricingCompute dynpComputeInf = new DynamicPricingComputeImpl(fareTypeConfigSet);
    dynpComputeInf.setDynpMinMaxForTotalFare(flatFareVO);
  }
}

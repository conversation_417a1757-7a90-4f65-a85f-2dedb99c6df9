package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.flatfare.impl;

import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.CALCULATE_BREAKDOWN_FARE_ERROR;
import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.GET_BOOKING_FEE_FAILED;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.BookingFare;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.EstimateRateConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.EventSurgeAddressConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareConfigSet;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareVO;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.HourRateBean;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.TierFare;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.FlatFareConfigService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.LocationSurchargeService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.flatfare.FlatFareCompute;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.CommonUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.DateUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.SurchargeUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.BookingFeeItem;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.FareService;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.DomainException;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.InternalServerException;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ServiceComponent
@AllArgsConstructor
@Slf4j
public abstract class FlatFareComputeImpl implements FlatFareCompute {

  protected String fareType;
  protected FlatFareConfigSet configSet;
  protected FlatFareConfigService configService;
  protected FareService fareService;
  protected LocationSurchargeService locationSurchargeService;

  protected FlatFareComputeImpl(
      FlatFareConfigSet configSet,
      FlatFareConfigService configService,
      FareService fareService,
      LocationSurchargeService locationSurchargeService) {
    this.configService = configService;
    this.configSet = configSet;
    this.fareType = configSet.getPrefixKey();
    this.fareService = fareService;
    this.locationSurchargeService = locationSurchargeService;
  }

  @Override
  public void calFlatDownRateFare(FlatFareVO vo) {
    log.info("Start calFlatDownRateFare with vehTypeId={}", vo.getFlatFareRequest().getVehTypeId());
    try {
      final String flagDown = configSet.getFlagDownRate();
      vo.setFlagDown(Double.parseDouble(flagDown));
    } catch (Exception e) {
      throw handleExceptionForBreakDownCalc(e, vo, "calFlatDownRateFare");
    }
  }

  @Override
  public void calTier1(FlatFareVO vo) {
    log.info("Start calTier1 with vehTypeId={}", vo.getFlatFareRequest().getVehTypeId());
    try {
      final long distance = vo.getFlatFareRequest().getRoutingDistance();
      final TierFare tier1FareConfig = configSet.getTier1Fare();
      final double perCountFare = Double.parseDouble(tier1FareConfig.getPerCountFare());
      final double perCountMeter = Double.parseDouble(tier1FareConfig.getPerCountMeter());
      final int startDist = Integer.parseInt(tier1FareConfig.getStartDistance());
      final int endDist = Integer.parseInt(tier1FareConfig.getEndDistance());

      double tier1Fare = 0.0;
      if (distance > startDist && distance > endDist) {
        tier1Fare = ((endDist - startDist) / perCountMeter) * perCountFare;
      } else if (distance > startDist) {
        tier1Fare = ((distance - startDist) / perCountMeter) * perCountFare;
      }
      vo.setTier1Fare(CommonUtils.roundToTwo(tier1Fare));

    } catch (Exception e) {
      throw handleExceptionForBreakDownCalc(e, vo, "calTier1");
    }
  }

  @Override
  public void calTier2(FlatFareVO vo) {
    log.info("Start calTier2 with vehTypeId={}", vo.getFlatFareRequest().getVehTypeId());
    try {
      final long distance = vo.getFlatFareRequest().getRoutingDistance();
      final TierFare tier2FareConfig = configSet.getTier2Fare();
      final double perCountFare = Double.parseDouble(tier2FareConfig.getPerCountFare());
      final double perCountMeter = Double.parseDouble(tier2FareConfig.getPerCountMeter());
      final int startDist = Integer.parseInt(tier2FareConfig.getStartDistance());

      double tier2Fare = 0.0;

      if (distance > startDist) {
        tier2Fare = ((distance - startDist) / perCountMeter) * perCountFare;
      }
      vo.setTier2Fare(CommonUtils.roundToTwo(tier2Fare));
    } catch (Exception e) {
      throw handleExceptionForBreakDownCalc(e, vo, "calTier2");
    }
  }

  @Override
  public void calEttFare(FlatFareVO vo) {
    log.info("Start calEttFare with vehTypeId={}", vo.getFlatFareRequest().getVehTypeId());
    final long ett = vo.getFlatFareRequest().getEtt();

    try {
      final double durationUnit = Double.parseDouble(configSet.getDurationUnitConfig());
      final double durationRate = Double.parseDouble(configSet.getDurationRateConfig());

      if (ett > 0) {
        final double waitTimeFare = (ett / durationUnit) * durationRate;
        vo.setWaitTimeFare(CommonUtils.roundToTwo(waitTimeFare));
      }
    } catch (Exception e) {
      throw handleExceptionForBreakDownCalc(e, vo, "calEttFare");
    }
  }

  @Override
  public void calPeakHourCharges(FlatFareVO vo) {
    log.info("Start calPeakHourCharges with vehTypeId={}", vo.getFlatFareRequest().getVehTypeId());
    try {
      final Date reqDate = vo.getFlatFareRequest().getRequestDate();
      final LocalTime reqTime = DateUtils.convertToLocalTime(reqDate);

      List<HourRateBean> hourRateBeans =
          getHourRateList(configSet.getPeakHoursRates(), fareType, FlatfareConstants.PEAK_HOUR);
      if (DateUtils.isHolidaySingTime(reqDate, configSet.getHolidayList())) {
        hourRateBeans =
            hourRateBeans.stream()
                .filter(
                    p ->
                        p.getDaysOfWeekIncluded().contains(FlatfareConstants.HOL)
                            && DateUtils.isBetween(
                                reqTime,
                                p.getStartTime().minusMinutes(FlatfareConstants.FIFTEEN_MINUTES),
                                p.getEndTime()))
                .toList();
      } else {
        hourRateBeans =
            hourRateBeans.stream()
                .filter(
                    p ->
                        p.getDaysOfWeekIncluded()
                                .contains(DateUtils.toDayOfWeekShortUpperCase(reqDate))
                            && DateUtils.isBetween(
                                reqTime,
                                p.getStartTime().minusMinutes(FlatfareConstants.FIFTEEN_MINUTES),
                                p.getEndTime()))
                .toList();
      }

      final double peakHourFare =
          getSumOfHourRate(
              hourRateBeans,
              vo.getFlagDown(),
              vo.getTier1Fare(),
              vo.getTier2Fare(),
              vo.getWaitTimeFare(),
              reqTime);

      vo.setPeakHrFare(CommonUtils.roundToTwo(peakHourFare));

    } catch (Exception e) {
      throw handleExceptionForBreakDownCalc(e, vo, "calPeakHourCharges");
    }
  }

  @Override
  public void calMidNightCharges(FlatFareVO vo) {
    log.info("Start calMidNightCharges with vehTypeId={}", vo.getFlatFareRequest().getVehTypeId());
    try {
      final Date reqDate = vo.getFlatFareRequest().getRequestDate();
      final LocalTime reqTime = DateUtils.convertToLocalTime(reqDate);

      List<HourRateBean> hourRateBeans =
          getHourRateList(configSet.getMidnightHoursRates(), fareType, FlatfareConstants.MIDNIGHT);
      if (DateUtils.isHolidaySingTime(reqDate, configSet.getHolidayList())) {
        hourRateBeans =
            hourRateBeans.stream()
                .filter(
                    p ->
                        p.getDaysOfWeekIncluded().contains(FlatfareConstants.HOL)
                            && DateUtils.isBetween(
                                reqTime,
                                p.getStartTime().minusMinutes(FlatfareConstants.FIFTEEN_MINUTES),
                                p.getEndTime()))
                .toList();
      } else {
        hourRateBeans =
            hourRateBeans.stream()
                .filter(
                    p ->
                        p.getDaysOfWeekIncluded()
                                .contains(DateUtils.toDayOfWeekShortUpperCase(reqDate))
                            && DateUtils.isBetween(
                                reqTime,
                                p.getStartTime().minusMinutes(FlatfareConstants.FIFTEEN_MINUTES),
                                p.getEndTime()))
                .toList();
      }

      final double midNightFare =
          getSumOfHourRate(
              hourRateBeans,
              vo.getFlagDown(),
              vo.getTier1Fare(),
              vo.getTier2Fare(),
              vo.getWaitTimeFare(),
              reqTime);
      vo.setMidNightFare(CommonUtils.roundToTwo(midNightFare));
    } catch (Exception e) {
      throw handleExceptionForBreakDownCalc(e, vo, "calMidNightCharges");
    }
  }

  private double getSumOfHourRate(
      final List<HourRateBean> hourRateBeans,
      final double flagDown,
      final double tier1Fare,
      final double tier2Fare,
      final double waitTimeFare,
      final LocalTime reqTime) {
    double sumOfHourRate = 0.0;
    for (HourRateBean hrb : hourRateBeans) {
      final LocalTime startTime = hrb.getStartTime();
      final LocalTime endTime = hrb.getEndTime();
      if (DateUtils.isBetween(
          reqTime, startTime, endTime.minusMinutes(FlatfareConstants.FIFTEEN_MINUTES))) {
        log.debug("DEBUG :: Within the midnight charge range");
        sumOfHourRate += (flagDown + tier1Fare + tier2Fare + waitTimeFare) * hrb.getRate();
      } else if (DateUtils.isBetween(
          reqTime, startTime.minusMinutes(FlatfareConstants.FIFTEEN_MINUTES), startTime)) {
        if (DateUtils.isBetween(
            reqTime, startTime.minusMinutes(FlatfareConstants.FIVE_MINUTES), startTime)) {
          log.debug("DEBUG :: Step up 0 - 5 minutes");
          sumOfHourRate += (flagDown + tier1Fare + tier2Fare + waitTimeFare) * hrb.getStepUp5();
        } else if (DateUtils.isBetween(
            reqTime, startTime.minusMinutes(FlatfareConstants.TEN_MINUTES), startTime)) {
          log.debug("DEBUG :: Step up 5 - 10 minutes");
          sumOfHourRate += (flagDown + tier1Fare + tier2Fare + waitTimeFare) * hrb.getStepUp10();
        } else {
          log.debug("DEBUG :: Step up 10 - 15 minutes");
          sumOfHourRate += (flagDown + tier1Fare + tier2Fare + waitTimeFare) * hrb.getStepUp15();
        }
      } else if (DateUtils.isBetween(
          reqTime, endTime.minusMinutes(FlatfareConstants.FIFTEEN_MINUTES), endTime)) {
        if (DateUtils.isBetween(
            reqTime, endTime.minusMinutes(FlatfareConstants.FIVE_MINUTES), endTime)) {
          log.debug("DEBUG :: Step down 0 - 5 minutes");
          sumOfHourRate += (flagDown + tier1Fare + tier2Fare + waitTimeFare) * hrb.getStepDown5();
        } else if (DateUtils.isBetween(
            reqTime, endTime.minusMinutes(FlatfareConstants.TEN_MINUTES), endTime)) {
          log.debug("DEBUG :: Step down 5 - 10 minutes");
          sumOfHourRate += (flagDown + tier1Fare + tier2Fare + waitTimeFare) * hrb.getStepDown10();
        } else {
          log.debug("DEBUG :: Step down 10 - 15 minutes");
          sumOfHourRate += (flagDown + tier1Fare + tier2Fare + waitTimeFare) * hrb.getStepDown15();
        }
      }
    }
    return sumOfHourRate;
  }

  private List<HourRateBean> getHourRateList(
      final List<Map<String, String>> mapHourRates,
      final String fareType,
      final String hourRateType) {
    AtomicInteger index = new AtomicInteger(-1);
    return mapHourRates.stream()
        .map(
            item -> {
              index.getAndIncrement();
              return HourRateBean.builder()
                  .daysOfWeekIncluded(item.get(fareType + hourRateType + "DAYS_" + index))
                  .startTime(
                      DateUtils.convertToLocalTime(
                          item.get(fareType + hourRateType + "START_TIME_" + index)))
                  .endTime(
                      DateUtils.convertToLocalTime(
                          item.get(fareType + hourRateType + "END_TIME_" + index)))
                  .stepUp5(
                      Double.parseDouble(
                          item.get(fareType + hourRateType + "STEPUP_5mins_" + index)))
                  .stepUp10(
                      Double.parseDouble(
                          item.get(fareType + hourRateType + "STEPUP_10mins_" + index)))
                  .stepUp15(
                      Double.parseDouble(
                          item.get(fareType + hourRateType + "STEPUP_15mins_" + index)))
                  .stepDown5(
                      Double.parseDouble(
                          item.get(fareType + hourRateType + "STEPDOWN_5mins_" + index)))
                  .stepDown10(
                      Double.parseDouble(
                          item.get(fareType + hourRateType + "STEPDOWN_10mins_" + index)))
                  .stepDown15(
                      Double.parseDouble(
                          item.get(fareType + hourRateType + "STEPDOWN_15mins_" + index)))
                  .rate(Double.parseDouble(item.get(fareType + hourRateType + "RATE_" + index)))
                  .build();
            })
        .toList();
  }

  @Override
  public void calAdditionalSurCharge(FlatFareVO vo) {
    log.info(
        "Start calAdditionalSurCharge with vehTypeId={}", vo.getFlatFareRequest().getVehTypeId());
    try {
      final List<Double> additionalChargeList = configSet.getAdditionalChargeList();
      final double sum =
          additionalChargeList.stream().reduce(FlatfareConstants.PRICE_DEFAULT, Double::sum);
      vo.setAdditionalSurcharge(sum);
    } catch (Exception e) {
      throw handleExceptionForBreakDownCalc(e, vo, "calAdditionalSurCharge");
    }
  }

  @Override
  public void calMultiDestSurcharge(FlatFareVO vo) {
    log.info(
        "Start calMultiDestSurcharge with vehTypeId={}", vo.getFlatFareRequest().getVehTypeId());
    vo.setMultiDestSurcharge(FlatfareConstants.PRICE_DEFAULT);
  }

  @Override
  public void calBaseFare(FlatFareVO vo) {
    log.info("Start calBaseFare with vehTypeId={}", vo.getFlatFareRequest().getVehTypeId());
    final double totalAmount =
        vo.getFlagDown()
            + vo.getTier1Fare()
            + vo.getTier2Fare()
            + vo.getWaitTimeFare()
            + vo.getPeakHrFare()
            + vo.getMidNightFare()
            + vo.getBookingFee()
            + vo.getAdditionalSurcharge()
            + vo.getMultiDestSurcharge()
            + vo.getTotalLocSurCharge();
    vo.setTotalFare(BigDecimal.valueOf(totalAmount));
  }

  @Override
  public void setTotalFareBeforeSurge(FlatFareVO vo) {
    vo.setTotalFareBeforeSurge(vo.getTotalFare().doubleValue());
  }

  @Override
  public void calEventSurgeAddrCharge(FlatFareVO vo) {
    log.info(
        "Start calEventSurgeAddrCharge with vehTypeId={}", vo.getFlatFareRequest().getVehTypeId());
    try {
      final Date reqDate = vo.getFlatFareRequest().getRequestDate();
      final String pickUpRef = vo.getFlatFareRequest().getOriginAddressRef();
      final String intermediateRef = vo.getFlatFareRequest().getIntermediateAddrRef();
      final String dropOffRef = vo.getFlatFareRequest().getDestAddressRef();

      List<EventSurgeAddressConfig> eventSurgeZoneConfigs =
          configSet.getEventSurgeAddressConfigList();
      eventSurgeZoneConfigs =
          eventSurgeZoneConfigs.stream()
              .filter(
                  configItem ->
                      SurchargeUtils.isEventConfigMatchingDayAndMonthAndTimeEffective(
                          configItem, reqDate))
              .toList();

      double eventSurgeAddrCharge = 0.0d;

      for (EventSurgeAddressConfig configItem : eventSurgeZoneConfigs) {
        if (SurchargeUtils.isAddressRefMatchingWithConfigUsingFixAmountAndChangeByPickUp(
                pickUpRef, configItem)
            || SurchargeUtils.isAddressRefMatchingWithConfigUsingFixAmountAndChangeByDestination(
                dropOffRef, configItem)
            || SurchargeUtils.isAddressRefMatchingWithConfigUsingFixAmountAndChangeByDestination(
                intermediateRef, configItem)) {
          eventSurgeAddrCharge += configItem.getChargeVal();
        } else if (SurchargeUtils.isAddressRefMatchingWithConfigUsingPercentageAndChangeByPickUp(
                pickUpRef, configItem)
            || SurchargeUtils.isAddressRefMatchingWithConfigUsingPercentageAndChangeByDestination(
                dropOffRef, configItem)
            || SurchargeUtils.isAddressRefMatchingWithConfigUsingPercentageAndChangeByDestination(
                intermediateRef, configItem)) {
          eventSurgeAddrCharge += vo.getTotalFareBeforeSurge() * configItem.getChargeVal();
        }
      }

      if (eventSurgeAddrCharge != 0) {
        eventSurgeAddrCharge = CommonUtils.roundToTwo(eventSurgeAddrCharge);
        vo.getEventSurgeCharge().add(eventSurgeAddrCharge);

        double totalFare = vo.getTotalFare().doubleValue() + eventSurgeAddrCharge;
        vo.setTotalFare(BigDecimal.valueOf(totalFare));
      }

    } catch (Exception e) {
      throw handleExceptionForBreakDownCalc(e, vo, "calEventSurgeAddrCharge");
    }
  }

  @Override
  public void roundToFiftyCent(FlatFareVO vo) {
    log.info("Start roundToFiftyCent with vehTypeId={}", vo.getFlatFareRequest().getVehTypeId());
    final double totalFare = vo.getTotalFare().doubleValue();
    final double fareLF = vo.getEstimatedFareLF().doubleValue();
    final double fareRT = vo.getEstimatedFareRT().doubleValue();
    vo.setTotalFare(BigDecimal.valueOf(CommonUtils.roundUpToFiftyCent(totalFare)));
    vo.setEstimatedFareLF(BigDecimal.valueOf(CommonUtils.roundUpToFiftyCent(fareLF)));
    vo.setEstimatedFareRT(BigDecimal.valueOf(CommonUtils.roundUpToFiftyCent(fareRT)));
  }

  @Override
  public void calEstimatedTotalFare(FlatFareVO vo) {
    log.info(
        "Start calEstimatedTotalFare with vehTypeId={}", vo.getFlatFareRequest().getVehTypeId());
    try {
      EstimateRateConfig estimateRateConfig = configSet.getEstimateRateConfig();

      final double estimatedLFRate =
          Double.parseDouble(estimateRateConfig.getTotalFareEstimateLF());
      final double estimatedRTRate =
          Double.parseDouble(estimateRateConfig.getTotalFareEstimateRT());

      final double totalFare = vo.getTotalFare().doubleValue();
      if (estimatedLFRate > 0) {
        vo.setEstimatedFareLF(CommonUtils.roundToTwoBD(totalFare * estimatedLFRate));
      }
      if (estimatedRTRate > 0) {
        vo.setEstimatedFareRT(CommonUtils.roundToTwoBD(totalFare * estimatedRTRate));
      }
      vo.setMeteredBaseFare(vo.getTotalFare().doubleValue());
    } catch (Exception e) {
      throw handleExceptionForBreakDownCalc(e, vo, "calEstimatedTotalFare");
    }
  }

  @Override
  public boolean isValidFlatfare(FlatFareVO vo) {
    return false;
  }

  @Override
  public String getFareType() {
    return this.fareType;
  }

  @Override
  public void calBookingFee(FlatFareVO vo) {
    log.info(
        "Start calBookingFee with vehTypeId={} productId={}",
        vo.getFlatFareRequest().getVehTypeId(),
        vo.getPdtId());
    final Date reqDate = vo.getFlatFareRequest().getRequestDate();
    final Integer vehTypeIdReq = vo.getFlatFareRequest().getVehTypeId();
    final String productIdReq = vo.getPdtId();
    final String flatFareTypeReq =
        switch (getFareType()) {
          case RedisKeyConstant.EST_LIMO_LIVE_TRAFFIC_KEY_PREFIX -> RedisKeyConstant
              .EST_LIMO_LIVE_TRAFFIC;
          case RedisKeyConstant.EST_LIVE_TRAFFIC_KEY_PREFIX -> RedisKeyConstant.EST_LIVE_TRAFFIC;
          case RedisKeyConstant.LIVE_TRAFFIC_KEY_PREFIX -> RedisKeyConstant.LIVE_TRAFFIC;
          default -> RedisKeyConstant.EMPTY;
        };

    Optional<BookingFeeItem> bookingFee =
        configSet.getBookingFeeList().stream()
            .filter(
                bookingFeeItem ->
                    vehTypeIdReq.equals(bookingFeeItem.getVehicleTypeId())
                        && flatFareTypeReq.equalsIgnoreCase(bookingFeeItem.getFlatFareType())
                        && productIdReq.equalsIgnoreCase(bookingFeeItem.getProductId()))
            .findFirst();
    if (bookingFee.isPresent()) {
      vo.setBookingFee(bookingFee.get().getBookingFee());
    } else {
      log.error("Failed to get booking fee");
      final String errorMsg =
          MessageFormat.format(
              GET_BOOKING_FEE_FAILED.getMessage(),
              vehTypeIdReq,
              flatFareTypeReq,
              productIdReq,
              vo.getFlatFareRequest().getJobType(),
              DateUtils.isHolidaySingTime(reqDate, configSet.getHolidayList()),
              reqDate.toInstant().atOffset(ZoneOffset.UTC));
      throw new InternalServerException(errorMsg, GET_BOOKING_FEE_FAILED.getErrorCode());
    }
  }

  public boolean isHoliday(Date requestDay) {
    return DateUtils.isHolidaySingTime(requestDay, configSet.getHolidayList());
  }

  protected boolean isMatchWithApplicableDayTimeInConfig(final Date reqDate, final BookingFare bf) {
    return bf.getApplicableDays() != null
        && ((isHoliday(reqDate) && bf.getApplicableDays().contains(FlatfareConstants.HOL))
            || (!isHoliday(reqDate)
                && bf.getApplicableDays().contains(DateUtils.toDayOfWeekShortUpperCase(reqDate))))
        && DateUtils.isBetween(
            DateUtils.convertToLocalTime(reqDate), bf.getStartTime(), bf.getEndTime());
  }

  protected boolean isHolidayAndMatchWithApplicableDayOtherTimeInConfig(
      final Date reqDate, final BookingFare bf) {
    LocalTime zeroTime = DateUtils.convertToLocalTime("00:00:00");
    return isHoliday(reqDate)
        && bf.getApplicableDays() != null
        && bf.getApplicableDays().contains(FlatfareConstants.HOL)
        && bf.getStartTime().equals(zeroTime)
        && bf.getEndTime().equals(zeroTime);
  }

  protected boolean isNormalDayAndMatchWithApplicableDayOtherTimeInConfig(
      final Date reqDate, final BookingFare bf) {
    LocalTime zeroTime = DateUtils.convertToLocalTime("00:00:00");
    return !isHoliday(reqDate)
        && bf.getApplicableDays() != null
        && bf.getApplicableDays().contains(DateUtils.toDayOfWeekShortUpperCase(reqDate))
        && bf.getStartTime().equals(zeroTime)
        && bf.getEndTime().equals(zeroTime);
  }

  protected DomainException handleExceptionForBreakDownCalc(
      final Exception e, final FlatFareVO vo, final String methodName) {
    log.error("Error at {} ErrorMessage={}, FlatFareVO={}", methodName, e.getMessage(), vo);
    var message = String.format("Error at %s, message=%s", methodName, e.getMessage());
    log.error(message);
    throw new DomainException(
        CALCULATE_BREAKDOWN_FARE_ERROR.getMessage(), CALCULATE_BREAKDOWN_FARE_ERROR.getErrorCode());
  }

  protected void setConfigSet(final FlatFareConfigSet configSet) {
    this.configSet = configSet;
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.SurgeCalculationDto;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.SurgeCalculationStrategy;

@ServiceComponent
public class SurgeCalculationV3Strategy implements SurgeCalculationStrategy {
  @Override
  public int calculate(SurgeCalculationDto surgeCalculationDto) {
    var unmet15 = surgeCalculationDto.getUnmet15();
    var prevUnmet15 = surgeCalculationDto.getPreviousUnmet15();
    var prevSurge = surgeCalculationDto.getCurrentSurge();
    int surgeLow = surgeCalculationDto.getSurgeLow();
    int surgeHighNew = surgeCalculationDto.getSurgeHighNew();
    double k1 = surgeCalculationDto.getK1();
    double k2 = surgeCalculationDto.getK2();
    double k3 = surgeCalculationDto.getK3();
    double k4 = surgeCalculationDto.getK4();
    double k5 = surgeCalculationDto.getK5();
    double k6 = surgeCalculationDto.getK6();
    double k7 = surgeCalculationDto.getK7();

    if (isZero(unmet15)) {
      return clamp(prevSurge, surgeLow, surgeHighNew);
    }

    var raise = getRaise(unmet15, prevUnmet15);
    if (prevUnmet15 == 0 || unmet15 > prevUnmet15) {
      // surge positive
      var part1 = k1 * raise / (1 + k2 * Math.exp(-k3 * raise));
      var part2 = (k4 * unmet15 * 1.0) / (1 + Math.exp(-unmet15));
      var surge = prevSurge + part1 + part2;
      return clamp((int) surge, surgeLow, surgeHighNew);
    }

    // surge negative
    var surge = prevSurge + k5 * raise / (1 + k6 * Math.exp(-k7 * raise));
    return clamp((int) surge, surgeLow, surgeHighNew);
  }

  private double getRaise(double unmet15, double prevUnmet15) {
    // Compare zero
    if (isZero(prevUnmet15)) {
      return 1;
    }

    return (unmet15 - prevUnmet15) / prevUnmet15;
  }

  public boolean isZero(double value) {
    return Math.abs(value) < 1e-3;
  }

  @Override
  public String getVersion() {
    return V3;
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.additionalcharge.AdditionalChargeConfigData;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.additionalcharge.AdditionalChargeDriverFeeData;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FlatFareVOPart implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  private int vehTypeId;

  private BigDecimal totalFare;

  private BigDecimal estimatedFareLF;

  private BigDecimal estimatedFareRT;

  private String pdtId;

  private int drvSurgeLvl;

  private int paxSurgeIndicator;

  private long platformFeeConfigId;

  private double platformFeeLower;

  private double platformFeeUpper;

  private List<AdditionalChargeConfigData> additionalCharges;

  private AdditionalChargeDriverFeeData additionalChargeDriverFeeData;
}

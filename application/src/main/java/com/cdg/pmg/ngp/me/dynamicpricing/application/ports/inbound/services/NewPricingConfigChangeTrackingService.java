package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.ConfigKeyValueConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.NewPricingModelConfigChangeEntity;
import java.util.List;

public interface NewPricingConfigChangeTrackingService {
  void saveAll(List<NewPricingModelConfigChangeEntity> newPricingModelConfigChangeEntity);

  void saveAllPropertyChanges(List<ConfigKeyValueConfigEntity> keyValueConfigs);
}

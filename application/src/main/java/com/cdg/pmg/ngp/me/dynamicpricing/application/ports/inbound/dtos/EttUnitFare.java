package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EttUnitFare implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  private String zoneIds;
  private String dayOfWeek;
  private Double eKm;
  private Double hr;
  private String chargeBy;
  private String taxiType;
  private Double rate;
}

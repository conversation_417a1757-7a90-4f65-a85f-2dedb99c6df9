package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import static com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant.*;
import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.GET_DRIVER_SURGE_CONFIGS_ERROR;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.*;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.DynpConfigCacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.FlatFareConfigService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.CommonUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.FlatFareConfigRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.CacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.CommonConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.ConfigKeyConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FlatFareConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FlatFareConfigCacheEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FlatFareHourRateConfCacheEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.FareTypeEnum;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.DomainException;
import com.cdg.pmg.ngp.me.dynamicpricing.utils.CacheKeyUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.utils.CollectionUtils;
import java.text.MessageFormat;
import java.util.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

@AllArgsConstructor
@ServiceComponent
@Slf4j
public class FlatFareConfigServiceImpl implements FlatFareConfigService {
  private static final String FLATFARE_KEY_CACHE_PREFIX =
      RedisKeyConstant.DYNAMIC_PRICING
          .concat(RedisKeyConstant.COLON)
          .concat(RedisKeyConstant.FLAT_FARE)
          .concat(RedisKeyConstant.COLON);
  private final DynpConfigCacheService dynpConfigCacheService;
  private final FlatFareConfigRepository flatFareConfigRepository;
  private final CacheService cacheService;

  private static final List<String> SPECIFIC_FLAT_FARE_CONFIGS =
      List.of(
          RedisKeyConstant.MAX_FLATFARE_CAP,
          RedisKeyConstant.PAX_SURGE_INDICATOR_THRESHOLD,
          RedisKeyConstant.PAX_SURGE_INDICATOR_THRESHOLD_0,
          RedisKeyConstant.LIMO_FLAT_FARE_VEH_IDS,
          RedisKeyConstant.EST_LIMO_FLAT_FARE_VEH_IDS,
          RedisKeyConstant.EST_FARE_VEH_GROUP_IDS,
          RedisKeyConstant.ADVANCE_VEH_GROUP_IDS,
          RedisKeyConstant.VEH_GRP_SHOW_METER_ONLY,
          RedisKeyConstant.VEH_GRP_SHOW_FF_ONLY,
          RedisKeyConstant.DYNAMIC_PRICING_ENABLED,
          RedisKeyConstant.DYNAMIC_PRICING_VEH_GROUP_IDS,
          RedisKeyConstant.CACHE_TIMER_MINS_BREAKDOWN_FLATFARE,
          RedisKeyConstant.CACHE_TIMER_MINS_MULTI_FLATFARE,
          RedisKeyConstant.MULTI_DESTINATION_ADDITIONAL_SURCHARGE);

  private static final List<String> SPECIFIC_LIVE_TRAFFIC_CONFIGS =
      List.of(
          RedisKeyConstant.EST_LIVE_TRAFFIC_DURATION_UNIT,
          RedisKeyConstant.EST_LIVE_TRAFFIC_DURATION_RATE,
          RedisKeyConstant.EST_LIVE_TRAFFIC_FLAG_DOWN_RATE,
          RedisKeyConstant.EST_LIVE_TRAFFIC_TIER_1_PER_COUNT_FARE,
          RedisKeyConstant.EST_LIVE_TRAFFIC_TIER_1_PER_COUNT_METER,
          RedisKeyConstant.EST_LIVE_TRAFFIC_TIER_1_START_DISTANCE,
          RedisKeyConstant.EST_LIVE_TRAFFIC_TIER_1_END_DISTANCE,
          RedisKeyConstant.EST_LIVE_TRAFFIC_TIER_2_PER_COUNT_FARE,
          RedisKeyConstant.EST_LIVE_TRAFFIC_TIER_2_PER_COUNT_METER,
          RedisKeyConstant.EST_LIVE_TRAFFIC_TIER_2_START_DISTANCE,
          RedisKeyConstant.EST_LIVE_TRAFFIC_TOTAL_FARE_ESTIMATE_LF,
          RedisKeyConstant.EST_LIVE_TRAFFIC_TOTAL_FARE_ESTIMATE_RT,
          RedisKeyConstant.EST_LIMO_LIVE_TRAFFIC_DURATION_UNIT,
          RedisKeyConstant.EST_LIMO_LIVE_TRAFFIC_DURATION_RATE,
          RedisKeyConstant.EST_LIMO_LIVE_TRAFFIC_FLAG_DOWN_RATE,
          RedisKeyConstant.EST_LIMO_LIVE_TRAFFIC_TIER_1_PER_COUNT_FARE,
          RedisKeyConstant.EST_LIMO_LIVE_TRAFFIC_TIER_1_PER_COUNT_METER,
          RedisKeyConstant.EST_LIMO_LIVE_TRAFFIC_TIER_1_START_DISTANCE,
          RedisKeyConstant.EST_LIMO_LIVE_TRAFFIC_TIER_1_END_DISTANCE,
          RedisKeyConstant.EST_LIMO_LIVE_TRAFFIC_TIER_2_PER_COUNT_FARE,
          RedisKeyConstant.EST_LIMO_LIVE_TRAFFIC_TIER_2_PER_COUNT_METER,
          RedisKeyConstant.EST_LIMO_LIVE_TRAFFIC_TIER_2_START_DISTANCE,
          RedisKeyConstant.EST_LIMO_LIVE_TRAFFIC_TIER_2_END_DISTANCE,
          RedisKeyConstant.EST_LIMO_LIVE_TRAFFIC_TOTAL_FARE_ESTIMATE_LF,
          RedisKeyConstant.EST_LIMO_LIVE_TRAFFIC_TOTAL_FARE_ESTIMATE_RT);

  @Override
  public void loadAllFlatFareConfig() {

    final List<FlatFareConfig> configs = flatFareConfigRepository.getAllFlatFareConfig();
    if (CollectionUtils.isEmpty(configs)) {
      log.info("Can't load flat fare configs. The flat fare configs is empty!");
      return;
    }

    final var flatFareConfCache = new FlatFareConfigCacheEntity();
    final var estLiveTrafficConfCache = new FlatFareHourRateConfCacheEntity();
    final var estLimoLiveTrafficConfCache = new FlatFareHourRateConfCacheEntity();

    for (FlatFareConfig config : configs) {
      this.handleFlatFareConfigByKey(
          config, flatFareConfCache, estLiveTrafficConfCache, estLimoLiveTrafficConfCache);
    }

    this.putFlatFareConfigsToCache(flatFareConfCache);
    this.putHourRateConfigToCache(
        RedisKeyConstant.EST_LIVE_TRAFFIC_KEY_PREFIX, estLiveTrafficConfCache);
    this.putHourRateConfigToCache(
        RedisKeyConstant.EST_LIMO_LIVE_TRAFFIC_KEY_PREFIX, estLimoLiveTrafficConfCache);
    log.info("Loaded flat fare configs to cache complete!");
  }

  @Override
  public FlatFareConfigSet collectFlatFareConfigSet(FareTypeEnum fareType) {
    final List<Map<String, String>> peakHourRate =
        dynpConfigCacheService.getMultiMapConfigsFromCache(
            CacheKeyUtils.getFlatFarePrefix(fareType) + PEAK_HOUR_WC);

    final List<Map<String, String>> midnightHoursRates =
        dynpConfigCacheService.getMultiMapConfigsFromCache(
            CacheKeyUtils.getFlatFarePrefix(fareType) + RedisKeyConstant.MID_NIGHT_WC);

    final String flagDownRate =
        dynpConfigCacheService.getValue(
            CacheKeyUtils.getFlatFarePrefix(fareType) + RedisKeyConstant.FLAG_DOWN_RATE,
            String.class);

    final TierFare tier1 = getTierFareSet(fareType, 1);

    final TierFare tier2 = getTierFareSet(fareType, 2);

    final EstimateRateConfig estimateRateConfig = getEstimateRateConfigSet(fareType);

    final String durationUnitConfig =
        dynpConfigCacheService.getValue(
            CacheKeyUtils.getFlatFarePrefix(fareType) + DURATION_UNIT, String.class);

    final String durationRateConfig =
        dynpConfigCacheService.getValue(
            CacheKeyUtils.getFlatFarePrefix(fareType) + DURATION_RATE, String.class);

    final String maxFlatFareCap =
        dynpConfigCacheService.getValue(
            CacheKeyUtils.getFlatFarePrefix() + MAX_FLATFARE_CAP, String.class);

    final String multiStopSurcharge =
        dynpConfigCacheService.getValue(
            CacheKeyUtils.getFlatFarePrefix() + MULTI_DESTINATION_ADDITIONAL_SURCHARGE,
            String.class);

    final List<Double> additionalChargeList = this.getFlatFareAdditionalSurchargeConfig();

    final List<EventSurgeAddressConfig> eventSurgeAddressConfigList =
        this.getListEventSurgeAddressConfig();

    return FlatFareConfigSet.builder()
        .prefixKey(fareType.getPrefix())
        .peakHoursRates(peakHourRate)
        .midnightHoursRates(midnightHoursRates)
        .flagDownRate(flagDownRate)
        .tier1Fare(tier1)
        .tier2Fare(tier2)
        .estimateRateConfig(estimateRateConfig)
        .durationUnitConfig(durationUnitConfig)
        .durationRateConfig(durationRateConfig)
        .maxFlatFareCap(maxFlatFareCap)
        .multiStopSurcharge(multiStopSurcharge)
        .additionalChargeList(additionalChargeList)
        .eventSurgeAddressConfigList(eventSurgeAddressConfigList)
        .build();
  }

  @Override
  public CommonConfigSet collectCommonConfigSet() {
    final String limoFlatFareVehIds =
        getStringValueByKey(
            DYNAMIC_PRICING_FLAT_FARE_PREFIX + RedisKeyConstant.LIMO_FLAT_FARE_VEH_IDS);

    final String estLimoFlatFareVehIds =
        getStringValueByKey(
            DYNAMIC_PRICING_FLAT_FARE_PREFIX + RedisKeyConstant.EST_LIMO_FLAT_FARE_VEH_IDS);
    final String estFareVehIds =
        getStringValueByKey(
            DYNAMIC_PRICING_FLAT_FARE_PREFIX + RedisKeyConstant.EST_FARE_VEH_GROUP_IDS);
    final String dynamicPricingVehIds =
        getStringValueByKey(
            DYNAMIC_PRICING_FLAT_FARE_PREFIX + RedisKeyConstant.DYNAMIC_PRICING_VEH_GROUP_IDS);
    final String advanceVehIds =
        getStringValueByKey(
            DYNAMIC_PRICING_FLAT_FARE_PREFIX + RedisKeyConstant.ADVANCE_VEH_GROUP_IDS);

    final String vehGrpShowMeterOnly =
        getStringValueByKey(
            DYNAMIC_PRICING_FLAT_FARE_PREFIX + FlatfareConstants.VEH_GRP_SHOW_METER_ONLY);
    final String vehGrpShowFFOnly =
        getStringValueByKey(
            DYNAMIC_PRICING_FLAT_FARE_PREFIX + FlatfareConstants.VEH_GRP_SHOW_FF_ONLY);
    final String cacheTimerMinsMultiFlatFare =
        getStringValueByKey(
            DYNAMIC_PRICING_FLAT_FARE_PREFIX + FlatfareConstants.CACHE_TIMER_MINS_MULTI_FLATFARE);
    final String cacheTimerMinsBreakdownFlatFare =
        getStringValueByKey(
            DYNAMIC_PRICING_FLAT_FARE_PREFIX
                + FlatfareConstants.CACHE_TIMER_MINS_BREAKDOWN_FLATFARE);
    final List<DriverSurgeLevelIndication> driverSurgeLevelIndications = getListDriverSurgeConfig();
    final String surgeIndicatorThreshold =
        getStringValueByKey(
            DYNAMIC_PRICING_FLAT_FARE_PREFIX + FlatfareConstants.PAX_SURGE_INDICATOR_THRESHOLD);
    final String surgeIndicatorThresholdZero =
        getStringValueByKey(
            DYNAMIC_PRICING_FLAT_FARE_PREFIX + FlatfareConstants.PAX_SURGE_INDICATOR_THRESHOLD_0);

    return CommonConfigSet.builder()
        .limoFlatFareVehIds(limoFlatFareVehIds)
        .estLimoFlatFareVehIds(estLimoFlatFareVehIds)
        .estFareVehIds(estFareVehIds)
        .dynamicPricingVehIds(dynamicPricingVehIds)
        .advanceVehIds(advanceVehIds)
        .vehGrpShowMeterOnly(vehGrpShowMeterOnly)
        .vehGrpShowFFOnly(vehGrpShowFFOnly)
        .cacheTimerMinsMultiFlatFare(cacheTimerMinsMultiFlatFare)
        .cacheTimerMinsBreakdownFlatFare(cacheTimerMinsBreakdownFlatFare)
        .driverSurgeLevelIndications(driverSurgeLevelIndications)
        .surgeIndicatorThreshold(surgeIndicatorThreshold)
        .surgeIndicatorThresholdZero(surgeIndicatorThresholdZero)
        .build();
  }

  private EstimateRateConfig getEstimateRateConfigSet(final FareTypeEnum fareType) {
    final String totalFareEstimateLF =
        dynpConfigCacheService.getValue(
            CacheKeyUtils.getFlatFarePrefix(fareType) + TOTAL_FARE_ESTIMATE_LF, String.class);

    final String totalFareEstimateRT =
        dynpConfigCacheService.getValue(
            CacheKeyUtils.getFlatFarePrefix(fareType) + TOTAL_FARE_ESTIMATE_RT, String.class);

    return EstimateRateConfig.builder()
        .totalFareEstimateLF(totalFareEstimateLF)
        .totalFareEstimateRT(totalFareEstimateRT)
        .build();
  }

  private TierFare getTierFareSet(final FareTypeEnum fareTypeEnum, final int tierNo) {
    final String tierPerCountFare =
        dynpConfigCacheService.getValue(
            CacheKeyUtils.getFlatFareTierPrefix(fareTypeEnum, tierNo)
                + RedisKeyConstant.PER_COUNT_FARE,
            String.class);

    final String tierPerCountMeter =
        dynpConfigCacheService.getValue(
            CacheKeyUtils.getFlatFareTierPrefix(fareTypeEnum, tierNo)
                + RedisKeyConstant.PER_COUNT_METER,
            String.class);

    final String tierStartDistance =
        dynpConfigCacheService.getValue(
            CacheKeyUtils.getFlatFareTierPrefix(fareTypeEnum, tierNo) + START_DISTANCE,
            String.class);

    final String tierEndDistance =
        dynpConfigCacheService.getValue(
            CacheKeyUtils.getFlatFareTierPrefix(fareTypeEnum, tierNo) + END_DISTANCE, String.class);

    return TierFare.builder()
        .perCountMeter(tierPerCountMeter)
        .perCountFare(tierPerCountFare)
        .startDistance(tierStartDistance)
        .endDistance(tierEndDistance)
        .build();
  }

  @Override
  public String getStringValueByKey(final String key) {
    return dynpConfigCacheService.getObjectValueByPattern(key, String.class);
  }

  @Override
  public List<LocationSurchargeConfig> getLocationSurchargeConfig(String key) {
    return dynpConfigCacheService.getListLocSurcharge(key, LocationSurchargeConfig.class);
  }

  @Override
  public List<Double> getFlatFareAdditionalSurchargeConfig() {
    return dynpConfigCacheService.getListDoubleConfigsFromCache(
        CacheKeyUtils.getFlatFarePrefix() + ADDITIONAL_CHARGE_WC,
        FlatfareConstants.ADDITIONAL_CHARGE_TYPE);
  }

  @Override
  public List<DriverSurgeLevelIndication> getListDriverSurgeConfig() {
    String key =
        CacheKeyUtils.getFlatFarePrefix() + RedisKeyConstant.DRIVER_SURGE_LEVEL_INDICATION_WC;
    List<DriverSurgeLevelIndication> driverSurges = new ArrayList<>();

    try {
      List<Map<String, String>> listMapResult =
          dynpConfigCacheService.getMultiMapConfigsFromCache(key);
      for (int i = 0; i < listMapResult.size(); i++) {
        Map<String, String> mapConfig = listMapResult.get(i);
        DriverSurgeLevelIndication config =
            DriverSurgeLevelIndication.builder()
                .effectTimeFromTs(
                    Long.parseLong(mapConfig.get(i + FlatfareConstants.DRV_EFFECT_FROM_TS)))
                .effectTimeToTs(
                    Long.parseLong(mapConfig.get(i + FlatfareConstants.DRV_EFFECT_TO_TS)))
                .colorHex(mapConfig.get(i + FlatfareConstants.DRV_SURGE_COLOR_HEX))
                .surgeLevel(Integer.parseInt(mapConfig.get(i + FlatfareConstants.DRV_SURGE_LEVEL)))
                .percFrom(
                    Double.parseDouble(mapConfig.get(i + FlatfareConstants.DRV_SURGE_PERC_FROM)))
                .percTo(Double.parseDouble(mapConfig.get(i + FlatfareConstants.DRV_SURGE_PERC_TO)))
                .build();
        driverSurges.add(config);
      }
    } catch (NumberFormatException | NullPointerException e) {
      log.error("Error to get driver surge config key={}", key);
      throw new DomainException(
          GET_DRIVER_SURGE_CONFIGS_ERROR.getMessage(),
          GET_DRIVER_SURGE_CONFIGS_ERROR.getErrorCode());
    }
    return driverSurges;
  }

  @Override
  public List<FlatFareHoliday> getListHoliday() {
    return cacheService.getListValue(
        DYNAMIC_PRICING + COLON + COMPANY_HOLIDAY, FlatFareHoliday.class);
  }

  @Override
  public List<EventSurgeAddressConfig> getListEventSurgeAddressConfig() {
    final String key = CacheKeyUtils.getFlatFarePrefix() + RedisKeyConstant.EVENT_SURGE_ADDRESS;
    List<EventSurgeAddressConfig> eventSurgeAddressConfigs = new ArrayList<>();

    try {
      final List<Map<String, String>> listMapResult =
          dynpConfigCacheService.getMultiMapConfigsFromCache(key);
      if (CollectionUtils.isNotEmpty(listMapResult)) {
        eventSurgeAddressConfigs =
            listMapResult.stream()
                .map(this::mapToEventSurgeAddressConfig)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .toList();
      }
    } catch (Exception e) {
      log.error("getListEventSurgeAddressConfig error {}", e.getMessage());
    }
    log.info("getListEventSurgeAddressConfig size={}", eventSurgeAddressConfigs.size());
    return eventSurgeAddressConfigs;
  }

  private Optional<EventSurgeAddressConfig> mapToEventSurgeAddressConfig(
      Map<String, String> mapConfig) {
    final EventSurgeAddressConfig eventSurgeAddressConfig = new EventSurgeAddressConfig();
    if (ObjectUtils.isEmpty(mapConfig)) {
      return Optional.empty();
    }
    Map.Entry<String, String> anyConfig = mapConfig.entrySet().iterator().next();
    eventSurgeAddressConfig.setConfigIndex(CommonUtils.getIndexConfigByKeyName(anyConfig.getKey()));

    mapConfig.forEach(
        (key, value) -> {
          final String keyWithoutIndex = CommonUtils.getConfigNameWithoutLastIndex(key);
          eventSurgeAddressConfig.setConfig(keyWithoutIndex, value);
        });
    if (!eventSurgeAddressConfig.isValidConfig()) {
      final String errorMsg =
          MessageFormat.format(
              "Error at getListEventSurgeAddressConfig index={0} errorConfigs={1}",
              eventSurgeAddressConfig.getConfigIndex(),
              eventSurgeAddressConfig.getInvalidConfigMsg());
      log.error(errorMsg);
      return Optional.empty();
    }
    return Optional.of(eventSurgeAddressConfig);
  }

  private void handleFlatFareConfigByKey(
      FlatFareConfig config,
      FlatFareConfigCacheEntity ffConfig,
      FlatFareHourRateConfCacheEntity estLiveTrafficConfCache,
      FlatFareHourRateConfCacheEntity estLimoLiveTrafficConfCache) {
    final var key = config.getParamKey();

    if (StringUtils.startsWith(key, ConfigKeyConstant.EVENT_SURGE_ADDR)) {
      this.handleEventSurgeAddresses(config, ffConfig);
      return;
    }

    if (StringUtils.startsWith(key, ConfigKeyConstant.EVENT_SURGE_ZONE)) {
      this.handleEventSurgeZones(config, ffConfig);
      return;
    }

    if (StringUtils.startsWith(key, ConfigKeyConstant.ADDITIONAL_CHARGE)) {
      this.handleAdditionalCharges(config, ffConfig);
      return;
    }

    if (StringUtils.endsWith(key, ConfigKeyConstant.DYNAMIC_PRICING_START_TIME)
        || StringUtils.endsWith(key, ConfigKeyConstant.DYNAMIC_PRICING_END_TIME)) {
      this.handleDynamicPriceScheduler(config, ffConfig);
      return;
    }

    if (StringUtils.endsWith(key, ConfigKeyConstant.DRV_SURGE_LEVEL)
        || StringUtils.endsWith(key, ConfigKeyConstant.DRV_SURGE_PERC_FROM)
        || StringUtils.endsWith(key, ConfigKeyConstant.DRV_SURGE_PERC_TO)
        || StringUtils.endsWith(key, ConfigKeyConstant.DRV_EFFECT_FROM_TS)
        || StringUtils.endsWith(key, ConfigKeyConstant.DRV_EFFECT_TO_TS)
        || StringUtils.endsWith(key, ConfigKeyConstant.DRV_SURGE_COLOR_HEX)) {
      this.handleDriverSurgeLevel(config, ffConfig);
      return;
    }

    if (StringUtils.startsWith(key, ConfigKeyConstant.EST_LIVE_TRAFFIC_PREFIX)) {
      final String prefix = ConfigKeyConstant.EST_LIVE_TRAFFIC_PREFIX;
      this.handleFlatFareHoursRatesConfig(config, prefix, estLiveTrafficConfCache);
      return;
    }

    if (StringUtils.startsWith(key, ConfigKeyConstant.EST_LIMO_LIVE_TRAFFIC_PREFIX)) {
      final String prefix = ConfigKeyConstant.EST_LIMO_LIVE_TRAFFIC_PREFIX;
      this.handleFlatFareHoursRatesConfig(config, prefix, estLimoLiveTrafficConfCache);
      return;
    }

    if (StringUtils.startsWith(key, ConfigKeyConstant.EST_WAIT_TIME_PREFIX)
        || StringUtils.startsWith(key, ConfigKeyConstant.LIVE_TRAFFIC_PREFIX)) {

      return;
    }

    if (CollectionUtils.contains(SPECIFIC_FLAT_FARE_CONFIGS, key)
        || StringUtils.equals(key, ConfigKeyConstant.IS_LIVE_TRAFFIC)) {
      cacheService.setValue(FLATFARE_KEY_CACHE_PREFIX + key, config.getParamValue());
      return;
    }

    final var otherConfig = this.getOtherFlatFareConf(config, ffConfig.getOtherConfigs());
    ffConfig.setOtherConfigs(otherConfig);
  }

  private void handleEventSurgeAddresses(
      final FlatFareConfig config, final FlatFareConfigCacheEntity entity) {
    final Map<String, Map<String, String>> eventSurgeAddresses =
        this.getFlatFareConfMapInCache(config, entity.getEventSurgeAddresses(), true);
    entity.setEventSurgeAddresses(eventSurgeAddresses);
  }

  private void handleEventSurgeZones(
      final FlatFareConfig config, final FlatFareConfigCacheEntity entity) {
    final Map<String, Map<String, String>> eventSurgeZones =
        this.getFlatFareConfMapInCache(config, entity.getEventSurgeZones(), true);
    entity.setEventSurgeZones(eventSurgeZones);
  }

  private void handleAdditionalCharges(
      final FlatFareConfig config, final FlatFareConfigCacheEntity entity) {
    final Map<String, Map<String, String>> additionalCharges =
        this.getFlatFareConfMapInCache(config, entity.getAdditionalCharges(), true);
    entity.setAdditionalCharges(additionalCharges);
  }

  private void handleDynamicPriceScheduler(
      final FlatFareConfig config, final FlatFareConfigCacheEntity entity) {
    final Map<String, Map<String, String>> dynamicPriceSchedulers =
        this.getFlatFareConfMapInCache(config, entity.getDynamicPriceSchedulers(), false);
    entity.setDynamicPriceSchedulers(dynamicPriceSchedulers);
  }

  private void handleDriverSurgeLevel(FlatFareConfig config, FlatFareConfigCacheEntity entity) {
    final Map<String, Map<String, String>> driverSurgeLevelIndications =
        this.getFlatFareConfMapInCache(config, entity.getDriverSurgeLevelIndications(), false);
    entity.setDriverSurgeLevelIndications(driverSurgeLevelIndications);
  }

  private Map<String, Map<String, String>> getFlatFareConfMapInCache(
      final FlatFareConfig config,
      Map<String, Map<String, String>> configsMapByType,
      boolean isPrefix) {
    final String[] splitKey = config.getParamKey().split(CommonConstant.UNDERSCORE);
    final String index = isPrefix ? splitKey[splitKey.length - 1] : splitKey[0];

    if (Objects.isNull(configsMapByType)) {
      configsMapByType = new TreeMap<>();
    }
    final Map<String, String> configsMapOfIndex =
        Optional.ofNullable(configsMapByType.get(index)).orElse(new TreeMap<>());
    configsMapOfIndex.put(config.getParamKey(), config.getParamValue());

    configsMapByType.put(index, configsMapOfIndex);
    return configsMapByType;
  }

  private Map<String, String> getOtherFlatFareConf(
      final FlatFareConfig config, Map<String, String> otherConfigs) {
    if (Objects.isNull(otherConfigs)) {
      otherConfigs = new TreeMap<>();
    }
    otherConfigs.put(config.getParamKey(), config.getParamValue());
    return otherConfigs;
  }

  private void handleFlatFareHoursRatesConfig(
      final FlatFareConfig config,
      final String prefix,
      final FlatFareHourRateConfCacheEntity hourRateConfig) {

    if (Objects.isNull(config)) {
      return;
    }
    final String key = config.getParamKey();

    if (StringUtils.startsWith(key, prefix + ConfigKeyConstant.PEAK_HOUR_PREFIX)) {
      final Map<String, Map<String, String>> peakHourRate =
          this.getFlatFareConfMapInCache(config, hourRateConfig.getPeakHoursRates(), true);
      hourRateConfig.setPeakHoursRates(peakHourRate);
      return;
    }

    if (StringUtils.startsWith(key, prefix + ConfigKeyConstant.MIDNIGHT_PREFIX)) {
      final Map<String, Map<String, String>> midnightHourRate =
          this.getFlatFareConfMapInCache(config, hourRateConfig.getMidnightHoursRate(), true);
      hourRateConfig.setMidnightHoursRate(midnightHourRate);
      return;
    }

    if (CollectionUtils.contains(SPECIFIC_LIVE_TRAFFIC_CONFIGS, key)) {
      cacheService.setValue(FLATFARE_KEY_CACHE_PREFIX + key, config.getParamValue());
      return;
    }

    final Map<String, String> otherConfig =
        this.getOtherFlatFareConf(config, hourRateConfig.getOtherConfigs());
    hourRateConfig.setOtherConfigs(otherConfig);
  }

  private void putFlatFareConfigsToCache(final FlatFareConfigCacheEntity entity) {
    if (Objects.isNull(entity)) {
      return;
    }
    final Map<String, Map<String, String>> eventSurgeAddresses = entity.getEventSurgeAddresses();
    final Map<String, Map<String, String>> eventSurgeZones = entity.getEventSurgeZones();
    final Map<String, Map<String, String>> additionalCharges = entity.getAdditionalCharges();
    final Map<String, Map<String, String>> dynamicPriceSchedulers =
        entity.getDynamicPriceSchedulers();
    final Map<String, Map<String, String>> driverSurgeLevelIndications =
        entity.getDriverSurgeLevelIndications();
    final Map<String, String> otherConfigs = entity.getOtherConfigs();

    this.addMapConfigToCacheByKey(
        RedisKeyConstant.EVENT_SURGE_ADDRESS_KEY_PREFIX, eventSurgeAddresses);
    this.addMapConfigToCacheByKey(RedisKeyConstant.EVENT_SURGE_ZONE_KEY_PREFIX, eventSurgeZones);
    this.addMapConfigToCacheByKey(RedisKeyConstant.ADDITIONAL_CHARGE_KEY_PREFIX, additionalCharges);
    this.addMapConfigToCacheByKey(
        RedisKeyConstant.DYNP_PRICE_SCHEDULER_KEY_PREFIX, dynamicPriceSchedulers);
    this.addMapConfigToCacheByKey(
        RedisKeyConstant.DRV_SURGE_LEVEL_KEY_PREFIX, driverSurgeLevelIndications);
    cacheService.setValue(
        FLATFARE_KEY_CACHE_PREFIX.concat(RedisKeyConstant.FLATFARE_OTHER_CONF_KEY), otherConfigs);
  }

  private void putHourRateConfigToCache(
      final String keyPrefix, final FlatFareHourRateConfCacheEntity entity) {
    if (Objects.isNull(entity)) {
      return;
    }

    final Map<String, Map<String, String>> peakHoursRates = entity.getPeakHoursRates();
    final String peakHoursRatesKeyPrefix = keyPrefix + RedisKeyConstant.PEAK_HOUR_KEY;

    final Map<String, Map<String, String>> midnightHoursRate = entity.getMidnightHoursRate();
    final String midnightHoursRateKeyPrefix = keyPrefix + RedisKeyConstant.MIDNIGHT_KEY;

    final Map<String, String> otherConfig = entity.getOtherConfigs();
    final String otherConfKeyCache =
        FLATFARE_KEY_CACHE_PREFIX
            .concat(keyPrefix)
            .concat(RedisKeyConstant.FLATFARE_OTHER_CONF_KEY);

    this.addMapConfigToCacheByKey(peakHoursRatesKeyPrefix, peakHoursRates);
    this.addMapConfigToCacheByKey(midnightHoursRateKeyPrefix, midnightHoursRate);
    cacheService.setValue(otherConfKeyCache, otherConfig);
  }

  private void addMapConfigToCacheByKey(
      final String subKey, final Map<String, Map<String, String>> configs) {
    if (Objects.isNull(configs)) {
      return;
    }
    final String keyPrefix = FLATFARE_KEY_CACHE_PREFIX.concat(subKey);
    configs.forEach(
        (indexSuffix, value) -> cacheService.setValue(keyPrefix.concat(indexSuffix), value));
  }
}

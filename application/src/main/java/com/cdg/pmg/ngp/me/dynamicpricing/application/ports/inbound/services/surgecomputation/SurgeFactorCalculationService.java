package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.H3RegionSurgeEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.ModelEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.ModelSurgeDataEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.PagedSurgeValuesResult;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.SurgeValuesFilterCriteria;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Service interface for surge factor calculation. This interface defines methods for triggering the
 * calculation of surge factors.
 */
public interface SurgeFactorCalculationService {

  /**
   * Triggers the calculation of surge factors across all regions. This is an asynchronous operation
   * that initiates the calculation process.
   *
   * @return true if the calculation was successfully initiated, false otherwise
   */
  boolean calculateSurgeFactor(Instant triggerTime);

  /**
   * Get h3 region surge list by model name from cache or database
   *
   * @param modelName the model name from {@link ModelEntity}
   * @return a list of h3 region surge
   */
  List<H3RegionSurgeEntity> getH3RegionSurge(String modelName);

  /**
   * Get h3 region surge map by model name from cache or database, the key is region id, the value
   * is the surge.
   *
   * <p>{regionId, surge}
   *
   * @param modelName the model name from {@link ModelEntity}
   * @return a map of h3 region surge
   */
  default Map<Long, BigDecimal> getH3RegionSurgeMap(String modelName) {
    return getH3RegionSurge(modelName).stream()
        .collect(Collectors.toMap(H3RegionSurgeEntity::getRegionId, H3RegionSurgeEntity::getSurge));
  }

  /**
   * Get current surge values for all models from cache for monitoring purposes. This method is
   * optimized for frequent calls and uses cache-first approach.
   *
   * @return a list of model surge data with current surge values from cache
   * @deprecated Use {@link #getCurrentSurgeValuesFromCache(SurgeValuesFilterCriteria)} instead
   */
  @Deprecated
  List<ModelSurgeDataEntity> getCurrentSurgeValuesFromCache();

  /**
   * Get current surge values from cache with pagination and filtering support. This method is
   * optimized for frequent calls and handles large datasets efficiently.
   *
   * @param filterCriteria pagination and filtering criteria
   * @return paginated result with model surge data from cache
   */
  PagedSurgeValuesResult getCurrentSurgeValuesFromCache(SurgeValuesFilterCriteria filterCriteria);
}

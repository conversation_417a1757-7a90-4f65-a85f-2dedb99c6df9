package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.SurgeCalculationDto;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.SurgeCalculationStrategy;

@ServiceComponent
public class SurgeCalculationV1Strategy implements SurgeCalculationStrategy {
  @Override
  public int calculate(SurgeCalculationDto surgeCalculationDto) {
    final int currentSurge = surgeCalculationDto.getCurrentSurge();

    int surgeLow = surgeCalculationDto.getSurgeLow();
    int surgeHigh = surgeCalculationDto.getSurgeHigh();

    int excessDemand30 = surgeCalculationDto.getExcessDemand30();
    if (excessDemand30 == 0) {
      return clamp(currentSurge, surgeLow, surgeHigh);
    }

    var stepSurge =
        currentSurge >= 0
            ? surgeCalculationDto.getStepPositive()
            : surgeCalculationDto.getStepNegative();

    if (excessDemand30 > 0) {
      final int surgeChange = currentSurge + stepSurge;
      return clamp(surgeChange, surgeLow, surgeHigh);
    }

    final int surgeChange = currentSurge - stepSurge;
    return clamp(surgeChange, surgeLow, surgeHigh);
  }

  @Override
  public String getVersion() {
    return V1;
  }
}

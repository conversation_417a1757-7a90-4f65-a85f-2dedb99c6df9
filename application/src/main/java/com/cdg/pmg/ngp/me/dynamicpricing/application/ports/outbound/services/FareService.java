package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.*;
import java.util.List;
import java.util.Map;

/** The interface Fare service */
public interface FareService {
  /**
   * Get platform fee from FareService
   *
   * @param platformFeeRequest platformFeeRequest
   * @return PlatformFeeResponse List
   */
  List<PlatformFeeResponse> getPlatformFee(PlatformFeeRequest platformFeeRequest);

  /**
   * Get platform fee by list from FareService
   *
   * @param platformFeeRequest platformFeeRequest
   * @return PlatformFeeResponse List
   */
  List<PlatformFeeResponse> getPlatformFeeByList(PlatformFeeListRequest platformFeeRequest);

  /**
   * Get booking fee from FareService
   *
   * @param bookingFeeRequest bookingFeeRequest
   * @return BookingFeeResponse
   */
  BookingFeeResponse getBookingFee(BookingFeeRequest bookingFeeRequest);

  /**
   * Get booking fee by list from FareService
   *
   * @param bookingFeeListRequest bookingFeeRequest
   * @return BookingFeeResponse
   */
  BookingFeeListResponse getBookingFeeByList(BookingFeeListRequest bookingFeeListRequest);

  /**
   * Get additional charge fee config map from FareService.
   *
   * @param chargeIds list of charge id
   * @param chargeTypes list of charge type
   * @param bookingChannel booking channel
   * @return Map<String,AdditionalChargeFeeConfigResponse> key=chargeType ;
   *     value=AdditionalChargeFeeConfigResponse
   */
  Map<String, List<AdditionalChargeFeeConfigResponse>> getAdditionalChargeFeeConfigMap(
      List<Integer> chargeIds, List<String> chargeTypes, String bookingChannel);

  /**
   * Get additional charge fee config list from FareService.
   *
   * @param chargeId charge id
   * @return List<AdditionalChargeFeeConfigResponse> list of AdditionalChargeFeeConfigResponse
   */
  List<AdditionalChargeFeeConfigResponse> getAdditionalChargeFeeConfigListByChargeId(
      Integer chargeId);
}

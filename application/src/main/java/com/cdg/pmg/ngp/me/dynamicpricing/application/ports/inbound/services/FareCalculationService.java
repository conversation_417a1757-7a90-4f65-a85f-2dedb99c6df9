package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.FareUploadConfiguration;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface FareCalculationService {
  /**
   * @param currentDate
   * @param fareUploadConfiguration
   * @param productList Calculates the fare based on the given configuration and product list
   */
  void calculateFare(
      final Date currentDate,
      final FareUploadConfiguration fareUploadConfiguration,
      final Map<String, List<String>> productList);
}

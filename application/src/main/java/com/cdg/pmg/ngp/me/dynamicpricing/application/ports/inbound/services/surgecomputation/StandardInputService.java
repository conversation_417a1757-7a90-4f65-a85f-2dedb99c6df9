package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StandardInputEntity;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * Interface for standard input services. This interface defines methods for retrieving information
 * about standard inputs used in surge computation.
 */
public interface StandardInputService {

  /**
   * Get all standard input names
   *
   * @return a list of standard inputs
   */
  List<StandardInputEntity> getStandardInputs();

  /**
   * Get standard input value map. The key is region id. The value is a map of standard input name
   * and value.
   *
   * <p>{regionId, {name, value}}
   *
   * @param bookingIds the list of booking ids in the specific time range
   * @return a map of standard input values
   */
  Map<Long, Map<String, BigDecimal>> getStandardInputValueMap(List<String> bookingIds);
}

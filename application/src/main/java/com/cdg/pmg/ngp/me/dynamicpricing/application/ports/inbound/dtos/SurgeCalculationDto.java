package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos;

import static org.apache.commons.lang3.ObjectUtils.defaultIfNull;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.DemandSupplyConfigV2;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.NewPricingModelConfigEntity;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Setter
@Builder
public class SurgeCalculationDto {
  @Getter private int currentSurge;
  private DemandSupplyConfigV2 demandConfig;
  private NewPricingModelConfigEntity newPricingModelConfigEntity;

  public int getSurgeHighNew() {
    return demandConfig.getSurgeHigh()
        + defaultIfNull(newPricingModelConfigEntity.getAdditionalSurgeHigh(), 0);
  }

  public int getSupply() {
    return demandConfig.getSupply();
  }

  public double getK1() {
    return defaultIfNull(newPricingModelConfigEntity.getK1(), 0.0);
  }

  public double getK2() {
    return defaultIfNull(newPricingModelConfigEntity.getK2(), 0.0);
  }

  public double getK3() {
    return defaultIfNull(newPricingModelConfigEntity.getK3(), 0.0);
  }

  public double getK4() {
    return defaultIfNull(newPricingModelConfigEntity.getK4(), 0.0);
  }

  public double getK5() {
    return defaultIfNull(newPricingModelConfigEntity.getK5(), 0.0);
  }

  public double getK6() {
    return defaultIfNull(newPricingModelConfigEntity.getK6(), 0.0);
  }

  public double getK7() {
    return defaultIfNull(newPricingModelConfigEntity.getK7(), 0.0);
  }

  public double getUnmet15() {
    return demandConfig.getUnmet15();
  }

  public double getPreviousUnmet15() {
    return demandConfig.getPreviousUnmet15();
  }

  public int getSurgeLow() {
    return demandConfig.getSurgeLow();
  }

  public int getSurgeHigh() {
    return demandConfig.getSurgeHigh();
  }

  public int getExcessDemand15() {
    return demandConfig.getExcessDemand15();
  }

  public int getDemand15() {
    return demandConfig.getDemand15();
  }

  public int getStepPositive() {
    return demandConfig.getStepPositive();
  }

  public int getStepNegative() {
    return demandConfig.getStepNegative();
  }

  public int getExcessDemand30() {
    return demandConfig.getExcessDemand30();
  }

  public double getUnmetRate1() {
    return defaultIfNull(newPricingModelConfigEntity.getUnmetRate1(), 0.0);
  }

  public double getUnmetRate2() {
    return defaultIfNull(newPricingModelConfigEntity.getUnmetRate2(), 0.0);
  }

  public double getSurgeHighTierRate() {
    return defaultIfNull(newPricingModelConfigEntity.getSurgeHighTierRate(), 0.0);
  }

  public double getNegativeDemandSupplyDownRate() {
    return defaultIfNull(newPricingModelConfigEntity.getNegativeDemandSupplyDownRate(), 0.0);
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.Data;

@Data
public class FlatFareVO implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  private String fareId;
  private FlatFareRequest flatFareRequest;
  private double flagDown;
  private double tier1Fare;
  private double tier2Fare;
  private double waitTimeFare;
  private double peakHrFare;
  private double midNightFare;
  private double hourlySurcharge;
  private double bookingFee;
  private double totalFareBeforeSurge;
  private double meteredBaseFare;
  private double additionalSurcharge;
  private double multiDestSurcharge;
  private BigDecimal totalFare;
  private BigDecimal estimatedFareLF;
  private BigDecimal estimatedFareRT;
  private List<LocSurcharge> locSurCharge;
  private Double pricePerKm;
  private Double dpSurgePercent;
  private Double dpSurgeAmt;
  private Double dpAplydSurgeAmt;
  private Double dpBaseFareForSurge;
  private Double dpFinalFare;
  private Double actualPDTFlatFee;
  private Double dpFareAfterSurge;
  private Integer batchKey;
  private List<Double> eventSurgeCharge;
  private Double dynaDosFareCharge;
  private String pdtId;
  private boolean error;
  private boolean calculated;

  public FlatFareVO() {
    this.fareId = "";
    this.locSurCharge = new ArrayList<>();
    this.eventSurgeCharge = new ArrayList<>();
    this.dynaDosFareCharge = 0.0;
    this.pdtId = "";
    this.meteredBaseFare = 0.0;
    this.estimatedFareLF = BigDecimal.valueOf(0.0d);
    this.estimatedFareRT = BigDecimal.valueOf(0.0d);
    this.totalFare = BigDecimal.valueOf(0.0d);
    this.error = false;
    this.calculated = false;
  }

  public Double getTotalLocSurCharge() {
    Double totalAmount = 0.0;
    for (LocSurcharge locSurc : locSurCharge) {
      totalAmount += locSurc.getAmount();
    }
    return totalAmount;
  }

  public double getTotalEventSurCharge() {
    double totalAmount = 0.0;
    for (Double eventSurcharge : eventSurgeCharge) {
      if (Objects.nonNull(eventSurcharge)) {
        totalAmount += eventSurcharge;
      }
    }
    return totalAmount;
  }

  @Override
  public String toString() {
    return "FlatfareVO={"
        + "flatFareRequest="
        + (flatFareRequest != null ? flatFareRequest.toString() : null)
        + ", pdtId="
        + pdtId
        + ", flagDown="
        + flagDown
        + ", tier1Fare="
        + tier1Fare
        + ", tier2Fare="
        + tier2Fare
        + ", waitTimeFare="
        + waitTimeFare
        + ", peakHrFare="
        + peakHrFare
        + ", midNightFare="
        + midNightFare
        + ", bookingFee="
        + bookingFee
        + ", locSurCharge="
        + getTotalLocSurCharge()
        + ", eventSurgeCharge="
        + getTotalEventSurCharge()
        + ", additionalSurcharge="
        + additionalSurcharge
        + ", totalFare="
        + totalFare
        + ", meteredBaseFare="
        + meteredBaseFare
        + ", estimatedFareLF="
        + estimatedFareLF
        + ", estimatedFareRT="
        + estimatedFareRT
        + ", pricePerKm="
        + pricePerKm
        + ", dpSurgePercent="
        + dpSurgePercent
        + ", dpSurgeAmt="
        + dpSurgeAmt
        + ", dpAplydSurgeAmt="
        + dpAplydSurgeAmt
        + ", dpBaseFareForSurge="
        + dpBaseFareForSurge
        + ", dpFinalFare="
        + dpFinalFare
        + "}";
  }
}

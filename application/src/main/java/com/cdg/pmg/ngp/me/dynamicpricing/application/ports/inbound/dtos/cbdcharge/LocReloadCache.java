package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.cbdcharge;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LocReloadCache implements Serializable {
  @Serial private static final long serialVersionUID = 1L;

  private List<String> addressRefList;
}

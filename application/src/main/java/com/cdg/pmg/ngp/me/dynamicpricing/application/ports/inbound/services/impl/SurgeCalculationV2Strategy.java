package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.SurgeCalculationDto;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.SurgeCalculationStrategy;

@ServiceComponent
public class SurgeCalculationV2Strategy implements SurgeCalculationStrategy {
  @Override
  public int calculate(SurgeCalculationDto surgeCalculationDto) {
    final int currentSurge = surgeCalculationDto.getCurrentSurge();
    int surgeLow = surgeCalculationDto.getSurgeLow();
    int demand15 = surgeCalculationDto.getDemand15();
    int surgeHighNew = surgeCalculationDto.getSurgeHighNew();
    int stepPositive = surgeCalculationDto.getStepPositive();
    int stepNegative = surgeCalculationDto.getStepNegative();
    double unmet15 = surgeCalculationDto.getUnmet15();
    double unmet15PerDemand15Rate =
        calculateRate(unmet15, demand15); //  q.unmet_15 / greatest(1.0, q.demand15)
    double excessDemand15 = surgeCalculationDto.getExcessDemand15();
    double mulSurge = surgeCalculationDto.getSurgeHighTierRate() * surgeHighNew;

    /*
     when q.unmet_m1 / greatest(1.0, q.m1) >= unmet_rate_2_15 and s.surge >= greatest(0, q.surge_high_tier_rate_15 * q.surge_high_new)
     then greatest(least(s.surge + q.step_positive, q.surge_high_new), q.surge_low)

     when q.unmet_m1 / greatest(1.0, q.m1) >= unmet_rate_1_15 and s.surge >= 0 and s.surge < surge_high_tier_rate_15 * q.surge_high_new
     then greatest(least(s.surge + q.step_positive, q.surge_high_new), q.surge_low)
    */
    if ((unmet15PerDemand15Rate >= surgeCalculationDto.getUnmetRate2()
            && currentSurge >= Math.max(0, mulSurge))
        || (unmet15PerDemand15Rate >= surgeCalculationDto.getUnmetRate1()
            && currentSurge >= 0
            && currentSurge < mulSurge)) {
      return clamp(currentSurge + stepPositive, surgeLow, surgeHighNew);
    }

    /*
    when (q.unmet_m1 / greatest(1.0, q.m1) >= unmet_rate_1_15 and s.surge < 0
    then greatest(least(s.surge + q.step_negative, q.surge_high_new), q.surge_low)
     */
    if (unmet15PerDemand15Rate >= surgeCalculationDto.getUnmetRate1() && currentSurge < 0) {
      return clamp(currentSurge + stepNegative, surgeLow, surgeHighNew);
    }

    /*
    when ( q.unmet_m1 / greatest(1.0, q.m1) < unmet_rate_2_15) and s.surge >= greatest(0, surge_high_tier_rate_15 * q.surge_high_new)
    then greatest(least(s.surge - q.step_positive, q.surge_high_new), q.surge_low)
    missing this
     */
    if (unmet15PerDemand15Rate < surgeCalculationDto.getUnmetRate2()
        && currentSurge >= Math.max(0, mulSurge)) {
      return clamp(currentSurge - stepPositive, surgeLow, surgeHighNew);
    }

    /*
    when (q.unmet_m1 / greatest(1.0, q.m1) < unmet_rate_1_15) and s.surge >= 0 and s.surge < surge_high_tier_rate_15 * q.surge_high_new
    then greatest(least(s.surge - q.step_positive, q.surge_high_new), q.surge_low)
    */
    if (unmet15PerDemand15Rate < surgeCalculationDto.getUnmetRate1()
        && currentSurge >= 0
        && currentSurge < mulSurge) {
      return clamp(currentSurge - stepPositive, surgeLow, surgeHighNew);
    }

    /*
    when s.surge < 0 and q.m1 / greatest(1.0, q.total_free) <= negative_dmnd_sply_dwn_rate_15
    then greatest(least(s.surge - q.step_negative, q.surge_high_new), q.surge_low)
    */
    if (currentSurge < 0
        && calculateRate(demand15, surgeCalculationDto.getSupply())
            <= surgeCalculationDto.getNegativeDemandSupplyDownRate()) {
      return clamp(currentSurge - stepNegative, surgeLow, surgeHighNew);
    }

    return clamp(currentSurge, surgeLow, surgeHighNew);
  }

  private static double calculateRate(double unmet15, int value) {
    return unmet15 / Math.max(1.0, value);
  }

  @Override
  public String getVersion() {
    return V2;
  }
}

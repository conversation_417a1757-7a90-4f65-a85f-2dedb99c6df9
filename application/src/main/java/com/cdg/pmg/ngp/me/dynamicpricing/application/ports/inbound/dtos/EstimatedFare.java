package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EstimatedFare implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  private FlatFareVO flatAndMeterFareForLimo;
  private FlatFareVO flatfareForStandard;
  private FlatFareVO meterFareForStandard;
  private FlatFareVO dynamicPricingForStandard;
  private Date calculatedDate;
}

package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.CMSConfigItem;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.CMSConfigList;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.CreateCMSConfigRequest;

/** The interface Cms service */
public interface CMSServiceOutboundPort {
  /**
   * Get list new pricing config model
   *
   * @return list of new pricing config model
   */
  CMSConfigList getCMSBySearchText(String searchText);

  /**
   * @param id id of properties
   * @param value value to update
   */
  void updateCMSConfig(Long id, String value);

  /**
   * @param createCmsConfigRequest createCmsConfigRequest the request to create new Cms
   *     configuration
   * @return CMSConfigItem
   */
  CMSConfigItem createCmsConfig(CreateCMSConfigRequest createCmsConfigRequest);
}

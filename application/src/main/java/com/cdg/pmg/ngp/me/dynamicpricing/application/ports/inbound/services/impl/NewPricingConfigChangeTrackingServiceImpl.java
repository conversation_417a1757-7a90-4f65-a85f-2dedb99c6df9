package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.HashObjectService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.NewPricingConfigChangeTrackingService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.NewPricingModelConfigChangeRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.CommonConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.ConfigKeyValueConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.NewPricingModelConfigChangeEntity;
import java.util.List;
import lombok.RequiredArgsConstructor;

@ServiceComponent
@RequiredArgsConstructor
public class NewPricingConfigChangeTrackingServiceImpl
    implements NewPricingConfigChangeTrackingService {

  private final NewPricingModelConfigChangeRepository newPricingModelConfigChangeRepository;
  private final HashObjectService hashObjectService;

  @Override
  public void saveAll(List<NewPricingModelConfigChangeEntity> newPricingModelConfigChangeEntities) {
    if (newPricingModelConfigChangeEntities.isEmpty()) {
      return;
    }

    for (NewPricingModelConfigChangeEntity newPricingModelConfigChangeEntity :
        newPricingModelConfigChangeEntities) {
      String objectHash = hashObjectService.hashObject(newPricingModelConfigChangeEntity);
      newPricingModelConfigChangeEntity.setUniquePayloadHash(objectHash);
    }
    newPricingModelConfigChangeRepository.saveAllPricingConfigModelChanges(
        newPricingModelConfigChangeEntities);
  }

  @Override
  public void saveAllPropertyChanges(List<ConfigKeyValueConfigEntity> keyValueConfigs) {
    if (keyValueConfigs.isEmpty()) {
      return;
    }

    keyValueConfigs.forEach(
        kv ->
            kv.setUniquePayloadHash(
                hashObjectService.hashObject(
                    kv.getKey() + CommonConstant.UNDERSCORE + kv.getValue())));
    newPricingModelConfigChangeRepository.saveAllPropertyChanges(keyValueConfigs);
  }
}

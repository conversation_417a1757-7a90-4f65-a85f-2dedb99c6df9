package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.mapper.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.SurgeComputationResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.H3RegionSurgeEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.SurgeComputationModelSurgeEntity;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper
public interface SurgeFactorCalculationMapper {

  @Mapping(target = "regionId", source = "h3_region_id")
  H3RegionSurgeEntity mapToH3RegionSurgeEntity(SurgeComputationResponse.Region region);

  List<H3RegionSurgeEntity> mapToH3RegionSurgeEntity(List<SurgeComputationResponse.Region> regions);

  List<H3RegionSurgeEntity> mapFromSurgeComputationModelSurgeEntity(
      List<SurgeComputationModelSurgeEntity> regions);
}

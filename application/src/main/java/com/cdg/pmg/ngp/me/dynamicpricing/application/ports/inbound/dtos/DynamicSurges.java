package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DynamicSurges is a data transfer object (DTO) representing the surge pricing information within a
 * specified zone. This class encapsulates various data points crucial for calculating and managing
 * surge pricing based on demand and supply dynamics.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DynamicSurges implements Serializable {
  @Serial private static final long serialVersionUID = 1L;
  private String zoneId;
  private int surge;
  private int surgeLow;
  private int surgeHigh;
  private int demandRecent;
  private int demandPrevious;
  private int demandPredicted;
  private int supply;
  private int excessDemand;
  private Timestamp lastUpdDt;
  private int prevSurge;
  private int batchKey;
}

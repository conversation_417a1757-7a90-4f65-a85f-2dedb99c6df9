package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils;

import static com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant.*;
import static com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant.COLON;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.DpsProperties;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.CommonConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Iterator;
import java.util.Objects;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class CommonUtils {

  private CommonUtils() {}

  private static final Double MAX_JOURNEY_DIST_KM_BLOCK = 25.0;
  public static final String COMMA_DELIMITER = ",";
  public static final BigDecimal ONE_HUNDRED = BigDecimal.valueOf(100);

  /**
   * Check any null
   *
   * @param objs arr objs
   * @return true if any null, false if no objs null
   */
  public static boolean anyNull(Object... objs) {
    for (Object obj : objs) {
      if (Objects.isNull(obj)) {
        return true;
      }
    }
    return false;
  }

  /**
   * Round up to fifty cent
   *
   * @param fare fare in Double
   * @return fare in Double rounded up to fifty cent
   */
  public static Double roundUpToFiftyCent(Double fare) {
    double gapVal = fare % 0.5;
    return fare + (0.5 - (gapVal > 0 ? gapVal : 0.5));
  }

  /**
   * Round to two
   *
   * @param fare fare in Double
   * @return fare in Double rounded to two
   */
  public static Double roundToTwo(Double fare) {
    BigDecimal bd = BigDecimal.valueOf(fare);
    bd = bd.setScale(2, BigDecimal.ROUND_HALF_UP);
    return bd.doubleValue();
  }

  /**
   * Round to two BigDecimal
   *
   * @param fare fare in Double
   * @return fare in BigDecimal rounded to two
   */
  public static BigDecimal roundToTwoBD(Double fare) {
    BigDecimal bd = BigDecimal.valueOf(fare);
    bd = bd.setScale(2, BigDecimal.ROUND_HALF_UP);
    return bd;
  }

  public static Double roundTo(Double val, int places) {
    if (places < 0) return val;
    BigDecimal bd = BigDecimal.valueOf(val);
    bd = bd.setScale(places, BigDecimal.ROUND_HALF_UP);
    return bd.doubleValue();
  }

  /**
   * Convert meter to kilometer
   *
   * @param distanceM meter
   * @return kilometer rounded
   */
  public static Double roundMeterToKm(long distanceM) {
    if (distanceM > 0) {
      double dist = (double) distanceM / 1000;
      BigDecimal bd = BigDecimal.valueOf(dist);
      bd = bd.setScale(2, BigDecimal.ROUND_HALF_UP);
      return bd.doubleValue();
    }
    return 0.0;
  }

  /**
   * Check distance and estimated distance is between five kilometer
   *
   * @param distanceKm distance in km
   * @param estimatedKm estimated distance in km
   * @return boolean
   */
  public static boolean inBetweenFiveKM(Double distanceKm, Double estimatedKm) {
    return (distanceKm >= (estimatedKm - 5) && distanceKm < estimatedKm)
        || (estimatedKm >= MAX_JOURNEY_DIST_KM_BLOCK && distanceKm >= MAX_JOURNEY_DIST_KM_BLOCK);
  }

  /**
   * Generate Trip ID (random UUID + time)
   *
   * @param reqDate request date
   * @return tripId
   */
  public static String generateTripId(final Date reqDate) {
    String tripId = StringUtils.EMPTY;
    try {
      final DateFormat df = new SimpleDateFormat(CommonConstant.YYYYMMDDHHMMSS);
      final String reqDateString = df.format(reqDate);
      tripId = UUID.randomUUID() + reqDateString;
    } catch (Exception exception) {
      log.error("Can not create tripId for reqDate={}", reqDate);
    }
    return tripId;
  }

  /**
   * Generate Fare ID (random UUID - time - mobile)
   *
   * @param reqDate reqDate
   * @param mobile mobile
   * @return fareId
   */
  public static String generateFareId(final Date reqDate, final String mobile) {
    String fareId = StringUtils.EMPTY;
    try {
      fareId =
          UUID.randomUUID()
              + FlatfareConstants.HYPHEN
              + reqDate.getTime()
              + FlatfareConstants.HYPHEN
              + mobile;
    } catch (Exception exception) {
      log.error("Can not create fareId for reqDate={} mobile={}", reqDate, mobile);
    }
    return fareId;
  }

  /**
   * Generate Multi-Fare Cache Key (random UUID - time - mobile)
   *
   * @param fareId fareId
   * @return Multi-Fare Cache Key
   */
  public static String generateMultiFareCacheKey(final String fareId) {
    return RedisKeyConstant.DYNAMIC_PRICING
        + RedisKeyConstant.COLON
        + RedisKeyConstant.MULTI_FARE
        + RedisKeyConstant.COLON
        + fareId;
  }

  /**
   * Generate fare breakdown cache key
   *
   * @param fareId fareId
   * @param vehTypeId vehicle type id
   * @return fare breakdown cache key
   */
  public static String generateFareBreakdownKey(String fareId, int vehTypeId) {
    return DYNAMIC_PRICING
        + COLON
        + BREAKDOWN
        + COLON
        + fareId
        + FlatfareConstants.HYPHEN
        + vehTypeId;
  }

  /**
   * Get Release Version. If no version return version 1
   *
   * @param dpsProperties dpsProperties
   * @return Release Version
   */
  public static int getReleaseVersion(DpsProperties dpsProperties) {
    if (Objects.nonNull(dpsProperties)) {
      return dpsProperties.getApplicationRelease();
    } else {
      return CommonConstant.RELEASE_VER_DEFAULT;
    }
  }

  /**
   * Get Iterable from Iterator.
   *
   * @param <T> the type parameter
   * @param iterator the iterator
   * @return the iterable
   */
  public static <T> Iterable<T> getIterableFromIterator(Iterator<T> iterator) {
    return () -> iterator;
  }

  /**
   * Parse String to Double or else null
   *
   * @param string String input
   * @return Double or else null
   */
  public static Double stringToDoubleOrElseNull(String string) {
    Double doubleNumber = null;
    try {
      doubleNumber = Double.parseDouble(string);
    } catch (Exception ex) {
      log.error("Parse String to Double got error, String={}", string);
    }
    return doubleNumber;
  }

  /**
   * Get config index by key name
   *
   * @param keyName key name
   * @return index String
   */
  public static String getIndexConfigByKeyName(String keyName) {
    if (keyName == null || !keyName.contains(CommonConstant.UNDERSCORE)) {
      return null;
    }
    String[] parts = keyName.split(CommonConstant.UNDERSCORE);
    return parts[parts.length - 1];
  }

  /**
   * Get key name without index
   *
   * @param keyName key name
   * @return key name without index
   */
  public static String getConfigNameWithoutLastIndex(String keyName) {
    if (keyName == null || !keyName.contains(CommonConstant.UNDERSCORE)) {
      return "";
    }
    int lastUnderscoreIndex = keyName.lastIndexOf(CommonConstant.UNDERSCORE);
    if (lastUnderscoreIndex != -1) {
      return keyName.substring(0, lastUnderscoreIndex + 1); // Include the underscore
    }
    return keyName;
  }

  public static boolean isEquals(BigDecimal a, BigDecimal b) {
    if (Objects.isNull(a) || Objects.isNull(b)) {
      return false;
    }
    return a.compareTo(b) == 0;
  }

  public static BigDecimal toBigDecimal(String number) {
    if (StringUtils.isBlank(number)) {
      return null;
    }

    try {
      return new BigDecimal(number.trim());
    } catch (NumberFormatException e) {
      log.warn("Can not convert {} to BigDecimal", number);
      return null;
    }
  }
}

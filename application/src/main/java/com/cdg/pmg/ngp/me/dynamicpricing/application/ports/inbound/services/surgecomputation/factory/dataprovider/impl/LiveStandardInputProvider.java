package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.factory.dataprovider.impl;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.factory.ConfigurationContext;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.factory.dataprovider.ConfigurationDataProvider;
import java.math.BigDecimal;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
public class LiveStandardInputProvider implements ConfigurationDataProvider {
  private final Map<Long /* regionId */, Map<String /* name */, BigDecimal>> standardInputValueMap;

  @Override
  public BigDecimal getConfigurationData(final ConfigurationContext context) {
    if (standardInputValueMap == null) {
      log.warn(
          "[calculateSurgeFactor][LiveStandardInputProvider] The standard input value map is null");
      return null;
    }

    final String name = context.getName();
    final Long regionId = context.getRegionId();

    Map<String, BigDecimal> regionMap = standardInputValueMap.get(regionId);
    if (regionMap == null) {
      log.warn(
          "[calculateSurgeFactor][LiveStandardInputProvider] There is no standard input value for regionId: {}",
          regionId);
      return null;
    }

    BigDecimal value = regionMap.get(name);
    if (value == null) {
      log.warn(
          "[calculateSurgeFactor][LiveStandardInputProvider] There is no standard input value for regionId: {}",
          regionId);
      return null;
    }

    if (log.isDebugEnabled()) {
      log.debug(
          "[calculateSurgeFactor][LiveStandardInputProvider] Retrieved value, regionId: {}, standard input name: {}, value: {}",
          regionId,
          name,
          value);
    }
    return value;
  }
}

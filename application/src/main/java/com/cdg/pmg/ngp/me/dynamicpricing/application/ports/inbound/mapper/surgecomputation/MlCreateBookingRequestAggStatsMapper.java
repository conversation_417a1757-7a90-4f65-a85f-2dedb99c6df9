package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.mapper.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.MultiFareResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FareBreakdownDetailEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.MlCreateBookingRequestAggStatsEntity;
import java.time.Instant;
import java.time.OffsetDateTime;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

@Mapper
public interface MlCreateBookingRequestAggStatsMapper {

  @Mapping(
      target = "fareCalcTime",
      source = "fareResponse.fareCalcTime",
      qualifiedByName = "convertOffsetDateTimeToInstant")
  @Mapping(target = "estimatedTripTime", source = "fareResponse.estimatedTripTime")
  @Mapping(target = "distance", source = "fareResponse.distance")
  @Mapping(target = "bookingId", source = "fareBreakdownData.bookingId")
  @Mapping(target = "fareId", source = "fareBreakdownData.fareId")
  @Mapping(target = "areaType", source = "fareBreakdownData.areaType")
  @Mapping(target = "pickupAddressRef", source = "fareBreakdownData.pickupAddressRef")
  @Mapping(target = "destAddressRef", source = "fareBreakdownData.destAddressRef")
  @Mapping(target = "pickupRegionId", source = "fareBreakdownData.regionId")
  @Mapping(target = "regionVersion", source = "fareBreakdownData.regionVersion")
  @Mapping(target = "modelId", source = "fareBreakdownData.modelId")
  @Mapping(target = "meteredBaseFare", source = "fareBreakdownData.meteredBaseFare")
  @Mapping(target = "totalFare", source = "fareBreakdownData.totalFare")
  @Mapping(target = "dpBaseFareForSurge", source = "fareBreakdownData.dpBaseFareForSurge")
  @Mapping(target = "dpSurgePer", source = "fareBreakdownData.dpSurgePercent")
  @Mapping(target = "dpFinalFare", source = "fareBreakdownData.dpFinalFare")
  @Mapping(target = "estimatedFareLF", source = "fareBreakdownData.estimatedFareLF")
  @Mapping(target = "estimatedFareRT", source = "fareBreakdownData.estimatedFareRT")
  @Mapping(target = "meterPlatformFeeLower", source = "fareBreakdownData.meterPlatformFeeLower")
  @Mapping(target = "meterPlatformFeeUpper", source = "fareBreakdownData.meterPlatformFeeUpper")
  @Mapping(target = "flatPlatformFee", source = "fareBreakdownData.flatPlatformFee")
  @Mapping(target = "createTimestamp", expression = "java(java.time.Instant.now())")
  MlCreateBookingRequestAggStatsEntity mapToMlCreateBookingRequestAggStatsEntity(
      FareBreakdownDetailEntity fareBreakdownData, MultiFareResponse fareResponse);

  @Named("convertOffsetDateTimeToInstant")
  static Instant convertOffsetDateTimeToInstant(OffsetDateTime offsetDateTime) {
    if (offsetDateTime == null) {
      return null;
    }
    return offsetDateTime.toInstant();
  }
}

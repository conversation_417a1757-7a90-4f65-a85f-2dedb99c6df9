package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.commands.NewPricingModelCommand;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.ListConfigObject;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.NewPricingModelConfigEntity;
import java.util.List;

/** Interface of dynamic pricing service */
public interface CmsConfigService {
  List<NewPricingModelConfigEntity> getListNewPricingModelConfigEntityInCms();

  NewPricingModelConfigEntity updateNewPricingModelConfigEntityInCms(
      NewPricingModelCommand newPricingModelCommand);

  NewPricingModelConfigEntity createNewPricingConfigModelConfigEntityInCms(
      NewPricingModelCommand newPricingModelCommand);

  void addNewConfig(String descriptionKey, String value);

  ListConfigObject getConfigsByDescriptionKey(String descriptionKey);

  void deleteNewConfig(String descriptionKey, String value);
}

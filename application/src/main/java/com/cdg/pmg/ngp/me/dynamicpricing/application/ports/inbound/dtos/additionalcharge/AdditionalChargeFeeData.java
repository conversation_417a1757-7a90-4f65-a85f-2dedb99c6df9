package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.additionalcharge;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * This bean contains two part data 1. calculated additional charge chargeAmt (1) chargeAmt: the
 * chargeAmt for totalFare
 *
 * <p>2. additional charge config from ngp-me-fare-svc table "additional_charge_fee_config" (1)
 * chargeId (2) chargeType (3) chargeThreshold (4) chargeUpperLimit (5) chargeLowerLimit
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdditionalChargeFeeData implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  private Integer chargeId;
  private String chargeType;
  private BigDecimal chargeAmt;
  private BigDecimal chargeThreshold;
  private BigDecimal chargeUpperLimit;
  private BigDecimal chargeLowerLimit;
}

package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class RouteInfo implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  private String tripId;
  private long routingDistance;
  private long ett;
  private String encodedPolyline;
  private String pickupAddressRef;
  private double pickupAddressLat;
  private double pickupAddressLng;
  private String pickupZoneId;
  private String destAddressRef;
  private double destAddressLat;
  private double destAddressLng;
  private String destZoneId;
  private String intermediateAddrRef;
  private double intermediateAddrLat;
  private double intermediateAddrLng;
  private String intermediateZoneId;
}

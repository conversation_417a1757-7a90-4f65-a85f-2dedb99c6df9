package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.factory.dataprovider.impl;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.factory.ConfigurationContext;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.factory.dataprovider.ConfigurationDataProvider;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.CommonUtils;
import java.math.BigDecimal;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
public class StaticRegionBasedConfigurationProvider implements ConfigurationDataProvider {
  private final Map<String /* name */, Map<Long /* regionId */, String>> staticRegionConfig;

  @Override
  public BigDecimal getConfigurationData(final ConfigurationContext context) {
    if (staticRegionConfig == null) {
      log.warn(
          "[calculateSurgeFactor][StaticRegionBasedConfigurationProvider] The static region based configuration is null");
      return null;
    }

    final String name = context.getName();
    final Long regionId = context.getRegionId();

    Map<Long, String> regionMap = staticRegionConfig.get(name);
    if (regionMap == null) {
      log.warn(
          "[calculateSurgeFactor][StaticRegionBasedConfigurationProvider] There is no static region based configuration for name: {}",
          name);
      return null;
    }

    BigDecimal value = CommonUtils.toBigDecimal(regionMap.get(regionId));
    if (value == null) {
      log.warn(
          "[calculateSurgeFactor][StaticRegionBasedConfigurationProvider] There is no static region based configuration for name: {} and regionId: {}",
          name,
          regionId);
      return null;
    }

    if (log.isDebugEnabled()) {
      log.debug(
          "[calculateSurgeFactor][StaticRegionBasedConfigurationProvider] Retrieved value, config name: {}, regionId: {}, value: {}",
          name,
          regionId,
          value);
    }
    return value;
  }
}

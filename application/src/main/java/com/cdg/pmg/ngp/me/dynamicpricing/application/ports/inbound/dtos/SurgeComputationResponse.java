package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

@Getter
@AllArgsConstructor
public class SurgeComputationResponse {

  private Long modelId;
  private String modelName;

  private String endpointUrl;
  private HttpResponse response;

  // {
  //   "request_id": "string",
  //   "model_name": "string",
  //   "request_time": "2025-05-09T03:45:03.070Z",
  //   "results": [
  //     {
  //       "h3_region_id": 0,
  //       "surge": 0
  //     }
  //   ]
  // }
  @Data
  public static class HttpResponse {
    private String request_id;
    private String model_name;
    private OffsetDateTime request_time;
    private List<Region> results;
  }

  @Data
  public static class Region {
    private Long h3_region_id;
    private BigDecimal surge;
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BookingFare implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  private String productId;
  private String vehTypeId;
  private String tariffTypeCode;
  private Double fareAmt;
  private Double levyAmt;
  private LocalTime startTime;
  private LocalTime endTime;
  private String applicableDays;

  public boolean isValidValue() {
    boolean isValid = true;
    if (productId == null || vehTypeId == null || tariffTypeCode == null || fareAmt == null) {
      isValid = false;
    }
    return isValid;
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import static com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants.EST_STANDARD_FLAT_FARE;
import static com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants.LIMO_FLAT_FARE;
import static com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants.NORMAL_FLATFARE_PDT_ID;
import static com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants.STANDARD_FLAT_FARE;
import static com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants.STD_PDT_ID;
import static com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.DateUtils.isHolidaySingTime;
import static com.cdg.pmg.ngp.me.dynamicpricing.constants.BookARideConfigsConstant.COMMA;
import static com.cdg.pmg.ngp.me.dynamicpricing.constants.BookARideConfigsConstant.ZERO_VALUE;
import static com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant.*;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.CommonConfigSet;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.DynamicSurges;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareHoliday;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.mapper.DynamicSurgesMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.mapper.LocationSurchargeMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.BookARideConfigService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.DynpConfigCacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.FlatFareConfigService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.flatfare.impl.FlatFareManager;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.BookingFareUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.DateUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.BookingFeeRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.BookingFeeResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.CacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.FareService;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.BookARideConfigsConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.DynamicSurgesEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FareUploadConfiguration;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.LocationSurchargeConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.VGProductFareBean;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.ProductTypeEnum;
import java.time.OffsetDateTime;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@AllArgsConstructor
@ServiceComponent
@Slf4j
public class BookARideConfigServiceImpl implements BookARideConfigService {
  private final DynpConfigCacheService dynpConfigCacheService;
  private final CacheService cacheService;
  private final DynamicSurgesMapper dynamicSurgesMapper;
  private final FlatFareConfigService configService;
  private final FareService fareService;
  private final FlatFareManager flatFareManager;
  private final LocationSurchargeMapper locationSurchargeMapper;

  @Override
  public List<String> getHolidayConfigs() {
    final String key =
        FlatfareConstants.DYNAMIC_PRICING
            + FlatfareConstants.COLON
            + FlatfareConstants.COMPANY_HOLIDAY;
    return dynpConfigCacheService.getListObjectConfigFromCache(key, FlatFareHoliday.class).stream()
        .map(FlatFareHoliday::getDate)
        .toList();
  }

  @Override
  public List<DynamicSurgesEntity> getDynpSurges() {
    final String key =
        RedisKeyConstant.DYNAMIC_PRICING
            .concat(RedisKeyConstant.COLON)
            .concat(RedisKeyConstant.DYNP_SURGES);
    return cacheService.getListValue(key, DynamicSurges.class).stream()
        .map(dynamicSurgesMapper::mapToEntity)
        .toList();
  }

  @Override
  public List<LocationSurchargeConfigEntity> getLocationSurchargeConfigs(
      final Date currentDate, final List<String> holidayList) {
    final String dayInWeekReq = DateUtils.toDayOfWeekShortUpperCase(currentDate);

    final boolean isHoliday =
        isHolidaySingTime(currentDate, DateUtils.mapToListFlatFareHoliday(holidayList));

    final List<
            com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos
                .LocationSurchargeConfig>
        locSurchargeDayInWeekConfigs =
            configService.getLocationSurchargeConfig(
                FlatfareConstants.LOC_SURC_KEY_PREFIX + dayInWeekReq);

    final List<
            com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos
                .LocationSurchargeConfig>
        locSurchargeHolidayConfigs =
            isHoliday
                ? configService.getLocationSurchargeConfig(
                    FlatfareConstants.LOC_SURC_KEY_PREFIX + FlatfareConstants.HOL)
                : Collections.emptyList();

    return Stream.concat(locSurchargeDayInWeekConfigs.stream(), locSurchargeHolidayConfigs.stream())
        .filter(Objects::nonNull)
        .map(locationSurchargeMapper::mapToLocationSurchargeConfigEntity)
        .filter(LocationSurchargeConfigEntity::isValidConfig)
        .filter(
            locationSurchargeConfig ->
                DateUtils.isBetween(
                    DateUtils.convertToLocalTime(currentDate),
                    DateUtils.convertToLocalTime(locationSurchargeConfig.getStartTime()),
                    DateUtils.convertToLocalTime(locationSurchargeConfig.getEndTime())))
        .toList();
  }

  @Override
  public Map<String, String> getFlatFareConfigs() {
    final String key =
        RedisKeyConstant.DYNAMIC_PRICING
            .concat(RedisKeyConstant.COLON)
            .concat(RedisKeyConstant.FLAT_FARE)
            .concat(RedisKeyConstant.COLON)
            .concat(RedisKeyConstant.WILDCARD);
    return dynpConfigCacheService.getMultiMapConfigsAndSingleValueFromCache(key);
  }

  @Override
  public void getBookingFareConfigs(
      final FareUploadConfiguration fareUploadConfiguration, final Date reqDate) {
    final boolean isHoliday =
        isHolidaySingTime(
            reqDate,
            DateUtils.mapToListFlatFareHoliday(fareUploadConfiguration.getHolidayConfig()));
    final Map<String, String> bookARideConfigs =
        fareUploadConfiguration.getBookARideConfiguration();
    final String[] cdgProductList = getProductList(bookARideConfigs);
    final String[] cdgVehicleGroupList = getVehicleGroupList(bookARideConfigs);
    BookingFeeResponse bookingFeeResponse = BookingFeeResponse.builder().bookingFee(0.0).build();
    final OffsetDateTime offsetDateTimeOfReqDate =
        DateUtils.convertFromDateToOffsetDateTime(reqDate);
    for (String vehicleGroupId : cdgVehicleGroupList) {
      for (String productId : cdgProductList) {
        final String fareType = determineFareType(vehicleGroupId, productId);
        if (!fareType.isEmpty()) {
          final BookingFeeRequest bookingFeeRequest =
              BookingFareUtils.createBookingFareRequest(
                  vehicleGroupId,
                  fareType,
                  isHoliday,
                  offsetDateTimeOfReqDate,
                  productId,
                  FlatfareConstants.JOB_TYPE_CJ);
          bookingFeeResponse = fareService.getBookingFee(bookingFeeRequest);
          try {
            VGProductFareBean vGProductFareBean =
                fareUploadConfiguration
                    .getVgProductFareMap()
                    .get(Integer.parseInt(vehicleGroupId))
                    .get(productId);
            vGProductFareBean.setBookingFee(bookingFeeResponse.getBookingFee());
            log.info(
                "Set booking fee for vehicleGroupId {}, productId {}, bookingFee {}",
                vehicleGroupId,
                productId,
                bookingFeeResponse.getBookingFee());
          } catch (Exception e) {
            log.error(
                "Error to set booking fee for vehicleGroupId {} and productId {}, error {}",
                vehicleGroupId,
                productId,
                e.getMessage());
          }
        } else {
          log.info(
              "Fare type is empty for vehicleGroupId {}, productId {}, bookingFee {}",
              vehicleGroupId,
              productId,
              bookingFeeResponse.getBookingFee());
        }
      }
    }
  }

  private String[] getProductList(final Map<String, String> bookARideConfigs) {
    final String cdgProductConfig =
        bookARideConfigs.get(BookARideConfigsConstant.BOOK_RIDE_PRODUCT);
    return cdgProductConfig != null && !cdgProductConfig.isEmpty()
        ? cdgProductConfig.split(COMMA)
        : new String[] {
          BookARideConfigsConstant.COMFORT_RIDE_PRODUCT, BookARideConfigsConstant.METER_FARE_PRODUCT
        };
  }

  private String[] getVehicleGroupList(final Map<String, String> bookARideConfigs) {
    String cdgVehicleGroup = bookARideConfigs.get(BookARideConfigsConstant.BOOK_RIDE_VEHICLE_GROUP);
    return cdgVehicleGroup != null && !cdgVehicleGroup.isEmpty()
        ? cdgVehicleGroup.split(",")
        : new String[] {ZERO_VALUE};
  }

  private String determineFareType(final String vehicleGroupId, final String productId) {
    // Get Common Config
    final CommonConfigSet commonConfigSet =
        cacheService.getValue(CACHE_KEY_COMMON_CONFIG_SET, CommonConfigSet.class);

    String productIdConverted = StringUtils.EMPTY;
    if (ProductTypeEnum.METER_FARE_PRODUCT.getProductType().equalsIgnoreCase(productId)) {
      productIdConverted = ProductTypeEnum.METER_FARE_PRODUCT.getProductCode();
    }
    if (ProductTypeEnum.COMFORT_RIDE_PRODUCT.getProductType().equalsIgnoreCase(productId)) {
      productIdConverted = ProductTypeEnum.METER_FARE_PRODUCT.getProductCode();
    }
    try {
      if (flatFareManager.isFlatLimoType(Integer.parseInt(vehicleGroupId), commonConfigSet)) {
        return LIMO_FLAT_FARE;
      }
      if (NORMAL_FLATFARE_PDT_ID.equalsIgnoreCase(productIdConverted)) {
        return STANDARD_FLAT_FARE;
      }
      if (STD_PDT_ID.equalsIgnoreCase(productIdConverted)) {
        return EST_STANDARD_FLAT_FARE;
      }
    } catch (Exception e) {
      log.error(
          "Error to determine fare type with vehicleGroupId {} and productId {}",
          vehicleGroupId,
          productId);
    }
    return StringUtils.EMPTY;
  }
}

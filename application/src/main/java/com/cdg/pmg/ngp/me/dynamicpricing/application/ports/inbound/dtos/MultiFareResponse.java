package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.ValidateFareEntity;
import java.io.Serial;
import java.io.Serializable;
import java.time.OffsetDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MultiFareResponse implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  private String fareId;
  private String pickupAddressRef;
  private String destAddressRef;
  private String intermediateAddrRef;
  private String mobile;
  private String countryCode;
  private OffsetDateTime fareCalcTime;
  private long estimatedTripTime;
  private long distance;
  private String encodedPolyline;
  private String tripId;
  private Integer fareExpiredIn;
  private Date calculatedDate;
  private List<FlatFareVOPart> flatFareVOParts;

  // These 4 fields below only used for display, does not support any business, and may be deleted
  // at any time
  private String areaType;
  private Long regionId;
  private String regionVersion;
  private Long modelId;
  private String modelName;

  public boolean isValidFare(ValidateFareEntity validateFareRequest) {
    return Objects.nonNull(validateFareRequest)
        && Objects.equals(validateFareRequest.getMobile(), this.mobile)
        && Objects.equals(validateFareRequest.getCountryCode(), this.countryCode)
        && Objects.equals(validateFareRequest.getPickupAddressRef(), this.pickupAddressRef)
        && Objects.equals(validateFareRequest.getDropoffAddressRef(), this.destAddressRef)
        && Objects.equals(validateFareRequest.getIntermediateAddrRef(), this.intermediateAddrRef)
        && flatFareVOParts.stream()
            .anyMatch(
                flatFareVOPart ->
                    Objects.nonNull(flatFareVOPart)
                        && FlatfareConstants.NORMAL_FLATFARE_PDT_ID.equals(
                            flatFareVOPart.getPdtId())
                        && flatFareVOPart.getVehTypeId() == validateFareRequest.getVehTypeId()
                        && Objects.equals(
                            flatFareVOPart.getTotalFare(), validateFareRequest.getFareAmount()));
  }
}

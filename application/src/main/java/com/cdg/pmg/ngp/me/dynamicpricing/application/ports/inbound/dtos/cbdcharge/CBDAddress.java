package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.cbdcharge;

import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CBDAddress implements Serializable {
  @Serial private static final long serialVersionUID = 1L;
  private String addressRef;
  private Boolean cbdFlag;
  private Boolean effectiveFlag;

  public boolean isValidConfig() {
    if (ObjectUtils.anyNull(addressRef, cbdFlag, effectiveFlag)) {
      return false;
    }
    return addressRef.matches("[0-9]+");
  }

  public boolean isInsertable() {
    return effectiveFlag && cbdFlag;
  }

  public boolean isDeletable() {
    return !(effectiveFlag && cbdFlag);
  }
}

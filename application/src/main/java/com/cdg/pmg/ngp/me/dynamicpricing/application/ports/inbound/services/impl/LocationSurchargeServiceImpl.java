package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareHoliday;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.LocationSurchargeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.LocationSurchargeConfigRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.FlatFareConfigService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.LocationSurchargeService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.DateUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.SurchargeUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.LocationSurchargeConfigRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.CacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.LocationSurchargeConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.queries.LocationSurchargeConfigQueryResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.utils.CollectionUtils;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;

@AllArgsConstructor
@ServiceComponent
@Slf4j
public class LocationSurchargeServiceImpl implements LocationSurchargeService {
  private static final String LOC_KEY_CACHE_PREFIX =
      RedisKeyConstant.DYNAMIC_PRICING
          .concat(RedisKeyConstant.COLON)
          .concat(RedisKeyConstant.LOC_SURC)
          .concat(RedisKeyConstant.COLON);

  private final LocationSurchargeConfigRepository locationSurchargeConfigRepository;
  private final CacheService cacheService;
  private final FlatFareConfigService flatFareConfigService;

  @Override
  public void loadAllLocationSurchargeConfigs() {
    log.info("[loadAllLocationSurchargeConfigs] Start loadAllLocationSurchargeConfigs!");

    Map<String, Map<String, List<LocationSurchargeConfigEntity>>> allConfigMap = new HashMap<>();

    int page = 0;
    LocationSurchargeConfigQueryResponse configs =
        locationSurchargeConfigRepository.getLocationSurchargeConfigs(page);
    log.info("[loadAllLocationSurchargeConfigs] Get location surcharge DB page {}", page);
    if (Objects.isNull(configs) || CollectionUtils.isEmpty(configs.getConfigs())) {
      log.info("[loadAllLocationSurchargeConfigs] Location surcharge configs in database is empty");
      return;
    }

    while (!configs.getConfigs().isEmpty()) {
      List<LocationSurchargeConfigEntity> listConfigs = configs.getConfigs();
      Map<String, Map<String, List<LocationSurchargeConfigEntity>>> lisConfigAfterGroup =
          SurchargeUtils.groupConfigByDayInWeekThenAddress(listConfigs);

      allConfigMap =
          SurchargeUtils.mergeGroupConfigByDayInWeekThenAddress(allConfigMap, lisConfigAfterGroup);
      log.info("[loadAllLocationSurchargeConfigs] Merge page {} to all config", page);

      page++;
      configs = locationSurchargeConfigRepository.getLocationSurchargeConfigs(page);
      log.info("[loadAllLocationSurchargeConfigs] Get location surcharge DB page {}", page);
    }

    pushAllConfigToCache(allConfigMap);

    log.info("[loadAllLocationSurchargeConfigs] End loadAllLocationSurchargeConfigs!");
  }

  private void pushAllConfigToCache(
      Map<String, Map<String, List<LocationSurchargeConfigEntity>>> allConfigMap) {
    long startTime = System.currentTimeMillis();
    pushConfigToCacheByDay(allConfigMap, FlatfareConstants.MON);
    long endTime = System.currentTimeMillis();
    log.info("[loadAllLocationSurchargeConfigs] reload MON take {}", endTime - startTime);

    pushConfigToCacheByDay(allConfigMap, FlatfareConstants.TUE);
    long startTime2 = System.currentTimeMillis();
    pushConfigToCacheByDay(allConfigMap, FlatfareConstants.WED);
    long endTime2 = System.currentTimeMillis();
    log.info("[loadAllLocationSurchargeConfigs] reload WED take {}", endTime2 - startTime2);

    pushConfigToCacheByDay(allConfigMap, FlatfareConstants.THU);
    pushConfigToCacheByDay(allConfigMap, FlatfareConstants.FRI);
    pushConfigToCacheByDay(allConfigMap, FlatfareConstants.SAT);
    pushConfigToCacheByDay(allConfigMap, FlatfareConstants.SUN);
    long startTime3 = System.currentTimeMillis();
    pushConfigToCacheByDay(allConfigMap, FlatfareConstants.HOL);
    long endTime3 = System.currentTimeMillis();
    log.info("[loadAllLocationSurchargeConfigs] reload HOL take {}", endTime3 - startTime3);
  }

  private void pushConfigToCacheByDay(
      Map<String, Map<String, List<LocationSurchargeConfigEntity>>> allConfigMap,
      String dayIndicator) {
    try {
      String keyPrefix = LOC_KEY_CACHE_PREFIX.concat(dayIndicator).concat(RedisKeyConstant.COLON);

      Map<String, List<LocationSurchargeConfigEntity>> configsByDay =
          allConfigMap.get(dayIndicator);

      deleteLocationSurchargeConfigsByDay(dayIndicator);

      configsByDay.forEach(
          (addressRef, configList) -> {
            String keyCache = keyPrefix + addressRef;
            cacheService.setListValue(keyCache, configList);
          });

    } catch (Exception e) {
      log.error("[loadAllLocationSurchargeConfigs] Error while push config to Cache", e);
    }
    log.info("[loadAllLocationSurchargeConfigs] Reload cache for {} successfuly", dayIndicator);
  }

  @Override
  public LocationSurchargeConfig getLocationSurchargeConfig(
      LocationSurchargeConfigRequest locationSurchargeConfigRequest) {
    Optional<LocationSurchargeConfig> locationSurchargeConfig;
    final Date requestDate = Date.from(locationSurchargeConfigRequest.getRequestDate().toInstant());
    final LocalTime requestDateTime = DateUtils.convertToLocalTime(requestDate);
    final String dayInWeekReq = DateUtils.toDayOfWeekShortUpperCase(requestDate);
    final String addressRef = locationSurchargeConfigRequest.getAddressRef();
    final String chargeByRequest = locationSurchargeConfigRequest.getChargeBy();
    final String productId =
        "FLAT_001".equalsIgnoreCase(locationSurchargeConfigRequest.getProductId())
            ? FlatfareConstants.NORMAL_FLATFARE_PDT_ID
            : locationSurchargeConfigRequest.getProductId();
    final String chargeByType;

    chargeByType =
        FlatfareConstants.CHARGE_BY_PICKUP.equalsIgnoreCase(chargeByRequest)
            ? FlatfareConstants.CHARGE_BY_PICKUP
            : FlatfareConstants.CHARGE_BY_DEST;
    Stream<LocationSurchargeConfig> locationSurchargeConfigStream;

    final List<LocationSurchargeConfig> configs =
        getLocSurConfigsByDayAndAddr(dayInWeekReq, addressRef);
    final List<LocationSurchargeConfig> locSurchargeDayInWeekConfigs =
        configs.stream()
            .filter(
                locSur ->
                    (addressRef.equalsIgnoreCase(locSur.getAddressRef()))
                        && FlatfareConstants.LOC_SURC_TYPE.equalsIgnoreCase(locSur.getFareType())
                        && chargeByType.equalsIgnoreCase(locSur.getChargeBy())
                        && productId.equalsIgnoreCase(locSur.getProductId())
                        && (locSur.getStartTime().isBefore(requestDateTime)
                            && locSur.getEndTime().isAfter(requestDateTime)))
            .toList();

    if (DateUtils.isHoliday(requestDate, flatFareConfigService.getListHoliday())) {
      final List<LocationSurchargeConfig> configsHOL =
          getLocSurConfigsByDayAndAddr(FlatfareConstants.HOL, addressRef);

      final List<LocationSurchargeConfig> locSurchargeHolidayConfigs =
          configsHOL.stream()
              .filter(
                  locSur ->
                      (addressRef.equalsIgnoreCase(locSur.getAddressRef()))
                          && FlatfareConstants.LOC_SURC_TYPE.equalsIgnoreCase(locSur.getFareType())
                          && chargeByType.equalsIgnoreCase(locSur.getChargeBy())
                          && productId.equalsIgnoreCase(locSur.getProductId())
                          && (locSur.getStartTime().isBefore(requestDateTime)
                              && locSur.getEndTime().isAfter(requestDateTime)))
              .toList();
      locationSurchargeConfigStream =
          Stream.concat(locSurchargeHolidayConfigs.stream(), locSurchargeDayInWeekConfigs.stream());
      if (locSurchargeHolidayConfigs.isEmpty()) {
        locationSurchargeConfig =
            locationSurchargeConfigStream
                .filter(locSur -> locSur.getDayIndicator().contains(dayInWeekReq))
                .reduce((firstLocSur, lastLocSur) -> lastLocSur);
      } else {
        locationSurchargeConfig =
            locationSurchargeConfigStream
                .filter(
                    locSur ->
                        locSur.getDayIndicator().contains(FlatfareConstants.HOL)
                            || locSur.getDayIndicator().contains(dayInWeekReq))
                .findFirst();
      }
    } else {
      locationSurchargeConfig =
          locSurchargeDayInWeekConfigs.stream().reduce((firstLocSur, lastLocSur) -> lastLocSur);
    }
    if (locationSurchargeConfig.isPresent()) {
      return locationSurchargeConfig.get();
    } else {
      log.info("[getLocationSurchargeConfig] locationSurchargeConfig is empty");
      return null;
    }
  }

  private void deleteLocationSurchargeConfigsByDay(final String dayIndicator) {
    final String locSurchargeKeyPattern =
        LOC_KEY_CACHE_PREFIX
            .concat(dayIndicator)
            .concat(RedisKeyConstant.COLON)
            .concat(RedisKeyConstant.WILDCARD);
    final Set<String> keys = cacheService.getKeysByPattern(locSurchargeKeyPattern);
    cacheService.deleteByKeys(keys);
  }

  @Override
  public List<LocationSurchargeConfig> getLocationSurchargeConfigList(
      final Date reqDate,
      final String pickupAddrRef,
      final String intermediateAddrRef,
      final String dropOffAddrRef,
      final List<FlatFareHoliday> holidayList) {
    final String dayInWeekReq = DateUtils.toDayOfWeekShortUpperCase(reqDate);

    final Stream<LocationSurchargeConfig> stream;

    final List<LocationSurchargeConfig> locSurchargeDayInWeekConfigs =
        getLocSurConfigsByDayAndAddr(
            dayInWeekReq, pickupAddrRef, intermediateAddrRef, dropOffAddrRef);

    if (DateUtils.isHolidaySingTime(reqDate, holidayList)) {
      final List<LocationSurchargeConfig> locSurchargeHolidayConfigs =
          getLocSurConfigsByDayAndAddr(
              FlatfareConstants.HOL, pickupAddrRef, intermediateAddrRef, dropOffAddrRef);
      stream =
          Stream.concat(locSurchargeHolidayConfigs.stream(), locSurchargeDayInWeekConfigs.stream());
    } else {
      stream = locSurchargeDayInWeekConfigs.stream();
    }
    return stream.toList();
  }

  private List<LocationSurchargeConfig> getLocSurConfigsByDayAndAddr(
      final String dayIndicator, final String... addressParts) {
    List<LocationSurchargeConfig> combinedConfigs = new ArrayList<>();
    if (ObjectUtils.isEmpty(addressParts)) {
      return combinedConfigs;
    }
    final String keyCache =
        RedisKeyConstant.DYNAMIC_PRICING
            .concat(RedisKeyConstant.COLON)
            .concat(RedisKeyConstant.LOC_SURC)
            .concat(RedisKeyConstant.COLON)
            .concat(dayIndicator)
            .concat(RedisKeyConstant.COLON);
    for (String addressPart : addressParts) {
      List<LocationSurchargeConfig> locSurConfigs =
          cacheService.getListValue(keyCache + addressPart, LocationSurchargeConfig.class);
      combinedConfigs.addAll(locSurConfigs);
    }

    return combinedConfigs;
  }
}

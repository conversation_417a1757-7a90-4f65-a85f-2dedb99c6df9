package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.RegionFareCountAggregateResult;

import java.time.Instant;
import java.util.List;

public interface MlGetFareRequestAggStatsService {

  void aggregateGetFareCountEveryMinute(Instant triggerTime);

  /**
   * Get the current get fare count for all regions 
   * 
   * @return a list of all regions get fare count
   */
  List<RegionFareCountAggregateResult> getCurrentGetFareCount();
}

package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services;

import java.util.List;
import java.util.Map;
import java.util.Set;

/** The interface Cache outbound service. */
public interface CacheService {

  /**
   * Set Key-value pair to List redis store.
   *
   * @param <T> data type
   * @param key key cache
   * @param data data store to cache
   */
  <T> void setListValue(final String key, List<T> data);

  /**
   * Sets value.
   *
   * @param <T> the type parameter
   * @param key the key
   * @param data the data
   */
  <T> void setValue(final String key, T data);

  /**
   * Sets value.
   *
   * @param <T> the type parameter
   * @param key the key
   * @param data the data
   * @param expireDuration the expire duration
   */
  <T> void setValue(final String key, T data, int expireDuration);

  /**
   * Sets value.
   *
   * @param <T> the type parameter
   * @param key the key
   * @param data the data
   * @param expireDuration the expire duration
   */
  <T> void setValue(final String key, T data, long expireDuration);

  /**
   * Gets value.
   *
   * @param <T> the type parameter
   * @param key the key
   * @param valueType the value type
   * @return the value
   */
  <T> T getValue(final String key, Class<T> valueType);

  /**
   * Delete by key.
   *
   * @param key the key
   */
  void deleteByKey(final String key);

  /**
   * Sets expire.
   *
   * @param key the key
   * @param expireDuration the expire duration
   */
  void setExpire(final String key, int expireDuration);

  /**
   * Sets expire.
   *
   * @param key the key
   * @param expireDuration the expire duration
   */
  void setExpire(final String key, long expireDuration);

  /**
   * Gets list of value (list) by multi key.
   *
   * @param keys the keys
   * @param valueType the value type
   * @return the List
   */
  <T> List<List<T>> getMultiValueList(final Set<String> keys, Class<T> valueType);

  /**
   * Gets list of value (map) by multi key.
   *
   * @param <K> the type parameter
   * @param <V> the type parameter
   * @param keys the keys
   * @param valueType the value type
   * @return the List
   */
  <K, V> List<Map<K, V>> getMultiValueMap(Set<String> keys, Class<K> keyType, Class<V> valueType);

  /**
   * Obtain JSON value using a key
   *
   * @param key the type parameter
   * @return the String
   */
  String getStringValue(String key);

  /**
   * Gets keys list in cache by pattern.
   *
   * @param pattern the pattern of the keys
   * @return the Set of keys
   */
  Set<String> getKeysByPattern(String pattern);

  /**
   * Gets list value in cache by key.
   *
   * @param <T> Value Class
   * @param key of cache
   * @return the list of value
   */
  <T> List<T> getListValue(String key, Class<T> typeOfList);

  /**
   * Gets map value in cache by key.
   *
   * @param <K> the type parameter
   * @param <V> the type parameter
   * @param key of cache
   * @return the map of value
   */
  <K, V> Map<K, V> getMapValue(String key, Class<K> keyType, Class<V> valueType);

  /**
   * Gets keys list in cache by pattern.
   *
   * @param subKey a part of key
   * @param prefix prefix of key
   * @param suffix suffix of key
   * @return the Set of keys
   */
  Set<String> getKeysByPattern(String subKey, String prefix, String suffix);

  /**
   * Delete keys from Redis store
   *
   * @param keys
   */
  void deleteByKeys(final Set<String> keys);

  List<String> getCompanyHoliday();
}

package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.S2CellEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.queries.S2CellListConfigQueryResponse;

/**
 * Service handling S2 cells. This service provides functionalities to interact with S2 cell data,
 * including retrieval and caching mechanisms.
 */
public interface S2CellService {
  /**
   * Retrieves a list of S2 cell entities.
   *
   * @return a list of {@link S2CellEntity} objects, each representing an individual S2 cell.
   */
  S2CellListConfigQueryResponse getS2CellList();

  /** Fetches the cached data of S2 cells. */
  void fetchCacheS2Cell();
}

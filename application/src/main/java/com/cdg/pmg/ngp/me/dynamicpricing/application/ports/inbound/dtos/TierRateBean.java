package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TierRateBean implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  private Double perCountFare;
  private Double perCountMeter;
  private Integer startDist;
  private Integer endDist;
}

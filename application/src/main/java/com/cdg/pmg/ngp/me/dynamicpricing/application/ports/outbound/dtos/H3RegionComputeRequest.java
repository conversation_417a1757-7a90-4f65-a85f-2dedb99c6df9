package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class H3RegionComputeRequest implements Serializable {
  @Serial private static final long serialVersionUID = 2736243378575585409L;

  private Double lat;
  private Double lng;

  public String getLatLngMapKey() {
    return lat + ":" + lng;
  }
}

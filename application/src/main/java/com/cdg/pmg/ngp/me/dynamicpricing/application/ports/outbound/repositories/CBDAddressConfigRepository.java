package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.LocationSurchargeConfigEntity;
import java.util.List;
import java.util.Set;

public interface CBDAddressConfigRepository {

  /**
   * Insert CBD Address
   *
   * @param insertCBDAddressList insert list
   */
  void insertCBDAddress(Set<String> insertCBDAddressList);

  /**
   * Delete CBD Address
   *
   * @param deleteCBDAddressList delete list
   */
  void deleteCBDAddress(Set<String> deleteCBDAddressList);

  /**
   * Get CBD config by address ref list
   *
   * @param addressRefList addressRefList
   * @return config list
   */
  List<LocationSurchargeConfigEntity> getLocConfigByAddressRefList(List<String> addressRefList);
}

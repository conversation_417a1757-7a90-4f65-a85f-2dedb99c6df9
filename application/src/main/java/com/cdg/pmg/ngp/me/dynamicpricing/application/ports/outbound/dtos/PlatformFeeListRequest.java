package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class PlatformFeeListRequest implements Serializable {
  @Serial private static final long serialVersionUID = 5876743711863097734L;

  private String bookingChannel;
  private List<PlatformFeeIdentify> platformFeeRequestList;
}

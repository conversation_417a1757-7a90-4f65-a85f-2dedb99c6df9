package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class GenerateRouteRequest implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  private LatLng origin;
  private LatLng destination;
  private List<LatLng> intermediates;
  private String tripId;
}

package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos;

import static com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants.*;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.CommonUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.DateUtils;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
public class EventSurgeAddressConfig implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  private LocalTime startTime;
  private LocalTime endTime;
  private String applicableDaysOfMonth;
  private String applicableMonths;
  private String chargeBy;
  private String chargeType;
  private String applicableAddresses;
  private Double chargeVal;
  private String configIndex;

  public boolean isValidConfig() {
    return ObjectUtils.allNotNull(
        startTime,
        endTime,
        applicableDaysOfMonth,
        applicableMonths,
        chargeBy,
        chargeType,
        applicableAddresses,
        chargeVal);
  }

  public String getInvalidConfigMsg() {
    List<String> nullFields = new ArrayList<>();
    if (Objects.isNull(this.applicableAddresses))
      nullFields.add(EVENT_SURGE_ADDR_APPC + configIndex);
    if (Objects.isNull(this.chargeBy)) nullFields.add(EVENT_SURGE_ADDR_CHARGEBY + configIndex);
    if (Objects.isNull(this.chargeType)) nullFields.add(EVENT_SURGE_ADDR_CHARGETYPE + configIndex);
    if (Objects.isNull(this.chargeVal)) nullFields.add(EVENT_SURGE_ADDR_CHARGEVAL + configIndex);
    if (Objects.isNull(this.applicableDaysOfMonth))
      nullFields.add(EVENT_SURGE_ADDR_DAYS + configIndex);
    if (Objects.isNull(this.applicableMonths))
      nullFields.add(EVENT_SURGE_ADDR_MONTHS + configIndex);
    if (Objects.isNull(this.startTime)) nullFields.add(EVENT_SURGE_ADDR_START_TIME + configIndex);
    if (Objects.isNull(this.endTime)) nullFields.add(EVENT_SURGE_ADDR_END_TIME + configIndex);

    if (!nullFields.isEmpty()) {
      return String.join(", ", nullFields);
    }
    return null;
  }

  public void setConfig(String key, String value) {
    switch (key) {
      case EVENT_SURGE_ADDR_APPC -> this.applicableAddresses = value;
      case EVENT_SURGE_ADDR_CHARGEBY -> this.chargeBy = value;
      case EVENT_SURGE_ADDR_CHARGETYPE -> this.chargeType = value;
      case EVENT_SURGE_ADDR_CHARGEVAL -> this.chargeVal =
          CommonUtils.stringToDoubleOrElseNull(value);
      case EVENT_SURGE_ADDR_DAYS -> this.applicableDaysOfMonth = value;
      case EVENT_SURGE_ADDR_MONTHS -> this.applicableMonths = value;
      case EVENT_SURGE_ADDR_START_TIME -> this.startTime = DateUtils.convertToLocalTime(value);
      case EVENT_SURGE_ADDR_END_TIME -> this.endTime = DateUtils.convertToLocalTime(value);
      default -> log.warn("EventSurgeAddressConfig set wrong key={}, value={}", key, value);
    }
  }
}

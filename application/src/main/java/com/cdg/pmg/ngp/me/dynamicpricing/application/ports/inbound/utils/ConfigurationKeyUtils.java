package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class ConfigurationKeyUtils {
  private ConfigurationKeyUtils() {}

  /**
   * Extracts the part of a string that comes after the last occurrence of a colon (:). If the
   * string does not contain a colon, the entire string is returned. This method is useful for
   * processing strings formatted with colon-separated values, such as key-value pairs or
   * configuration strings.
   *
   * @param fullKey The string from which to extract the key part. It is expected to be in a format
   *     where parts are separated by colons. Cannot be null.
   * @return The substring after the last colon in the given string. If the colon is not present,
   *     returns the entire string as is. If the string ends with a colon, an empty string is
   *     returned.
   * @throws NullPointerException if {@code fullKey} is null.
   * @see StringUtils#lastIndexOf(String, char) Used for finding the last index of colon.
   */
  public static String extractKeyPart(final String fullKey) {
    final int lastColonIndex = StringUtils.lastIndexOf(fullKey, ':');
    return lastColonIndex < 0 ? fullKey : fullKey.substring(lastColonIndex + 1);
  }
}

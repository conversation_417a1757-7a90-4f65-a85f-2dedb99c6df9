package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import java.time.OffsetDateTime;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class BookingFeeListRequest implements Serializable {
  @Serial private static final long serialVersionUID = 1L;
  private String jobType;
  private Boolean isHoliday;
  private OffsetDateTime requestDate;
  private List<BookingFeeItem> bookingFeeRequestList;
}

package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.H3RegionSurgeEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.SurgeComputationModelSurgeEntity;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for surge computation model surges. Provides methods for CRUD operations on
 * surge values calculated by the surge computation model service.
 */
public interface SurgeComputationModelSurgeRepository {

  /**
   * Batch upsert surge entities.
   *
   * @param modelId the model ID from {@link
   *     com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.ModelEntity}
   * @param entities a list of the surge entities to save
   */
  void batchUpsertSurges(Long modelId, List<H3RegionSurgeEntity> entities);

  /**
   * Find all surge entities by model name.
   *
   * @param modelName the model name from {@link
   *     com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.ModelEntity}
   * @return a list of all surge entities
   */
  List<SurgeComputationModelSurgeEntity> findByModelName(String modelName);

  /**
   * Find all surge entities by model ID.
   *
   * @param modelId the model ID from {@link
   *     com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.ModelEntity}
   * @return a list of all surge entities for the specified model
   */
  List<SurgeComputationModelSurgeEntity> findByModelId(Long modelId);

  /**
   * Find surge entity by region ID.
   *
   * @param modelId the model ID from {@link
   *     com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.ModelEntity}
   * @param regionId the region ID to search for
   * @return an optional surge entity for the specified region
   */
  Optional<SurgeComputationModelSurgeEntity> findByModelIdAndRegionId(Long modelId, Long regionId);
}

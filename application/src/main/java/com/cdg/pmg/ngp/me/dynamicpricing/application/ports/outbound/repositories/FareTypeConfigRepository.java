package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.FareTypeConfig;
import java.util.List;
import java.util.Set;

/** The interface fare type config repository. */
public interface FareTypeConfigRepository {

  /**
   * Gets all flat fare configuration.
   *
   * @return List of FareTypeConfig
   */
  List<FareTypeConfig> getFareTypeConfigs();

  /**
   * Get param config by set fare type
   *
   * @param listFareType Set fare type
   * @return List<FareTypeConfig> List fare type config
   */
  List<FareTypeConfig> getParamConfigByListFareType(final Set<String> listFareType);

  /**
   * Create fare type config
   *
   * @param fareTypeConfig Fare type config
   * @return FareTypeConfig Fare type config
   */
  FareTypeConfig createFareTypeConfig(final FareTypeConfig fareTypeConfig);

  /**
   * Update fare type config
   *
   * @param fareTypeConfig Fare type config
   * @return FareTypeConfig Fare type config
   */
  FareTypeConfig updateFareTypeConfig(final FareTypeConfig fareTypeConfig);
}

package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.flatfare.impl;

import static com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants.GUA_PDT_ID;
import static com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants.GUD_PDT_ID;
import static com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants.NORMAL_FLATFARE_PDT_ID;
import static com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants.OWT_PDT_ID;
import static com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants.STD_PDT_ID;
import static com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant.EST_LIMO_LIVE_TRAFFIC;
import static com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant.EST_LIVE_TRAFFIC;
import static com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant.LIVE_TRAFFIC;
import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.COMPUTE_SURGE_INDICATOR_ERROR;
import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.FLAT_FARE_COMPUTED_NOT_VALID;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.CommonConfigSet;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.DriverSurgeLevelIndication;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareConfigSet;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareHoliday;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareVO;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.FlatFareConfigService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.LocationSurchargeService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.flatfare.FlatFareCompute;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.CommonUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.DateUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.BookingFeeItem;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.BookingFeeListRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.PlatformFeeIdentify;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.PlatformFeeListRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.FareService;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.DomainException;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ServiceComponent
@Slf4j
@RequiredArgsConstructor
public class FlatFareManager {

  private final FlatFareConfigService configService;
  private final FareService fareService;
  private final LocationSurchargeService locationSurchargeService;

  public boolean isFlatLimoType(final int vehTypeId, final CommonConfigSet commonConfigSet) {
    return isVehTypeIdListContain(vehTypeId, commonConfigSet.getLimoFlatFareVehIds());
  }

  public boolean isEstLimoType(final int vehTypeId, final CommonConfigSet commonConfigSet) {
    return isVehTypeIdListContain(vehTypeId, commonConfigSet.getEstLimoFlatFareVehIds());
  }

  public boolean isEstFareType(final int vehTypeId, final CommonConfigSet commonConfigSet) {
    return isVehTypeIdListContain(vehTypeId, commonConfigSet.getEstFareVehIds());
  }

  public boolean isDynamicFareType(final int vehTypeId, final CommonConfigSet commonConfigSet) {
    return isVehTypeIdListContain(vehTypeId, commonConfigSet.getDynamicPricingVehIds());
  }

  public boolean isValidVehTypeId(final int vehTypeId, final CommonConfigSet commonConfigSet) {
    return isFlatLimoType(vehTypeId, commonConfigSet)
        || isEstLimoType(vehTypeId, commonConfigSet)
        || isEstFareType(vehTypeId, commonConfigSet)
        || isDynamicFareType(vehTypeId, commonConfigSet);
  }

  public boolean isShowMeterFareOnly(final int vehTypeId, final CommonConfigSet commonConfigSet) {
    return (isEstLimoType(vehTypeId, commonConfigSet)
            && !isFlatLimoType(vehTypeId, commonConfigSet))
        || (isEstFareType(vehTypeId, commonConfigSet)
            && !isDynamicFareType(vehTypeId, commonConfigSet));
  }

  public boolean isShowFlatFareOnly(final int vehTypeId, final CommonConfigSet commonConfigSet) {
    return (isFlatLimoType(vehTypeId, commonConfigSet)
            && !isEstLimoType(vehTypeId, commonConfigSet))
        || (isDynamicFareType(vehTypeId, commonConfigSet)
            && !isEstFareType(vehTypeId, commonConfigSet));
  }

  public boolean isAdvanceJobWhiteList(final int vehTypeId, final CommonConfigSet commonConfigSet) {
    return isVehTypeIdListContain(vehTypeId, commonConfigSet.getAdvanceVehIds());
  }

  public boolean isVehTypeIdListContain(final int vehTypeId, final String vehTypeIdList) {
    return vehTypeIdList != null
        && vehTypeIdList.contains(
            CommonUtils.COMMA_DELIMITER + vehTypeId + CommonUtils.COMMA_DELIMITER);
  }

  public List<Integer> filterAdvanceVehId(
      final List<Integer> vehTypeIds, final CommonConfigSet commonConfigSet) {
    final List<Integer> advanceVehIdList = new ArrayList<>();
    for (final int vehTypeId : vehTypeIds) {
      if (isAdvanceJobWhiteList(vehTypeId, commonConfigSet)) {
        advanceVehIdList.add(vehTypeId);
      }
    }
    return advanceVehIdList;
  }

  public int getCacheTimerMultiFlatFareInMinute(final String timeInMinuteInCache) {
    int timeInMinute;
    try {
      timeInMinute = Integer.parseInt(timeInMinuteInCache);
    } catch (Exception e) {
      log.error("Error to get CACHE_TIMER_MINS_MULTI_FLATFARE config");
      timeInMinute = FlatfareConstants.CACHE_TIMER_MINS_MULTI_FLATFARE_DEFAULT;
    }
    return timeInMinute;
  }

  public int getCacheTimerBreakDownFlatFareInMinute(final String timeInMinuteInCache) {
    int timeInMinute;
    try {
      timeInMinute = Integer.parseInt(timeInMinuteInCache);
    } catch (Exception e) {
      log.error("Error to get CACHE_TIMER_MINS_BREAKDOWN_FLATFARE config");
      timeInMinute = FlatfareConstants.CACHE_TIMER_MINS_BREAKDOWN_FLATFARE_DEFAULT;
    }
    return timeInMinute;
  }

  public int computeSurgeLevel(
      final boolean isDynamicPrice,
      final FlatFareVO flatfareVO,
      final List<DriverSurgeLevelIndication> driverSurgeLevelIndications) {
    int surgeLvl = 0;
    if (!isDynamicPrice) {
      return surgeLvl;
    }

    final Double dpSurgePerc = flatfareVO.getPricePerKm();
    final long currReqDateTs = flatfareVO.getFlatFareRequest().getRequestDate().getTime();

    if (dpSurgePerc != null) {
      final List<DriverSurgeLevelIndication> filtered =
          driverSurgeLevelIndications.stream()
              .filter(
                  p ->
                      currReqDateTs >= p.getEffectTimeFromTs()
                          && currReqDateTs < p.getEffectTimeToTs()
                          && dpSurgePerc >= p.getPercFrom()
                          && dpSurgePerc < p.getPercTo())
              .toList();

      if (!filtered.isEmpty()) {
        surgeLvl = filtered.get(0).getSurgeLevel();
      }
    }

    return surgeLvl;
  }

  public int computeSurgeIndicator(
      final boolean isDynamicPrice,
      FlatFareVO flatfareVO,
      final String surgeIndicatorThresholdStr,
      final String surgeIndicatorThresholdZeroStr) {
    int surgeIndicator = 0;
    if (!isDynamicPrice) {
      return surgeIndicator;
    }

    try {
      final double surgeIndicatorThreshold = Double.parseDouble(surgeIndicatorThresholdStr);
      final double surgeIndicatorThresholdZero = Double.parseDouble(surgeIndicatorThresholdZeroStr);

      final Double dpSurgePerc = flatfareVO.getPricePerKm();
      if (dpSurgePerc != null) {
        if (dpSurgePerc >= surgeIndicatorThreshold) {
          surgeIndicator = 1;
        } else if (dpSurgePerc >= surgeIndicatorThresholdZero) {
          surgeIndicator = 0;
        } else {
          surgeIndicator = -1;
        }
      }

    } catch (NumberFormatException | NullPointerException e) {
      log.error(e.getMessage(), e);
      throw new DomainException(
          COMPUTE_SURGE_INDICATOR_ERROR.getMessage(), COMPUTE_SURGE_INDICATOR_ERROR.getErrorCode());
    }

    return surgeIndicator;
  }

  public FlatFareVO computeTotalFare(
      final FlatFareConfigSet configSet, final FlatFareRequest flatFareRequest) {
    FlatFareVO flatfareVO = new FlatFareVO();
    flatfareVO.setFlatFareRequest(flatFareRequest);

    final FlatFareCompute flatFareComputeInf =
        switch (configSet.getPrefixKey()) {
          case RedisKeyConstant.EST_LIMO_LIVE_TRAFFIC_KEY_PREFIX -> new LimoFlatFareComputeImpl(
              configSet, configService, fareService, locationSurchargeService);
          case RedisKeyConstant.EST_LIVE_TRAFFIC_KEY_PREFIX -> new EstStandardFlatFareComputeImpl(
              configSet, configService, fareService, locationSurchargeService);
          default -> null;
        };

    if (flatFareComputeInf != null) {
      log.info("FlatFareManager compute fareType={}", flatFareComputeInf.getFareType());

      flatFareComputeInf.initVORequest(flatfareVO);
      flatFareComputeInf.calFlatDownRateFare(flatfareVO);
      flatFareComputeInf.calTier1(flatfareVO);
      flatFareComputeInf.calTier2(flatfareVO);
      flatFareComputeInf.calEttFare(flatfareVO);
      flatFareComputeInf.calLocSurcharges(flatfareVO);
      flatFareComputeInf.calBookingFee(flatfareVO);
      flatFareComputeInf.calPeakHourCharges(flatfareVO);
      flatFareComputeInf.calMidNightCharges(flatfareVO);
      flatFareComputeInf.calAdditionalSurCharge(flatfareVO);
      flatFareComputeInf.calMultiDestSurcharge(flatfareVO);
      flatFareComputeInf.calBaseFare(flatfareVO);

      flatFareComputeInf.setTotalFareBeforeSurge(flatfareVO);

      flatFareComputeInf.calEventSurgeAddrCharge(flatfareVO);

      flatFareComputeInf.calEstimatedTotalFare(flatfareVO);

      flatFareComputeInf.roundToFiftyCent(flatfareVO);

      flatFareComputeInf.finalizeTotalAmount(flatfareVO);
    }

    if (flatFareComputeInf == null || !flatFareComputeInf.isValidFlatfare(flatfareVO)) {
      final String msg =
          String.format(
              "Flat fare computed is not valid vehTypeId=%s",
              flatfareVO.getFlatFareRequest().getVehTypeId());
      log.error(msg);
      throw new DomainException(
          FLAT_FARE_COMPUTED_NOT_VALID.getMessage(), FLAT_FARE_COMPUTED_NOT_VALID.getErrorCode());
    }
    flatfareVO.setCalculated(Boolean.TRUE);
    log.info("ComputeTotalFare  details: {}", flatfareVO);

    return flatfareVO;
  }

  public BookingFeeListRequest createBookingFeeListRequest(
      final FlatFareRequest flatFareRequest,
      final CommonConfigSet commonConfigSet,
      final List<FlatFareHoliday> holidayList) {
    final List<BookingFeeItem> bookingFeeRequestList =
        initBookingFeeRequestList(flatFareRequest.getVehTypeIdList(), commonConfigSet);
    return BookingFeeListRequest.builder()
        .jobType(flatFareRequest.getJobType())
        .isHoliday(DateUtils.isHolidaySingTime(flatFareRequest.getRequestDate(), holidayList))
        .requestDate(flatFareRequest.getRequestDate().toInstant().atOffset(ZoneOffset.UTC))
        .bookingFeeRequestList(bookingFeeRequestList)
        .build();
  }

  public List<BookingFeeItem> initBookingFeeRequestList(
      final List<Integer> vehTypeIdList, final CommonConfigSet commonConfigSet) {
    List<BookingFeeItem> bookingFeeItemList = new ArrayList<>();
    for (int vehTypeId : vehTypeIdList) {
      BookingFeeItem bookingFeeForMeter =
          buildBookingFeeRequest(vehTypeId, STD_PDT_ID, EST_LIVE_TRAFFIC);
      bookingFeeItemList.add(bookingFeeForMeter);
      if (isVehTypeIdListContain(vehTypeId, commonConfigSet.getLimoFlatFareVehIds())) {
        BookingFeeItem bookingFeeForOWT =
            buildBookingFeeRequest(vehTypeId, OWT_PDT_ID, EST_LIMO_LIVE_TRAFFIC);
        bookingFeeItemList.add(bookingFeeForOWT);

        BookingFeeItem bookingFeeForGUA =
            buildBookingFeeRequest(vehTypeId, GUA_PDT_ID, EST_LIMO_LIVE_TRAFFIC);
        bookingFeeItemList.add(bookingFeeForGUA);

        BookingFeeItem bookingFeeForOUD =
            buildBookingFeeRequest(vehTypeId, GUD_PDT_ID, EST_LIMO_LIVE_TRAFFIC);
        bookingFeeItemList.add(bookingFeeForOUD);
      } else {
        BookingFeeItem bookingFeeForFlat =
            buildBookingFeeRequest(vehTypeId, NORMAL_FLATFARE_PDT_ID, LIVE_TRAFFIC);
        bookingFeeItemList.add(bookingFeeForFlat);
      }
    }
    return bookingFeeItemList;
  }

  private BookingFeeItem buildBookingFeeRequest(int vehTypeId, String pdtId, String flatFareType) {
    return BookingFeeItem.builder()
        .vehicleTypeId(vehTypeId)
        .productId(pdtId)
        .flatFareType(flatFareType)
        .build();
  }

  public PlatformFeeListRequest createPlatformFeeListRequest(
      final FlatFareRequest flatFareRequest, final CommonConfigSet commonConfigSet) {
    final List<PlatformFeeIdentify> platformFeeIdentifyList =
        initPlatformFeeRequestList(flatFareRequest.getVehTypeIdList(), commonConfigSet);
    return PlatformFeeListRequest.builder()
        .bookingChannel(flatFareRequest.getBookingChannel())
        .platformFeeRequestList(platformFeeIdentifyList)
        .build();
  }

  private List<PlatformFeeIdentify> initPlatformFeeRequestList(
      final List<Integer> vehTypeIdList, final CommonConfigSet commonConfigSet) {
    List<PlatformFeeIdentify> platformFeeRequestList = new ArrayList<>();
    for (int vehTypeId : vehTypeIdList) {
      PlatformFeeIdentify platformFeeForMeter = buildPlatformFeeIdentify(vehTypeId, STD_PDT_ID);
      platformFeeRequestList.add(platformFeeForMeter);
      if (isVehTypeIdListContain(vehTypeId, commonConfigSet.getLimoFlatFareVehIds())) {
        PlatformFeeIdentify platformFeeForOWT = buildPlatformFeeIdentify(vehTypeId, OWT_PDT_ID);
        platformFeeRequestList.add(platformFeeForOWT);

        PlatformFeeIdentify platformFeeForGUA = buildPlatformFeeIdentify(vehTypeId, GUA_PDT_ID);
        platformFeeRequestList.add(platformFeeForGUA);

        PlatformFeeIdentify platformFeeForOUD = buildPlatformFeeIdentify(vehTypeId, GUD_PDT_ID);
        platformFeeRequestList.add(platformFeeForOUD);
      } else {
        PlatformFeeIdentify platformFeeForFlat =
            buildPlatformFeeIdentify(vehTypeId, NORMAL_FLATFARE_PDT_ID);
        platformFeeRequestList.add(platformFeeForFlat);
      }
    }
    return platformFeeRequestList;
  }

  private PlatformFeeIdentify buildPlatformFeeIdentify(
      final int vehTypeId, final String productId) {
    return PlatformFeeIdentify.builder()
        .productId(productId)
        .vehicleGroupId(String.valueOf(vehTypeId))
        .build();
  }
}

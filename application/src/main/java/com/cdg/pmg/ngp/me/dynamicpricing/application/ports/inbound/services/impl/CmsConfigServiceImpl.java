package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.commands.NewPricingModelCommand;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.ListConfigObject;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.mapper.NewPricingModelMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.CmsConfigService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.CMSConfigList;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.CMSServiceOutboundPort;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.ConfigurationServiceOutboundPort;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.NewPricingModelConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.BadRequestException;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.NotFoundException;
import com.cdg.pmg.ngp.me.dynamicpricing.outbound.NewPricingModelRepositoryPort;
import com.cdg.pmg.ngp.me.dynamicpricing.service.NewPricingModelService;
import com.cdg.pmg.ngp.me.dynamicpricing.utils.CollectionUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@AllArgsConstructor
@ServiceComponent
@Slf4j
public class CmsConfigServiceImpl implements CmsConfigService {

  private final CMSServiceOutboundPort cmsServiceOutboundPort;
  private final NewPricingModelMapper newPricingModelMapper;
  private final NewPricingModelService newPricingModelService;
  private final ObjectMapper objectMapper;
  private final ConfigurationServiceOutboundPort configurationServiceOutboundPort;

  @Override
  public List<NewPricingModelConfigEntity> getListNewPricingModelConfigEntityInCms() {
    return newPricingModelService.getListNewPricingModelConfigEntityInCms();
  }

  @Override
  public NewPricingModelConfigEntity updateNewPricingModelConfigEntityInCms(
      NewPricingModelCommand newPricingModelCommand) {
    CMSConfigList newPricingConfigResponse =
        cmsServiceOutboundPort.getCMSBySearchText(
            NewPricingModelRepositoryPort.generateDescriptionKey(
                newPricingModelCommand.getIndex()));
    List<NewPricingModelConfigEntity> newPricingModelConfigEntities =
        newPricingModelMapper.mapToNewPricingModelConfigEntities(
            objectMapper, newPricingConfigResponse);

    if (CollectionUtils.isEmpty(newPricingModelConfigEntities)) {
      throw new NotFoundException(
          MessageFormat.format(
              ErrorEnum.NOT_FOUND_NEW_PRICING_MODEL_INDEX.getMessage(),
              newPricingModelCommand.getIndex()),
          ErrorEnum.NOT_FOUND_NEW_PRICING_MODEL_INDEX.getErrorCode());
    }

    NewPricingModelConfigEntity target = newPricingModelConfigEntities.get(0);
    newPricingModelMapper.mergeNewPricingModel(target, newPricingModelCommand);
    newPricingModelService.validateIgnoreZoneCheck(target);
    return newPricingModelService.update(target);
  }

  @Override
  public NewPricingModelConfigEntity createNewPricingConfigModelConfigEntityInCms(
      NewPricingModelCommand newPricingModelCommand) {
    NewPricingModelConfigEntity newPricingModelConfigEntity =
        newPricingModelMapper.mapToNewPricingModelConfigEntity(newPricingModelCommand);
    newPricingModelService.validate(newPricingModelConfigEntity);
    int newIndex = configurationServiceOutboundPort.getTotalSizeNewPricingModelConfigEntities();
    return newPricingModelService.create(newPricingModelConfigEntity, newIndex);
  }

  private boolean checkExist(List<String> existingValues, String value) {
    return existingValues.stream().anyMatch(s -> StringUtils.equals(value, s));
  }

  @Override
  public void addNewConfig(String descriptionKey, String value) {
    var listConfig = getConfigsByDescriptionKey(descriptionKey);
    if (checkExist(listConfig.getItems(), value)) {
      throw new BadRequestException(
          MessageFormat.format(ErrorEnum.DUPLICATE_VALUE.getMessage(), value),
          ErrorEnum.DUPLICATE_VALUE.getErrorCode());
    }
    var items = new ArrayList<>(listConfig.getItems());
    items.add(value);
    cmsServiceOutboundPort.updateCMSConfig(listConfig.getId(), String.join(",", items));
  }

  @Override
  public ListConfigObject getConfigsByDescriptionKey(String descriptionKey) {
    return Optional.ofNullable(cmsServiceOutboundPort.getCMSBySearchText(descriptionKey).getData())
        .stream()
        .flatMap(Collection::stream)
        .map(
            x -> {
              Stream<String> channelStream =
                  Stream.of(Objects.requireNonNullElse(x.getValue(), "").split(","))
                      .filter(StringUtils::isNoneBlank);
              return ListConfigObject.builder().items(channelStream.toList()).id(x.getId()).build();
            })
        .findFirst()
        .orElseThrow(
            () -> new NotFoundException("Not found description key " + descriptionKey, 0L));
  }

  @Override
  public void deleteNewConfig(String descriptionKey, String value) {
    var listConfig = getConfigsByDescriptionKey(descriptionKey);
    if (!checkExist(listConfig.getItems(), value)) {
      throw new NotFoundException(
          MessageFormat.format(
              ErrorEnum.NOT_FOUND_VALUE.getMessage(), value, listConfig.getItems().toString()),
          ErrorEnum.NOT_FOUND_VALUE.getErrorCode());
    }
    var items = new ArrayList<>(listConfig.getItems());
    items.remove(value);
    cmsServiceOutboundPort.updateCMSConfig(listConfig.getId(), String.join(",", items));
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.additionalcharge;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.AdditionalChargeFeeConfigResponse;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Interface of additional charge processor, all different additional charge logic should implement
 * this interface. Since only have one type of additional charge "DRIVER_FEE" at present, only need
 * to implement one Class "DriverFeeAdditionalChargeProcessor".
 *
 * <p>In the future, if there is any new additional charge, may use "Chain of Responsibility"
 * Pattern to organize all different type additional charge processors.
 */
public interface AdditionalChargeProcessor<P, R> {

  /**
   * Do additional charge fee calculate. If the {@code bookingChannel} is null, get all additional
   * charge fee configs like DRIVER_FEE, BOOSTER_SEAT.
   *
   * @param p p
   * @param additionalChargeFeeConfigMap list of property values of each additional charge fee
   *     config by charge type
   * @return the calculate result
   */
  Optional<R> calculateAdditionalCharge(
      P p, Map<String, List<AdditionalChargeFeeConfigResponse>> additionalChargeFeeConfigMap);

  /**
   * Get the additional charge fee type name.
   *
   * @return additional type string name
   */
  String getAdditionalChargeType();
}

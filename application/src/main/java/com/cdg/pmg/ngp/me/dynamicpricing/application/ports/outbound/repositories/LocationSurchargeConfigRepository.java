package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories;

import com.cdg.pmg.ngp.me.dynamicpricing.queries.LocationSurchargeConfigQueryResponse;

public interface LocationSurchargeConfigRepository {
  /**
   * Gets all location surcharge configuration.
   *
   * @param page search page
   * @return LocationSurchargeConfigQueryResponse
   */
  LocationSurchargeConfigQueryResponse getLocationSurchargeConfigs(int page);
}

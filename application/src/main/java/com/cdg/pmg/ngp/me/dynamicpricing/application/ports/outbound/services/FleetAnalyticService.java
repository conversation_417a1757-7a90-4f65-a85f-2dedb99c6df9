package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.DemandSupplyStatisticsResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.DemandSupplyStatisticsResponseV2;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.RegionDemandSupplyStatistic;
import java.util.List;

/** The interface Fleet Analytic service */
public interface FleetAnalyticService {
  /**
   * Get demand supply statistics
   *
   * @return list of demand supply
   */
  List<DemandSupplyStatisticsResponse> getDemandSuppyStatistics();

  List<DemandSupplyStatisticsResponseV2> getDemandSupplyStatisticsV2();

  List<RegionDemandSupplyStatistic> calculateRegionDemandSupplyStatistics(List<String> bookingIds);
}

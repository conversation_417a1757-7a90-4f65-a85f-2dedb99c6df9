package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import static org.junit.jupiter.api.Assertions.*;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareVOPart;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.additionalcharge.AdditionalChargeDriverFeeData;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.additionalcharge.DriverFeeAdditionalChargeProcessor;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.helper.AdditionalChargeFeeConfigHelper;
import java.math.BigDecimal;
import java.util.Map;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class DriverFeeAdditionalChargeProcessorTest {

  @Mock private DriverFeeAdditionalChargeProcessor driverFeeAdditionalChargeProcessor;

  @BeforeEach
  void setUp() {
    driverFeeAdditionalChargeProcessor = new DriverFeeAdditionalChargeProcessor();
  }

  @Test
  void getAdditionalChargeType_success() {
    String additionalChargeType = driverFeeAdditionalChargeProcessor.getAdditionalChargeType();
    assertEquals("DRIVER_FEE", additionalChargeType);
  }

  @Test
  void driverFeeCalculateNull_WithoutIsCountInTotalfare() {

    FlatFareVOPart origin = new FlatFareVOPart();
    origin.setTotalFare(BigDecimal.valueOf(13.0));
    origin.setEstimatedFareRT(BigDecimal.valueOf(12.0));
    origin.setEstimatedFareLF(BigDecimal.valueOf(15.0));
    Optional<AdditionalChargeDriverFeeData> additionalChargeDriverFeeData =
        driverFeeAdditionalChargeProcessor.calculateAdditionalCharge(
            origin,
            AdditionalChargeFeeConfigHelper.generateDriverFeeConfigWithoutIsCountInTotalfare());
    assertTrue(additionalChargeDriverFeeData.isEmpty());
  }

  @Test
  void driverFeeCalculateNull_IsCountInTotalfareFalse() {

    FlatFareVOPart origin = new FlatFareVOPart();
    origin.setTotalFare(BigDecimal.valueOf(13.0));
    origin.setEstimatedFareLF(BigDecimal.valueOf(12.0));
    origin.setEstimatedFareRT(BigDecimal.valueOf(15.0));
    Optional<AdditionalChargeDriverFeeData> additionalChargeDriverFeeData =
        driverFeeAdditionalChargeProcessor.calculateAdditionalCharge(
            origin,
            AdditionalChargeFeeConfigHelper.generateDriverFeeConfigIsCountInTotalfareFalse());
    assertTrue(additionalChargeDriverFeeData.isEmpty());
  }

  @Test
  void driverFeeCalculateNull_WithEmptyDriverFeeConfig() {

    FlatFareVOPart origin = new FlatFareVOPart();
    origin.setTotalFare(BigDecimal.valueOf(13.0));
    origin.setEstimatedFareLF(BigDecimal.valueOf(12.0));
    origin.setEstimatedFareRT(BigDecimal.valueOf(15.0));
    Optional<AdditionalChargeDriverFeeData> additionalChargeDriverFeeData =
        driverFeeAdditionalChargeProcessor.calculateAdditionalCharge(origin, Map.of());
    assertTrue(additionalChargeDriverFeeData.isEmpty());
  }

  @Test
  void driverFeeCalculateValueNull_WithOnlyIsCountInTotalfareTrue() {

    FlatFareVOPart origin = new FlatFareVOPart();
    origin.setTotalFare(BigDecimal.valueOf(13.0));
    origin.setEstimatedFareLF(BigDecimal.valueOf(12.0));
    origin.setEstimatedFareRT(BigDecimal.valueOf(15.0));
    Optional<AdditionalChargeDriverFeeData> additionalChargeDriverFeeData =
        driverFeeAdditionalChargeProcessor.calculateAdditionalCharge(
            origin,
            AdditionalChargeFeeConfigHelper.generateDriverFeeConfigOnlyIsCountInTotalfareTrue());
    assertTrue(additionalChargeDriverFeeData.isPresent());
    assertNull(additionalChargeDriverFeeData.get().getTotalFareDriverFee());
    assertNull(additionalChargeDriverFeeData.get().getEstimatedFareLFDriverFee());
    assertNull(additionalChargeDriverFeeData.get().getEstimatedFareRTDriverFee());
  }

  @Test
  void driverFeeCalculateValueNull_WithConfigValueEmpty() {

    FlatFareVOPart origin = new FlatFareVOPart();
    origin.setTotalFare(BigDecimal.valueOf(13.0));
    origin.setEstimatedFareLF(BigDecimal.valueOf(12.0));
    origin.setEstimatedFareRT(BigDecimal.valueOf(15.0));
    Optional<AdditionalChargeDriverFeeData> additionalChargeDriverFeeData =
        driverFeeAdditionalChargeProcessor.calculateAdditionalCharge(
            origin, AdditionalChargeFeeConfigHelper.generateDriverFeeConfigWithEmptyValue());
    assertTrue(additionalChargeDriverFeeData.isPresent());
    assertNull(additionalChargeDriverFeeData.get().getTotalFareDriverFee());
    assertNull(additionalChargeDriverFeeData.get().getEstimatedFareLFDriverFee());
    assertNull(additionalChargeDriverFeeData.get().getEstimatedFareRTDriverFee());
  }

  @Test
  void driverFeeCalculateValueNull_WithNullOriginFareValue() {

    FlatFareVOPart origin = new FlatFareVOPart();
    origin.setTotalFare(null);
    origin.setEstimatedFareLF(null);
    origin.setEstimatedFareRT(null);
    Optional<AdditionalChargeDriverFeeData> additionalChargeDriverFeeData =
        driverFeeAdditionalChargeProcessor.calculateAdditionalCharge(
            origin, AdditionalChargeFeeConfigHelper.generateDriverFeeConfig());
    assertTrue(additionalChargeDriverFeeData.isPresent());
    assertNull(additionalChargeDriverFeeData.get().getTotalFareDriverFee());
    assertNull(additionalChargeDriverFeeData.get().getEstimatedFareLFDriverFee());
    assertNull(additionalChargeDriverFeeData.get().getEstimatedFareRTDriverFee());
  }
}

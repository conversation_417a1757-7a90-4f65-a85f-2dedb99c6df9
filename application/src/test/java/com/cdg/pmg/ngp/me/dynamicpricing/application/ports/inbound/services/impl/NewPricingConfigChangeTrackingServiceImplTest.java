package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.HashObjectService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.NewPricingModelConfigChangeRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.NewPricingModelConfigChangeEntity;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class NewPricingConfigChangeTrackingServiceImplTest {

  @Spy private HashObjectService hashObjectService = new HashObjectServiceImpl();

  @Mock private NewPricingModelConfigChangeRepository newPricingModelConfigChangeRepository;

  @InjectMocks
  private NewPricingConfigChangeTrackingServiceImpl newPricingConfigChangeTrackingService;

  @Test
  void givenListOfNewPricingModelConfigChangeEntity_whenPerformSaveAll_thenShouldSuccess() {

    // Arrange
    List<NewPricingModelConfigChangeEntity> changeEntities =
        List.of(
            spy(NewPricingModelConfigChangeEntity.builder().configId(1L).build()),
            spy(NewPricingModelConfigChangeEntity.builder().configId(2L).build()));

    // Act
    newPricingConfigChangeTrackingService.saveAll(changeEntities);

    // Assert
    verify(newPricingModelConfigChangeRepository).saveAllPricingConfigModelChanges(changeEntities);
    assertNotNull(changeEntities.get(0).getUniquePayloadHash());
    assertNotNull(changeEntities.get(1).getUniquePayloadHash());
  }
}

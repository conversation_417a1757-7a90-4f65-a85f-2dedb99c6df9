package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.*;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.DynpConfigCacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.FlatFareConfigService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.FlatFareConfigRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.CacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.ConfigKeyConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FlatFareConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.FareTypeEnum;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.DomainException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class FlatFareConfigServiceImplTest {
  @Mock private FlatFareConfigService flatFareConfigService;

  @Mock private FlatFareConfigRepository flatFareConfigRepository;

  @Mock private DynpConfigCacheService dynpConfigCacheService;

  @Mock private CacheService cacheService;

  @BeforeEach
  void setUp() {
    flatFareConfigService =
        new FlatFareConfigServiceImpl(
            dynpConfigCacheService, flatFareConfigRepository, cacheService);
  }

  @Test
  void givenEmptyDataInDB_whenLoadAllFlatFareConfig_thenReturnEmptyConfig() {
    Mockito.when(flatFareConfigRepository.getAllFlatFareConfig()).then(config -> new ArrayList<>());
    flatFareConfigService.loadAllFlatFareConfig();
    Mockito.verify(dynpConfigCacheService, Mockito.times(0))
        .addListConfigsToCache(Mockito.anyString(), Mockito.anyList());
  }

  @Test
  void givenNormalDataInDB_whenLoadAllFlatFareConfig_thenNormalConfig() {
    final List<FlatFareConfig> configs =
        List.of(
            new FlatFareConfig(ConfigKeyConstant.EVENT_SURGE_ADDR_DAYS_0, ""),
            new FlatFareConfig(ConfigKeyConstant.EVENT_SURGE_ZONE_DAYS_0, ""),
            new FlatFareConfig(ConfigKeyConstant.ADDITIONAL_CHARGE_0, ""),
            //            new FlatFareConfig(ConfigKeyConstant.LIVE_TRAFFIC_PEAK_HOUR_DAYS_0, ""),
            //            new FlatFareConfig(ConfigKeyConstant.LIVE_TRAFFIC_MID_NIGHT_DAYS_0, ""),
            //            new FlatFareConfig(ConfigKeyConstant.LIVE_TRAFFIC_TIER_2_END_DISTANCE,
            // ""),
            new FlatFareConfig(ConfigKeyConstant.EST_LIVE_TRAFFIC_PEAK_HOUR_DAYS_0, ""),
            new FlatFareConfig(ConfigKeyConstant.EST_LIMO_LIVE_TRAFFIC_PEAK_HOUR_DAYS_0, ""),
            new FlatFareConfig(ConfigKeyConstant.EST_WAIT_TIME_PEAK_HOUR_DAYS_0, ""),
            new FlatFareConfig(ConfigKeyConstant.EST_PEAK_HOUR_RATE_0, ""),
            // Group data for DYNP_PRICE_SCHEDULER key
            new FlatFareConfig(ConfigKeyConstant.DYNAMIC_PRICING_START_TIME_0, ""),
            new FlatFareConfig(ConfigKeyConstant.DYNAMIC_PRICING_END_TIME_0, ""),
            // Group data for DRIVER_SURGE_LEVEL_INDICATION key
            new FlatFareConfig(ConfigKeyConstant.DRV_EFFECT_FROM_TS_0, ""),
            new FlatFareConfig(ConfigKeyConstant.DRV_EFFECT_TO_TS_0, ""),
            new FlatFareConfig(ConfigKeyConstant.DRV_SURGE_COLOR_HEX_0, ""),
            new FlatFareConfig(ConfigKeyConstant.DRV_SURGE_LEVEL_0, ""),
            new FlatFareConfig(ConfigKeyConstant.DRV_SURGE_PERC_FROM_0, ""),
            new FlatFareConfig(ConfigKeyConstant.DRV_SURGE_PERC_TO_0, ""));

    Mockito.when(flatFareConfigRepository.getAllFlatFareConfig()).then(config -> configs);
    flatFareConfigService.loadAllFlatFareConfig();
    Mockito.verify(cacheService, Mockito.times(8)).setValue(Mockito.anyString(), Mockito.anyMap());
  }

  @Test
  void givenNormalConfig_whenGetFlatFareConfigSet_thenReturnSuccess() {
    // GIVEN
    Map<String, String> peakHourRate0 = new HashMap<>();
    peakHourRate0.put("LIVE_TRAFFIC_PEAK_HOUR_DAYS_0", "MON,TUE,WED,THU,FRI");
    peakHourRate0.put("LIVE_TRAFFIC_PEAK_HOUR_END_TIME_0", "09:29:59");
    peakHourRate0.put("LIVE_TRAFFIC_PEAK_HOUR_RATE_0", "0.25");
    peakHourRate0.put("LIVE_TRAFFIC_PEAK_HOUR_START_TIME_0", "06:00:00");
    peakHourRate0.put("LIVE_TRAFFIC_PEAK_HOUR_STEPDOWN_10mins_0", "0.15");
    peakHourRate0.put("LIVE_TRAFFIC_PEAK_HOUR_STEPDOWN_15mins_0", "0.2");
    peakHourRate0.put("LIVE_TRAFFIC_PEAK_HOUR_STEPDOWN_5mins_0", "0.1");
    peakHourRate0.put("LIVE_TRAFFIC_PEAK_HOUR_STEPUP_10mins_0", "0.15");
    peakHourRate0.put("LIVE_TRAFFIC_PEAK_HOUR_STEPUP_15mins_0", "0.1");
    peakHourRate0.put("LIVE_TRAFFIC_PEAK_HOUR_STEPUP_5mins_0", "0.2");
    List<Map<String, String>> peakHourRateList = List.of(peakHourRate0);

    Map<String, String> midnightRate0 = new HashMap<>();
    midnightRate0.put("LIVE_TRAFFIC_MID_NIGHT_DAYS_0", "MON,TUE,WED,THU,FRI");
    midnightRate0.put("LIVE_TRAFFIC_MID_NIGHT_END_TIME_0", "09:29:59");
    midnightRate0.put("LIVE_TRAFFIC_MID_NIGHT_RATE_0", "0.25");
    midnightRate0.put("LIVE_TRAFFIC_MID_NIGHT_START_TIME_0", "06:00:00");
    midnightRate0.put("LIVE_TRAFFIC_MID_NIGHT_STEPDOWN_10mins_0", "0.15");
    midnightRate0.put("LIVE_TRAFFIC_MID_NIGHT_STEPDOWN_15mins_0", "0.2");
    midnightRate0.put("LIVE_TRAFFIC_MID_NIGHT_STEPDOWN_5mins_0", "0.1");
    midnightRate0.put("LIVE_TRAFFIC_MID_NIGHT_STEPUP_10mins_0", "0.15");
    midnightRate0.put("LIVE_TRAFFIC_MID_NIGHT_STEPUP_15mins_0", "0.1");
    midnightRate0.put("LIVE_TRAFFIC_MID_NIGHT_STEPUP_5mins_0", "0.2");
    List<Map<String, String>> midnightRateList = List.of(midnightRate0);

    Map<String, String> sigleConfig = new HashMap<>();
    sigleConfig.put("SINGLE_CONFIG", "VALUE");

    String flagDownRate = "0.5";
    String tierPerCountFare = "value";
    String tierPerCountMeter = "value";
    String tierStartDistance = "value";
    String tierEndDistance = "value";

    String totalFareEstimateLF = "value";
    String totalFareEstimateRT = "value";

    String durationUnitConfig = "value";
    String durationRateConfig = "value";

    String maxFlatFareCap = "value";

    // WHEN
    when(dynpConfigCacheService.getMultiMapConfigsFromCache(Mockito.anyString()))
        .thenReturn(peakHourRateList)
        .thenReturn(midnightRateList);

    when(dynpConfigCacheService.getValue(Mockito.anyString(), Mockito.any()))
        .thenReturn(flagDownRate)
        .thenReturn(tierPerCountFare)
        .thenReturn(tierPerCountMeter)
        .thenReturn(tierStartDistance)
        .thenReturn(tierEndDistance)
        .thenReturn(tierPerCountFare)
        .thenReturn(tierPerCountMeter)
        .thenReturn(tierStartDistance)
        .thenReturn(tierEndDistance)
        .thenReturn(totalFareEstimateLF)
        .thenReturn(totalFareEstimateRT)
        .thenReturn(durationUnitConfig)
        .thenReturn(durationRateConfig)
        .thenReturn(maxFlatFareCap);

    List<Map<String, String>> listMapResult = getEventSurgeChargeConfigs();
    when(dynpConfigCacheService.getMultiMapConfigsFromCache(Mockito.anyString()))
        .thenReturn(listMapResult);

    // THEN
    FlatFareConfigSet resultConfig =
        flatFareConfigService.collectFlatFareConfigSet(FareTypeEnum.EST_LIVE_TRAFFIC);
    assertNotNull(resultConfig);
  }

  private static List<Map<String, String>> getEventSurgeChargeConfigs() {
    Map<String, String> mapConfig0 = new HashMap<>();
    int i = 0;
    mapConfig0.put(FlatfareConstants.EVENT_SURGE_ADDR_APPC + i, "value");
    mapConfig0.put(FlatfareConstants.EVENT_SURGE_ADDR_CHARGEBY + i, "value");
    mapConfig0.put(FlatfareConstants.EVENT_SURGE_ADDR_CHARGETYPE + i, "value");
    mapConfig0.put(FlatfareConstants.EVENT_SURGE_ADDR_CHARGEVAL + i, "0.1");
    mapConfig0.put(FlatfareConstants.EVENT_SURGE_ADDR_DAYS + i, "value");
    mapConfig0.put(FlatfareConstants.EVENT_SURGE_ADDR_MONTHS + i, "value");
    mapConfig0.put(FlatfareConstants.EVENT_SURGE_ADDR_START_TIME + i, "00:00:00");
    mapConfig0.put(FlatfareConstants.EVENT_SURGE_ADDR_END_TIME + i, "23:59:59");
    i = 1;
    Map<String, String> mapConfig1 = new HashMap<>();
    mapConfig1.put(FlatfareConstants.EVENT_SURGE_ADDR_APPC + i, "value");
    mapConfig1.put(FlatfareConstants.EVENT_SURGE_ADDR_CHARGEBY + i, "value");
    mapConfig1.put(FlatfareConstants.EVENT_SURGE_ADDR_CHARGETYPE + i, "value");
    mapConfig1.put(FlatfareConstants.EVENT_SURGE_ADDR_CHARGEVAL + i, "0.1");
    mapConfig1.put(FlatfareConstants.EVENT_SURGE_ADDR_DAYS + i, "value");
    mapConfig1.put(FlatfareConstants.EVENT_SURGE_ADDR_MONTHS + i, "value");
    mapConfig1.put(FlatfareConstants.EVENT_SURGE_ADDR_START_TIME + i, "00:00:00");
    mapConfig1.put(FlatfareConstants.EVENT_SURGE_ADDR_END_TIME + i, "23:59:59");

    List<Map<String, String>> listMapResult = List.of(mapConfig0, mapConfig1);
    return listMapResult;
  }

  private static List<Map<String, String>> getEventSurgeChargeConfigsMissing() {
    Map<String, String> mapConfig0 = new HashMap<>();
    int i = 0;
    mapConfig0.put(FlatfareConstants.EVENT_SURGE_ADDR_APPC + i, "value");
    mapConfig0.put(FlatfareConstants.EVENT_SURGE_ADDR_DAYS + i, "value");
    mapConfig0.put(FlatfareConstants.EVENT_SURGE_ADDR_MONTHS + i, "value");
    mapConfig0.put(FlatfareConstants.EVENT_SURGE_ADDR_START_TIME + i, "00:00:00");
    mapConfig0.put(FlatfareConstants.EVENT_SURGE_ADDR_END_TIME + i, "23:59:59");
    i = 1;
    Map<String, String> mapConfig1 = new HashMap<>();
    mapConfig1.put(FlatfareConstants.EVENT_SURGE_ADDR_APPC + i, "value");
    mapConfig1.put(FlatfareConstants.EVENT_SURGE_ADDR_CHARGEBY + i, "value");
    mapConfig1.put(FlatfareConstants.EVENT_SURGE_ADDR_CHARGETYPE + i, "value");
    mapConfig1.put(FlatfareConstants.EVENT_SURGE_ADDR_CHARGEVAL + i, "0.1");
    mapConfig1.put(FlatfareConstants.EVENT_SURGE_ADDR_DAYS + i, "value");
    mapConfig1.put(FlatfareConstants.EVENT_SURGE_ADDR_MONTHS + i, "value");
    mapConfig1.put(FlatfareConstants.EVENT_SURGE_ADDR_START_TIME + i, "00:00:00");
    mapConfig1.put(FlatfareConstants.EVENT_SURGE_ADDR_END_TIME + i, "23:59:59");

    List<Map<String, String>> listMapResult = List.of(mapConfig0, mapConfig1);
    return listMapResult;
  }

  private static List<Map<String, String>> getEventSurgeChargeConfigsNotContinuous() {
    Map<String, String> mapConfig0 = new HashMap<>();
    int i = 1;
    Map<String, String> mapConfig1 = new HashMap<>();
    mapConfig1.put(FlatfareConstants.EVENT_SURGE_ADDR_APPC + i, "value");
    mapConfig1.put(FlatfareConstants.EVENT_SURGE_ADDR_CHARGEBY + i, "value");
    mapConfig1.put(FlatfareConstants.EVENT_SURGE_ADDR_CHARGETYPE + i, "value");
    mapConfig1.put(FlatfareConstants.EVENT_SURGE_ADDR_CHARGEVAL + i, "0.1");
    mapConfig1.put(FlatfareConstants.EVENT_SURGE_ADDR_DAYS + i, "value");
    mapConfig1.put(FlatfareConstants.EVENT_SURGE_ADDR_MONTHS + i, "value");
    mapConfig1.put(FlatfareConstants.EVENT_SURGE_ADDR_START_TIME + i, "00:00:00");
    mapConfig1.put(FlatfareConstants.EVENT_SURGE_ADDR_END_TIME + i, "23:59:59");
    i = 3;
    Map<String, String> mapConfig3 = new HashMap<>();
    mapConfig3.put(FlatfareConstants.EVENT_SURGE_ADDR_APPC + i, "value");
    mapConfig3.put(FlatfareConstants.EVENT_SURGE_ADDR_CHARGEBY + i, "value");
    mapConfig3.put(FlatfareConstants.EVENT_SURGE_ADDR_CHARGETYPE + i, "value");
    mapConfig3.put(FlatfareConstants.EVENT_SURGE_ADDR_CHARGEVAL + i, "0.1");
    mapConfig3.put(FlatfareConstants.EVENT_SURGE_ADDR_DAYS + i, "value");
    mapConfig3.put(FlatfareConstants.EVENT_SURGE_ADDR_MONTHS + i, "value");
    mapConfig3.put(FlatfareConstants.EVENT_SURGE_ADDR_START_TIME + i, "00:00:00");
    mapConfig3.put(FlatfareConstants.EVENT_SURGE_ADDR_END_TIME + i, "23:59:59");
    i = 4;
    Map<String, String> mapConfig4 = new HashMap<>();
    mapConfig4.put(FlatfareConstants.EVENT_SURGE_ADDR_APPC + i, "value");
    mapConfig4.put(FlatfareConstants.EVENT_SURGE_ADDR_CHARGEBY + i, "value");
    mapConfig4.put(FlatfareConstants.EVENT_SURGE_ADDR_CHARGETYPE + i, "value");
    mapConfig4.put(FlatfareConstants.EVENT_SURGE_ADDR_START_TIME + i, "00:00:00");
    mapConfig4.put(FlatfareConstants.EVENT_SURGE_ADDR_END_TIME + i, "23:59:59");
    List<Map<String, String>> listMapResult =
        List.of(mapConfig0, mapConfig1, mapConfig3, mapConfig4);
    return listMapResult;
  }

  @Test
  void givenNormalConfig_whenCollectCommonConfigSet_thenReturnSuccess() {
    // GIVEN
    Map<String, String> mapConfig = new HashMap<>();
    int i = 0;
    mapConfig.put(i + FlatfareConstants.DRV_EFFECT_FROM_TS, "3");
    mapConfig.put(i + FlatfareConstants.DRV_EFFECT_TO_TS, "3");
    mapConfig.put(i + FlatfareConstants.DRV_SURGE_COLOR_HEX, "3");
    mapConfig.put(i + FlatfareConstants.DRV_SURGE_LEVEL, "3");
    mapConfig.put(i + FlatfareConstants.DRV_SURGE_PERC_FROM, "3");
    mapConfig.put(i + FlatfareConstants.DRV_SURGE_PERC_TO, "3");
    // WHEN
    when(dynpConfigCacheService.getMultiMapConfigsFromCache(Mockito.anyString()))
        .thenReturn(List.of(mapConfig));

    // WHEN
    when(dynpConfigCacheService.getObjectValueByPattern(Mockito.anyString(), Mockito.any()))
        .thenReturn("limoFlatFareVehIds")
        .thenReturn("vehGrpShowMeterOnly")
        .thenReturn("vehGrpShowFFOnly")
        .thenReturn("cacheTimerMinsMultiFlatFare")
        .thenReturn("cacheTimerMinsBreakdownFlatFare")
        .thenReturn("surgeIndicatorThreshold")
        .thenReturn("surgeIndicatorThresholdZero");

    // THEN
    CommonConfigSet resultConfig = flatFareConfigService.collectCommonConfigSet();
    assertNotNull(resultConfig);
  }

  @Test
  void givenNormalConfig_whenGetStringValueByKey_thenReturnSuccess() {
    // GIVEN
    String value = "value";
    // WHEN
    when(dynpConfigCacheService.getObjectValueByPattern(Mockito.anyString(), Mockito.any()))
        .thenReturn(value);
    // THEN
    String resultConfig = flatFareConfigService.getStringValueByKey("key");
    assertEquals(value, resultConfig);
  }

  @Test
  void givenNormalConfig_whenGetLocationSurchargeConfig_thenReturnSuccess() {
    // WHEN
    when(dynpConfigCacheService.getListLocSurcharge(Mockito.anyString(), Mockito.any()))
        .thenReturn(List.of(LocationSurchargeConfig.builder().surchargeValue(3.0).build()));
    // THEN
    List<LocationSurchargeConfig> resultConfig =
        flatFareConfigService.getLocationSurchargeConfig("key");
    assertEquals(3.0, resultConfig.get(0).getSurchargeValue());
  }

  @Test
  void givenNormalConfig_whenGetFlatFareAdditionalSurchargeConfig_thenReturnSuccess() {
    // WHEN
    when(dynpConfigCacheService.getListDoubleConfigsFromCache(
            Mockito.anyString(), Mockito.anyString()))
        .thenReturn(List.of(3.0));
    // THEN
    List<Double> resultConfig = flatFareConfigService.getFlatFareAdditionalSurchargeConfig();
    assertEquals(3.0, resultConfig.get(0).doubleValue());
  }

  @Test
  void givenNormalConfig_whenGetListDriverSurgeConfig_thenReturnSuccess() {
    // GIVEN
    Map<String, String> mapConfig = new HashMap<>();
    int i = 0;
    mapConfig.put(i + FlatfareConstants.DRV_EFFECT_FROM_TS, "3");
    mapConfig.put(i + FlatfareConstants.DRV_EFFECT_TO_TS, "3");
    mapConfig.put(i + FlatfareConstants.DRV_SURGE_COLOR_HEX, "3");
    mapConfig.put(i + FlatfareConstants.DRV_SURGE_LEVEL, "3");
    mapConfig.put(i + FlatfareConstants.DRV_SURGE_PERC_FROM, "3");
    mapConfig.put(i + FlatfareConstants.DRV_SURGE_PERC_TO, "3");
    // WHEN
    when(dynpConfigCacheService.getMultiMapConfigsFromCache(Mockito.anyString()))
        .thenReturn(List.of(mapConfig));
    // THEN
    List<DriverSurgeLevelIndication> resultConfig =
        flatFareConfigService.getListDriverSurgeConfig();
    assertEquals(3, resultConfig.get(0).getSurgeLevel());
  }

  @Test
  void givenNormalConfig_whenGetListDriverSurgeConfig_thenThrowsException() {
    // GIVEN
    Map<String, String> mapConfig = new HashMap<>();
    int i = 0;
    mapConfig.put(i + FlatfareConstants.DRV_EFFECT_FROM_TS, "string");
    mapConfig.put(i + FlatfareConstants.DRV_EFFECT_TO_TS, "string");
    mapConfig.put(i + FlatfareConstants.DRV_SURGE_COLOR_HEX, "string");
    mapConfig.put(i + FlatfareConstants.DRV_SURGE_LEVEL, "string");
    mapConfig.put(i + FlatfareConstants.DRV_SURGE_PERC_FROM, "string");
    mapConfig.put(i + FlatfareConstants.DRV_SURGE_PERC_TO, "string");
    // WHEN
    when(dynpConfigCacheService.getMultiMapConfigsFromCache(Mockito.anyString()))
        .thenReturn(List.of(mapConfig));
    // THEN
    assertThrows(DomainException.class, () -> flatFareConfigService.getListDriverSurgeConfig());
  }

  @Test
  void givenNormalConfig_whenGetListHoliday_thenReturnSuccess() {
    // WHEN
    when(cacheService.getListValue(Mockito.anyString(), Mockito.any()))
        .thenReturn(List.of(FlatFareHoliday.builder().date("2023-09-20 00:00:00").build()));
    // THEN
    List<FlatFareHoliday> listActual = flatFareConfigService.getListHoliday();
    assertEquals("2023-09-20 00:00:00", listActual.get(0).getDate());
  }

  @Test
  void getListEventSurgeAddressConfig_noParam_success() {
    List<Map<String, String>> listMapResult = getEventSurgeChargeConfigs();
    when(dynpConfigCacheService.getMultiMapConfigsFromCache(Mockito.anyString()))
        .thenReturn(listMapResult);
    List<EventSurgeAddressConfig> resultConfig =
        flatFareConfigService.getListEventSurgeAddressConfig();

    int expectedSize = 2;
    int actual = resultConfig.size();

    assertEquals(expectedSize, actual);
  }

  @Test
  void getListEventSurgeAddressConfig_noParam_errorConfig() {
    Map<String, String> mapConfig0 = new HashMap<>();
    int i = 0;
    mapConfig0.put(FlatfareConstants.EVENT_SURGE_ADDR_APPC + i, "value");
    mapConfig0.put(FlatfareConstants.EVENT_SURGE_ADDR_CHARGEBY + i, "value");
    mapConfig0.put(FlatfareConstants.EVENT_SURGE_ADDR_CHARGETYPE + i, "value");
    mapConfig0.put(FlatfareConstants.EVENT_SURGE_ADDR_CHARGEVAL + i, "STRING");
    mapConfig0.put(FlatfareConstants.EVENT_SURGE_ADDR_DAYS + i, "value");
    mapConfig0.put(FlatfareConstants.EVENT_SURGE_ADDR_MONTHS + i, "value");
    mapConfig0.put(FlatfareConstants.EVENT_SURGE_ADDR_START_TIME + i, "00:00:00");
    mapConfig0.put(FlatfareConstants.EVENT_SURGE_ADDR_END_TIME + i, "23:59:59");

    List<Map<String, String>> listMapResult = List.of(mapConfig0);
    when(dynpConfigCacheService.getMultiMapConfigsFromCache(Mockito.anyString()))
        .thenReturn(listMapResult);

    assertDoesNotThrow(() -> flatFareConfigService.getListEventSurgeAddressConfig());
  }

  @Test
  void givenMissingConfig_whenGetListEventSurgeAddressConfig_thenSuccess() {
    List<Map<String, String>> listMapResult = getEventSurgeChargeConfigsMissing();
    when(dynpConfigCacheService.getMultiMapConfigsFromCache(Mockito.anyString()))
        .thenReturn(listMapResult);
    List<EventSurgeAddressConfig> resultConfig =
        flatFareConfigService.getListEventSurgeAddressConfig();

    int expectedSize = 1;
    int actual = resultConfig.size();

    assertEquals(expectedSize, actual);
  }

  @Test
  void givenConfigIsNotContinuous_whenGetListEventSurgeAddressConfig_thenSuccess() {
    List<Map<String, String>> listMapResult = getEventSurgeChargeConfigsNotContinuous();
    when(dynpConfigCacheService.getMultiMapConfigsFromCache(Mockito.anyString()))
        .thenReturn(listMapResult);
    List<EventSurgeAddressConfig> resultConfig =
        flatFareConfigService.getListEventSurgeAddressConfig();

    int expectedSize = 2;
    int actual = resultConfig.size();

    assertEquals(expectedSize, actual);
  }
}

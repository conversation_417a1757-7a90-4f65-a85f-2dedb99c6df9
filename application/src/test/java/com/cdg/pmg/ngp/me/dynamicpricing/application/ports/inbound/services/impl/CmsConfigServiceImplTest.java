package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.commands.NewPricingModelCommand;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.ListConfigObject;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.mapper.NewPricingModelMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.CMSConfigItem;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.CMSConfigList;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.CMSServiceOutboundPort;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.ConfigurationServiceOutboundPort;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.NewPricingModelConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.BadRequestException;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.NotFoundException;
import com.cdg.pmg.ngp.me.dynamicpricing.service.NewPricingModelService;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CmsConfigServiceImplTest {

  @Mock private CMSServiceOutboundPort cmsServiceOutboundPort;
  @Mock private NewPricingModelMapper newPricingModelMapper;
  @Mock private NewPricingModelService newPricingModelService;
  @Mock private ConfigurationServiceOutboundPort configurationServiceOutboundPort;
  @Mock private ObjectMapper objectMapper;
  @Captor ArgumentCaptor<Integer> indexCaptor;
  @Captor ArgumentCaptor<Long> longValueCaptor;
  @Captor ArgumentCaptor<String> stringArgumentCaptor;
  private CmsConfigServiceImpl cmsConfigService;

  @BeforeEach
  public void setUp() {
    cmsConfigService =
        spy(
            new CmsConfigServiceImpl(
                cmsServiceOutboundPort,
                newPricingModelMapper,
                newPricingModelService,
                objectMapper,
                configurationServiceOutboundPort));
  }

  @Test
  void whenGetListNewPricingModelConfigEntityInCms_thenReturnListNewPricingModelConfigEntity() {
    // Arrange
    when(newPricingModelService.getListNewPricingModelConfigEntityInCms()).thenReturn(List.of());

    // Act
    List<NewPricingModelConfigEntity> newPricingModelConfigEntities =
        cmsConfigService.getListNewPricingModelConfigEntityInCms();
    // Assert
    assertTrue(newPricingModelConfigEntities.isEmpty());
  }

  @Test
  void givenNotFoundIndex_whenUpdateNewPricingModelConfigEntityInCms_thenThrowsNotFoundException() {
    // Arrange
    NewPricingModelCommand command = new NewPricingModelCommand();
    when(cmsServiceOutboundPort.getCMSBySearchText(anyString()))
        .thenReturn(CMSConfigList.builder().build());

    // Act
    // Assert
    assertThrows(
        NotFoundException.class,
        () -> cmsConfigService.updateNewPricingModelConfigEntityInCms(command));
  }

  @Test
  void givenFoundIndex_whenUpdateNewPricingModelConfigEntityInCms_thenSuccessUpdate() {
    // Arrange
    NewPricingModelCommand command = new NewPricingModelCommand();
    when(cmsServiceOutboundPort.getCMSBySearchText(anyString()))
        .thenReturn(CMSConfigList.builder().data(List.of(CMSConfigItem.builder().build())).build());
    doNothing().when(newPricingModelMapper).mergeNewPricingModel(any(), any());
    when(newPricingModelMapper.mapToNewPricingModelConfigEntities(any(), any()))
        .thenReturn(List.of(new NewPricingModelConfigEntity()));

    // Act
    cmsConfigService.updateNewPricingModelConfigEntityInCms(command);

    // Assert
    verify(newPricingModelMapper, times(1)).mapToNewPricingModelConfigEntities(any(), any());
    verify(newPricingModelService, times(1)).validateIgnoreZoneCheck(any());
    verify(newPricingModelService, times(1)).update(any());
  }

  @Test
  void
      givenNewPricingModelCommand_whenCreateNewPricingConfigModelConfigEntityInCms_thenSuccessCreateNewPricingModelConfig() {
    // Arrange
    var command = new NewPricingModelCommand();
    when(newPricingModelMapper.mapToNewPricingModelConfigEntity(command))
        .thenReturn(new NewPricingModelConfigEntity());
    when(configurationServiceOutboundPort.getTotalSizeNewPricingModelConfigEntities())
        .thenReturn(2);

    // Act
    cmsConfigService.createNewPricingConfigModelConfigEntityInCms(command);

    // Assert
    verify(newPricingModelService, times(1)).create(any(), indexCaptor.capture());
    assertEquals(2, indexCaptor.getValue());
  }

  @Test
  void givenDescriptionKeyAndValue_whenAddNewConfig_thenPerformSuccessfully() {
    //  Arrange
    String descriptionKey = "test-key";
    long expectedLongId = 10L;
    String expectedListResult = "A,B,C,D";
    doReturn(ListConfigObject.builder().id(expectedLongId).items(List.of("A", "B", "C")).build())
        .when(cmsConfigService)
        .getConfigsByDescriptionKey(descriptionKey);

    // Act
    cmsConfigService.addNewConfig(descriptionKey, "D");

    // Assert
    verify(cmsServiceOutboundPort, times(1))
        .updateCMSConfig(longValueCaptor.capture(), stringArgumentCaptor.capture());
    assertEquals(expectedLongId, longValueCaptor.getValue());
    assertEquals(expectedListResult, stringArgumentCaptor.getValue());
  }

  @Test
  void givenDescriptionKeyAndValueAlreadyExists_whenAddNewConfig_thenThrowsBadRequestException() {
    // Arrange
    String descriptionKey = "test-key";
    long expectedLongId = 10L;
    doReturn(ListConfigObject.builder().id(expectedLongId).items(List.of("A", "B", "C")).build())
        .when(cmsConfigService)
        .getConfigsByDescriptionKey(descriptionKey);

    // Act
    // Assert
    assertThrows(
        BadRequestException.class, () -> cmsConfigService.addNewConfig(descriptionKey, "C"));
    verify(cmsServiceOutboundPort, never()).updateCMSConfig(any(), any());
  }

  @Test
  void
      givenDescriptionKeyNotFoundInCMS_whenGetConfigsByDescriptionKey_thenThrowsNotFoundException() {
    // Arrange
    var descriptionKey = "NOT_FOUND_DESCRIPTION_KEY";
    when(cmsServiceOutboundPort.getCMSBySearchText(descriptionKey))
        .thenReturn(CMSConfigList.builder().build());

    // Act
    // Assert
    assertThrows(
        NotFoundException.class, () -> cmsConfigService.getConfigsByDescriptionKey(descriptionKey));
  }

  @Test
  void givenDescriptionKeyFoundInCMS_whenGetConfigsByDescriptionKey_thenReturnListConfigObject() {
    // Arrange
    var descriptionKey = "NOT_FOUND_DESCRIPTION_KEY";
    when(cmsServiceOutboundPort.getCMSBySearchText(descriptionKey))
        .thenReturn(
            CMSConfigList.builder()
                .data(List.of(CMSConfigItem.builder().id(0L).value("A,B").build()))
                .build());
    // Act
    ListConfigObject listConfigObject = cmsConfigService.getConfigsByDescriptionKey(descriptionKey);

    // Assert
    assertNotNull(listConfigObject);
    assertEquals(2, listConfigObject.getItems().size());
  }

  @Test
  void
      givenDescriptionKeyNotFoundInCMS_whenDeleteNewConfigByDescriptionKey_thenThrowNotFoundException() {
    // Arrange
    var descriptionKey = "NOT_FOUND_DESCRIPTION_KEY";
    when(cmsServiceOutboundPort.getCMSBySearchText(descriptionKey))
        .thenReturn(
            CMSConfigList.builder()
                .data(List.of(CMSConfigItem.builder().id(0L).value("A,B").build()))
                .build());

    // Act
    // Assert
    assertThrows(
        NotFoundException.class, () -> cmsConfigService.deleteNewConfig(descriptionKey, ""));
  }

  @Test
  void givenDescriptionKeyFoundInCMS_whenDeleteNewConfigByDescriptionKey_thenRemoveSuccess() {
    // Arrange
    var descriptionKey = "NOT_FOUND_DESCRIPTION_KEY";
    var expectedList = "A";
    when(cmsServiceOutboundPort.getCMSBySearchText(descriptionKey))
        .thenReturn(
            CMSConfigList.builder()
                .data(List.of(CMSConfigItem.builder().id(0L).value("A,B").build()))
                .build());

    // Act
    cmsConfigService.deleteNewConfig(descriptionKey, "B");

    // Assert
    verify(cmsServiceOutboundPort)
        .updateCMSConfig(longValueCaptor.capture(), stringArgumentCaptor.capture());
    assertEquals(expectedList, stringArgumentCaptor.getValue());
    assertEquals(0L, longValueCaptor.getValue());
  }
}

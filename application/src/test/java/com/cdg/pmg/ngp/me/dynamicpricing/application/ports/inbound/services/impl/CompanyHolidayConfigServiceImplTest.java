package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.CompanyHolidayConfigService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.DateUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.CompanyHolidayRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.CacheService;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CompanyHolidayConfigServiceImplTest {
  @Mock private CompanyHolidayConfigService companyHolidayConfigService;
  @Mock private CompanyHolidayRepository companyHolidayRepository;
  @Mock private CacheService cacheService;

  private DateFormat df;

  @BeforeEach
  void setUp() {
    companyHolidayConfigService =
        new CompanyHolidayConfigServiceImpl(cacheService, companyHolidayRepository);
    df = new SimpleDateFormat(DateUtils.YYYYMMDD_HHMMSS_FORMAT);
  }

  @Test
  void loadCompanyHolidayConfig_isEmptyConfigs() {
    Mockito.when(companyHolidayRepository.getCompanyHolidays()).then(config -> new ArrayList<>());
    companyHolidayConfigService.loadCompanyHolidayConfig();
    Mockito.verify(cacheService, Mockito.times(0))
        .setListValue(Mockito.anyString(), Mockito.anyList());
  }

  @Test
  void loadCompanyHolidayConfig_isNotEmptyConfigs() {
    final var currentDate = df.format(new Date());
    final List<String> configs = List.of(currentDate);
    Mockito.when(companyHolidayRepository.getCompanyHolidays()).then(config -> configs);
    companyHolidayConfigService.loadCompanyHolidayConfig();
    Mockito.verify(cacheService, Mockito.times(1))
        .setListValue(Mockito.anyString(), Mockito.anyList());
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.dynamicpricing.impl;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.*;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.dynamicpricing.DynamicPricingCompute;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.CommonUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.DateUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FareTypeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.DomainException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.Month;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class DynamicPricingComputeImplTest {
  @Mock private DynamicPricingCompute dynamicPricingCompute;
  @Mock private DynamicPricingConfigSet configSet;
  private FlatFareVO flatFareVO;

  @BeforeEach
  void setUpDynamicPricingComputeForCalculation() {
    setUpConfigSet();
    dynamicPricingCompute = new DynamicPricingComputeImpl(configSet);
  }

  @Test
  void calFlagDown_calculateSuccessfully_currentDayIsHol_doNotThrowException()
      throws ParseException {
    String startDateString = "09/20/2023";
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy");
    Date currentDate = df.parse(startDateString);

    FlatFareRequest flatFareRequest = FlatFareRequest.builder().requestDate(currentDate).build();
    FlatFareVO flatFareVO = new FlatFareVO();
    flatFareVO.setFlatFareRequest(flatFareRequest);
    dynamicPricingCompute.calFlagDown(flatFareVO);
    assertDoesNotThrow(flatFareVO::getFlagDown);
  }

  @Test
  void calFlagDown_calculateSuccessfully_currentDayIsNormalDay_doNotThrowException() {
    Date currentDate = new Date();
    FlatFareRequest flatFareRequest = FlatFareRequest.builder().requestDate(currentDate).build();
    FlatFareVO flatFareVO = new FlatFareVO();
    flatFareVO.setFlatFareRequest(flatFareRequest);
    dynamicPricingCompute.calFlagDown(flatFareVO);
    assertEquals(3, flatFareVO.getFlagDown());
  }

  @Test
  void calFlagDown_calculateSuccessfully_noConfigForNormalDayAndHoliday_doNotThrowException() {
    Date currentDate = new Date();
    FlatFareRequest flatFareRequest = FlatFareRequest.builder().requestDate(currentDate).build();
    FlatFareVO flatFareVO = new FlatFareVO();
    flatFareVO.setFlatFareRequest(flatFareRequest);
    configSet
        .getFlagDownConfigList()
        .remove(
            RedisKeyConstant.DYNAMIC_PRICING
                + RedisKeyConstant.COLON
                + RedisKeyConstant.FARE_TYPE
                + RedisKeyConstant.COLON
                + RedisKeyConstant.DYNP_FLAG_DOWN_RATE
                + RedisKeyConstant.COLON
                + DateUtils.toDayOfWeekShortUpperCase(currentDate));

    dynamicPricingCompute.calFlagDown(flatFareVO);
    assertEquals(2.5, flatFareVO.getFlagDown());
  }

  @Test
  void calFlagDown_calculateSuccessfully_noValidConfig_doNotThrowException() {
    Date currentDate = new Date();
    FlatFareRequest flatFareRequest = FlatFareRequest.builder().requestDate(currentDate).build();
    FlatFareVO flatFareVO = new FlatFareVO();
    flatFareVO.setFlatFareRequest(flatFareRequest);
    configSet
        .getFlagDownConfigList()
        .remove(
            RedisKeyConstant.DYNAMIC_PRICING
                + RedisKeyConstant.COLON
                + RedisKeyConstant.FARE_TYPE
                + RedisKeyConstant.COLON
                + RedisKeyConstant.DYNP_FLAG_DOWN_RATE
                + RedisKeyConstant.COLON
                + DateUtils.toDayOfWeekShortUpperCase(currentDate));
    configSet
        .getFlagDownConfigList()
        .remove(
            RedisKeyConstant.DYNAMIC_PRICING
                + RedisKeyConstant.COLON
                + RedisKeyConstant.FARE_TYPE
                + RedisKeyConstant.COLON
                + RedisKeyConstant.DYNP_FLAG_DOWN_RATE
                + RedisKeyConstant.COLON
                + RedisKeyConstant.ALL);
    dynamicPricingCompute.calFlagDown(flatFareVO);
    assertEquals(0, flatFareVO.getFlagDown());
  }

  @Test
  void calTier1Fare_calculateSuccessfully_doNotThrowException() {
    Date currentDate = new Date();
    FlatFareRequest flatFareRequest =
        FlatFareRequest.builder().requestDate(currentDate).routingDistance(2000).build();
    FlatFareVO flatFareVO = new FlatFareVO();
    flatFareVO.setFlatFareRequest(flatFareRequest);
    dynamicPricingCompute.calTier1Fare(flatFareVO);
    assertEquals(4.0, flatFareVO.getTier1Fare());
  }

  @Test
  void calTier2Fare_calculateSuccessfully_doNotThrowException() {
    Date currentDate = new Date();
    FlatFareRequest flatFareRequest =
        FlatFareRequest.builder().requestDate(currentDate).routingDistance(12000).build();
    FlatFareVO flatFareVO = new FlatFareVO();
    flatFareVO.setFlatFareRequest(flatFareRequest);
    dynamicPricingCompute.calTier2Fare(flatFareVO);
    assertEquals(4, flatFareVO.getTier2Fare());
  }

  @Test
  void calWaitTimeFare_calculateSuccessfully_doNotThrowException() {
    Date currentDate = new Date();
    FlatFareRequest flatFareRequest =
        FlatFareRequest.builder().requestDate(currentDate).ett(300).build();
    FlatFareVO flatFareVO = new FlatFareVO();
    flatFareVO.setFlatFareRequest(flatFareRequest);
    dynamicPricingCompute.calWaitTimeFare(flatFareVO);
    assertEquals(11.5, flatFareVO.getWaitTimeFare());
  }

  @Test
  void calBookingFee_calculateSuccessfully_doNotThrowException() {
    Date currentDate = new Date();
    FlatFareRequest flatFareRequest = FlatFareRequest.builder().requestDate(currentDate).build();
    FlatFareVO flatFareVO = new FlatFareVO();
    flatFareVO.setFlatFareRequest(flatFareRequest);
    dynamicPricingCompute.calBookingFee(flatFareVO);
    assertEquals(4, flatFareVO.getBookingFee());
  }

  @Test
  void calHourlySurcharge_calculateSuccessfully_doNotThrowException() {
    Date currentDate = new Date();
    FlatFareRequest flatFareRequest = FlatFareRequest.builder().requestDate(currentDate).build();
    FlatFareVO flatFareVO = new FlatFareVO();
    flatFareVO.setFlagDown(2d);
    flatFareVO.setTier1Fare(0.4);
    flatFareVO.setTier2Fare(0d);
    flatFareVO.setWaitTimeFare(11.5d);
    flatFareVO.setFlatFareRequest(flatFareRequest);
    dynamicPricingCompute.calHourlySurcharge(flatFareVO);
    assertEquals(2.78, CommonUtils.roundToTwoBD(flatFareVO.getHourlySurcharge()).doubleValue());
  }

  @Test
  void givenIntermediateAddr_whenCalMultiDestSurcharge_thenShouldSetSurcharge() {
    // Given
    FlatFareVO flatFareVO = new FlatFareVO();
    flatFareVO.setFlatFareRequest(FlatFareRequest.builder().intermediateAddrRef("id").build());

    // When
    dynamicPricingCompute.calMultiDestSurcharge(flatFareVO);

    // Then
    assertEquals(3.0, flatFareVO.getMultiDestSurcharge());
  }

  @Test
  void givenNoIntermediateAddr_whenCalMultiDestSurcharge_thenShouldSetSurcharge() {
    // Given
    FlatFareVO flatFareVO = new FlatFareVO();
    flatFareVO.setFlatFareRequest(FlatFareRequest.builder().intermediateAddrRef(null).build());

    // When
    dynamicPricingCompute.calMultiDestSurcharge(flatFareVO);

    // Then
    assertEquals(0.0, flatFareVO.getMultiDestSurcharge());
  }

  @Test
  void givenInvalidConfig_whenCalMultiDestSurcharge_thenShouldSetSurcharge() {
    // Given
    FlatFareVO flatFareVO = new FlatFareVO();
    flatFareVO.setFlatFareRequest(FlatFareRequest.builder().intermediateAddrRef("id").build());
    configSet = DynamicPricingConfigSet.builder().multiStopSurcharge("invalid").build();
    dynamicPricingCompute = new DynamicPricingComputeImpl(configSet);

    // When
    dynamicPricingCompute.calMultiDestSurcharge(flatFareVO);

    // Then
    assertEquals(0.0, flatFareVO.getMultiDestSurcharge());
  }

  /*
  Set up dynamic pricing config set
  */
  private void setUpConfigSet() {
    Date currentDate = new Date();
    FareTypeConfig flagDownConfigHoliday =
        FareTypeConfig.builder()
            .fareType(RedisKeyConstant.DYNP_FLAG_DOWN_RATE)
            .defaultFixed(2d)
            .defaultPercent(0d)
            .hour(DateUtils.getHourOfDate(currentDate).toString())
            .day(RedisKeyConstant.HOL)
            .startDate(LocalDate.of(1000, Month.JANUARY, 31))
            .endDate(LocalDate.of(3000, Month.DECEMBER, 31))
            .build();
    FareTypeConfig flagDownConfigNormalDay =
        FareTypeConfig.builder()
            .fareType(RedisKeyConstant.DYNP_FLAG_DOWN_RATE)
            .defaultFixed(3d)
            .defaultPercent(0d)
            .hour(DateUtils.getHourOfDate(currentDate).toString())
            .day(DateUtils.toDayOfWeekShortUpperCase(currentDate))
            .startDate(LocalDate.of(1000, Month.JANUARY, 31))
            .endDate(LocalDate.of(3000, Month.DECEMBER, 31))
            .build();
    FareTypeConfig flagDownConfigAll =
        FareTypeConfig.builder()
            .fareType(RedisKeyConstant.DYNP_FLAG_DOWN_RATE)
            .defaultFixed(2.5d)
            .defaultPercent(0d)
            .hour(RedisKeyConstant.ALL)
            .day(RedisKeyConstant.ALL)
            .startDate(LocalDate.of(1000, Month.JANUARY, 31))
            .endDate(LocalDate.of(3000, Month.DECEMBER, 31))
            .build();
    Map<String, List<FareTypeConfig>> flagDownConfigList = new HashMap<>();
    flagDownConfigList.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_FLAG_DOWN_RATE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.HOL,
        List.of(flagDownConfigHoliday));
    flagDownConfigList.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_FLAG_DOWN_RATE
            + RedisKeyConstant.COLON
            + DateUtils.toDayOfWeekShortUpperCase(currentDate),
        List.of(flagDownConfigNormalDay));
    flagDownConfigList.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_FLAG_DOWN_RATE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.ALL,
        List.of(flagDownConfigAll));

    FareTypeConfig tier1EndDistAll =
        FareTypeConfig.builder()
            .fareType(RedisKeyConstant.DYNP_TIER_1_END_DIST)
            .defaultFixed(10000d)
            .defaultPercent(0d)
            .hour(RedisKeyConstant.ALL)
            .day(RedisKeyConstant.ALL)
            .startDate(LocalDate.of(1000, Month.JANUARY, 31))
            .endDate(LocalDate.of(3000, Month.DECEMBER, 31))
            .build();
    Map<String, List<FareTypeConfig>> tier1EndDistConfigList = new HashMap<>();
    tier1EndDistConfigList.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_TIER_1_END_DIST
            + RedisKeyConstant.COLON
            + RedisKeyConstant.ALL,
        List.of(tier1EndDistAll));

    FareTypeConfig tier1PriceMultiplierAll =
        FareTypeConfig.builder()
            .fareType(RedisKeyConstant.DYNP_TIER_1_PRICE_MULTIPLIER)
            .defaultFixed(2d)
            .defaultPercent(0d)
            .hour(RedisKeyConstant.ALL)
            .day(RedisKeyConstant.ALL)
            .startDate(LocalDate.of(1000, Month.JANUARY, 31))
            .endDate(LocalDate.of(3000, Month.DECEMBER, 31))
            .build();
    Map<String, List<FareTypeConfig>> tier1PriceMultiplierConfigList = new HashMap<>();
    tier1PriceMultiplierConfigList.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_TIER_1_PRICE_MULTIPLIER
            + RedisKeyConstant.COLON
            + RedisKeyConstant.ALL,
        List.of(tier1PriceMultiplierAll));

    FareTypeConfig tier2StartDistAll =
        FareTypeConfig.builder()
            .fareType(RedisKeyConstant.DYNP_TIER_2_START_DIST)
            .defaultFixed(10000d)
            .defaultPercent(0d)
            .hour(RedisKeyConstant.ALL)
            .day(RedisKeyConstant.ALL)
            .startDate(LocalDate.of(1000, Month.JANUARY, 31))
            .endDate(LocalDate.of(3000, Month.DECEMBER, 31))
            .build();
    Map<String, List<FareTypeConfig>> tier2StartDistConfigList = new HashMap<>();
    tier2StartDistConfigList.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_TIER_2_START_DIST
            + RedisKeyConstant.COLON
            + RedisKeyConstant.ALL,
        List.of(tier2StartDistAll));

    FareTypeConfig tier2PriceMultiplierAll =
        FareTypeConfig.builder()
            .fareType(RedisKeyConstant.DYNP_TIER_2_PRICE_MULTIPLIER)
            .defaultFixed(2d)
            .defaultPercent(0d)
            .hour(RedisKeyConstant.ALL)
            .day(RedisKeyConstant.ALL)
            .startDate(LocalDate.of(1000, Month.JANUARY, 31))
            .endDate(LocalDate.of(3000, Month.DECEMBER, 31))
            .build();
    Map<String, List<FareTypeConfig>> tier2riceMultiplierConfigList = new HashMap<>();
    tier2riceMultiplierConfigList.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_TIER_2_PRICE_MULTIPLIER
            + RedisKeyConstant.COLON
            + RedisKeyConstant.ALL,
        List.of(tier2PriceMultiplierAll));

    FareTypeConfig durationRateAll =
        FareTypeConfig.builder()
            .fareType(RedisKeyConstant.DYNP_DURATION_RATE)
            .defaultFixed(0d)
            .defaultPercent(2.3)
            .hour(RedisKeyConstant.ALL)
            .day(RedisKeyConstant.ALL)
            .startDate(LocalDate.of(1000, Month.JANUARY, 31))
            .endDate(LocalDate.of(3000, Month.DECEMBER, 31))
            .build();
    Map<String, List<FareTypeConfig>> durationRateConfigList = new HashMap<>();
    durationRateConfigList.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_DURATION_RATE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.ALL,
        List.of(durationRateAll));

    FareTypeConfig bookingFeeAll =
        FareTypeConfig.builder()
            .fareType(RedisKeyConstant.DYNP_BOOKING_FEE)
            .defaultFixed(4d)
            .defaultPercent(0d)
            .hour(DateUtils.getHourOfDate(currentDate).toString())
            .day(DateUtils.toDayOfWeekShortUpperCase(currentDate))
            .startDate(LocalDate.of(1000, Month.JANUARY, 31))
            .endDate(LocalDate.of(3000, Month.DECEMBER, 31))
            .build();
    Map<String, List<FareTypeConfig>> bookingFeeConfigList = new HashMap<>();
    bookingFeeConfigList.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_BOOKING_FEE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.ALL,
        List.of(bookingFeeAll));

    FareTypeConfig hourlySurchargeNormalDay =
        FareTypeConfig.builder()
            .fareType(RedisKeyConstant.DYNP_PEAK_MIDNIGHT_HOUR_RATE)
            .defaultFixed(0d)
            .defaultPercent(0.2d)
            .hour(DateUtils.getHourOfDate(currentDate).toString())
            .day(DateUtils.toDayOfWeekShortUpperCase(currentDate))
            .startDate(LocalDate.of(1000, Month.JANUARY, 31))
            .endDate(LocalDate.of(3000, Month.DECEMBER, 31))
            .build();
    Map<String, List<FareTypeConfig>> hourlySurchargeConfigList = new HashMap<>();
    hourlySurchargeConfigList.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_PEAK_MIDNIGHT_HOUR_RATE
            + RedisKeyConstant.COLON
            + DateUtils.toDayOfWeekShortUpperCase(currentDate),
        List.of(hourlySurchargeNormalDay));

    Map<String, List<FareTypeConfig>> minSurgeAmountConfigList = new HashMap<>();
    minSurgeAmountConfigList.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_MIN_SURGE_AMOUNT
            + RedisKeyConstant.COLON
            + RedisKeyConstant.ALL,
        List.of(
            FareTypeConfig.builder()
                .fareTypeId(2202)
                .fareType(RedisKeyConstant.DYNP_MIN_SURGE_AMOUNT)
                .fixedValue(10000.0)
                .percentValue(10.0)
                .defaultFixed(10000.0)
                .defaultPercent(10.0)
                .day(RedisKeyConstant.ALL)
                .hour(RedisKeyConstant.ALL)
                .startDate(LocalDate.of(2023, 2, 22))
                .endDate(LocalDate.of(2100, 2, 22))
                .build()));
    Map<String, List<FareTypeConfig>> surgeBufferConfigList = new HashMap<>();
    surgeBufferConfigList.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_SURGE_BUFFER
            + RedisKeyConstant.COLON
            + RedisKeyConstant.ALL,
        List.of(
            FareTypeConfig.builder()
                .fareTypeId(2205)
                .fareType(RedisKeyConstant.DYNP_SURGE_BUFFER)
                .day(RedisKeyConstant.ALL)
                .hour(RedisKeyConstant.ALL)
                .startDate(LocalDate.of(2023, 2, 22))
                .endDate(LocalDate.of(2100, 2, 22))
                .build()));
    Map<String, List<FareTypeConfig>> desurgeMaxCapConfigList = new HashMap<>();
    desurgeMaxCapConfigList.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_DESURGE_MAX_CAP
            + RedisKeyConstant.COLON
            + RedisKeyConstant.ALL,
        List.of(
            FareTypeConfig.builder()
                .fareTypeId(2201)
                .fareType(RedisKeyConstant.DYNP_DESURGE_MAX_CAP)
                .day(RedisKeyConstant.ALL)
                .hour(RedisKeyConstant.ALL)
                .startDate(LocalDate.of(2023, 2, 22))
                .endDate(LocalDate.of(2100, 2, 22))
                .build()));
    Map<String, List<FareTypeConfig>> minCapConfigList = new HashMap<>();
    minCapConfigList.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_MIN_CAP
            + RedisKeyConstant.COLON
            + RedisKeyConstant.ALL,
        List.of(
            FareTypeConfig.builder()
                .fareTypeId(2202)
                .fareType(RedisKeyConstant.DYNP_MIN_CAP)
                .defaultFixed(0.0)
                .defaultPercent(0.0)
                .day(RedisKeyConstant.ALL)
                .hour(RedisKeyConstant.ALL)
                .startDate(LocalDate.of(2023, 2, 22))
                .endDate(LocalDate.of(2100, 2, 22))
                .build()));
    Map<String, List<FareTypeConfig>> maxCapConfigList = new HashMap<>();
    maxCapConfigList.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_MAX_CAP
            + RedisKeyConstant.COLON
            + RedisKeyConstant.ALL,
        List.of(
            FareTypeConfig.builder()
                .fareTypeId(2202)
                .fareType(RedisKeyConstant.DYNP_MAX_CAP)
                .fixedValue(10000.0)
                .percentValue(10.0)
                .defaultFixed(10000.0)
                .defaultPercent(10.0)
                .day(RedisKeyConstant.ALL)
                .hour(RedisKeyConstant.ALL)
                .startDate(LocalDate.of(2023, 2, 22))
                .endDate(LocalDate.of(2100, 2, 22))
                .build()));

    FlatFareHoliday holiday = FlatFareHoliday.builder().date("2023-09-20 00:00:00").build();
    List<FlatFareHoliday> flatFareHolidayList = new ArrayList<>();
    flatFareHolidayList.add(holiday);

    String multiDestSurcharge = "3.0";

    configSet =
        DynamicPricingConfigSet.builder()
            .flagDownConfigList(flagDownConfigList)
            .tier1EndDestConfigList(tier1EndDistConfigList)
            .tier1PriceMultiplierConfigList(tier1PriceMultiplierConfigList)
            .tier2StartDestConfigList(tier2StartDistConfigList)
            .tier2PriceMultiplierConfigList(tier2riceMultiplierConfigList)
            .durationRateConfigList(durationRateConfigList)
            .bookingFee(bookingFeeConfigList)
            .hourlySurcharge(hourlySurchargeConfigList)
            .minSurgeAmountConfigList(minSurgeAmountConfigList)
            .surgeBufferConfigList(surgeBufferConfigList)
            .desurgeMaxCapConfigList(desurgeMaxCapConfigList)
            .minCapConfigList(minCapConfigList)
            .maxCapConfigList(maxCapConfigList)
            .holidayList(flatFareHolidayList)
            .multiStopSurcharge(multiDestSurcharge)
            .eventSurgeAddressConfigList(initEventSurgeAddressConfigList())
            .build();
  }

  private List<EventSurgeAddressConfig> initEventSurgeAddressConfigList() {
    EventSurgeAddressConfig chargeByPickUpFixedAmountConfig =
        EventSurgeAddressConfig.builder()
            .startTime(LocalTime.parse("00:00:00"))
            .endTime(LocalTime.parse("23:59:59"))
            .applicableDaysOfMonth(",19,20,21,22,")
            .applicableMonths(",8,9,10,11,")
            .chargeBy(FlatfareConstants.CHARGE_BY_PICKUP)
            .chargeType(FlatfareConstants.FIXED_AMOUNT)
            .chargeVal(0.5)
            .applicableAddresses(",1001,")
            .build();
    EventSurgeAddressConfig chargeByPickUpPercentConfig =
        EventSurgeAddressConfig.builder()
            .startTime(LocalTime.parse("00:00:00"))
            .endTime(LocalTime.parse("23:59:59"))
            .applicableDaysOfMonth(",19,20,21,22,")
            .applicableMonths(",8,9,10,11,")
            .chargeBy(FlatfareConstants.CHARGE_BY_PICKUP)
            .chargeType(FlatfareConstants.PERCENTAGE)
            .chargeVal(0.5)
            .applicableAddresses(",1001,")
            .build();
    EventSurgeAddressConfig chargeByDestFixedAmountConfig =
        EventSurgeAddressConfig.builder()
            .startTime(LocalTime.parse("00:00:00"))
            .endTime(LocalTime.parse("23:59:59"))
            .applicableDaysOfMonth(",19,20,21,22,")
            .applicableMonths(",8,9,10,11,")
            .chargeBy(FlatfareConstants.CHARGE_BY_DEST)
            .chargeType(FlatfareConstants.FIXED_AMOUNT)
            .chargeVal(0.5)
            .applicableAddresses(",3003,")
            .build();
    EventSurgeAddressConfig chargeByDestPercentConfig =
        EventSurgeAddressConfig.builder()
            .startTime(LocalTime.parse("00:00:00"))
            .endTime(LocalTime.parse("23:59:59"))
            .applicableDaysOfMonth(",19,20,21,22,")
            .applicableMonths(",8,9,10,11,")
            .chargeBy(FlatfareConstants.CHARGE_BY_DEST)
            .chargeType(FlatfareConstants.PERCENTAGE)
            .chargeVal(0.5)
            .applicableAddresses(",3003,")
            .build();
    EventSurgeAddressConfig chargeByIntermediateFixedAmountConfig =
        EventSurgeAddressConfig.builder()
            .startTime(LocalTime.parse("00:00:00"))
            .endTime(LocalTime.parse("23:59:59"))
            .applicableDaysOfMonth(",19,20,21,22,")
            .applicableMonths(",8,9,10,11,")
            .chargeBy(FlatfareConstants.CHARGE_BY_DEST)
            .chargeType(FlatfareConstants.FIXED_AMOUNT)
            .chargeVal(0.5)
            .applicableAddresses(",2002,")
            .build();
    EventSurgeAddressConfig chargeByIntermediatePercentConfig =
        EventSurgeAddressConfig.builder()
            .startTime(LocalTime.parse("00:00:00"))
            .endTime(LocalTime.parse("23:59:59"))
            .applicableDaysOfMonth(",19,20,21,22,")
            .applicableMonths(",8,9,10,11,")
            .chargeBy(FlatfareConstants.CHARGE_BY_DEST)
            .chargeType(FlatfareConstants.PERCENTAGE)
            .chargeVal(0.5)
            .applicableAddresses(",2002,")
            .build();

    List<EventSurgeAddressConfig> eventSurgeAddressConfigList =
        List.of(
            chargeByPickUpFixedAmountConfig,
            chargeByPickUpPercentConfig,
            chargeByDestFixedAmountConfig,
            chargeByDestPercentConfig,
            chargeByIntermediateFixedAmountConfig,
            chargeByIntermediatePercentConfig);
    return eventSurgeAddressConfigList;
  }

  @Test
  void updateAfterApplyingSurgeFare_priceGreaterMinSurgeAmt() {
    flatFareVO = new FlatFareVO();
    flatFareVO.setDpBaseFareForSurge(1000.0);
    flatFareVO.setDpFareAfterSurge(1000.0);
    flatFareVO.setMeteredBaseFare(2000.0);
    flatFareVO.setLocSurCharge(List.of(LocSurcharge.builder().amount(10.0).build()));
    flatFareVO.setEventSurgeCharge(List.of(10.0));
    flatFareVO.setFlatFareRequest(FlatFareRequest.builder().requestDate(new Date()).build());
    Map<String, List<FareTypeConfig>> minSurgeAmountConfigList = new HashMap<>();
    minSurgeAmountConfigList.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_MIN_SURGE_AMOUNT
            + RedisKeyConstant.COLON
            + RedisKeyConstant.ALL,
        List.of(
            FareTypeConfig.builder()
                .fareTypeId(2202)
                .fareType(RedisKeyConstant.DYNP_MIN_SURGE_AMOUNT)
                .fixedValue(-999.0)
                .percentValue(0.0)
                .defaultFixed(-999.0)
                .defaultPercent(0.0)
                .day(RedisKeyConstant.ALL)
                .hour(RedisKeyConstant.ALL)
                .startDate(LocalDate.of(2023, 2, 22))
                .endDate(LocalDate.of(2100, 2, 22))
                .build()));
    configSet.setMinSurgeAmountConfigList(minSurgeAmountConfigList);
    dynamicPricingCompute = new DynamicPricingComputeImpl(configSet);
    dynamicPricingCompute.updateAfterApplyingSurgeFare(flatFareVO);
    FlatFareVO expect = new FlatFareVO();
    expect.setDpBaseFareForSurge(1000.0);
    expect.setDpFareAfterSurge(1000.0);
    expect.setMeteredBaseFare(2000.0);
    expect.setLocSurCharge(List.of(LocSurcharge.builder().amount(10.0).build()));
    expect.setEventSurgeCharge(List.of(10.0));
    expect.setFlatFareRequest(FlatFareRequest.builder().requestDate(new Date()).build());
    expect.setDpFinalFare(2000.0);
    expect.setTotalFare(CommonUtils.roundToTwoBD(2000.0));
    expect.setDpAplydSurgeAmt(980.0);
    assertEquals(expect.getDpAplydSurgeAmt(), flatFareVO.getDpAplydSurgeAmt());
    assertEquals(expect.getDpFinalFare(), flatFareVO.getDpFinalFare());
    assertEquals(expect.getTotalFare(), flatFareVO.getTotalFare());
  }

  @Test
  void updateAfterApplyingSurgeFare_priceLesserMinSurgeAmt() {
    flatFareVO = new FlatFareVO();
    flatFareVO.setDpBaseFareForSurge(1000.0);
    flatFareVO.setDpFareAfterSurge(1000.0);
    flatFareVO.setMeteredBaseFare(2000.0);
    flatFareVO.setLocSurCharge(List.of(LocSurcharge.builder().amount(10.0).build()));
    flatFareVO.setEventSurgeCharge(List.of(10.0));
    flatFareVO.setFlatFareRequest(FlatFareRequest.builder().requestDate(new Date()).build());
    dynamicPricingCompute.updateAfterApplyingSurgeFare(flatFareVO);
    FlatFareVO expect = new FlatFareVO();
    expect.setDpBaseFareForSurge(1000.0);
    expect.setDpFareAfterSurge(1000.0);
    expect.setMeteredBaseFare(2000.0);
    expect.setLocSurCharge(List.of(LocSurcharge.builder().amount(10.0).build()));
    expect.setEventSurgeCharge(List.of(10.0));
    expect.setFlatFareRequest(FlatFareRequest.builder().requestDate(new Date()).build());
    expect.setDpFinalFare(11020.0);
    expect.setTotalFare(CommonUtils.roundToTwoBD(11020.0));
    expect.setDpAplydSurgeAmt(10000.0);
    assertEquals(expect.getDpAplydSurgeAmt(), flatFareVO.getDpAplydSurgeAmt());
    assertEquals(expect.getDpFinalFare(), flatFareVO.getDpFinalFare());
    assertEquals(expect.getTotalFare(), flatFareVO.getTotalFare());
  }

  @Test
  void updateAfterApplyingSurgeFare_priceLesserMaxSurgeAmt() {
    flatFareVO = new FlatFareVO();
    flatFareVO.setDpBaseFareForSurge(1000.0);
    flatFareVO.setDpFareAfterSurge(1000.0);
    flatFareVO.setMeteredBaseFare(2000.0);
    flatFareVO.setLocSurCharge(List.of(LocSurcharge.builder().amount(10.0).build()));
    flatFareVO.setEventSurgeCharge(List.of(10.0));
    flatFareVO.setFlatFareRequest(FlatFareRequest.builder().requestDate(new Date()).build());
    Map<String, List<FareTypeConfig>> desurgeMaxCapConfigList = new HashMap<>();
    desurgeMaxCapConfigList.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_DESURGE_MAX_CAP
            + RedisKeyConstant.COLON
            + RedisKeyConstant.ALL,
        List.of(
            FareTypeConfig.builder()
                .fareTypeId(2201)
                .fareType(RedisKeyConstant.DYNP_DESURGE_MAX_CAP)
                .defaultFixed(10000.0)
                .defaultPercent(0.0)
                .day(RedisKeyConstant.ALL)
                .hour(RedisKeyConstant.ALL)
                .startDate(LocalDate.of(2023, 2, 22))
                .endDate(LocalDate.of(2100, 2, 22))
                .build()));
    configSet.setDesurgeMaxCapConfigList(desurgeMaxCapConfigList);
    dynamicPricingCompute = new DynamicPricingComputeImpl(configSet);
    dynamicPricingCompute.updateAfterApplyingSurgeFare(flatFareVO);
    FlatFareVO expect = new FlatFareVO();
    expect.setDpBaseFareForSurge(1000.0);
    expect.setDpFareAfterSurge(1000.0);
    expect.setMeteredBaseFare(2000.0);
    expect.setLocSurCharge(List.of(LocSurcharge.builder().amount(10.0).build()));
    expect.setEventSurgeCharge(List.of(10.0));
    expect.setFlatFareRequest(FlatFareRequest.builder().requestDate(new Date()).build());
    expect.setDpFinalFare(11020.0);
    expect.setTotalFare(CommonUtils.roundToTwoBD(11020.0));
    expect.setDpAplydSurgeAmt(10000.0);
    assertEquals(expect.getDpAplydSurgeAmt(), flatFareVO.getDpAplydSurgeAmt());
    assertEquals(expect.getDpFinalFare(), flatFareVO.getDpFinalFare());
    assertEquals(expect.getTotalFare(), flatFareVO.getTotalFare());
  }

  @Test
  void setDynpMinMaxCapForTotalFare_totalFareLesserThanMinCap() {
    flatFareVO = new FlatFareVO();
    flatFareVO.setTotalFare(CommonUtils.roundToTwoBD(1000.0));
    flatFareVO.setFlatFareRequest(FlatFareRequest.builder().requestDate(new Date()).build());
    Map<String, List<FareTypeConfig>> minCapConfigList = new HashMap<>();
    minCapConfigList.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_MIN_CAP
            + RedisKeyConstant.COLON
            + RedisKeyConstant.ALL,
        List.of(
            FareTypeConfig.builder()
                .fareTypeId(2202)
                .fareType(RedisKeyConstant.DYNP_MIN_CAP)
                .defaultFixed(10000.0)
                .defaultPercent(10.0)
                .day(RedisKeyConstant.ALL)
                .hour(RedisKeyConstant.ALL)
                .startDate(LocalDate.of(2023, 2, 22))
                .endDate(LocalDate.of(2100, 2, 22))
                .build()));
    configSet.setMinCapConfigList(minCapConfigList);
    dynamicPricingCompute = new DynamicPricingComputeImpl(configSet);
    dynamicPricingCompute.setDynpMinMaxForTotalFare(flatFareVO);
    assertEquals(CommonUtils.roundToTwoBD(10000.0), flatFareVO.getTotalFare());
  }

  @ParameterizedTest
  @CsvSource({"1000.0, 1000.0", "1000.0, 1000.0", "100000.0, 10000.0"})
  void setDynpMinMaxCapForTotalFare(double input, double expected) {
    flatFareVO = new FlatFareVO();
    flatFareVO.setTotalFare(CommonUtils.roundToTwoBD(input));
    flatFareVO.setFlatFareRequest(FlatFareRequest.builder().requestDate(new Date()).build());
    dynamicPricingCompute.setDynpMinMaxForTotalFare(flatFareVO);
    assertEquals(CommonUtils.roundToTwoBD(expected), flatFareVO.getTotalFare());
  }

  @Test
  void setDynpMinMaxCapForTotalFare_ExceptionMinCap() {
    flatFareVO = new FlatFareVO();
    flatFareVO.setTotalFare(CommonUtils.roundToTwoBD(100000.0));
    flatFareVO.setFlatFareRequest(FlatFareRequest.builder().requestDate(new Date()).build());
    configSet.setMinCapConfigList(null);
    dynamicPricingCompute = new DynamicPricingComputeImpl(configSet);
    assertThrows(
        DomainException.class, () -> dynamicPricingCompute.setDynpMinMaxForTotalFare(flatFareVO));
  }

  @Test
  void setDynpMinMaxCapForTotalFare_ExceptionMaxCap() {
    flatFareVO = new FlatFareVO();
    flatFareVO.setTotalFare(CommonUtils.roundToTwoBD(100000.0));
    flatFareVO.setFlatFareRequest(FlatFareRequest.builder().requestDate(new Date()).build());
    configSet.setMaxCapConfigList(null);
    dynamicPricingCompute = new DynamicPricingComputeImpl(configSet);
    assertThrows(
        DomainException.class, () -> dynamicPricingCompute.setDynpMinMaxForTotalFare(flatFareVO));
  }

  @Test
  void setDynpMinMaxCapForTotalFare_nullFlatFare_exception() {
    assertThrows(
        NullPointerException.class,
        () -> dynamicPricingCompute.setDynpMinMaxForTotalFare(flatFareVO));
  }

  @Test
  void givenNormalParam_whencalEventSurcharge_thenReturnSuccess() throws ParseException {
    // GIVEN
    String dateString = "09/20/2023 01:00:00";
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy hh:mm:ss");
    Date reqDate = df.parse(dateString);
    FlatFareRequest request =
        FlatFareRequest.builder()
            .requestDate(reqDate)
            .originAddressRef("1001")
            .intermediateAddrRef("2002")
            .destAddressRef("3003")
            .vehTypeId(100)
            .build();
    FlatFareVO flatFareVO = new FlatFareVO();
    flatFareVO.setFlatFareRequest(request);
    flatFareVO.setTotalFareBeforeSurge(1.0);

    EventSurgeAddressConfig chargeByPickUpFixedAmountConfig =
        EventSurgeAddressConfig.builder()
            .startTime(LocalTime.parse("00:00:00"))
            .endTime(LocalTime.parse("23:59:59"))
            .applicableDaysOfMonth(",19,20,21,22,")
            .applicableMonths(",8,9,10,11,")
            .chargeBy(FlatfareConstants.CHARGE_BY_PICKUP)
            .chargeType(FlatfareConstants.FIXED_AMOUNT)
            .chargeVal(0.5)
            .applicableAddresses(",1001,")
            .build();
    EventSurgeAddressConfig chargeByPickUpPercentConfig =
        EventSurgeAddressConfig.builder()
            .startTime(LocalTime.parse("00:00:00"))
            .endTime(LocalTime.parse("23:59:59"))
            .applicableDaysOfMonth(",19,20,21,22,")
            .applicableMonths(",8,9,10,11,")
            .chargeBy(FlatfareConstants.CHARGE_BY_PICKUP)
            .chargeType(FlatfareConstants.PERCENTAGE)
            .chargeVal(0.5)
            .applicableAddresses(",1001,")
            .build();
    EventSurgeAddressConfig chargeByDestFixedAmountConfig =
        EventSurgeAddressConfig.builder()
            .startTime(LocalTime.parse("00:00:00"))
            .endTime(LocalTime.parse("23:59:59"))
            .applicableDaysOfMonth(",19,20,21,22,")
            .applicableMonths(",8,9,10,11,")
            .chargeBy(FlatfareConstants.CHARGE_BY_DEST)
            .chargeType(FlatfareConstants.FIXED_AMOUNT)
            .chargeVal(0.5)
            .applicableAddresses(",3003,")
            .build();
    EventSurgeAddressConfig chargeByDestPercentConfig =
        EventSurgeAddressConfig.builder()
            .startTime(LocalTime.parse("00:00:00"))
            .endTime(LocalTime.parse("23:59:59"))
            .applicableDaysOfMonth(",19,20,21,22,")
            .applicableMonths(",8,9,10,11,")
            .chargeBy(FlatfareConstants.CHARGE_BY_DEST)
            .chargeType(FlatfareConstants.PERCENTAGE)
            .chargeVal(0.5)
            .applicableAddresses(",3003,")
            .build();
    EventSurgeAddressConfig chargeByIntermediateFixedAmountConfig =
        EventSurgeAddressConfig.builder()
            .startTime(LocalTime.parse("00:00:00"))
            .endTime(LocalTime.parse("23:59:59"))
            .applicableDaysOfMonth(",19,20,21,22,")
            .applicableMonths(",8,9,10,11,")
            .chargeBy(FlatfareConstants.CHARGE_BY_DEST)
            .chargeType(FlatfareConstants.FIXED_AMOUNT)
            .chargeVal(0.5)
            .applicableAddresses(",2002,")
            .build();
    EventSurgeAddressConfig chargeByIntermediatePercentConfig =
        EventSurgeAddressConfig.builder()
            .startTime(LocalTime.parse("00:00:00"))
            .endTime(LocalTime.parse("23:59:59"))
            .applicableDaysOfMonth(",19,20,21,22,")
            .applicableMonths(",8,9,10,11,")
            .chargeBy(FlatfareConstants.CHARGE_BY_DEST)
            .chargeType(FlatfareConstants.PERCENTAGE)
            .chargeVal(0.5)
            .applicableAddresses(",2002,")
            .build();

    List<EventSurgeAddressConfig> eventSurgeAddressConfigList =
        List.of(
            chargeByPickUpFixedAmountConfig,
            chargeByPickUpPercentConfig,
            chargeByDestFixedAmountConfig,
            chargeByDestPercentConfig,
            chargeByIntermediateFixedAmountConfig,
            chargeByIntermediatePercentConfig);

    // THEN
    dynamicPricingCompute.calEventSurcharge(flatFareVO);
    double actual = CommonUtils.roundToTwo(flatFareVO.getTotalEventSurCharge());
    double expected = 1.5;
    assertEquals(expected, actual);
  }

  @Test
  void givenNullConfig_whenCalEventSurcharge_thenThrowsDomainException() throws ParseException {
    // GIVEN
    String dateString = "09/20/2023 01:00:00";
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy hh:mm:ss");
    Date reqDate = df.parse(dateString);
    FlatFareRequest request =
        FlatFareRequest.builder()
            .requestDate(reqDate)
            .originAddressRef("1001")
            .intermediateAddrRef("2002")
            .destAddressRef("3003")
            .vehTypeId(100)
            .build();
    FlatFareVO flatFareVO = new FlatFareVO();
    flatFareVO.setFlatFareRequest(request);
    flatFareVO.setTotalFareBeforeSurge(1.0);

    EventSurgeAddressConfig chargeByPickUpFixedAmountConfig =
        EventSurgeAddressConfig.builder()
            .startTime(LocalTime.parse("00:00:00"))
            .endTime(LocalTime.parse("23:59:59"))
            .applicableDaysOfMonth(",19,20,21,22,")
            .applicableMonths(",8,9,10,11,")
            .chargeBy(FlatfareConstants.CHARGE_BY_PICKUP)
            .chargeType(FlatfareConstants.FIXED_AMOUNT)
            .chargeVal(null)
            .applicableAddresses(",1001,")
            .build();

    List<EventSurgeAddressConfig> eventSurgeAddressConfigList =
        List.of(chargeByPickUpFixedAmountConfig);

    configSet.setEventSurgeAddressConfigList(eventSurgeAddressConfigList);

    // THEN
    assertThrows(DomainException.class, () -> dynamicPricingCompute.calEventSurcharge(flatFareVO));
  }
}

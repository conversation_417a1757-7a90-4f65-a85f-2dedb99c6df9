package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.cbdcharge.LocReloadCache;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.*;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.cbdcharge.CBDAddressConfigService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.CacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.InternalServerException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ConfigManagementServiceImplTest {
  @Mock private ConfigManagementService configManagementService;
  @Mock private FlatFareConfigService flatFareConfigService;
  @Mock private FareTypeConfigService fareTypeConfigService;
  @Mock private CompanyHolidayConfigService companyHolidayConfigService;
  @Mock private LocationSurchargeService locationSurchargeService;
  @Mock private DemandSupplyService demandSupplyService;
  @Mock private CBDAddressConfigService cbdAddressConfigService;
  @Mock private CacheService cacheService;
  @Mock private FlatFareAdjustmentConfService flatFareAdjustmentConfService;

  @BeforeEach
  void setUp() {
    configManagementService =
        new ConfigManagementServiceImpl(
            flatFareConfigService,
            fareTypeConfigService,
            companyHolidayConfigService,
            locationSurchargeService,
            demandSupplyService,
            cbdAddressConfigService,
            cacheService,
            flatFareAdjustmentConfService);
  }

  @Test
  void whenLoadToCacheAllConfig_thenLoadConfig() {
    configManagementService.reloadAllConfig();
    Mockito.verify(flatFareConfigService, Mockito.times(1)).loadAllFlatFareConfig();
    Mockito.verify(fareTypeConfigService, Mockito.times(1)).loadFareTypeConfig();
    Mockito.verify(companyHolidayConfigService, Mockito.times(1)).loadCompanyHolidayConfig();
    Mockito.verify(locationSurchargeService, Mockito.times(1)).loadAllLocationSurchargeConfigs();
  }

  @Test
  void whenCheckEmptyAndReloadConfigs_thenLoadConfig() {
    configManagementService.checkEmptyAndReloadConfigs();
    Mockito.verify(flatFareConfigService, Mockito.times(1)).loadAllFlatFareConfig();
    Mockito.verify(fareTypeConfigService, Mockito.times(1)).loadFareTypeConfig();
    Mockito.verify(companyHolidayConfigService, Mockito.times(1)).loadCompanyHolidayConfig();
    Mockito.verify(locationSurchargeService, Mockito.times(1)).loadAllLocationSurchargeConfigs();
  }

  @Test
  void givenNormalCondition_whenReloadFlatFareConfig_thenSuccess() {
    configManagementService.reloadFlatFareConfig();
    Mockito.verify(flatFareConfigService, Mockito.times(1)).loadAllFlatFareConfig();
  }

  @Test
  void givenNormalCondition_whenReloadFareTypeConfig_thenSuccess() {
    configManagementService.reloadFareTypeConfig();
    Mockito.verify(fareTypeConfigService, Mockito.times(1)).reloadFareTypeConfigSet();
  }

  @Test
  void givenNormalCondition_whenReloadCompanyHolidayConfig_thenSuccess() {
    configManagementService.reloadCompanyHolidayConfig();
    Mockito.verify(companyHolidayConfigService, Mockito.times(1)).loadCompanyHolidayConfig();
  }

  @Test
  void givenNormalCondition_whenReloadLocationSurchargeConfig_thenSuccess() {
    configManagementService.reloadLocationSurchargeConfig();
    Mockito.verify(locationSurchargeService, Mockito.times(1)).loadAllLocationSurchargeConfigs();
  }

  @Test
  void givenNormalCondition_whenReloadDynpSurgeConfig_thenSuccess() {
    configManagementService.reloadDynpSurgeConfig();
    Mockito.verify(demandSupplyService, Mockito.times(1)).loadDynpSurgeConfigs();
    Mockito.verify(demandSupplyService, Mockito.times(1)).loadDynpSurgeNgpConfigs();
  }

  @Test
  void givenNormalCondition_whenReloadCBDAddressConfig_thenSuccess() {
    configManagementService.reloadCBDAddressConfig(LocReloadCache.builder().build());
    Mockito.verify(cbdAddressConfigService, Mockito.times(1)).reloadCache(Mockito.any());
  }

  @Test
  void givenException_whenReloadCacheConfig_thenSuccess() {
    LocReloadCache locReloadCache = LocReloadCache.builder().build();
    Mockito.doThrow(new InternalServerException("Exception", 1L))
        .when(cbdAddressConfigService)
        .reloadCache(Mockito.any());
    Assertions.assertThrows(
        InternalServerException.class,
        () -> configManagementService.reloadCBDAddressConfig(locReloadCache));
  }

  @Test
  void test_reloadFareFareAdjustmentConfig_success() {
    String expectedResult = "Reload reloadFareFareAdjustmentConfig successfully";
    String result = configManagementService.reloadFareFareAdjustmentConfig();
    Assertions.assertEquals(expectedResult, result);
    Mockito.verify(flatFareAdjustmentConfService).loadFlatFareAdjustmentConf();
  }
}

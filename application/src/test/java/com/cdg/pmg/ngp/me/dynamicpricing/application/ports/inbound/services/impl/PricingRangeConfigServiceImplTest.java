package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import static com.cdg.pmg.ngp.me.dynamicpricing.constants.DomainConstant.MON;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.mapper.PricingRangeConfigAppMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.PricingRangeConfigService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.PricingRangeConfigRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.commands.PricingRangeConfigCommand;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.PricingRangeConfigEntity;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

/** The type Pricing range config service impl test. */
@ExtendWith(MockitoExtension.class)
class PricingRangeConfigServiceImplTest {
  @Mock private PricingRangeConfigService pricingRangeConfigService;
  @Mock private PricingRangeConfigRepository pricingRangeConfigRepository;
  @Mock private PricingRangeConfigAppMapper pricingRangeConfigAppMapper;

  private PricingRangeConfigEntity pricingRangeConfigEntity;
  private PricingRangeConfigCommand pricingRangeConfigCommand;

  /** Sets up service and mock data. */
  @BeforeEach
  void setUpServiceAndMockData() {
    pricingRangeConfigService =
        new PricingRangeConfigServiceImpl(
            pricingRangeConfigRepository, pricingRangeConfigAppMapper);
    pricingRangeConfigEntity =
        PricingRangeConfigEntity.builder()
            .pricingRangeId(1)
            .day(MON)
            .startPrice(8.0)
            .endPrice(8.0)
            .stepPositive(8.0)
            .stepNegative(8.0)
            .build();
    pricingRangeConfigCommand =
        PricingRangeConfigCommand.builder()
            .day(MON)
            .startPrice(8.0)
            .endPrice(8.0)
            .stepPositive(8.0)
            .stepNegative(8.0)
            .build();
  }

  /** Given no param when get pricing range configs then return successfully. */
  @Test
  void givenNoParam_whenGetPricingRangeConfigs_thenReturnSuccessfully() {
    List<PricingRangeConfigEntity> pricingRangeConfigEntities = new ArrayList<>();
    pricingRangeConfigEntities.add(pricingRangeConfigEntity);
    when(pricingRangeConfigRepository.getPricingRangeConfigs())
        .thenReturn(pricingRangeConfigEntities);

    var actual = pricingRangeConfigService.getPricingRangeConfigs().get(0).getDay();
    assertEquals(MON, actual);
  }

  /**
   * Given pricing range configs by day and hour is empty when insert or update pricing range config
   * then update pricing range config successfully.
   */
  @Test
  void
      givenPricingRangeConfigsByDayAndHourIsEmpty_whenInsertOrUpdatePricingRangeConfig_thenUpdatePricingRangeConfigSuccessfully() {
    when(pricingRangeConfigAppMapper.mapPricingRangeConfigCommandToPricingRangeConfigEntity(
            Mockito.any()))
        .thenReturn(pricingRangeConfigEntity);

    List<PricingRangeConfigEntity> pricingRangeConfigsByDayAndHour = new ArrayList<>();
    pricingRangeConfigsByDayAndHour.add(pricingRangeConfigEntity);
    when(pricingRangeConfigRepository.getPricingRangeConfigsByDayAndHour(
            Mockito.any(), Mockito.any()))
        .thenReturn(pricingRangeConfigsByDayAndHour);

    when(pricingRangeConfigRepository.updatePricingRangeConfig(Mockito.any(), Mockito.any()))
        .thenReturn(pricingRangeConfigEntity);

    var actual =
        pricingRangeConfigService
            .insertOrUpdatePricingRangeConfig(pricingRangeConfigCommand)
            .getDay();
    assertEquals(MON, actual);
  }

  /**
   * Given valid input when insert or update pricing range config then insert pricing range config
   * successfully.
   */
  @Test
  void
      givenValidInput_whenInsertOrUpdatePricingRangeConfig_thenInsertPricingRangeConfigSuccessfully() {
    when(pricingRangeConfigAppMapper.mapPricingRangeConfigCommandToPricingRangeConfigEntity(
            Mockito.any()))
        .thenReturn(pricingRangeConfigEntity);

    List<PricingRangeConfigEntity> pricingRangeConfigsByDayAndHour = new ArrayList<>();
    when(pricingRangeConfigRepository.getPricingRangeConfigsByDayAndHour(
            Mockito.any(), Mockito.any()))
        .thenReturn(pricingRangeConfigsByDayAndHour);

    when(pricingRangeConfigRepository.insertPricingRangeConfig(Mockito.any()))
        .thenReturn(pricingRangeConfigEntity);

    var actual =
        pricingRangeConfigService
            .insertOrUpdatePricingRangeConfig(pricingRangeConfigCommand)
            .getDay();
    assertEquals(MON, actual);
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.SurgeCalculationDto;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.SurgeCalculationStrategy;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.DemandSupplyConfigV2;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.NewPricingModelConfigEntity;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class SurgeCalculationV3StrategyTest {

  @Spy private SurgeCalculationV3Strategy surgeCalculationV3Strategy;

  @Test
  void getVersion_shouldReturnV3() {
    assertEquals(SurgeCalculationStrategy.V3, surgeCalculationV3Strategy.getVersion());
  }

  @Test
  void givenUnmet15EqualsZero_whenCalculate_doNotThrowException() {
    // given
    var currentSurge = 25;
    SurgeCalculationDto surgeCalculationDto =
        SurgeCalculationDto.builder()
            .currentSurge(currentSurge)
            .demandConfig(
                DemandSupplyConfigV2.builder().surgeLow(20).unmet15(0.0).surgeHigh(30).build())
            .newPricingModelConfigEntity(
                NewPricingModelConfigEntity.builder().additionalSurgeHigh(2).build())
            .build();

    // when
    int surge = surgeCalculationV3Strategy.calculate(surgeCalculationDto);
    // then
    assertEquals(currentSurge, surge);
    verify(surgeCalculationV3Strategy, times(1))
        .clamp(
            anyInt(),
            eq(surgeCalculationDto.getSurgeLow()),
            eq(surgeCalculationDto.getSurgeHighNew()));
  }

  @Test
  void givenUnmet15NotEqualsZeroAndPreviousUnmet15EqualsZero_thenCalculate_doNotThrowException() {
    // given
    var currentSurge = 25;
    SurgeCalculationDto surgeCalculationDto =
        SurgeCalculationDto.builder()
            .currentSurge(currentSurge)
            .demandConfig(
                DemandSupplyConfigV2.builder()
                    .surgeLow(20)
                    .unmet15(0.1)
                    .previousUnmet15(0.0)
                    .surgeHigh(30)
                    .build())
            .newPricingModelConfigEntity(
                NewPricingModelConfigEntity.builder()
                    .k1(20.0)
                    .k2(1.5)
                    .k3(2.0)
                    .k4(3.0)
                    .additionalSurgeHigh(50)
                    .build())
            .build();

    // 25 + (20 / (1 + 1.5 * exp(-2)) + 3.0 * 0.1/(1 + exp(-0.1)) = 41
    var expectedSurge = 41;

    // when
    int result = surgeCalculationV3Strategy.calculate(surgeCalculationDto);
    // then
    assertEquals(
        expectedSurge,
        result,
        () -> "Expected surge = " + expectedSurge + " but got surge = " + result);
    verify(surgeCalculationV3Strategy, times(1))
        .clamp(
            anyInt(),
            eq(surgeCalculationDto.getSurgeLow()),
            eq(surgeCalculationDto.getSurgeHighNew()));
  }

  @Test
  void givenUnmet15GreaterThanPreviousUnmet15_whenCalculate_doNotThrowException() {
    // given
    var surge = 25;
    SurgeCalculationDto surgeCalculationDto =
        SurgeCalculationDto.builder()
            .currentSurge(surge)
            .demandConfig(
                DemandSupplyConfigV2.builder()
                    .surgeLow(20)
                    .unmet15(0.7)
                    .previousUnmet15(0.1)
                    .surgeHigh(30)
                    .build())
            .newPricingModelConfigEntity(
                NewPricingModelConfigEntity.builder()
                    .k1(20.0)
                    .k2(1.5)
                    .k3(2.0)
                    .k4(3.0)
                    .additionalSurgeHigh(150)
                    .build())
            .build();

    //  25 + (20.0 * ((0.7 - 0.1) / 0.1) /(1 + 1.5 * EXP(-2.0 * ((0.7 - 0.1) / 0.1 )))) + (3.0 * 0.7
    // / (1 + EXP(-0.7)))))
    var expectedSurge = 146;

    // when
    int result = surgeCalculationV3Strategy.calculate(surgeCalculationDto);

    // then
    assertEquals(
        expectedSurge,
        result,
        () -> "Expected surge = " + expectedSurge + " but got surge = " + result);
    verify(surgeCalculationV3Strategy, times(1))
        .clamp(
            anyInt(),
            eq(surgeCalculationDto.getSurgeLow()),
            eq(surgeCalculationDto.getSurgeHighNew()));
  }

  @Test
  void givenUnmet15LessThanPreviousUnmet15AndSurgeNegative_whenCalculate_doNotThrowException() {
    // given
    var surge = 20;
    SurgeCalculationDto surgeCalculationDto =
        SurgeCalculationDto.builder()
            .currentSurge(surge)
            .demandConfig(
                DemandSupplyConfigV2.builder()
                    .surgeLow(-100)
                    .surgeHigh(40)
                    .unmet15(1)
                    .previousUnmet15(2)
                    .build())
            .newPricingModelConfigEntity(
                NewPricingModelConfigEntity.builder()
                    .k5(380.0)
                    .k6(2.1)
                    .k7(2.5)
                    .additionalSurgeHigh(150)
                    .build())
            .build();

    //  20 + 380 * ((1 - 2) / 2) / (1 + 2.1 * EXP(-2.5 * ((1 - 2) / 2)))
    var expectedSurge = -2;

    // when
    int result = surgeCalculationV3Strategy.calculate(surgeCalculationDto);

    // then
    assertEquals(
        expectedSurge,
        result,
        () -> "Expected surge = " + expectedSurge + " but got surge = " + result);
    verify(surgeCalculationV3Strategy, times(1))
        .clamp(
            anyInt(),
            eq(surgeCalculationDto.getSurgeLow()),
            eq(surgeCalculationDto.getSurgeHighNew()));
  }

  @Test
  void givenUnmet15EqualsPreviousUnmet15_whenCalculate_doNotThrowException() {
    // given
    var surge = 20;
    SurgeCalculationDto surgeCalculationDto =
        SurgeCalculationDto.builder()
            .currentSurge(surge)
            .demandConfig(
                DemandSupplyConfigV2.builder()
                    .surgeLow(-100)
                    .surgeHigh(40)
                    .unmet15(2)
                    .previousUnmet15(2)
                    .build())
            .newPricingModelConfigEntity(
                NewPricingModelConfigEntity.builder()
                    .k5(380.0)
                    .k6(2.1)
                    .k7(2.5)
                    .additionalSurgeHigh(150)
                    .build())
            .build();

    //  20 + 380 * ((2 - 2) / 2) / (1 + 2.1 * EXP(-2.5 * ((2 - 2) / 2)))
    var expectedSurge = 20;

    // when
    int result = surgeCalculationV3Strategy.calculate(surgeCalculationDto);

    // then
    assertEquals(
        expectedSurge,
        result,
        () -> "Expected surge = " + expectedSurge + " but got surge = " + result);
    verify(surgeCalculationV3Strategy, times(1))
        .clamp(
            anyInt(),
            eq(surgeCalculationDto.getSurgeLow()),
            eq(surgeCalculationDto.getSurgeHighNew()));
  }
}

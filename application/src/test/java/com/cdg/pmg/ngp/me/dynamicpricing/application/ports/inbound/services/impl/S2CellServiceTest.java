package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.S2CellService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.S2CellRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.CacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.S2CellEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.queries.S2CellListConfigQueryResponse;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class S2CellServiceTest {
  private S2CellEntity s2Cell;
  private S2CellListConfigQueryResponse s2CellListConfigQueryResponse;
  @Mock private S2CellService s2Service;

  @Mock private S2CellRepository repository;

  @Mock private CacheService cacheService;

  private static final String S2_CELL_KEY_CACHE_PREFIX =
      RedisKeyConstant.S2_CELL.concat(RedisKeyConstant.COLON);

  @BeforeEach
  void setup() {
    MockitoAnnotations.openMocks(this);
    s2Service = new S2CellServiceImpl(repository, cacheService);
    s2Cell =
        S2CellEntity.builder()
            .s2CellId("s2CellId")
            .s2CellDesc("s2CellDesc")
            .s2CellLatitude(1.0)
            .s2CellLocationId("s2CellLocationId")
            .s2CellTokenId("s2CellTokenId")
            .s2CellLevel(1)
            .s2CellSeqId(1L)
            .s2CellLocDesc("s2CellLocDesc")
            .s2CellZoneId("s2CellZoneId")
            .s2CellLongitude(1.0)
            .build();

    s2CellListConfigQueryResponse =
        S2CellListConfigQueryResponse.builder()
            .s2CellList(Collections.singletonList(s2Cell))
            .build();
  }

  @Test
  void givenCacheNull_whenGetS2CellList_thenReturnS2CellDB() {
    when(cacheService.getListValue(S2_CELL_KEY_CACHE_PREFIX, S2CellEntity.class)).thenReturn(null);
    when(repository.getAllS2Cell()).thenReturn(List.of(s2Cell));
    Assertions.assertEquals(
        s2CellListConfigQueryResponse.getS2CellList(), s2Service.getS2CellList().getS2CellList());
  }

  @Test
  void givenS2CellIsEmpty_whenGetS2CellList_thenGetAllS2CellFromDB() {
    when(cacheService.getListValue(S2_CELL_KEY_CACHE_PREFIX, S2CellEntity.class))
        .thenReturn(Collections.emptyList());
    when(repository.getAllS2Cell()).thenReturn(List.of(s2Cell));
    Assertions.assertEquals(
        s2CellListConfigQueryResponse.getS2CellList(), s2Service.getS2CellList().getS2CellList());
  }

  @Test
  void givenCacheNotNull_whenGetS2CellList_thenReturnS2CellFromCache() {
    when(cacheService.getListValue(S2_CELL_KEY_CACHE_PREFIX, S2CellEntity.class))
        .thenReturn(List.of(s2Cell));
    Assertions.assertEquals(
        s2CellListConfigQueryResponse.getS2CellList(), s2Service.getS2CellList().getS2CellList());
  }

  @Test
  void givenRequest_whenFetchCacheS2Cell_thenReturnSuccess() {
    when(repository.getAllS2Cell()).thenReturn(List.of(s2Cell));
    s2Service.fetchCacheS2Cell();
    verify(cacheService, times(1)).setValue(S2_CELL_KEY_CACHE_PREFIX, List.of(s2Cell));
  }

  @Test
  void givenNoParams_whenGetS2CellList_thenReturnException() {
    when(cacheService.getListValue(anyString(), eq(S2CellEntity.class)))
        .thenThrow(new RuntimeException());
    when(repository.getAllS2Cell()).thenThrow(new RuntimeException());
    List<S2CellEntity> result = s2Service.getS2CellList().getS2CellList();
    assertTrue(result.isEmpty());
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.flatfare.impl;

import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.CALCULATE_BREAKDOWN_FARE_ERROR;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.EventSurgeAddressConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareConfigSet;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareHoliday;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareVO;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.LocationSurchargeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.FlatFareConfigService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.LocationSurchargeService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.CommonUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.DateUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.BookingFeeItem;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.FareService;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.DomainException;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.InternalServerException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Stream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class EstStandardFlatFareComputeImplTest {
  @Mock private EstStandardFlatFareComputeImpl estStandardFlatFare;

  @Mock private FlatFareConfigService configService;

  @Mock private FareService fareService;

  @Mock private LocationSurchargeService locationSurchargeService;

  SimpleDateFormat dateFormat;

  @BeforeEach
  void setUp() {
    dateFormat = new SimpleDateFormat(DateUtils.YYYYMMDD_HHMMSS_FORMAT);
    FlatFareConfigSet flatFareConfigSet = new FlatFareConfigSet();
    flatFareConfigSet.setPrefixKey(RedisKeyConstant.EST_LIVE_TRAFFIC_KEY_PREFIX);
    flatFareConfigSet.setBookingFeeList(initBookingFeeList());

    estStandardFlatFare =
        new EstStandardFlatFareComputeImpl(
            flatFareConfigSet, configService, fareService, locationSurchargeService);
  }

  @Test
  void givenHolidayConfig_whenCalLocSurcharges_thenReturnSuccess() throws ParseException {
    // GIVEN
    String startDateString = "09/20/2023";
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy");
    var reqDate = df.parse(startDateString);
    LocalTime currentTime = DateUtils.convertToLocalTime(reqDate);

    FlatFareHoliday holiday = FlatFareHoliday.builder().date("2023-09-20 00:00:00").build();
    List<FlatFareHoliday> flatFareHolidayList = new ArrayList<>();
    flatFareHolidayList.add(holiday);

    var request =
        FlatFareRequest.builder()
            .originAddressRef("origin")
            .destAddressRef("dest")
            .intermediateAddrRef("inter")
            .requestDate(reqDate)
            .build();

    var flatfareVO = new FlatFareVO();
    flatfareVO.setFlatFareRequest(request);

    FlatFareConfigSet flatFareConfigSet =
        FlatFareConfigSet.builder()
            .locationSurchargeConfigList(
                Stream.concat(
                        initLocSurchargeNormalDay(currentTime).stream(),
                        initLocSurchargeHOL(currentTime).stream())
                    .toList())
            .build();
    estStandardFlatFare.setConfigSet(flatFareConfigSet);

    // THEN
    estStandardFlatFare.calLocSurcharges(flatfareVO);
    var actual = CommonUtils.roundToTwo(flatfareVO.getTotalLocSurCharge());
    var expected = 0.9;
    assertEquals(expected, actual);
  }

  @Test
  void givenNormalDayConfig_whenCalLocSurcharges_thenReturnSuccess() throws ParseException {
    // GIVEN
    String startDateString = "09/20/2023";
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy");
    var reqDate = df.parse(startDateString);
    LocalTime currentTime = DateUtils.convertToLocalTime(reqDate);

    FlatFareHoliday holiday = FlatFareHoliday.builder().date("2023-09-22 00:00:00").build();
    List<FlatFareHoliday> flatFareHolidayList = new ArrayList<>();
    flatFareHolidayList.add(holiday);

    var request =
        FlatFareRequest.builder()
            .originAddressRef("origin")
            .destAddressRef("dest")
            .intermediateAddrRef("inter")
            .requestDate(reqDate)
            .build();
    var flatfareVO = new FlatFareVO();
    flatfareVO.setFlatFareRequest(request);

    FlatFareConfigSet flatFareConfigSet =
        FlatFareConfigSet.builder()
            .locationSurchargeConfigList(initLocSurchargeNormalDay(currentTime))
            .build();
    estStandardFlatFare.setConfigSet(flatFareConfigSet);

    // THEN
    estStandardFlatFare.calLocSurcharges(flatfareVO);
    var actual = CommonUtils.roundToTwo(flatfareVO.getTotalLocSurCharge());
    var expected = 3.6;
    assertEquals(expected, actual);
  }

  @Test
  void givenNullConfig_whenCalLocSurcharges_thenThrowException() throws ParseException {
    // GIVEN
    String startDateString = "09/20/2023";
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy");
    var reqDate = df.parse(startDateString);

    var request =
        FlatFareRequest.builder()
            .originAddressRef("origin")
            .destAddressRef("dest")
            .intermediateAddrRef("inter")
            .requestDate(reqDate)
            .build();

    var flatfareVO = new FlatFareVO();
    flatfareVO.setFlatFareRequest(request);

    // THEN
    try {
      estStandardFlatFare.calLocSurcharges(flatfareVO);
    } catch (DomainException e) {
      assertEquals(CALCULATE_BREAKDOWN_FARE_ERROR.getErrorCode(), e.getErrorCode());
    }
  }

  private List<LocationSurchargeConfig> initLocSurchargeHOL(LocalTime currentTime) {
    var locItemHOL1 =
        LocationSurchargeConfig.builder()
            .addressRef("origin")
            .startTime(currentTime)
            .endTime(currentTime)
            .productId("STD001")
            .chargeBy("PICKUP")
            .surchargeValue(0.3)
            .applicableDays("HOL")
            .dayIndicator("HOL")
            .locationName("Changi Airport")
            .build();
    var locItemHOL2 =
        LocationSurchargeConfig.builder()
            .addressRef("dest")
            .startTime(currentTime)
            .endTime(currentTime)
            .productId("STD001")
            .chargeBy("DEST")
            .surchargeValue(0.3)
            .applicableDays("HOL")
            .dayIndicator("HOL")
            .locationName("Changi Airport")
            .build();
    var locItemHOL3 =
        LocationSurchargeConfig.builder()
            .addressRef("inter")
            .startTime(currentTime)
            .endTime(currentTime)
            .productId("STD001")
            .chargeBy("DEST")
            .surchargeValue(0.3)
            .applicableDays("HOL")
            .dayIndicator("HOL")
            .locationName("Changi Airport")
            .build();

    return List.of(locItemHOL1, locItemHOL2, locItemHOL3);
  }

  private List<LocationSurchargeConfig> initLocSurchargeNormalDay(LocalTime currentTime) {
    var locItemHOL1 =
        LocationSurchargeConfig.builder()
            .addressRef("origin")
            .startTime(currentTime)
            .endTime(currentTime)
            .productId("STD001")
            .chargeBy("PICKUP")
            .surchargeValue(1.2)
            .applicableDays("WED")
            .dayIndicator("WED")
            .locationName("Changi Airport")
            .build();
    var locItemHOL2 =
        LocationSurchargeConfig.builder()
            .addressRef("dest")
            .startTime(currentTime)
            .endTime(currentTime)
            .productId("STD001")
            .chargeBy("DEST")
            .surchargeValue(1.2)
            .applicableDays("WED")
            .dayIndicator("WED")
            .locationName("Changi Airport")
            .build();
    var locItemHOL3 =
        LocationSurchargeConfig.builder()
            .addressRef("inter")
            .startTime(currentTime)
            .endTime(currentTime)
            .productId("STD001")
            .chargeBy("DEST")
            .surchargeValue(1.2)
            .applicableDays("WED")
            .dayIndicator("WED")
            .locationName("Changi Airport")
            .build();

    return List.of(locItemHOL1, locItemHOL2, locItemHOL3);
  }

  @Test
  void givenNoParam_whenCalAdditionalSurCharge_thenReturnSuccess() {
    // GIVEN
    FlatFareRequest request = FlatFareRequest.builder().vehTypeId(100).build();
    FlatFareVO flatFareVO = new FlatFareVO();
    flatFareVO.setFlatFareRequest(request);

    // THEN
    estStandardFlatFare.calAdditionalSurCharge(flatFareVO);
    var actual = flatFareVO.getAdditionalSurcharge();
    var expected = 0;
    assertEquals(expected, actual);
  }

  @Test
  void calEttFare_noParam_success() {
    var request = FlatFareRequest.builder().ett(1200L).build();

    FlatFareConfigSet flatFareConfigSet =
        FlatFareConfigSet.builder().durationUnitConfig("45").durationRateConfig("0.25").build();
    estStandardFlatFare.setConfigSet(flatFareConfigSet);

    var flatfareVO = new FlatFareVO();
    flatfareVO.setFlatFareRequest(request);
    estStandardFlatFare.calEttFare(flatfareVO);

    var actual = CommonUtils.roundToTwo(flatfareVO.getWaitTimeFare());
    var expected = 6.67;

    assertEquals(expected, actual);
  }

  @Test
  void calEttFare_noParam_ettZero() {
    var request = FlatFareRequest.builder().ett(1200L).build();

    FlatFareConfigSet flatFareConfigSet =
        FlatFareConfigSet.builder().durationUnitConfig("45").durationRateConfig("0").build();
    estStandardFlatFare.setConfigSet(flatFareConfigSet);

    var flatfareVO = new FlatFareVO();
    flatfareVO.setFlatFareRequest(request);
    estStandardFlatFare.calEttFare(flatfareVO);

    var actual = CommonUtils.roundToTwo(flatfareVO.getWaitTimeFare());
    var expected = 0.0;

    assertEquals(expected, actual);
  }

  @Test
  void calEttFare_noParam_error() {
    var request = FlatFareRequest.builder().ett(1200L).build();
    var flatfareVO = new FlatFareVO();
    flatfareVO.setFlatFareRequest(request);
    try {
      estStandardFlatFare.calEttFare(flatfareVO);
    } catch (DomainException e) {
      assertEquals(CALCULATE_BREAKDOWN_FARE_ERROR.getErrorCode(), e.getErrorCode());
    }
  }

  @Test
  void givenNormalParam_whenCalEventSurgeAddrCharge_thenReturnSuccess() throws ParseException {
    // GIVEN
    String dateString = "09/20/2023 01:00:00";
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy hh:mm:ss");
    Date reqDate = df.parse(dateString);
    FlatFareRequest request =
        FlatFareRequest.builder()
            .requestDate(reqDate)
            .originAddressRef("1001")
            .intermediateAddrRef("2002")
            .destAddressRef("3003")
            .vehTypeId(100)
            .build();
    FlatFareVO flatFareVO = new FlatFareVO();
    flatFareVO.setFlatFareRequest(request);
    flatFareVO.setTotalFareBeforeSurge(1.0);

    EventSurgeAddressConfig chargeByPickUpFixedAmountConfig =
        EventSurgeAddressConfig.builder()
            .startTime(LocalTime.parse("00:00:00"))
            .endTime(LocalTime.parse("23:59:59"))
            .applicableDaysOfMonth(",19,20,21,22,")
            .applicableMonths(",8,9,10,11,")
            .chargeBy(FlatfareConstants.CHARGE_BY_PICKUP)
            .chargeType(FlatfareConstants.FIXED_AMOUNT)
            .chargeVal(0.5)
            .applicableAddresses(",1001,")
            .build();
    EventSurgeAddressConfig chargeByPickUpPercentConfig =
        EventSurgeAddressConfig.builder()
            .startTime(LocalTime.parse("00:00:00"))
            .endTime(LocalTime.parse("23:59:59"))
            .applicableDaysOfMonth(",19,20,21,22,")
            .applicableMonths(",8,9,10,11,")
            .chargeBy(FlatfareConstants.CHARGE_BY_PICKUP)
            .chargeType(FlatfareConstants.PERCENTAGE)
            .chargeVal(0.5)
            .applicableAddresses(",1001,")
            .build();
    EventSurgeAddressConfig chargeByDestFixedAmountConfig =
        EventSurgeAddressConfig.builder()
            .startTime(LocalTime.parse("00:00:00"))
            .endTime(LocalTime.parse("23:59:59"))
            .applicableDaysOfMonth(",19,20,21,22,")
            .applicableMonths(",8,9,10,11,")
            .chargeBy(FlatfareConstants.CHARGE_BY_DEST)
            .chargeType(FlatfareConstants.FIXED_AMOUNT)
            .chargeVal(0.5)
            .applicableAddresses(",3003,")
            .build();
    EventSurgeAddressConfig chargeByDestPercentConfig =
        EventSurgeAddressConfig.builder()
            .startTime(LocalTime.parse("00:00:00"))
            .endTime(LocalTime.parse("23:59:59"))
            .applicableDaysOfMonth(",19,20,21,22,")
            .applicableMonths(",8,9,10,11,")
            .chargeBy(FlatfareConstants.CHARGE_BY_DEST)
            .chargeType(FlatfareConstants.PERCENTAGE)
            .chargeVal(0.5)
            .applicableAddresses(",3003,")
            .build();
    EventSurgeAddressConfig chargeByIntermediateFixedAmountConfig =
        EventSurgeAddressConfig.builder()
            .startTime(LocalTime.parse("00:00:00"))
            .endTime(LocalTime.parse("23:59:59"))
            .applicableDaysOfMonth(",19,20,21,22,")
            .applicableMonths(",8,9,10,11,")
            .chargeBy(FlatfareConstants.CHARGE_BY_DEST)
            .chargeType(FlatfareConstants.FIXED_AMOUNT)
            .chargeVal(0.5)
            .applicableAddresses(",2002,")
            .build();
    EventSurgeAddressConfig chargeByIntermediatePercentConfig =
        EventSurgeAddressConfig.builder()
            .startTime(LocalTime.parse("00:00:00"))
            .endTime(LocalTime.parse("23:59:59"))
            .applicableDaysOfMonth(",19,20,21,22,")
            .applicableMonths(",8,9,10,11,")
            .chargeBy(FlatfareConstants.CHARGE_BY_DEST)
            .chargeType(FlatfareConstants.PERCENTAGE)
            .chargeVal(0.5)
            .applicableAddresses(",2002,")
            .build();

    List<EventSurgeAddressConfig> eventSurgeAddressConfigList =
        List.of(
            chargeByPickUpFixedAmountConfig,
            chargeByPickUpPercentConfig,
            chargeByDestFixedAmountConfig,
            chargeByDestPercentConfig,
            chargeByIntermediateFixedAmountConfig,
            chargeByIntermediatePercentConfig);

    // WHEN
    estStandardFlatFare.configSet.setEventSurgeAddressConfigList(eventSurgeAddressConfigList);

    // THEN
    estStandardFlatFare.calEventSurgeAddrCharge(flatFareVO);
    double actual = CommonUtils.roundToTwo(flatFareVO.getTotalEventSurCharge());
    double expected = 3.0;
    assertEquals(expected, actual);
  }

  @Test
  void givenNullConfig_whenCalEventSurgeAddrCharge_thenThrowsDomainException()
      throws ParseException {
    // GIVEN
    String dateString = "09/20/2023 01:00:00";
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy hh:mm:ss");
    Date reqDate = df.parse(dateString);
    FlatFareRequest request =
        FlatFareRequest.builder()
            .requestDate(reqDate)
            .originAddressRef("1001")
            .intermediateAddrRef("2002")
            .destAddressRef("3003")
            .vehTypeId(100)
            .build();
    FlatFareVO flatFareVO = new FlatFareVO();
    flatFareVO.setFlatFareRequest(request);
    flatFareVO.setTotalFareBeforeSurge(1.0);

    EventSurgeAddressConfig chargeByPickUpFixedAmountConfig =
        EventSurgeAddressConfig.builder()
            .startTime(LocalTime.parse("00:00:00"))
            .endTime(LocalTime.parse("23:59:59"))
            .applicableDaysOfMonth(",19,20,21,22,")
            .applicableMonths(",8,9,10,11,")
            .chargeBy(FlatfareConstants.CHARGE_BY_PICKUP)
            .chargeType(FlatfareConstants.FIXED_AMOUNT)
            .chargeVal(null)
            .applicableAddresses(",1001,")
            .build();

    List<EventSurgeAddressConfig> eventSurgeAddressConfigList =
        List.of(chargeByPickUpFixedAmountConfig);

    // THEN
    assertThrows(
        DomainException.class, () -> estStandardFlatFare.calEventSurgeAddrCharge(flatFareVO));
  }

  @Test
  void givenNormalCondition_whenCalBookingFee_thenReturnSuccess() throws ParseException {
    String dateTimeString = "2023-02-06 01:00:00";
    FlatFareVO flatfareVO = new FlatFareVO();
    flatfareVO.setFlatFareRequest(
        FlatFareRequest.builder()
            .vehTypeId(0)
            .requestDate(dateFormat.parse(dateTimeString))
            .build());
    flatfareVO.setPdtId("STD001");
    estStandardFlatFare.calBookingFee(flatfareVO);
    assertEquals(2.2, flatfareVO.getBookingFee());
  }

  @Test
  void givenNoBookingFeeConfig_whenCalBookingFee_thenThrowError() throws ParseException {
    String dateTimeString = "2023-02-06 01:00:00";
    FlatFareVO flatfareVO = new FlatFareVO();
    flatfareVO.setFlatFareRequest(
        FlatFareRequest.builder()
            .vehTypeId(-1)
            .requestDate(dateFormat.parse(dateTimeString))
            .build());
    flatfareVO.setPdtId("STD001");
    assertThrows(
        InternalServerException.class, () -> estStandardFlatFare.calBookingFee(flatfareVO));
  }

  private List<BookingFeeItem> initBookingFeeList() {
    List<BookingFeeItem> bookingFeeList = new ArrayList<>();
    bookingFeeList.add(buildBookingFeeItem("StandardFlatFare", "FLAT-001", 0, 3.3));
    bookingFeeList.add(buildBookingFeeItem("EstStandardFlatFare", "STD001", 0, 2.2));
    bookingFeeList.add(buildBookingFeeItem("LimoFlatFare", "OWT-001", 1, 50));
    bookingFeeList.add(buildBookingFeeItem("EstStandardFlatFare", "STD001", 1, 10));
    return bookingFeeList;
  }

  private BookingFeeItem buildBookingFeeItem(
      String flatFareType, String productId, int vehicleTypeId, double bookingFee) {
    return BookingFeeItem.builder()
        .flatFareType(flatFareType)
        .productId(productId)
        .vehicleTypeId(vehicleTypeId)
        .bookingFee(bookingFee)
        .build();
  }
}

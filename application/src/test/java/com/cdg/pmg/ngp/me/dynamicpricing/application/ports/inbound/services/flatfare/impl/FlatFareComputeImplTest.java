package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.flatfare.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.BookingFare;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.EstimateRateConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareConfigSet;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareHoliday;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareVO;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.LocSurcharge;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.TierFare;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.FlatFareConfigService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.LocationSurchargeService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.CommonUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.DateUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.FareService;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.DomainException;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.InternalServerException;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class FlatFareComputeImplTest {
  @Mock private FlatFareComputeImpl flatFareComputeImpl;
  @Mock protected FlatFareConfigService configService;

  @Mock private FareService fareService;

  @Mock private LocationSurchargeService locationSurchargeService;

  private FlatFareVO flatFareVO;

  SimpleDateFormat dateFormat;

  @BeforeEach
  void setUp() {
    dateFormat = new SimpleDateFormat(DateUtils.YYYYMMDD_HHMMSS_FORMAT);
    FlatFareConfigSet flatFareConfigSet = new FlatFareConfigSet();
    flatFareConfigSet.setPrefixKey(RedisKeyConstant.EST_LIVE_TRAFFIC_KEY_PREFIX);
    flatFareComputeImpl =
        new EstStandardFlatFareComputeImpl(
            flatFareConfigSet, configService, fareService, locationSurchargeService);
    flatFareComputeImpl.configSet =
        FlatFareConfigSet.builder()
            .flagDownRate("3.7")
            .tier1Fare(
                TierFare.builder()
                    .perCountFare("0.4")
                    .perCountMeter("1000")
                    .startDistance("0")
                    .endDistance("10000")
                    .build())
            .tier2Fare(
                TierFare.builder()
                    .perCountFare("0.5")
                    .perCountMeter("1000")
                    .startDistance("10000")
                    .build())
            .peakHoursRates(
                List.of(
                    Map.of(
                        "EST_LIVE_TRAFFIC_PEAK_HOUR_DAYS_0",
                        "MON,TUE,WED,THU,FRI",
                        "EST_LIVE_TRAFFIC_PEAK_HOUR_END_TIME_0",
                        "09:29:59",
                        "EST_LIVE_TRAFFIC_PEAK_HOUR_RATE_0",
                        "0.25",
                        "EST_LIVE_TRAFFIC_PEAK_HOUR_START_TIME_0",
                        "06:00:00",
                        "EST_LIVE_TRAFFIC_PEAK_HOUR_STEPDOWN_10mins_0",
                        "0.15",
                        "EST_LIVE_TRAFFIC_PEAK_HOUR_STEPDOWN_15mins_0",
                        "0.2",
                        "EST_LIVE_TRAFFIC_PEAK_HOUR_STEPDOWN_5mins_0",
                        "0.1",
                        "EST_LIVE_TRAFFIC_PEAK_HOUR_STEPUP_10mins_0",
                        "0.15",
                        "EST_LIVE_TRAFFIC_PEAK_HOUR_STEPUP_15mins_0",
                        "0.1",
                        "EST_LIVE_TRAFFIC_PEAK_HOUR_STEPUP_5mins_0",
                        "0.2")))
            .midnightHoursRates(
                List.of(
                    Map.of(
                        "EST_LIVE_TRAFFIC_MID_NIGHT_DAYS_0",
                        "MON,TUE,WED,THU,FRI,SAT",
                        "EST_LIVE_TRAFFIC_MID_NIGHT_END_TIME_0",
                        "00:59:59",
                        "EST_LIVE_TRAFFIC_MID_NIGHT_RATE_0",
                        "0.50",
                        "EST_LIVE_TRAFFIC_MID_NIGHT_START_TIME_0",
                        "00:00:00",
                        "EST_LIVE_TRAFFIC_MID_NIGHT_STEPDOWN_10mins_0",
                        "0.40",
                        "EST_LIVE_TRAFFIC_MID_NIGHT_STEPDOWN_15mins_0",
                        "0.45",
                        "EST_LIVE_TRAFFIC_MID_NIGHT_STEPDOWN_5mins_0",
                        "0.35",
                        "EST_LIVE_TRAFFIC_MID_NIGHT_STEPUP_10mins_0",
                        "0.40",
                        "EST_LIVE_TRAFFIC_MID_NIGHT_STEPUP_15mins_0",
                        "0.35",
                        "EST_LIVE_TRAFFIC_MID_NIGHT_STEPUP_5mins_0",
                        "0.45")))
            .build();
    flatFareVO = new FlatFareVO();
    flatFareVO.setFlatFareRequest(FlatFareRequest.builder().vehTypeId(100).build());
    flatFareVO.setFlagDown(100.0);
    flatFareVO.setTier1Fare(10.0);
    flatFareVO.setTier2Fare(0.0);
  }

  @Test
  void calFlatDownRateFare() {
    flatFareComputeImpl.calFlatDownRateFare(flatFareVO);
    assertEquals(3.7, flatFareVO.getFlagDown());
  }

  @Test
  void calFlatDownRateFare_Exception() {
    flatFareComputeImpl.configSet.setFlagDownRate(null);
    assertThrows(DomainException.class, () -> flatFareComputeImpl.calFlatDownRateFare(flatFareVO));
  }

  @ParameterizedTest
  @CsvSource({"100, 0.04", "20000, 4.0", "0, 0.0"})
  void calTier1(int input, double expected) {
    flatFareVO.setFlatFareRequest(FlatFareRequest.builder().routingDistance(input).build());
    flatFareComputeImpl.calTier1(flatFareVO);
    assertEquals(CommonUtils.roundToTwo(expected), flatFareVO.getTier1Fare());
  }

  @Test
  void calTier1_exception() {
    flatFareComputeImpl.configSet.setTier1Fare(TierFare.builder().perCountFare("testing").build());
    assertThrows(DomainException.class, () -> flatFareComputeImpl.calTier1(flatFareVO));
  }

  @ParameterizedTest
  @CsvSource({"100, 0.0", "20000, 5.0"})
  void calTier2(int input, double expected) {
    flatFareVO.setFlatFareRequest(FlatFareRequest.builder().routingDistance(input).build());
    flatFareComputeImpl.calTier2(flatFareVO);
    assertEquals(CommonUtils.roundToTwo(expected), flatFareVO.getTier2Fare());
  }

  @Test
  void calBaseFare() {
    flatFareVO.setFlagDown(10.0);
    flatFareVO.setTier1Fare(10.0);
    flatFareVO.setTier2Fare(10.0);
    flatFareVO.setPeakHrFare(100.0);
    flatFareVO.setMidNightFare(100.0);
    flatFareVO.setBookingFee(100.0);
    flatFareVO.setAdditionalSurcharge(1000.0);
    flatFareVO.setLocSurCharge(List.of(LocSurcharge.builder().amount(10.0).build()));
    flatFareComputeImpl.calBaseFare(flatFareVO);
    assertEquals(BigDecimal.valueOf(1340.0), flatFareVO.getTotalFare());
  }

  @Test
  void calEstimatedTotalFare() {
    flatFareComputeImpl.configSet =
        FlatFareConfigSet.builder()
            .estimateRateConfig(
                EstimateRateConfig.builder()
                    .totalFareEstimateLF("10.0")
                    .totalFareEstimateRT("10.0")
                    .build())
            .build();
    flatFareVO.setTotalFare(BigDecimal.valueOf(10.0));
    flatFareComputeImpl.calEstimatedTotalFare(flatFareVO);
    assertEquals(CommonUtils.roundToTwoBD(100.0), flatFareVO.getEstimatedFareLF());
    assertEquals(CommonUtils.roundToTwoBD(100.0), flatFareVO.getEstimatedFareRT());
  }

  @Test
  void givenConfigValueZero_whenCalEstimatedTotalFare_thenReturnSuccess() {
    // GIVEN
    flatFareComputeImpl.configSet =
        FlatFareConfigSet.builder()
            .estimateRateConfig(
                EstimateRateConfig.builder()
                    .totalFareEstimateLF("0.0")
                    .totalFareEstimateRT("0.0")
                    .build())
            .build();
    flatFareVO.setTotalFare(BigDecimal.valueOf(10.0));

    // THEN
    flatFareComputeImpl.calEstimatedTotalFare(flatFareVO);
    assertEquals(BigDecimal.valueOf(0.0), flatFareVO.getEstimatedFareLF());
    assertEquals(BigDecimal.valueOf(0.0), flatFareVO.getEstimatedFareRT());
  }

  @Test
  void calEstimatedTotalFare_exception() {
    flatFareComputeImpl.configSet =
        FlatFareConfigSet.builder()
            .estimateRateConfig(
                EstimateRateConfig.builder()
                    .totalFareEstimateLF("null")
                    .totalFareEstimateRT("10.0")
                    .build())
            .build();
    flatFareVO.setTotalFare(BigDecimal.valueOf(10.0));
    assertThrows(
        DomainException.class, () -> flatFareComputeImpl.calEstimatedTotalFare(flatFareVO));
  }

  @Test
  void givenNormalParam_whenIsHoliday_thenThrowsInternalServerException() throws ParseException {
    // GIVEN
    String startDateString = "09/20/2023";
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy");
    var reqDate = df.parse(startDateString);

    var holiday = FlatFareHoliday.builder().date("123-123-123").build();
    List<FlatFareHoliday> listHolidayConfig = new ArrayList<>();
    listHolidayConfig.add(holiday);

    // WHEN
    flatFareComputeImpl.configSet =
        FlatFareConfigSet.builder().holidayList(listHolidayConfig).build();

    // THEN
    assertThrows(InternalServerException.class, () -> flatFareComputeImpl.isHoliday(reqDate));
  }

  /*
   * Note:
   * [2023-01-22 8:30:00, 0.0] : Mock for Sunday
   * [2023-01-23 8:30:00, 27.5] : Mock for Monday and Request time is between start and end day
   * [2023-01-23 10:30:00, 0.0] : Mock for Monday
   * [2023-01-23 9:29:00, 11.0] : Mock for step down 0 - 5
   * [2023-01-23 9:20:00, 16.5] : Mock for step down 5 - 10
   * [2023-01-23 9:15:00, 22.0] : Mock for step down 10 - 15
   * [2023-01-23 5:55:00, 22.0] : Mock for step up 0 - 5
   * [2023-01-23 5:50:00, 16.5] : Mock for step up 5 - 10
   * [2023-01-23 5:45:00, 11.0] : Mock for step up 10 - 15
   */
  @ParameterizedTest
  //  @CsvSource({
  //    "2023-01-22 8:30:00, 0.0",
  //    "2023-01-23 8:30:00, 27.5",
  //    "2023-01-23 10:30:00, 0.0",
  //    "2023-01-23 9:29:00, 11.0",
  //    "2023-01-23 9:20:00, 16.5",
  //    "2023-01-23 9:15:00, 22.0",
  //    "2023-01-23 5:55:00, 22.0",
  //    "2023-01-23 5:50:00, 16.5",
  //    "2023-01-23 5:45:00, 11.0"
  //  })
  @CsvSource({
    "2023-01-22T00:30:00Z, 0.0",
    "2023-01-23T00:30:00Z, 27.5",
    "2023-01-23T02:30:00Z, 0.0",
    "2023-01-23T01:29:00Z, 11.0",
    "2023-01-23T01:20:00Z, 16.5",
    "2023-01-23T01:15:00Z, 22.0",
    "2023-01-22T21:55:00Z, 22.0",
    "2023-01-22T21:50:00Z, 16.5",
    "2023-01-22T21:45:00Z, 11.0"
  })
  void calPeakHourCharges(String input, double expected) throws ParseException {
    Date reqDateUTC = Date.from(Instant.parse(input));
    flatFareVO.setFlatFareRequest(FlatFareRequest.builder().requestDate(reqDateUTC).build());
    flatFareComputeImpl.calPeakHourCharges(flatFareVO);
    assertEquals(expected, flatFareVO.getPeakHrFare());
  }

  @Test
  void calPeakHourCharges_Holiday_success() throws ParseException {
    // Define a date-time string in a specific format
    String dateTimeString = "2023-02-06 00:00:00"; // "yyyy-MM-dd HH:mm:ss"

    // Day mock is Holiday
    flatFareVO.setFlatFareRequest(
        FlatFareRequest.builder().requestDate(dateFormat.parse(dateTimeString)).build());

    flatFareComputeImpl.configSet.setHolidayList(
        List.of(FlatFareHoliday.builder().date(dateTimeString).build()));
    flatFareComputeImpl.calPeakHourCharges(flatFareVO);
    assertEquals(0.0, flatFareVO.getPeakHrFare());
  }

  @Test
  void calPeakHourCharges_HolidayConfigHOL_success() throws ParseException {
    flatFareComputeImpl.configSet =
        FlatFareConfigSet.builder()
            .peakHoursRates(
                List.of(
                    Map.of(
                        "EST_LIVE_TRAFFIC_PEAK_HOUR_DAYS_0",
                        "MON,TUE,WED,THU,FRI,HOL",
                        "EST_LIVE_TRAFFIC_PEAK_HOUR_END_TIME_0",
                        "09:29:59",
                        "EST_LIVE_TRAFFIC_PEAK_HOUR_RATE_0",
                        "0.25",
                        "EST_LIVE_TRAFFIC_PEAK_HOUR_START_TIME_0",
                        "06:00:00",
                        "EST_LIVE_TRAFFIC_PEAK_HOUR_STEPDOWN_10mins_0",
                        "0.15",
                        "EST_LIVE_TRAFFIC_PEAK_HOUR_STEPDOWN_15mins_0",
                        "0.2",
                        "EST_LIVE_TRAFFIC_PEAK_HOUR_STEPDOWN_5mins_0",
                        "0.1",
                        "EST_LIVE_TRAFFIC_PEAK_HOUR_STEPUP_10mins_0",
                        "0.15",
                        "EST_LIVE_TRAFFIC_PEAK_HOUR_STEPUP_15mins_0",
                        "0.1",
                        "EST_LIVE_TRAFFIC_PEAK_HOUR_STEPUP_5mins_0",
                        "0.2")))
            .build();
    // Define a date-time string in a specific format
    String dateTimeString = "2023-02-06 00:00:00"; // "yyyy-MM-dd HH:mm:ss"
    Date reqDateUTC = Date.from(Instant.parse("2023-02-05T16:00:00Z"));

    // Day mock is Holiday
    flatFareVO.setFlatFareRequest(FlatFareRequest.builder().requestDate(reqDateUTC).build());

    flatFareComputeImpl.configSet.setHolidayList(
        List.of(FlatFareHoliday.builder().date(dateTimeString).build()));
    flatFareComputeImpl.calPeakHourCharges(flatFareVO);
    assertEquals(0.0, flatFareVO.getPeakHrFare());
  }

  @Test
  void calPeakHourCharges_HolidayConfigHOLTimeBetween_success() throws ParseException {
    flatFareComputeImpl.configSet =
        FlatFareConfigSet.builder()
            .peakHoursRates(
                List.of(
                    Map.of(
                        "EST_LIVE_TRAFFIC_PEAK_HOUR_DAYS_0",
                        "MON,TUE,WED,THU,FRI,HOL",
                        "EST_LIVE_TRAFFIC_PEAK_HOUR_END_TIME_0",
                        "09:29:59",
                        "EST_LIVE_TRAFFIC_PEAK_HOUR_RATE_0",
                        "0.25",
                        "EST_LIVE_TRAFFIC_PEAK_HOUR_START_TIME_0",
                        "06:00:00",
                        "EST_LIVE_TRAFFIC_PEAK_HOUR_STEPDOWN_10mins_0",
                        "0.15",
                        "EST_LIVE_TRAFFIC_PEAK_HOUR_STEPDOWN_15mins_0",
                        "0.2",
                        "EST_LIVE_TRAFFIC_PEAK_HOUR_STEPDOWN_5mins_0",
                        "0.1",
                        "EST_LIVE_TRAFFIC_PEAK_HOUR_STEPUP_10mins_0",
                        "0.15",
                        "EST_LIVE_TRAFFIC_PEAK_HOUR_STEPUP_15mins_0",
                        "0.1",
                        "EST_LIVE_TRAFFIC_PEAK_HOUR_STEPUP_5mins_0",
                        "0.2")))
            .build();
    // Define a date-time string in a specific format
    String dateTimeString = "2023-02-06 07:00:00"; // "yyyy-MM-dd HH:mm:ss"
    Date reqDateUTC = Date.from(Instant.parse("2023-02-06T01:00:00Z"));

    // Day mock is Holiday
    flatFareVO.setFlatFareRequest(FlatFareRequest.builder().requestDate(reqDateUTC).build());

    flatFareComputeImpl.configSet.setHolidayList(
        List.of(FlatFareHoliday.builder().date(dateTimeString).build()));

    flatFareComputeImpl.calPeakHourCharges(flatFareVO);
    assertEquals(27.5, flatFareVO.getPeakHrFare());
  }

  @Test
  void calPeakHourCharges_nullRequestDate_exception() {
    assertThrows(DomainException.class, () -> flatFareComputeImpl.calPeakHourCharges(flatFareVO));
  }

  /*
   * Note:
   * [2023-01-22 8:30:00, 0.0] : Mock for Sunday
   * [2023-01-23 00:30:00, 55.0] : Mock for Monday and Request time is between start and end day
   * [2023-01-23 10:30:00, 0.0] : Mock for Monday
   */
  @ParameterizedTest
  @CsvSource({
    "2023-01-22T00:30:00Z, 0.0",
    "2023-01-22T16:30:00Z, 55.0",
    "2023-01-23T02:30:00Z, 0.0",
  })
  void calMidNightCharges(String input, double expected) throws ParseException {
    Date reqDateUTC = Date.from(Instant.parse(input));
    flatFareVO.setFlatFareRequest(FlatFareRequest.builder().requestDate(reqDateUTC).build());
    flatFareComputeImpl.calMidNightCharges(flatFareVO);
    assertEquals(expected, flatFareVO.getMidNightFare());
  }

  @Test
  void calMidNightCharges_Holiday_success() throws ParseException {
    // Define a date-time string in a specific format
    String dateTimeString = "2023-02-06 00:00:00"; // "yyyy-MM-dd HH:mm:ss"

    // Day mock is Holiday
    flatFareVO.setFlatFareRequest(
        FlatFareRequest.builder().requestDate(dateFormat.parse(dateTimeString)).build());

    flatFareComputeImpl.configSet.setHolidayList(
        List.of(FlatFareHoliday.builder().date(dateTimeString).build()));

    flatFareComputeImpl.calMidNightCharges(flatFareVO);
    assertEquals(0.0, flatFareVO.getMidNightFare());
  }

  @Test
  void calMidNightCharges_HolidayConfigHOL_success() throws ParseException {
    flatFareComputeImpl.configSet =
        FlatFareConfigSet.builder()
            .midnightHoursRates(
                List.of(
                    Map.of(
                        "EST_LIVE_TRAFFIC_MID_NIGHT_DAYS_0",
                        "MON,TUE,WED,THU,FRI,SAT,HOL",
                        "EST_LIVE_TRAFFIC_MID_NIGHT_END_TIME_0",
                        "00:59:59",
                        "EST_LIVE_TRAFFIC_MID_NIGHT_RATE_0",
                        "0.50",
                        "EST_LIVE_TRAFFIC_MID_NIGHT_START_TIME_0",
                        "00:00:00",
                        "EST_LIVE_TRAFFIC_MID_NIGHT_STEPDOWN_10mins_0",
                        "0.40",
                        "EST_LIVE_TRAFFIC_MID_NIGHT_STEPDOWN_15mins_0",
                        "0.45",
                        "EST_LIVE_TRAFFIC_MID_NIGHT_STEPDOWN_5mins_0",
                        "0.35",
                        "EST_LIVE_TRAFFIC_MID_NIGHT_STEPUP_10mins_0",
                        "0.40",
                        "EST_LIVE_TRAFFIC_MID_NIGHT_STEPUP_15mins_0",
                        "0.35",
                        "EST_LIVE_TRAFFIC_MID_NIGHT_STEPUP_5mins_0",
                        "0.45")))
            .build();
    // Define a date-time string in a specific format
    String dateTimeString = "2023-02-06 00:00:00"; // "yyyy-MM-dd HH:mm:ss"
    Date reqDateUTC = Date.from(Instant.parse("2023-02-05T16:00:00Z"));

    // Day mock is Holiday
    flatFareVO.setFlatFareRequest(FlatFareRequest.builder().requestDate(reqDateUTC).build());

    flatFareComputeImpl.configSet.setHolidayList(
        List.of(FlatFareHoliday.builder().date(dateTimeString).build()));

    flatFareComputeImpl.calMidNightCharges(flatFareVO);
    assertEquals(55.0, flatFareVO.getMidNightFare());
  }

  @Test
  void calMidNightCharges_HolidayConfigHOLTimeBetween_success() throws ParseException {
    flatFareComputeImpl.configSet =
        FlatFareConfigSet.builder()
            .midnightHoursRates(
                List.of(
                    Map.of(
                        "EST_LIVE_TRAFFIC_MID_NIGHT_DAYS_0",
                        "MON,TUE,WED,THU,FRI,SAT,HOL",
                        "EST_LIVE_TRAFFIC_MID_NIGHT_END_TIME_0",
                        "00:59:59",
                        "EST_LIVE_TRAFFIC_MID_NIGHT_RATE_0",
                        "0.50",
                        "EST_LIVE_TRAFFIC_MID_NIGHT_START_TIME_0",
                        "00:00:00",
                        "EST_LIVE_TRAFFIC_MID_NIGHT_STEPDOWN_10mins_0",
                        "0.40",
                        "EST_LIVE_TRAFFIC_MID_NIGHT_STEPDOWN_15mins_0",
                        "0.45",
                        "EST_LIVE_TRAFFIC_MID_NIGHT_STEPDOWN_5mins_0",
                        "0.35",
                        "EST_LIVE_TRAFFIC_MID_NIGHT_STEPUP_10mins_0",
                        "0.40",
                        "EST_LIVE_TRAFFIC_MID_NIGHT_STEPUP_15mins_0",
                        "0.35",
                        "EST_LIVE_TRAFFIC_MID_NIGHT_STEPUP_5mins_0",
                        "0.45")))
            .build();
    // Define a date-time string in a specific format
    String dateTimeString = "2023-02-06 07:40:00"; // "yyyy-MM-dd HH:mm:ss"

    // Day mock is Holiday
    flatFareVO.setFlatFareRequest(
        FlatFareRequest.builder().requestDate(dateFormat.parse(dateTimeString)).build());

    flatFareComputeImpl.configSet.setHolidayList(
        List.of(FlatFareHoliday.builder().date(dateTimeString).build()));

    flatFareComputeImpl.calMidNightCharges(flatFareVO);
    assertEquals(0.0, flatFareVO.getMidNightFare());
  }

  @Test
  void calMidNightCharges_nullRequestDate_exception() {
    assertThrows(DomainException.class, () -> flatFareComputeImpl.calMidNightCharges(flatFareVO));
  }

  @Test
  void givenNormalParam_whenIsValidFlatfare_thenReturnSuccess() {
    boolean actual = flatFareComputeImpl.isValidFlatfare(flatFareVO);
    assertEquals(Boolean.TRUE, actual);
  }

  @Test
  void givenHoliday_whenIsMatchWithApplicableDayTimeInConfig_thenReturnSuccess()
      throws ParseException {
    // GIVEN
    FlatFareHoliday holiday = FlatFareHoliday.builder().date("2023-09-20 00:00:00").build();
    List<FlatFareHoliday> flatFareHolidayList = new ArrayList<>();
    flatFareHolidayList.add(holiday);

    String reqDateString = "09/20/2023 01:00:00";
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy hh:mm:ss");
    Date reqDate = df.parse(reqDateString);

    BookingFare bookingFareConfig =
        BookingFare.builder()
            .applicableDays("WED,HOL")
            .startTime(LocalTime.of(0, 0, 0))
            .endTime(LocalTime.of(12, 0, 0))
            .build();

    // WHEN
    flatFareComputeImpl.configSet.setHolidayList(flatFareHolidayList);

    // THEN
    boolean actual =
        flatFareComputeImpl.isMatchWithApplicableDayTimeInConfig(reqDate, bookingFareConfig);
    assertEquals(Boolean.TRUE, actual);
  }

  @Test
  void givenNormalDay_whenIsMatchWithApplicableDayTimeInConfig_thenReturnSuccess()
      throws ParseException {
    // GIVEN
    FlatFareHoliday holiday = FlatFareHoliday.builder().date("2023-09-21 00:00:00").build();
    List<FlatFareHoliday> flatFareHolidayList = new ArrayList<>();
    flatFareHolidayList.add(holiday);

    String reqDateString = "09/20/2023 01:00:00";
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy hh:mm:ss");
    Date reqDate = df.parse(reqDateString);

    BookingFare bookingFareConfig =
        BookingFare.builder()
            .applicableDays("WED,FRI")
            .startTime(LocalTime.of(0, 0, 0))
            .endTime(LocalTime.of(12, 0, 0))
            .build();

    // WHEN
    flatFareComputeImpl.configSet.setHolidayList(flatFareHolidayList);

    // THEN
    boolean actual =
        flatFareComputeImpl.isMatchWithApplicableDayTimeInConfig(reqDate, bookingFareConfig);
    assertEquals(Boolean.TRUE, actual);
  }

  @Test
  void givenHoliday_whenIsHolidayAndMatchWithApplicableDayOtherTimeInConfig_thenReturnSuccess()
      throws ParseException {
    // GIVEN
    FlatFareHoliday holiday = FlatFareHoliday.builder().date("2023-09-20 00:00:00").build();
    List<FlatFareHoliday> flatFareHolidayList = new ArrayList<>();
    flatFareHolidayList.add(holiday);

    String reqDateString = "09/20/2023 01:00:00";
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy hh:mm:ss");
    Date reqDate = df.parse(reqDateString);

    BookingFare bookingFareConfig =
        BookingFare.builder()
            .applicableDays("WED,HOL")
            .startTime(DateUtils.convertToLocalTime("00:00:00"))
            .endTime(DateUtils.convertToLocalTime("00:00:00"))
            .build();

    // WHEN
    flatFareComputeImpl.configSet.setHolidayList(flatFareHolidayList);

    // THEN
    boolean actual =
        flatFareComputeImpl.isHolidayAndMatchWithApplicableDayOtherTimeInConfig(
            reqDate, bookingFareConfig);
    assertEquals(Boolean.TRUE, actual);
  }

  @Test
  void givenNormalDay_whenIsHolidayAndMatchWithApplicableDayOtherTimeInConfig_thenReturnSuccess()
      throws ParseException {
    // GIVEN
    FlatFareHoliday holiday = FlatFareHoliday.builder().date("2023-09-21 00:00:00").build();
    List<FlatFareHoliday> flatFareHolidayList = new ArrayList<>();
    flatFareHolidayList.add(holiday);

    String reqDateString = "09/20/2023 01:00:00";
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy hh:mm:ss");
    Date reqDate = df.parse(reqDateString);

    BookingFare bookingFareConfig =
        BookingFare.builder()
            .applicableDays("WED,FRI")
            .startTime(DateUtils.convertToLocalTime("00:00:00"))
            .endTime(DateUtils.convertToLocalTime("00:00:00"))
            .build();

    // WHEN
    flatFareComputeImpl.configSet.setHolidayList(flatFareHolidayList);

    // THEN
    boolean actual =
        flatFareComputeImpl.isNormalDayAndMatchWithApplicableDayOtherTimeInConfig(
            reqDate, bookingFareConfig);
    assertEquals(Boolean.TRUE, actual);
  }

  @Test
  void givenNoParam_whenCalEttFare_thenReturnSuccess() {
    // GIVEN
    var request = FlatFareRequest.builder().ett(1200L).build();

    FlatFareConfigSet flatFareConfigSet =
        FlatFareConfigSet.builder().durationUnitConfig("45").durationRateConfig("0.25").build();
    flatFareComputeImpl.setConfigSet(flatFareConfigSet);

    var flatfareVO = new FlatFareVO();
    flatfareVO.setFlatFareRequest(request);

    // THEN
    flatFareComputeImpl.calEttFare(flatfareVO);

    var actual = CommonUtils.roundToTwo(flatfareVO.getWaitTimeFare());
    var expected = 6.67;

    assertEquals(expected, actual);
  }

  @Test
  void givenNoParam_whenCalEttFare_thenReturnEttZero() {
    // GIVEN
    var request = FlatFareRequest.builder().ett(1200L).build();

    FlatFareConfigSet flatFareConfigSet =
        FlatFareConfigSet.builder().durationUnitConfig("45").durationRateConfig("0").build();
    flatFareComputeImpl.setConfigSet(flatFareConfigSet);

    var flatfareVO = new FlatFareVO();
    flatfareVO.setFlatFareRequest(request);

    // THEN
    flatFareComputeImpl.calEttFare(flatfareVO);

    var actual = CommonUtils.roundToTwo(flatfareVO.getWaitTimeFare());
    var expected = 0.0;

    assertEquals(expected, actual);
  }

  @Test
  void givenNullConfig_whenCalEttFare_thenThrowsException() {
    var request = FlatFareRequest.builder().ett(1200L).vehTypeId(1).build();
    var flatfareVO = new FlatFareVO();
    flatfareVO.setFlatFareRequest(request);

    assertThrows(DomainException.class, () -> flatFareComputeImpl.calEttFare(flatfareVO));
  }

  @Test
  void givenValidTotalFareAndEstimatedFare_whenRoundToFiftyCent_thenReturnRoundedFares() {
    // GIVEN
    FlatFareVO flatFareVO = new FlatFareVO();
    flatFareVO.setFlatFareRequest(FlatFareRequest.builder().vehTypeId(101).build());
    flatFareVO.setTotalFare(BigDecimal.valueOf(15.456));
    flatFareVO.setEstimatedFareLF(BigDecimal.valueOf(10.56));
    flatFareVO.setEstimatedFareRT(BigDecimal.valueOf(20.456));

    // THEN
    flatFareComputeImpl.roundToFiftyCent(flatFareVO);
    assertEquals(BigDecimal.valueOf(15.5), flatFareVO.getTotalFare());
    assertEquals(BigDecimal.valueOf(11.0), flatFareVO.getEstimatedFareLF());
    assertEquals(BigDecimal.valueOf(20.5), flatFareVO.getEstimatedFareRT());
  }

  @Test
  void givenNormalCondition_whenCalMultiDestSurcharge_thenReturnZero() {
    FlatFareVO flatFareVO = new FlatFareVO();
    flatFareVO.setFlatFareRequest(FlatFareRequest.builder().vehTypeId(101).build());
    flatFareComputeImpl.calMultiDestSurcharge(flatFareVO);
    assertEquals(0.0, flatFareVO.getMultiDestSurcharge());
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.dynamicpricing.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.DynamicPricingConfigSet;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.EventSurgeAddressConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareHoliday;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareVO;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.LocSurcharge;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.DemandSupplyService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.CommonUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.DateUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.DynamicSurgesEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FareTypeConfig;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.Month;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class DynamicPricingManagerTest {

  @Mock private DynamicPricingManager dynamicPricingManager;
  @Mock private DemandSupplyService demandSupplyService;
  private FlatFareVO flatFareVO;

  @BeforeEach
  void setUpDynamicPricingManager() {
    flatFareVO = new FlatFareVO();
    dynamicPricingManager = new DynamicPricingManager(demandSupplyService);
  }

  @Test
  void computeTotalFare_surgeIsNotNull_doNotThrowException() {
    DynamicPricingConfigSet configSet = initConfigSet();
    Date currentDate = new Date();
    FlatFareRequest flatFareRequest =
        FlatFareRequest.builder()
            .requestDate(currentDate)
            .originZoneId("1")
            .ett(300)
            .routingDistance(12000)
            .build();
    FlatFareVO flatFareVO = new FlatFareVO();
    flatFareVO.setFlatFareRequest(flatFareRequest);
    LocSurcharge firstLocSurcharge = LocSurcharge.builder().amount(4d).build();
    LocSurcharge secondLocSurcharge = LocSurcharge.builder().amount(4d).build();
    flatFareVO.setLocSurCharge(Arrays.asList(firstLocSurcharge, secondLocSurcharge));
    flatFareVO.setEventSurgeCharge(Arrays.asList(4d, 2d));
    DynamicSurgesEntity firstSurgesEntity =
        DynamicSurgesEntity.builder().zoneId("1").surge(10).build();
    DynamicSurgesEntity secondSurgesEntity =
        DynamicSurgesEntity.builder().zoneId("2").surge(20).build();
    List<DynamicSurgesEntity> listSurges = Arrays.asList(firstSurgesEntity, secondSurgesEntity);
    when(demandSupplyService.getDynpSurges()).thenReturn(listSurges);
    dynamicPricingManager.computeTotalFare(flatFareVO, configSet);
    assertEquals(10.0, flatFareVO.getDpSurgePercent());
    assertEquals(50.2, flatFareVO.getDpBaseFareForSurge());
    assertEquals(0.1, flatFareVO.getPricePerKm());
    assertEquals(69.22, flatFareVO.getDpFareAfterSurge());
    assertEquals(Boolean.TRUE, flatFareVO.isCalculated());
  }

  @Test
  void computeTotalFare_surgeIsNull_doNotThrowException() {
    DynamicPricingConfigSet configSet = initConfigSet();

    Date currentDate = new Date();
    FlatFareRequest flatFareRequest =
        FlatFareRequest.builder()
            .requestDate(currentDate)
            .originZoneId("1")
            .ett(300)
            .routingDistance(12000)
            .build();
    FlatFareVO flatFareVO = new FlatFareVO();
    flatFareVO.setFlatFareRequest(flatFareRequest);
    LocSurcharge firstLocSurcharge = LocSurcharge.builder().amount(4d).build();
    LocSurcharge secondLocSurcharge = LocSurcharge.builder().amount(4d).build();
    flatFareVO.setLocSurCharge(Arrays.asList(firstLocSurcharge, secondLocSurcharge));
    flatFareVO.setEventSurgeCharge(Arrays.asList(4d, 2d));
    List<DynamicSurgesEntity> listSurges = Collections.emptyList();
    when(demandSupplyService.getDynpSurges()).thenReturn(listSurges);
    dynamicPricingManager.computeTotalFare(flatFareVO, configSet);
    assertEquals(0.0, flatFareVO.getDpSurgePercent());
    assertEquals(50.2, flatFareVO.getDpBaseFareForSurge());
    assertEquals(0.0, flatFareVO.getPricePerKm());
    assertEquals(64.2, flatFareVO.getDpFareAfterSurge());
    assertEquals(Boolean.TRUE, flatFareVO.isCalculated());
  }

  DynamicPricingConfigSet initConfigSet() {
    Date currentDate = new Date();
    FareTypeConfig flagDownConfigHoliday =
        FareTypeConfig.builder()
            .fareType(RedisKeyConstant.DYNP_FLAG_DOWN_RATE)
            .defaultFixed(2d)
            .defaultPercent(0d)
            .hour(DateUtils.getHourOfDate(currentDate).toString())
            .day(RedisKeyConstant.HOL)
            .startDate(LocalDate.of(1000, Month.JANUARY, 31))
            .endDate(LocalDate.of(3000, Month.DECEMBER, 31))
            .build();
    FareTypeConfig flagDownConfigNormalDay =
        FareTypeConfig.builder()
            .fareType(RedisKeyConstant.DYNP_FLAG_DOWN_RATE)
            .defaultFixed(3d)
            .defaultPercent(0d)
            .hour(DateUtils.getHourOfDate(currentDate).toString())
            .day(DateUtils.toDayOfWeekShortUpperCase(currentDate))
            .startDate(LocalDate.of(1000, Month.JANUARY, 31))
            .endDate(LocalDate.of(3000, Month.DECEMBER, 31))
            .build();
    FareTypeConfig flagDownConfigAll =
        FareTypeConfig.builder()
            .fareType(RedisKeyConstant.DYNP_FLAG_DOWN_RATE)
            .defaultFixed(2.5d)
            .defaultPercent(0d)
            .hour(RedisKeyConstant.ALL)
            .day(RedisKeyConstant.ALL)
            .startDate(LocalDate.of(1000, Month.JANUARY, 31))
            .endDate(LocalDate.of(3000, Month.DECEMBER, 31))
            .build();
    Map<String, List<FareTypeConfig>> flagDownConfigList = new HashMap<>();
    flagDownConfigList.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_FLAG_DOWN_RATE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.HOL,
        List.of(flagDownConfigHoliday));
    flagDownConfigList.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_FLAG_DOWN_RATE
            + RedisKeyConstant.COLON
            + DateUtils.toDayOfWeekShortUpperCase(currentDate),
        List.of(flagDownConfigNormalDay));
    flagDownConfigList.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_FLAG_DOWN_RATE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.ALL,
        List.of(flagDownConfigAll));

    FareTypeConfig tier1EndDistAll =
        FareTypeConfig.builder()
            .fareType(RedisKeyConstant.DYNP_TIER_1_END_DIST)
            .defaultFixed(10000d)
            .defaultPercent(0d)
            .hour(RedisKeyConstant.ALL)
            .day(RedisKeyConstant.ALL)
            .startDate(LocalDate.of(1000, Month.JANUARY, 31))
            .endDate(LocalDate.of(3000, Month.DECEMBER, 31))
            .build();
    Map<String, List<FareTypeConfig>> tier1EndDistConfigList = new HashMap<>();
    tier1EndDistConfigList.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_TIER_1_END_DIST
            + RedisKeyConstant.COLON
            + RedisKeyConstant.ALL,
        List.of(tier1EndDistAll));

    FareTypeConfig tier1PriceMultiplierAll =
        FareTypeConfig.builder()
            .fareType(RedisKeyConstant.DYNP_TIER_1_PRICE_MULTIPLIER)
            .defaultFixed(2d)
            .defaultPercent(0d)
            .hour(RedisKeyConstant.ALL)
            .day(RedisKeyConstant.ALL)
            .startDate(LocalDate.of(1000, Month.JANUARY, 31))
            .endDate(LocalDate.of(3000, Month.DECEMBER, 31))
            .build();
    Map<String, List<FareTypeConfig>> tier1PriceMultiplierConfigList = new HashMap<>();
    tier1PriceMultiplierConfigList.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_TIER_1_PRICE_MULTIPLIER
            + RedisKeyConstant.COLON
            + RedisKeyConstant.ALL,
        List.of(tier1PriceMultiplierAll));

    FareTypeConfig tier2StartDistAll =
        FareTypeConfig.builder()
            .fareType(RedisKeyConstant.DYNP_TIER_2_START_DIST)
            .defaultFixed(10000d)
            .defaultPercent(0d)
            .hour(RedisKeyConstant.ALL)
            .day(RedisKeyConstant.ALL)
            .startDate(LocalDate.of(1000, Month.JANUARY, 31))
            .endDate(LocalDate.of(3000, Month.DECEMBER, 31))
            .build();
    Map<String, List<FareTypeConfig>> tier2StartDistConfigList = new HashMap<>();
    tier2StartDistConfigList.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_TIER_2_START_DIST
            + RedisKeyConstant.COLON
            + RedisKeyConstant.ALL,
        List.of(tier2StartDistAll));

    FareTypeConfig tier2PriceMultiplierAll =
        FareTypeConfig.builder()
            .fareType(RedisKeyConstant.DYNP_TIER_2_PRICE_MULTIPLIER)
            .defaultFixed(2d)
            .defaultPercent(0d)
            .hour(RedisKeyConstant.ALL)
            .day(RedisKeyConstant.ALL)
            .startDate(LocalDate.of(1000, Month.JANUARY, 31))
            .endDate(LocalDate.of(3000, Month.DECEMBER, 31))
            .build();
    Map<String, List<FareTypeConfig>> tier2riceMultiplierConfigList = new HashMap<>();
    tier2riceMultiplierConfigList.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_TIER_2_PRICE_MULTIPLIER
            + RedisKeyConstant.COLON
            + RedisKeyConstant.ALL,
        List.of(tier2PriceMultiplierAll));

    FareTypeConfig durationRateAll =
        FareTypeConfig.builder()
            .fareType(RedisKeyConstant.DYNP_DURATION_RATE)
            .defaultFixed(0d)
            .defaultPercent(2.3)
            .hour(RedisKeyConstant.ALL)
            .day(RedisKeyConstant.ALL)
            .startDate(LocalDate.of(1000, Month.JANUARY, 31))
            .endDate(LocalDate.of(3000, Month.DECEMBER, 31))
            .build();
    Map<String, List<FareTypeConfig>> durationRateConfigList = new HashMap<>();
    durationRateConfigList.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_DURATION_RATE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.ALL,
        List.of(durationRateAll));

    FareTypeConfig bookingFeeAll =
        FareTypeConfig.builder()
            .fareType(RedisKeyConstant.DYNP_BOOKING_FEE)
            .defaultFixed(4d)
            .defaultPercent(0d)
            .hour(DateUtils.getHourOfDate(currentDate).toString())
            .day(DateUtils.toDayOfWeekShortUpperCase(currentDate))
            .startDate(LocalDate.of(1000, Month.JANUARY, 31))
            .endDate(LocalDate.of(3000, Month.DECEMBER, 31))
            .build();
    Map<String, List<FareTypeConfig>> bookingFeeConfigList = new HashMap<>();
    bookingFeeConfigList.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_BOOKING_FEE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.ALL,
        List.of(bookingFeeAll));

    FareTypeConfig hourlySurchargeNormalDay =
        FareTypeConfig.builder()
            .fareType(RedisKeyConstant.DYNP_PEAK_MIDNIGHT_HOUR_RATE)
            .defaultFixed(0d)
            .defaultPercent(0.2d)
            .hour(DateUtils.getHourOfDate(currentDate).toString())
            .day(DateUtils.toDayOfWeekShortUpperCase(currentDate))
            .startDate(LocalDate.of(1000, Month.JANUARY, 31))
            .endDate(LocalDate.of(3000, Month.DECEMBER, 31))
            .build();
    Map<String, List<FareTypeConfig>> hourlySurchargeConfigList = new HashMap<>();
    hourlySurchargeConfigList.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_PEAK_MIDNIGHT_HOUR_RATE
            + RedisKeyConstant.COLON
            + DateUtils.toDayOfWeekShortUpperCase(currentDate),
        List.of(hourlySurchargeNormalDay));
    Map<String, List<FareTypeConfig>> minSurgeAmountConfigList = new HashMap<>();
    minSurgeAmountConfigList.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_MIN_SURGE_AMOUNT
            + RedisKeyConstant.COLON
            + RedisKeyConstant.ALL,
        List.of(
            FareTypeConfig.builder()
                .fareTypeId(2202)
                .fareType(RedisKeyConstant.DYNP_MIN_SURGE_AMOUNT)
                .fixedValue(10000.0)
                .percentValue(10.0)
                .defaultFixed(10000.0)
                .defaultPercent(10.0)
                .day(RedisKeyConstant.ALL)
                .hour(RedisKeyConstant.ALL)
                .startDate(LocalDate.of(2023, 2, 22))
                .endDate(LocalDate.of(2100, 2, 22))
                .build()));
    Map<String, List<FareTypeConfig>> surgeBufferConfigList = new HashMap<>();
    surgeBufferConfigList.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_SURGE_BUFFER
            + RedisKeyConstant.COLON
            + RedisKeyConstant.ALL,
        List.of(
            FareTypeConfig.builder()
                .fareTypeId(2205)
                .fareType(RedisKeyConstant.DYNP_SURGE_BUFFER)
                .day(RedisKeyConstant.ALL)
                .hour(RedisKeyConstant.ALL)
                .startDate(LocalDate.of(2023, 2, 22))
                .endDate(LocalDate.of(2100, 2, 22))
                .build()));
    Map<String, List<FareTypeConfig>> desurgeMaxCapConfigList = new HashMap<>();
    desurgeMaxCapConfigList.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_DESURGE_MAX_CAP
            + RedisKeyConstant.COLON
            + RedisKeyConstant.ALL,
        List.of(
            FareTypeConfig.builder()
                .fareTypeId(2201)
                .fareType(RedisKeyConstant.DYNP_DESURGE_MAX_CAP)
                .day(RedisKeyConstant.ALL)
                .hour(RedisKeyConstant.ALL)
                .startDate(LocalDate.of(2023, 2, 22))
                .endDate(LocalDate.of(2100, 2, 22))
                .build()));
    Map<String, List<FareTypeConfig>> minCapConfigList = new HashMap<>();
    minCapConfigList.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_MIN_CAP
            + RedisKeyConstant.COLON
            + RedisKeyConstant.ALL,
        List.of(
            FareTypeConfig.builder()
                .fareTypeId(2202)
                .fareType(RedisKeyConstant.DYNP_MIN_CAP)
                .defaultFixed(0.0)
                .defaultPercent(0.0)
                .day(RedisKeyConstant.ALL)
                .hour(RedisKeyConstant.ALL)
                .startDate(LocalDate.of(2023, 2, 22))
                .endDate(LocalDate.of(2100, 2, 22))
                .build()));
    Map<String, List<FareTypeConfig>> maxCapConfigList = new HashMap<>();
    maxCapConfigList.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_MAX_CAP
            + RedisKeyConstant.COLON
            + RedisKeyConstant.ALL,
        List.of(
            FareTypeConfig.builder()
                .fareTypeId(2202)
                .fareType(RedisKeyConstant.DYNP_MAX_CAP)
                .fixedValue(10000.0)
                .percentValue(10.0)
                .defaultFixed(10000.0)
                .defaultPercent(10.0)
                .day(RedisKeyConstant.ALL)
                .hour(RedisKeyConstant.ALL)
                .startDate(LocalDate.of(2023, 2, 22))
                .endDate(LocalDate.of(2100, 2, 22))
                .build()));
    FlatFareHoliday holiday = FlatFareHoliday.builder().date("2023-09-20 00:00:00").build();
    List<FlatFareHoliday> flatFareHolidayList = new ArrayList<>();
    flatFareHolidayList.add(holiday);

    List<EventSurgeAddressConfig> eventSurgeAddressConfigList = getListEventSurgeAddressConfigs();

    return DynamicPricingConfigSet.builder()
        .flagDownConfigList(flagDownConfigList)
        .tier1EndDestConfigList(tier1EndDistConfigList)
        .tier1PriceMultiplierConfigList(tier1PriceMultiplierConfigList)
        .tier2StartDestConfigList(tier2StartDistConfigList)
        .tier2PriceMultiplierConfigList(tier2riceMultiplierConfigList)
        .durationRateConfigList(durationRateConfigList)
        .bookingFee(bookingFeeConfigList)
        .hourlySurcharge(hourlySurchargeConfigList)
        .minSurgeAmountConfigList(minSurgeAmountConfigList)
        .surgeBufferConfigList(surgeBufferConfigList)
        .desurgeMaxCapConfigList(desurgeMaxCapConfigList)
        .maxCapConfigList(maxCapConfigList)
        .minCapConfigList(minCapConfigList)
        .holidayList(flatFareHolidayList)
        .eventSurgeAddressConfigList(eventSurgeAddressConfigList)
        .build();
  }

  private List<EventSurgeAddressConfig> getListEventSurgeAddressConfigs() {
    EventSurgeAddressConfig chargeByPickUpFixedAmountConfig =
        EventSurgeAddressConfig.builder()
            .startTime(LocalTime.parse("00:00:00"))
            .endTime(LocalTime.parse("23:59:59"))
            .applicableDaysOfMonth(",19,20,21,22,")
            .applicableMonths(",8,9,10,11,")
            .chargeBy(FlatfareConstants.CHARGE_BY_PICKUP)
            .chargeType(FlatfareConstants.FIXED_AMOUNT)
            .chargeVal(0.5)
            .applicableAddresses(",1001,")
            .build();
    EventSurgeAddressConfig chargeByPickUpPercentConfig =
        EventSurgeAddressConfig.builder()
            .startTime(LocalTime.parse("00:00:00"))
            .endTime(LocalTime.parse("23:59:59"))
            .applicableDaysOfMonth(",19,20,21,22,")
            .applicableMonths(",8,9,10,11,")
            .chargeBy(FlatfareConstants.CHARGE_BY_PICKUP)
            .chargeType(FlatfareConstants.PERCENTAGE)
            .chargeVal(0.5)
            .applicableAddresses(",1001,")
            .build();
    EventSurgeAddressConfig chargeByDestFixedAmountConfig =
        EventSurgeAddressConfig.builder()
            .startTime(LocalTime.parse("00:00:00"))
            .endTime(LocalTime.parse("23:59:59"))
            .applicableDaysOfMonth(",19,20,21,22,")
            .applicableMonths(",8,9,10,11,")
            .chargeBy(FlatfareConstants.CHARGE_BY_DEST)
            .chargeType(FlatfareConstants.FIXED_AMOUNT)
            .chargeVal(0.5)
            .applicableAddresses(",3003,")
            .build();
    EventSurgeAddressConfig chargeByDestPercentConfig =
        EventSurgeAddressConfig.builder()
            .startTime(LocalTime.parse("00:00:00"))
            .endTime(LocalTime.parse("23:59:59"))
            .applicableDaysOfMonth(",19,20,21,22,")
            .applicableMonths(",8,9,10,11,")
            .chargeBy(FlatfareConstants.CHARGE_BY_DEST)
            .chargeType(FlatfareConstants.PERCENTAGE)
            .chargeVal(0.5)
            .applicableAddresses(",3003,")
            .build();
    EventSurgeAddressConfig chargeByIntermediateFixedAmountConfig =
        EventSurgeAddressConfig.builder()
            .startTime(LocalTime.parse("00:00:00"))
            .endTime(LocalTime.parse("23:59:59"))
            .applicableDaysOfMonth(",19,20,21,22,")
            .applicableMonths(",8,9,10,11,")
            .chargeBy(FlatfareConstants.CHARGE_BY_DEST)
            .chargeType(FlatfareConstants.FIXED_AMOUNT)
            .chargeVal(0.5)
            .applicableAddresses(",2002,")
            .build();
    EventSurgeAddressConfig chargeByIntermediatePercentConfig =
        EventSurgeAddressConfig.builder()
            .startTime(LocalTime.parse("00:00:00"))
            .endTime(LocalTime.parse("23:59:59"))
            .applicableDaysOfMonth(",19,20,21,22,")
            .applicableMonths(",8,9,10,11,")
            .chargeBy(FlatfareConstants.CHARGE_BY_DEST)
            .chargeType(FlatfareConstants.PERCENTAGE)
            .chargeVal(0.5)
            .applicableAddresses(",2002,")
            .build();

    List<EventSurgeAddressConfig> eventSurgeAddressConfigList =
        List.of(
            chargeByPickUpFixedAmountConfig,
            chargeByPickUpPercentConfig,
            chargeByDestFixedAmountConfig,
            chargeByDestPercentConfig,
            chargeByIntermediateFixedAmountConfig,
            chargeByIntermediatePercentConfig);
    return eventSurgeAddressConfigList;
  }

  @Test
  void validateDesurge() {
    DynamicPricingConfigSet configSet = initConfigSet();

    flatFareVO.setDpBaseFareForSurge(1000.0);
    flatFareVO.setDpFareAfterSurge(1000.0);
    flatFareVO.setMeteredBaseFare(2000.0);
    flatFareVO.setLocSurCharge(List.of(LocSurcharge.builder().amount(10.0).build()));
    flatFareVO.setEventSurgeCharge(List.of(10.0));
    flatFareVO.setFlatFareRequest(FlatFareRequest.builder().requestDate(new Date()).build());
    dynamicPricingManager.validateDesurge(flatFareVO, configSet);
    FlatFareVO expect = new FlatFareVO();
    expect.setDpBaseFareForSurge(1000.0);
    expect.setDpFareAfterSurge(1000.0);
    expect.setMeteredBaseFare(2000.0);
    expect.setLocSurCharge(List.of(LocSurcharge.builder().amount(10.0).build()));
    expect.setEventSurgeCharge(List.of(10.0));
    expect.setFlatFareRequest(FlatFareRequest.builder().requestDate(new Date()).build());
    expect.setDpFinalFare(11020.0);
    expect.setTotalFare(CommonUtils.roundToTwoBD(11020.0));
    expect.setDpAplydSurgeAmt(10000.0);
    assertEquals(expect.getDpAplydSurgeAmt(), flatFareVO.getDpAplydSurgeAmt());
    assertEquals(expect.getDpFinalFare(), flatFareVO.getDpFinalFare());
    assertEquals(expect.getTotalFare(), flatFareVO.getTotalFare());
  }
}

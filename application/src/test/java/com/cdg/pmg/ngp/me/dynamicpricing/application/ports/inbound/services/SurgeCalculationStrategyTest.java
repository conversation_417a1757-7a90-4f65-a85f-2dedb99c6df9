package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services;

import static org.junit.jupiter.api.Assertions.assertEquals;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class SurgeCalculationStrategyTest {

  @Spy private SurgeCalculationStrategy surgeCalculationStrategy;

  @Test
  void clamp_valueWithinRangeInclusive_shouldInRange() {
    // given
    var value = 10;
    var min = 0;
    var max = 10;

    // when
    int result = surgeCalculationStrategy.clamp(value, min, max);

    // then
    assertEquals(value, result);
  }

  @Test
  void clamp_valueEqualsMin_shouldReturnMin() {
    // given
    var value = 10;
    var min = 10;
    var max = 20;

    // when
    var result = surgeCalculationStrategy.clamp(value, min, max);

    // then
    assertEquals(min, result);
  }

  @Test
  void clamp_valueEqualsMax_shouldReturnMax() {
    // given
    var value = 20;
    var min = 0;
    var max = 20;

    // when
    var result = surgeCalculationStrategy.clamp(value, min, max);

    // then
    assertEquals(max, result);
  }

  @Test
  void clamp_valueGreaterThanMax_shouldReturnMax() {
    // given
    var value = 21;
    var min = 0;
    var max = 20;

    // when
    var result = surgeCalculationStrategy.clamp(value, min, max);

    // then
    assertEquals(max, result);
  }

  @Test
  void clamp_valueLessThanMin_shouldReturnMin() {
    // given
    var value = -1;
    var min = 0;
    var max = 20;

    // when
    var result = surgeCalculationStrategy.clamp(value, min, max);

    // then
    assertEquals(min, result);
  }
}

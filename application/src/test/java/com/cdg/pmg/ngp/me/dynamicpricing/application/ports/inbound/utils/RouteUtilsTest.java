package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertFalse;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.GenerateRouteResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.MultiFareRequestEntity;
import org.junit.jupiter.api.Test;

class RouteUtilsTest {

  private final MultiFareRequestEntity multiFareRequestEntity =
      MultiFareRequestEntity.builder()
          .pickupAddressLat(1.123)
          .pickupAddressLng(103.111)
          .destAddressLat(1.234)
          .destAddressLng(103.222)
          .build();
  private final GenerateRouteResponse generateRouteResponse =
      GenerateRouteResponse.builder().distanceMeters(1000L).duration("1000").build();
  private final String tripId = "tripId";

  @Test
  void givenNormalCondition_whenCreateGenerateRouteRequest_thenSuccess() {
    assertDoesNotThrow(() -> RouteUtils.createGenerateRouteRequest(multiFareRequestEntity, tripId));
  }

  @Test
  void givenNormalCondition_whenCreateRouteInfo_thenSuccess() {
    assertDoesNotThrow(
        () -> RouteUtils.createRouteInfo(multiFareRequestEntity, generateRouteResponse, tripId));
  }

  @Test
  void givenNormalCondition_whenIsRouteResponseEmpty_thenSuccess() {
    assertFalse(RouteUtils.isRouteResponseEmpty(generateRouteResponse));
  }

  @Test
  void givenNormalCondition_whenIsRouteResponseEttDistInvalid_thenSuccess() {
    assertFalse(RouteUtils.isRouteResponseEttDistInvalid(generateRouteResponse));
  }
}

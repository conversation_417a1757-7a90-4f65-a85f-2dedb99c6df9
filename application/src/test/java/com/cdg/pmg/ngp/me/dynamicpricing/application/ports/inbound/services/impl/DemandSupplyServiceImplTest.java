package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.DemandSupplyService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.DynamicSurgeService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.DateUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.DpsProperties;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.CompanyHolidayRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.DynamicSurgeRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.DynpSurgeLogsRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.PricingRangeRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.CacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.DpsPropertiesService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.FleetAnalyticService;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.CommonConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.DynamicSurgesEntity;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class DemandSupplyServiceImplTest {
  @Mock private DemandSupplyService demandSupplyService;
  @Mock private CacheService cacheService;
  @Mock private PricingRangeRepository pricingRangeRepository;
  @Mock private FleetAnalyticService fleetAnalyticService;
  @Mock private DynamicSurgeRepository dynamicSurgeRepository;
  @Mock private DynpSurgeLogsRepository dynpSurgeLogsRepository;
  @Mock private CompanyHolidayRepository companyHolidayRepository;
  @Mock private DpsPropertiesService dpsPropertiesService;
  @Mock private DynamicSurgeService dynamicSurgeService;

  private static final String DYNP_SURGES_KEY_CACHE =
      RedisKeyConstant.DYNAMIC_PRICING
          .concat(RedisKeyConstant.COLON)
          .concat(RedisKeyConstant.DYNP_SURGES);

  private static final String DYNP_SURGES_NGP_KEY_CACHE =
      RedisKeyConstant.DYNAMIC_PRICING
          .concat(RedisKeyConstant.COLON)
          .concat(RedisKeyConstant.DYNP_SURGES_NGP);

  private DateFormat df;
  private final String ZERO_KEY_SUFFIX = "_0";

  @BeforeEach
  void setUp() {
    demandSupplyService =
        new DemandSupplyServiceImpl(
            cacheService,
            fleetAnalyticService,
            companyHolidayRepository,
            pricingRangeRepository,
            dynamicSurgeRepository,
            dynpSurgeLogsRepository,
            dpsPropertiesService,
            dynamicSurgeService);
    df = new SimpleDateFormat(DateUtils.YYYYMMDD_HHMMSS_FORMAT);
  }

  //  @Test
  //  void givenFleetDataAndHoliday_whenCalculateDemandSupplySurge_thenNotThrowException() {
  //    final var fleetAnalysticResponse1 =
  //        DemandSupplyStatisticsResponse.builder()
  //            .zoneId("01")
  //            .batchCounter(1)
  //            .recentDemand(100)
  //            .previousDemand(200)
  //            .supply(5)
  //            .predictedDemand(7)
  //            .excessDemand(2)
  //            .build();
  //    final var fleetAnalysticResponse2 =
  //        DemandSupplyStatisticsResponse.builder()
  //            .zoneId("02")
  //            .batchCounter(2)
  //            .recentDemand(100)
  //            .previousDemand(200)
  //            .supply(7)
  //            .predictedDemand(5)
  //            .excessDemand(-2)
  //            .build();
  //    final var fleetAnalysticResponse3 =
  //        DemandSupplyStatisticsResponse.builder()
  //            .zoneId("03")
  //            .batchCounter(2)
  //            .recentDemand(100)
  //            .previousDemand(200)
  //            .supply(5)
  //            .predictedDemand(5)
  //            .excessDemand(0)
  //            .build();
  //    Mockito.when(fleetAnalyticService.getDemandSuppyStatistics())
  //        .then(
  //            config ->
  //                List.of(fleetAnalysticResponse1, fleetAnalysticResponse2,
  // fleetAnalysticResponse3));
  //
  //    final var currentDate = df.format(new Date());
  //    final var companyHolidays = List.of(currentDate);
  //    Mockito.when(cacheService.getListValue(RedisKeyConstant.COMPANY_HOLIDAY, String.class))
  //        .then(config -> companyHolidays);
  //
  //    final var dynpConfig1 =
  //        PricingRangeCalDemandSurgeQueryResponse.builder()
  //            .surgeLow(-20)
  //            .surgeHigh(41)
  //            .stepNegative(3)
  //            .stepPositive(4)
  //            .zoneId("01")
  //            .build();
  //    final var dynpConfig2 =
  //        PricingRangeCalDemandSurgeQueryResponse.builder()
  //            .surgeLow(-30)
  //            .surgeHigh(31)
  //            .stepNegative(2)
  //            .stepPositive(5)
  //            .zoneId("02")
  //            .build();
  //    final boolean isHistory = true;
  //    Mockito.when(pricingRangeRepository.getDynpConfigForDemandSurge(isHistory))
  //        .then(config -> List.of(dynpConfig1, dynpConfig2));
  //
  //    final var dynpSurgeEntity1 =
  //        DynamicSurgesEntity.builder()
  //            .zoneId("01")
  //            .surge(52)
  //            .prevSurge(0)
  //            .surgeLow(-23)
  //            .surgeHigh(63)
  //            .demandPredicted(10)
  //            .demandRecent(20)
  //            .demandPrevious(30)
  //            .supply(5)
  //            .build();
  //    final var dynpSurgeEntity2 =
  //        DynamicSurgesEntity.builder()
  //            .zoneId("02")
  //            .surge(42)
  //            .prevSurge(0)
  //            .surgeLow(-23)
  //            .surgeHigh(63)
  //            .demandPredicted(10)
  //            .demandRecent(20)
  //            .demandPrevious(30)
  //            .supply(5)
  //            .build();
  //    final var dynpSurgeEntity3 =
  //        DynamicSurgesEntity.builder()
  //            .zoneId("03")
  //            .surge(32)
  //            .prevSurge(0)
  //            .surgeLow(-23)
  //            .surgeHigh(63)
  //            .demandPredicted(10)
  //            .demandRecent(20)
  //            .demandPrevious(30)
  //            .supply(5)
  //            .build();
  //    Mockito.when(dynamicSurgeRepository.getDynpSurges())
  //        .then(config -> List.of(dynpSurgeEntity1, dynpSurgeEntity2, dynpSurgeEntity3));
  //
  //    demandSupplyService.calculateDemandSupplySurge();
  //    Mockito.verify(dynpSurgeLogsRepository, Mockito.times(1)).insertDynpSurgesLog();
  //  }

  //  @Test
  //  void givenDemandSupplyEmpty_whenCalculateDemandSupplySurge_thenReturnNon() {
  //    Mockito.when(fleetAnalyticService.getDemandSuppyStatistics()).then(config -> List.of());
  //    demandSupplyService.calculateDemandSupplySurge();
  //    Mockito.verify(dynpSurgeLogsRepository, Mockito.times(0)).insertDynpSurgesLog();
  //  }

  @Test
  void givenDynpSurgesNotEmptyR1_whenGetDynpSurges_thenReturnList() {
    final var entity = new DynamicSurgesEntity();
    final List<DynamicSurgesEntity> cacheData = List.of(entity);
    final DpsProperties dpsProperties =
        DpsProperties.builder().applicationRelease(CommonConstant.RELEASE_1).build();

    Mockito.when(dpsPropertiesService.getDpsProperties()).thenReturn(dpsProperties);
    Mockito.when(cacheService.getListValue(DYNP_SURGES_KEY_CACHE, DynamicSurgesEntity.class))
        .then(config -> cacheData);
    final List<DynamicSurgesEntity> actual = demandSupplyService.getDynpSurges();

    final List<DynamicSurgesEntity> expected = List.of(entity);
    Assertions.assertEquals(expected, actual);
  }

  @Test
  void givenDynpSurgesNotEmptyR2_whenGetDynpSurges_thenReturnList() {
    final var entity = new DynamicSurgesEntity();
    final List<DynamicSurgesEntity> cacheData = List.of(entity);
    final DpsProperties dpsProperties =
        DpsProperties.builder().applicationRelease(CommonConstant.RELEASE_2).build();

    Mockito.when(dpsPropertiesService.getDpsProperties()).thenReturn(dpsProperties);
    Mockito.when(cacheService.getListValue(DYNP_SURGES_NGP_KEY_CACHE, DynamicSurgesEntity.class))
        .then(config -> cacheData);
    final List<DynamicSurgesEntity> actual = demandSupplyService.getDynpSurges();

    final List<DynamicSurgesEntity> expected = List.of(entity);
    Assertions.assertEquals(expected, actual);
  }

  @Test
  void givenDynpSurgesNotEmptyReleaseDefault_whenGetDynpSurges_thenReturnList() {
    final var entity = new DynamicSurgesEntity();
    final List<DynamicSurgesEntity> cacheData = List.of(entity);
    final DpsProperties dpsProperties = DpsProperties.builder().applicationRelease(3).build();

    Mockito.when(dpsPropertiesService.getDpsProperties()).thenReturn(dpsProperties);
    Mockito.when(cacheService.getListValue(DYNP_SURGES_NGP_KEY_CACHE, DynamicSurgesEntity.class))
        .then(config -> cacheData);
    final List<DynamicSurgesEntity> actual = demandSupplyService.getDynpSurges();

    final List<DynamicSurgesEntity> expected = List.of(entity);
    Assertions.assertEquals(expected, actual);
  }

  @Test
  void givenEmptyListConfig_whenLoadDynpSurgeConfigs_thenReturnNon() {
    Mockito.when(dynamicSurgeRepository.getDynpSurges()).then(config -> new ArrayList<>());
    demandSupplyService.loadDynpSurgeConfigs();
    Mockito.verify(cacheService, Mockito.times(0))
        .setListValue(Mockito.anyString(), Mockito.anyList());
  }

  @Test
  void givenListConfig_whenLoadDynpSurgeConfigs_thenSetValue() {
    Mockito.when(dynamicSurgeRepository.getDynpSurges())
        .then(config -> List.of(new DynamicSurgesEntity()));
    demandSupplyService.loadDynpSurgeConfigs();
    Mockito.verify(cacheService, Mockito.times(1))
        .setListValue(Mockito.anyString(), Mockito.anyList());
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import static com.cdg.pmg.ngp.me.dynamicpricing.constants.ConfigKeyConstant.DYNP_BOOKING_FEE;
import static com.cdg.pmg.ngp.me.dynamicpricing.constants.ConfigKeyConstant.DYNP_MIN_CAP;
import static com.cdg.pmg.ngp.me.dynamicpricing.constants.DomainConstant.MON;
import static com.cdg.pmg.ngp.me.dynamicpricing.constants.ErrorMessageConstant.INVALID_END_DATE;
import static com.cdg.pmg.ngp.me.dynamicpricing.constants.ErrorMessageConstant.INVALID_START_DATE;
import static com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant.WILDCARD;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.DynamicPricingConfigSet;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.mapper.FareTypeConfigAppMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.FareTypeConfigService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.FareTypeConfigRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.CacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.commands.FareTypeConfigCommand;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FareTypeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.BadRequestException;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.text.ParseException;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class FareTypeConfigServiceImplTest {

  @Mock private FareTypeConfigService service;
  @Mock private FareTypeConfigRepository fareTypeConfigRepository;
  @Mock private FareTypeConfigAppMapper fareTypeConfigAppMapper;
  @Mock private CacheService cacheService;
  @Mock private ObjectMapper objectMapper;

  private FareTypeConfig fareTypeConfig;
  private FareTypeConfigCommand fareTypeConfigCommand;

  @BeforeEach
  void init() {
    service =
        new FareTypeConfigServiceImpl(
            fareTypeConfigRepository, fareTypeConfigAppMapper, cacheService, objectMapper);
    var startDate = OffsetDateTime.now().plusDays(2).toLocalDate();
    var endDate = OffsetDateTime.now().plusDays(4).toLocalDate();

    fareTypeConfig =
        FareTypeConfig.builder()
            .fareType(DYNP_MIN_CAP)
            .defaultFixed(0.2)
            .defaultPercent(0.2)
            .startDate(startDate)
            .endDate(endDate)
            .day(MON)
            .hour("10")
            .build();

    fareTypeConfigCommand =
        FareTypeConfigCommand.builder()
            .fareType(DYNP_MIN_CAP)
            .defaultFixed(0.2)
            .defaultPercent(0.2)
            .startDate(startDate.toString())
            .endDate(endDate.toString())
            .day(MON)
            .hour("10")
            .build();
  }

  @Test
  void getParamConfigByListFareType_listMapConfigFromCacheIsNull_returnOK() {
    Set<String> listKey = new HashSet<>();
    listKey.add(WILDCARD + DYNP_MIN_CAP + WILDCARD);
    when(cacheService.getKeysByPattern(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(listKey);

    when(cacheService.getMultiValueList(Mockito.any(), Mockito.any())).thenReturn(null);

    List<FareTypeConfig> listFareTypeConfig = new ArrayList<>();
    listFareTypeConfig.add(fareTypeConfig);
    when(fareTypeConfigRepository.getParamConfigByListFareType(Mockito.any()))
        .thenReturn(listFareTypeConfig);

    Set<String> listFareType = new HashSet<>();
    listFareType.add(DYNP_MIN_CAP);
    var actual = service.getParamConfigsByListFareType(listFareType).get(0).getDay();

    assertEquals(MON, actual);
  }

  @Test
  void updateFareTypeConfig_invalidStartDate_throwBadRequestException() throws ParseException {
    fareTypeConfig.setStartDate(OffsetDateTime.now().plusDays(-2).toLocalDate());
    when(fareTypeConfigAppMapper.mapFareTypeConfigCommandToFareTypeConfig(Mockito.any()))
        .thenReturn(fareTypeConfig);

    try {
      service.insertOrUpdateFareTypeConfig(fareTypeConfigCommand);
    } catch (BadRequestException e) {
      assertEquals(INVALID_START_DATE, e.getMessage());
    }
  }

  @Test
  void updateFareTypeConfig_invalidEndDate_throwBadRequestException() throws ParseException {
    fareTypeConfig.setEndDate(OffsetDateTime.now().plusDays(1).toLocalDate());
    when(fareTypeConfigAppMapper.mapFareTypeConfigCommandToFareTypeConfig(Mockito.any()))
        .thenReturn(fareTypeConfig);

    try {
      service.insertOrUpdateFareTypeConfig(fareTypeConfigCommand);
    } catch (BadRequestException e) {
      assertEquals(INVALID_END_DATE, e.getMessage());
    }
  }

  @Test
  void updateFareTypeConfig_validInput_createTypeConfig() throws ParseException {
    when(fareTypeConfigAppMapper.mapFareTypeConfigCommandToFareTypeConfig(Mockito.any()))
        .thenReturn(fareTypeConfig);

    List<FareTypeConfig> fareTypeConfigList = new ArrayList<>();
    when(fareTypeConfigRepository.getParamConfigByListFareType(Mockito.any()))
        .thenReturn(fareTypeConfigList);
    when(fareTypeConfigRepository.createFareTypeConfig(Mockito.any())).thenReturn(fareTypeConfig);

    var actual = service.insertOrUpdateFareTypeConfig(fareTypeConfigCommand).getFareType();
    assertEquals(DYNP_MIN_CAP, actual);
  }

  @Test
  void updateFareTypeConfig_validInput_updateTypeConfig() throws ParseException {
    when(fareTypeConfigAppMapper.mapFareTypeConfigCommandToFareTypeConfig(Mockito.any()))
        .thenReturn(fareTypeConfig);

    List<FareTypeConfig> fareTypeConfigList = new ArrayList<>();
    fareTypeConfigList.add(fareTypeConfig);
    when(fareTypeConfigRepository.getParamConfigByListFareType(Mockito.any()))
        .thenReturn(fareTypeConfigList);

    when(fareTypeConfigRepository.updateFareTypeConfig(Mockito.any())).thenReturn(fareTypeConfig);

    var actual = service.insertOrUpdateFareTypeConfig(fareTypeConfigCommand).getFareType();
    assertEquals(DYNP_MIN_CAP, actual);
  }

  @Test
  void loadFareTypeConfig_isEmptyConfig() {
    Mockito.when(fareTypeConfigRepository.getFareTypeConfigs()).then(config -> new ArrayList<>());
    service.loadFareTypeConfig();
    Mockito.verify(cacheService, Mockito.times(0))
        .setListValue(Mockito.anyString(), Mockito.anyList());
  }

  @Test
  void loadFareTypeConfig_isNotEmptyConfig() {
    final var configs =
        List.of(FareTypeConfig.builder().fareType(DYNP_BOOKING_FEE).day("").build());
    Mockito.when(fareTypeConfigRepository.getFareTypeConfigs()).then(config -> configs);
    service.loadFareTypeConfig();
    Mockito.verify(cacheService, Mockito.times(1))
        .setListValue(Mockito.anyString(), Mockito.anyList());
  }

  @Test
  void givenNormalCondition_whenReloadFareTypeConfigSet_thenSuccess() {
    setupForGetDynamicPricingConfigSet();
    // Assert
    doNothing().when(cacheService).setValue(Mockito.anyString(), Mockito.any());

    assertDoesNotThrow(() -> service.reloadFareTypeConfigSet());
  }

  @Test
  void getDynamicPricingConfigSet() {
    DynamicPricingConfigSet expected = setupForGetDynamicPricingConfigSet();
    // Assert
    DynamicPricingConfigSet dynamicPricingConfigSet = service.getDynamicPricingConfigSet();
    assertEquals(
        expected
            .getBookingFee()
            .get(
                RedisKeyConstant.DYNAMIC_PRICING
                    + RedisKeyConstant.COLON
                    + RedisKeyConstant.FARE_TYPE
                    + RedisKeyConstant.COLON
                    + RedisKeyConstant.DYNP_BOOKING_FEE
                    + RedisKeyConstant.HOL)
            .get(0)
            .getFareType(),
        dynamicPricingConfigSet
            .getBookingFee()
            .get(
                RedisKeyConstant.DYNAMIC_PRICING
                    + RedisKeyConstant.COLON
                    + RedisKeyConstant.FARE_TYPE
                    + RedisKeyConstant.COLON
                    + RedisKeyConstant.DYNP_BOOKING_FEE
                    + RedisKeyConstant.HOL)
            .get(0)
            .getFareType());
  }

  private DynamicPricingConfigSet setupForGetDynamicPricingConfigSet() {
    // Mock booking config list
    Map<String, List<FareTypeConfig>> bookingFeeConfigMap = new HashMap<>();
    bookingFeeConfigMap.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_BOOKING_FEE
            + RedisKeyConstant.HOL,
        List.of(FareTypeConfig.builder().fareType(RedisKeyConstant.DYNP_BOOKING_FEE).build()));

    // Mock flag down config list
    Map<String, List<FareTypeConfig>> flagDownConfigMap = new HashMap<>();
    flagDownConfigMap.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_FLAG_DOWN_RATE
            + RedisKeyConstant.HOL,
        List.of(FareTypeConfig.builder().fareType(RedisKeyConstant.DYNP_FLAG_DOWN_RATE).build()));

    // Mock tier1 start dist config list
    Map<String, List<FareTypeConfig>> tier1StartDistConfigMap = new HashMap<>();
    tier1StartDistConfigMap.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_TIER_1_START_DIST
            + RedisKeyConstant.HOL,
        List.of(
            FareTypeConfig.builder().fareType(RedisKeyConstant.DYNP_TIER_1_START_DIST).build()));

    // Mock tier 1 end dist config list
    Map<String, List<FareTypeConfig>> tier1EndDistConfigMap = new HashMap<>();
    tier1EndDistConfigMap.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_TIER_1_END_DIST
            + RedisKeyConstant.HOL,
        List.of(FareTypeConfig.builder().fareType(RedisKeyConstant.DYNP_TIER_1_END_DIST).build()));

    // Mock tier 1 price multiple config list
    Map<String, List<FareTypeConfig>> tier1PriceMultiplierConfigMap = new HashMap<>();
    tier1PriceMultiplierConfigMap.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_TIER_1_PRICE_MULTIPLIER
            + RedisKeyConstant.HOL,
        List.of(
            FareTypeConfig.builder()
                .fareType(RedisKeyConstant.DYNP_TIER_1_PRICE_MULTIPLIER)
                .build()));

    // Mock tier 2 start dist config list
    Map<String, List<FareTypeConfig>> tier2StartDistConfigMap = new HashMap<>();
    tier2StartDistConfigMap.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_TIER_2_START_DIST
            + RedisKeyConstant.HOL,
        List.of(
            FareTypeConfig.builder().fareType(RedisKeyConstant.DYNP_TIER_2_START_DIST).build()));

    // Mock tier 2 end dist config list
    Map<String, List<FareTypeConfig>> tier2EndDistConfigMap = new HashMap<>();
    tier2EndDistConfigMap.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_TIER_2_END_DIST
            + RedisKeyConstant.HOL,
        List.of(FareTypeConfig.builder().fareType(RedisKeyConstant.DYNP_TIER_2_END_DIST).build()));

    // Mock tier 2 price multiple config list
    Map<String, List<FareTypeConfig>> tier2PriceMultiplierConfigMap = new HashMap<>();
    tier2PriceMultiplierConfigMap.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_TIER_2_PRICE_MULTIPLIER
            + RedisKeyConstant.HOL,
        List.of(
            FareTypeConfig.builder()
                .fareType(RedisKeyConstant.DYNP_TIER_2_PRICE_MULTIPLIER)
                .build()));

    // Mock duration rate config list
    Map<String, List<FareTypeConfig>> durationRateConfigMap = new HashMap<>();
    durationRateConfigMap.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_DURATION_RATE
            + RedisKeyConstant.HOL,
        List.of(FareTypeConfig.builder().fareType(RedisKeyConstant.DYNP_DURATION_RATE).build()));

    // Mock hourly surcharge config list
    Map<String, List<FareTypeConfig>> hourlySurchargeConfigMap = new HashMap<>();
    hourlySurchargeConfigMap.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_PEAK_MIDNIGHT_HOUR_RATE
            + RedisKeyConstant.HOL,
        List.of(
            FareTypeConfig.builder()
                .fareType(RedisKeyConstant.DYNP_PEAK_MIDNIGHT_HOUR_RATE)
                .build()));

    // Mock dynamic desurge buffer
    Map<String, List<FareTypeConfig>> dynpSurgeBufferMap = new HashMap<>();
    dynpSurgeBufferMap.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_SURGE_BUFFER
            + RedisKeyConstant.HOL,
        List.of(FareTypeConfig.builder().fareType(RedisKeyConstant.DYNP_SURGE_BUFFER).build()));

    // Mock dynamic desurge max cap
    Map<String, List<FareTypeConfig>> desurgeMaxCapMap = new HashMap<>();
    desurgeMaxCapMap.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_DESURGE_MAX_CAP
            + RedisKeyConstant.HOL,
        List.of(FareTypeConfig.builder().fareType(RedisKeyConstant.DYNP_DESURGE_MAX_CAP).build()));

    // Mock min surge amount
    Map<String, List<FareTypeConfig>> minSurgeAmountMap = new HashMap<>();
    minSurgeAmountMap.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_MIN_SURGE_AMOUNT
            + RedisKeyConstant.HOL,
        List.of(FareTypeConfig.builder().fareType(RedisKeyConstant.DYNP_MIN_SURGE_AMOUNT).build()));

    // Mock min cap
    Map<String, List<FareTypeConfig>> minCapMap = new HashMap<>();
    minCapMap.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_MIN_CAP
            + RedisKeyConstant.HOL,
        List.of(FareTypeConfig.builder().fareType(RedisKeyConstant.DYNP_MIN_CAP).build()));

    // Mock max cap
    Map<String, List<FareTypeConfig>> maxCapMap = new HashMap<>();
    maxCapMap.put(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.FARE_TYPE
            + RedisKeyConstant.COLON
            + RedisKeyConstant.DYNP_MAX_CAP
            + RedisKeyConstant.HOL,
        List.of(FareTypeConfig.builder().fareType(RedisKeyConstant.DYNP_MAX_CAP).build()));

    DynamicPricingConfigSet expected =
        DynamicPricingConfigSet.builder()
            .surgeBufferConfigList(dynpSurgeBufferMap)
            .desurgeMaxCapConfigList(desurgeMaxCapMap)
            .minSurgeAmountConfigList(minSurgeAmountMap)
            .minCapConfigList(minCapMap)
            .maxCapConfigList(maxCapMap)
            .flagDownConfigList(flagDownConfigMap)
            .tier1StartDestConfigList(tier1StartDistConfigMap)
            .tier1EndDestConfigList(tier1EndDistConfigMap)
            .tier1PriceMultiplierConfigList(tier1PriceMultiplierConfigMap)
            .tier2StartDestConfigList(tier2StartDistConfigMap)
            .tier2EndDestConfigList(tier2EndDistConfigMap)
            .tier2PriceMultiplierConfigList(tier2PriceMultiplierConfigMap)
            .durationRateConfigList(durationRateConfigMap)
            .hourlySurcharge(hourlySurchargeConfigMap)
            .bookingFee(bookingFeeConfigMap)
            .build();

    // Mock call service
    when(cacheService.getKeysByPattern(anyString()))
        .thenReturn(
            Set.of(
                RedisKeyConstant.DYNAMIC_PRICING
                    + RedisKeyConstant.COLON
                    + RedisKeyConstant.FARE_TYPE
                    + RedisKeyConstant.COLON
                    + RedisKeyConstant.DYNP_BOOKING_FEE
                    + RedisKeyConstant.HOL))
        .thenReturn(
            Set.of(
                RedisKeyConstant.DYNAMIC_PRICING
                    + RedisKeyConstant.COLON
                    + RedisKeyConstant.FARE_TYPE
                    + RedisKeyConstant.COLON
                    + RedisKeyConstant.DYNP_TIER_1_START_DIST
                    + RedisKeyConstant.HOL))
        .thenReturn(
            Set.of(
                RedisKeyConstant.DYNAMIC_PRICING
                    + RedisKeyConstant.COLON
                    + RedisKeyConstant.FARE_TYPE
                    + RedisKeyConstant.COLON
                    + RedisKeyConstant.DYNP_TIER_1_START_DIST
                    + RedisKeyConstant.HOL))
        .thenReturn(
            Set.of(
                RedisKeyConstant.DYNAMIC_PRICING
                    + RedisKeyConstant.COLON
                    + RedisKeyConstant.FARE_TYPE
                    + RedisKeyConstant.COLON
                    + RedisKeyConstant.DYNP_TIER_1_END_DIST
                    + RedisKeyConstant.HOL))
        .thenReturn(
            Set.of(
                RedisKeyConstant.DYNAMIC_PRICING
                    + RedisKeyConstant.COLON
                    + RedisKeyConstant.FARE_TYPE
                    + RedisKeyConstant.COLON
                    + RedisKeyConstant.DYNP_TIER_1_PRICE_MULTIPLIER
                    + RedisKeyConstant.HOL))
        .thenReturn(
            Set.of(
                RedisKeyConstant.DYNAMIC_PRICING
                    + RedisKeyConstant.COLON
                    + RedisKeyConstant.FARE_TYPE
                    + RedisKeyConstant.COLON
                    + RedisKeyConstant.DYNP_TIER_2_START_DIST
                    + RedisKeyConstant.HOL))
        .thenReturn(
            Set.of(
                RedisKeyConstant.DYNAMIC_PRICING
                    + RedisKeyConstant.COLON
                    + RedisKeyConstant.FARE_TYPE
                    + RedisKeyConstant.COLON
                    + RedisKeyConstant.DYNP_TIER_2_END_DIST
                    + RedisKeyConstant.HOL))
        .thenReturn(
            Set.of(
                RedisKeyConstant.DYNAMIC_PRICING
                    + RedisKeyConstant.COLON
                    + RedisKeyConstant.FARE_TYPE
                    + RedisKeyConstant.COLON
                    + RedisKeyConstant.DYNP_TIER_2_PRICE_MULTIPLIER
                    + RedisKeyConstant.HOL))
        .thenReturn(
            Set.of(
                RedisKeyConstant.DYNAMIC_PRICING
                    + RedisKeyConstant.COLON
                    + RedisKeyConstant.FARE_TYPE
                    + RedisKeyConstant.COLON
                    + RedisKeyConstant.DYNP_DURATION_RATE
                    + RedisKeyConstant.HOL))
        .thenReturn(
            Set.of(
                RedisKeyConstant.DYNAMIC_PRICING
                    + RedisKeyConstant.COLON
                    + RedisKeyConstant.FARE_TYPE
                    + RedisKeyConstant.COLON
                    + RedisKeyConstant.DYNP_PEAK_MIDNIGHT_HOUR_RATE
                    + RedisKeyConstant.HOL))
        .thenReturn(
            Set.of(
                RedisKeyConstant.DYNAMIC_PRICING
                    + RedisKeyConstant.COLON
                    + RedisKeyConstant.FARE_TYPE
                    + RedisKeyConstant.COLON
                    + RedisKeyConstant.DYNP_SURGE_BUFFER
                    + RedisKeyConstant.HOL))
        .thenReturn(
            Set.of(
                RedisKeyConstant.DYNAMIC_PRICING
                    + RedisKeyConstant.COLON
                    + RedisKeyConstant.FARE_TYPE
                    + RedisKeyConstant.COLON
                    + RedisKeyConstant.DYNP_DESURGE_MAX_CAP
                    + RedisKeyConstant.HOL))
        .thenReturn(
            Set.of(
                RedisKeyConstant.DYNAMIC_PRICING
                    + RedisKeyConstant.COLON
                    + RedisKeyConstant.FARE_TYPE
                    + RedisKeyConstant.COLON
                    + RedisKeyConstant.DYNP_MIN_SURGE_AMOUNT
                    + RedisKeyConstant.HOL))
        .thenReturn(
            Set.of(
                RedisKeyConstant.DYNAMIC_PRICING
                    + RedisKeyConstant.COLON
                    + RedisKeyConstant.FARE_TYPE
                    + RedisKeyConstant.COLON
                    + RedisKeyConstant.DYNP_MIN_CAP
                    + RedisKeyConstant.HOL))
        .thenReturn(
            Set.of(
                RedisKeyConstant.DYNAMIC_PRICING
                    + RedisKeyConstant.COLON
                    + RedisKeyConstant.FARE_TYPE
                    + RedisKeyConstant.COLON
                    + RedisKeyConstant.DYNP_MAX_CAP
                    + RedisKeyConstant.HOL));
    when(cacheService.getMultiValueList(Mockito.anySet(), Mockito.any()))
        .thenReturn(
            List.of(
                List.of(
                    FareTypeConfig.builder().fareType(RedisKeyConstant.DYNP_BOOKING_FEE).build())))
        .thenReturn(
            List.of(
                List.of(
                    FareTypeConfig.builder()
                        .fareType(RedisKeyConstant.DYNP_FLAG_DOWN_RATE)
                        .build())))
        .thenReturn(
            List.of(
                List.of(
                    FareTypeConfig.builder()
                        .fareType(RedisKeyConstant.DYNP_TIER_1_START_DIST)
                        .build())))
        .thenReturn(
            List.of(
                List.of(
                    FareTypeConfig.builder()
                        .fareType(RedisKeyConstant.DYNP_TIER_1_END_DIST)
                        .build())))
        .thenReturn(
            List.of(
                List.of(
                    FareTypeConfig.builder()
                        .fareType(RedisKeyConstant.DYNP_TIER_1_PRICE_MULTIPLIER)
                        .build())))
        .thenReturn(
            List.of(
                List.of(
                    FareTypeConfig.builder()
                        .fareType(RedisKeyConstant.DYNP_TIER_2_START_DIST)
                        .build())))
        .thenReturn(
            List.of(
                List.of(
                    FareTypeConfig.builder()
                        .fareType(RedisKeyConstant.DYNP_TIER_2_END_DIST)
                        .build())))
        .thenReturn(
            List.of(
                List.of(
                    FareTypeConfig.builder()
                        .fareType(RedisKeyConstant.DYNP_TIER_2_PRICE_MULTIPLIER)
                        .build())))
        .thenReturn(
            List.of(
                List.of(
                    FareTypeConfig.builder()
                        .fareType(RedisKeyConstant.DYNP_DURATION_RATE)
                        .build())))
        .thenReturn(
            List.of(
                List.of(
                    FareTypeConfig.builder()
                        .fareType(RedisKeyConstant.DYNP_PEAK_MIDNIGHT_HOUR_RATE)
                        .build())))
        .thenReturn(
            List.of(
                List.of(
                    FareTypeConfig.builder().fareType(RedisKeyConstant.DYNP_SURGE_BUFFER).build())))
        .thenReturn(
            List.of(
                List.of(
                    FareTypeConfig.builder()
                        .fareType(RedisKeyConstant.DYNP_DESURGE_MAX_CAP)
                        .build())))
        .thenReturn(
            List.of(
                List.of(
                    FareTypeConfig.builder()
                        .fareType(RedisKeyConstant.DYNP_MIN_SURGE_AMOUNT)
                        .build())))
        .thenReturn(
            List.of(
                List.of(FareTypeConfig.builder().fareType(RedisKeyConstant.DYNP_MIN_CAP).build())))
        .thenReturn(
            List.of(
                List.of(FareTypeConfig.builder().fareType(RedisKeyConstant.DYNP_MAX_CAP).build())));

    return expected;
  }
}

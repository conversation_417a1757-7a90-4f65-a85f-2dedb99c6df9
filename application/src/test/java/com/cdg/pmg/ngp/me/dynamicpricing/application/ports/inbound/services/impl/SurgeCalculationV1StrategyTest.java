package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import static org.junit.jupiter.api.Assertions.*;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.SurgeCalculationDto;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.SurgeCalculationStrategy;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.DemandSupplyConfigV2;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class SurgeCalculationV1StrategyTest {

  private final SurgeCalculationV1Strategy surgeCalculationV1Strategy =
      new SurgeCalculationV1Strategy();

  @Test
  void getVersion_shouldReturnV1() {
    assertEquals(SurgeCalculationStrategy.V1, surgeCalculationV1Strategy.getVersion());
  }

  @Test
  void
      givenExcessDemand30GreaterThanZeroAndSurgeGreaterThanZeroAndPositiveSurge_whenCalculate_doNotThrowException() {
    // given
    var dto =
        SurgeCalculationDto.builder()
            .currentSurge(10)
            .demandConfig(
                DemandSupplyConfigV2.builder()
                    .excessDemand30(10)
                    .surgeLow(-14)
                    .stepPositive(10)
                    .surgeHigh(50)
                    .build())
            .build();
    var expectedSurge = 20;
    // when
    var result = surgeCalculationV1Strategy.calculate(dto);
    // then
    assertEquals(
        expectedSurge,
        result,
        () -> "Surge expected equals " + expectedSurge + " but got " + result);
  }

  @Test
  void
      givenExcessDemand30GreaterThanZeroAndSurgeEqualsZeroAndPositiveSurge_whenCalculate_doNotThrowException() {
    // given
    var dto =
        SurgeCalculationDto.builder()
            .currentSurge(0)
            .demandConfig(
                DemandSupplyConfigV2.builder()
                    .excessDemand30(10)
                    .surgeLow(-14)
                    .stepPositive(10)
                    .surgeHigh(50)
                    .build())
            .build();
    var expectedSurge = 10;
    // when
    var result = surgeCalculationV1Strategy.calculate(dto);
    // then
    assertEquals(
        expectedSurge,
        result,
        () -> "Surge expected equals " + expectedSurge + " but got " + result);
  }

  @Test
  void
      givenExcessDemand30LessThanZeroAndSurgeEqualsZeroAndPositiveSurge_whenCalculate_doNotThrowException() {
    // given
    var dto =
        SurgeCalculationDto.builder()
            .currentSurge(0)
            .demandConfig(
                DemandSupplyConfigV2.builder()
                    .excessDemand30(-10)
                    .stepPositive(10)
                    .surgeLow(-14)
                    .surgeHigh(50)
                    .build())
            .build();
    var expectedSurge = -10;
    // when
    var result = surgeCalculationV1Strategy.calculate(dto);
    // then
    assertEquals(
        expectedSurge,
        result,
        () -> "Surge expected equals " + expectedSurge + " but got " + result);
  }

  @Test
  void
      givenExcessDemand30LessThanZeroAndSurgeGreaterThanZeroAndPositiveSurge_whenCalculate_doNotThrowException() {
    // given
    var dto =
        SurgeCalculationDto.builder()
            .currentSurge(1)
            .demandConfig(
                DemandSupplyConfigV2.builder()
                    .excessDemand30(-10)
                    .stepPositive(10)
                    .surgeLow(-14)
                    .surgeHigh(50)
                    .build())
            .build();
    var expectedSurge = -9;
    // when
    var result = surgeCalculationV1Strategy.calculate(dto);
    // then
    assertEquals(
        expectedSurge,
        result,
        () -> "Surge expected equals " + expectedSurge + " but got " + result);
  }

  @Test
  void
      givenExcessDemand30GreaterThanZeroAndSurgeLessThanZeroAndNegativeSurge_whenCalculate_doNotThrowException() {
    // given
    var dto =
        SurgeCalculationDto.builder()
            .currentSurge(-1)
            .demandConfig(
                DemandSupplyConfigV2.builder()
                    .excessDemand30(10)
                    .stepNegative(5)
                    .surgeLow(-14)
                    .surgeHigh(50)
                    .build())
            .build();
    // -1 + 5
    var expectedSurge = 4;
    // when
    var result = surgeCalculationV1Strategy.calculate(dto);
    // then
    assertEquals(
        expectedSurge,
        result,
        () -> "Surge expected equals " + expectedSurge + " but got " + result);
  }

  @Test
  void
      givenExcessDemand30LessThanZeroAndSurgeLessThanZeroAndNegativeSurge_whenCalculate_doNotThrowException() {
    // given
    var dto =
        SurgeCalculationDto.builder()
            .currentSurge(-1)
            .demandConfig(
                DemandSupplyConfigV2.builder()
                    .excessDemand30(-1)
                    .stepNegative(5)
                    .surgeLow(-14)
                    .surgeHigh(50)
                    .build())
            .build();
    var expectedSurge = -6;
    // when
    var result = surgeCalculationV1Strategy.calculate(dto);
    // then
    assertEquals(
        expectedSurge,
        result,
        () -> "Surge expected equals " + expectedSurge + " but got " + result);
  }

  @Test
  void
      givenExcessDemand30EqualsZeroAndSurgeEqualsZeroNegativeSurge_whenCalculate_doNotThrowException() {
    // given
    var dto =
        SurgeCalculationDto.builder()
            .currentSurge(0)
            .demandConfig(
                DemandSupplyConfigV2.builder()
                    .excessDemand30(0)
                    .stepNegative(5)
                    .surgeLow(-14)
                    .surgeHigh(50)
                    .build())
            .build();
    var expectedSurge = 0;
    // when
    var result = surgeCalculationV1Strategy.calculate(dto);
    // then
    assertEquals(
        expectedSurge,
        result,
        () -> "Surge expected equals " + expectedSurge + " but got " + result);
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.BookingFeeRequest;
import java.time.OffsetDateTime;
import org.junit.jupiter.api.Test;

/** This class contains unit tests for the BookingFareUtils class. */
class BookingFareUtilsTest {
  @Test
  void givenData_whenCreateBookingFareRequest_thenReturnExpected() {
    // Arrange
    String vehicleGroupId = "1";
    String fareType = "FlatFare";
    boolean isHoliday = false;
    OffsetDateTime requestDate = OffsetDateTime.now();
    String cdgProduct = "SomeProduct";
    String jobType = FlatfareConstants.JOB_TYPE_CJ;

    // Act
    BookingFeeRequest result =
        BookingFareUtils.createBookingFareRequest(
            vehicleGroupId, fareType, isHoliday, requestDate, cdgProduct, jobType);

    // Assert
    assertEquals(vehicleGroupId, result.getVehicleTypeId());
    assertEquals(fareType, result.getFlatFareType());
    assertEquals(isHoliday, result.getIsHoliday());
    assertEquals(requestDate, result.getRequestDate());
    assertEquals(cdgProduct, result.getProductId());
    assertEquals(jobType, result.getJobType());
  }
}

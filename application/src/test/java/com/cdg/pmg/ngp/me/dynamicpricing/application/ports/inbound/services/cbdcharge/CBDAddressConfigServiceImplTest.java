package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.cbdcharge;

import static org.junit.jupiter.api.Assertions.*;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.cbdcharge.CBDAddress;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.cbdcharge.UpdateCBDAddress;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.CBDAddressConfigRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.CacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.LocationSurchargeConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.BadRequestException;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.DomainException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CBDAddressConfigServiceImplTest {
  @InjectMocks private CBDAddressConfigServiceImpl cbdAddressService;

  @Mock private CBDAddressConfigRepository cbdAddressConfigRepository;
  @Mock private CacheService cacheService;

  @Test
  void givenEmptyRequest_whenUpdateCBDAddress_thenThrowsException() {
    UpdateCBDAddress request = new UpdateCBDAddress();
    assertThrows(BadRequestException.class, () -> cbdAddressService.updateCBDAddress(request));
  }

  @Test
  void given2InvalidList_whenUpdateCBDAddress_thenDoNothing() {
    UpdateCBDAddress request = initInvalidCBDAddress();
    cbdAddressService.updateCBDAddress(request);
    Mockito.verify(cbdAddressConfigRepository, Mockito.times(0)).insertCBDAddress(Mockito.any());
    Mockito.verify(cbdAddressConfigRepository, Mockito.times(0)).deleteCBDAddress(Mockito.any());
  }

  @Test
  void givenNormalCondition_whenUpdateCBDAddress_thenSuccess() {
    UpdateCBDAddress request = initCBDAddress();
    cbdAddressService.updateCBDAddress(request);
    Mockito.verify(cbdAddressConfigRepository, Mockito.times(1)).insertCBDAddress(Mockito.any());
    Mockito.verify(cbdAddressConfigRepository, Mockito.times(1)).insertCBDAddress(Mockito.any());
  }

  @Test
  void givenExceptionHappened_whenUpdateCBDAddress_thenSuccess() {
    UpdateCBDAddress request = initCBDAddress();
    Mockito.doThrow(new DomainException("Exception", 1L))
        .when(cbdAddressConfigRepository)
        .insertCBDAddress(Mockito.any());
    Mockito.doThrow(new DomainException("Exception", 1L))
        .when(cbdAddressConfigRepository)
        .deleteCBDAddress(Mockito.any());
    cbdAddressService.updateCBDAddress(request);
    Mockito.verify(cbdAddressConfigRepository, Mockito.times(1)).insertCBDAddress(Mockito.any());
    Mockito.verify(cbdAddressConfigRepository, Mockito.times(1)).insertCBDAddress(Mockito.any());
  }

  UpdateCBDAddress initCBDAddress() {
    List<CBDAddress> cbdAddressList =
        List.of(
            new CBDAddress("1", true, true),
            new CBDAddress("2", true, true),
            new CBDAddress("3", true, true),
            new CBDAddress("4", false, true),
            new CBDAddress("4", false, true));
    return UpdateCBDAddress.builder().cbdAddressList(cbdAddressList).build();
  }

  UpdateCBDAddress initInvalidCBDAddress() {
    List<CBDAddress> cbdAddressList =
        List.of(
            new CBDAddress("a", true, true),
            new CBDAddress("2b", true, true),
            new CBDAddress("c", true, true),
            new CBDAddress("d3", false, true),
            new CBDAddress("ee", false, true));
    return UpdateCBDAddress.builder().cbdAddressList(cbdAddressList).build();
  }

  @Test
  void givenEmpty_whenAddCBDConfigCache_thenReturn() {
    assertDoesNotThrow(() -> cbdAddressService.addCBDConfigCache(null));
  }

  @Test
  void givenRepoException_whenAddCBDConfigCache_thenReturn() {
    List<String> cbdAddressAdded = List.of("999999", "999998");
    Mockito.when(cbdAddressConfigRepository.getLocConfigByAddressRefList(Mockito.any()))
        .thenThrow(new DomainException("Exception", 1L));
    assertDoesNotThrow(() -> cbdAddressService.addCBDConfigCache(cbdAddressAdded));
  }

  @Test
  void givenNormalCondition_whenAddCBDConfigCache_thenReturnSuccess() {
    List<String> cbdAddressAdded = List.of("999999", "999998");
    Mockito.when(cbdAddressConfigRepository.getLocConfigByAddressRefList(Mockito.any()))
        .thenReturn(getLocationSurchargeConfigEntityList());
    Mockito.when(cacheService.getListValue(Mockito.anyString(), Mockito.any()))
        .thenReturn(new ArrayList<>());
    Mockito.doNothing().when(cacheService).deleteByKey(Mockito.any());
    Mockito.doNothing().when(cacheService).setListValue(Mockito.anyString(), Mockito.any());
    assertDoesNotThrow(() -> cbdAddressService.addCBDConfigCache(cbdAddressAdded));
  }

  @Test
  void givenEmpty_whenRemoveCBDConfigCache_thenReturn() {
    assertDoesNotThrow(() -> cbdAddressService.removeCBDConfigCache(null));
  }

  @Test
  void givenNormalCondition_whenRemoveCBDConfigCache_thenReturnSuccess() {
    List<String> cbdAddressAdded = List.of("999999", "999998");
    Set<String> setKey = cbdAddressAdded.stream().collect(Collectors.toSet());
    Mockito.when(cacheService.getKeysByPattern(Mockito.anyString())).thenReturn(setKey);
    Mockito.doNothing().when(cacheService).deleteByKey(Mockito.any());
    assertDoesNotThrow(() -> cbdAddressService.removeCBDConfigCache(cbdAddressAdded));
  }

  private List<LocationSurchargeConfigEntity> getLocationSurchargeConfigEntityList() {
    LocationSurchargeConfigEntity locationSurchargeConfigEntity =
        new LocationSurchargeConfigEntity();
    locationSurchargeConfigEntity.setLocationId(1);
    locationSurchargeConfigEntity.setLocationName("air port");
    locationSurchargeConfigEntity.setSurchargeValue(4.0);
    locationSurchargeConfigEntity.setChargeBy("PICKUP");
    locationSurchargeConfigEntity.setAddressRef("5394324");
    locationSurchargeConfigEntity.setDayIndicator("MON");
    locationSurchargeConfigEntity.setFareType("FLAT");
    locationSurchargeConfigEntity.setProductId("STD");
    locationSurchargeConfigEntity.setStartTime("00:00:00");
    locationSurchargeConfigEntity.setEndTime("23:59:59");

    LocationSurchargeConfigEntity locationSurchargeConfigEntity2 =
        new LocationSurchargeConfigEntity();
    locationSurchargeConfigEntity2.setLocationId(5);
    locationSurchargeConfigEntity2.setLocationName("air port");
    locationSurchargeConfigEntity2.setSurchargeValue(4.0);
    locationSurchargeConfigEntity2.setChargeBy("PICKUP");
    locationSurchargeConfigEntity2.setAddressRef("5394324");
    locationSurchargeConfigEntity2.setDayIndicator("MON");
    locationSurchargeConfigEntity2.setFareType("FLAT");
    locationSurchargeConfigEntity2.setProductId("STD");
    locationSurchargeConfigEntity2.setStartTime("00:00:00");
    locationSurchargeConfigEntity2.setEndTime("23:59:59");

    return List.of(locationSurchargeConfigEntity, locationSurchargeConfigEntity2);
  }
}

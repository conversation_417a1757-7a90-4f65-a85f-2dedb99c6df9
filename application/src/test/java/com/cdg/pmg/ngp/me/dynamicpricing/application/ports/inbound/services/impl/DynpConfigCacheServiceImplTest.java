package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareHoliday;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.DynpConfigCacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.CacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.InternalServerException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.function.Executable;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class DynpConfigCacheServiceImplTest {

  @Mock private DynpConfigCacheService dynpConfigCacheService;

  @Mock private CacheService cacheService;

  @Mock private ObjectMapper objectMapper;

  @BeforeEach
  void setUp() {
    dynpConfigCacheService = new DynpConfigCacheServiceImpl(cacheService, objectMapper);
  }

  @Test
  void givenNormalParam_whenGetMapOfListObject_thenReturnSuccess() throws ParseException {
    // GIVEN
    Set<String> keyResultSet = new TreeSet<>();
    keyResultSet.add("key_1");
    keyResultSet.add("key_2");

    // WHEN
    when(cacheService.getKeysByPattern(Mockito.anyString())).thenReturn(keyResultSet);
    when(cacheService.getListValue(Mockito.anyString(), any()))
        .thenReturn(List.of("value1_0", "value1_1"))
        .thenReturn(List.of("value2_0", "value2_1"));

    // THEN
    Map<String, List<String>> mapOfListObject =
        dynpConfigCacheService.getMapOfListObject("any", String.class);

    String actual = mapOfListObject.get("key_2").get(0);
    String expected = "value2_0";

    assertEquals(expected, actual);
  }

  @Test
  void givenDateParam_whenIsHoliday_thenReturnSuccess() throws ParseException {
    // GIVEN
    String startDateString = "09/20/2023";
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy");
    Date reqDate = df.parse(startDateString);

    // WHEN
    when(cacheService.getKeysByPattern(Mockito.anyString()))
        .thenReturn(Set.of("COMPANY_HOLIDAY_KEY"));

    when(cacheService.getListValue(Mockito.anyString(), any()))
        .thenReturn(List.of(FlatFareHoliday.builder().date("2023-09-20 00:00:00").build()));

    // THEN
    boolean isHoliday = dynpConfigCacheService.isHoliday(reqDate);
    assertEquals(Boolean.TRUE, isHoliday);
  }

  @Test
  void givenDateParam_whenIsHoliday_thenThrowsException() throws ParseException {
    // GIVEN
    String startDateString = "09/20/2023";
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy");
    Date reqDate = df.parse(startDateString);

    // WHEN
    when(cacheService.getKeysByPattern(Mockito.anyString()))
        .thenReturn(Set.of("COMPANY_HOLIDAY_KEY"));
    when(cacheService.getListValue(Mockito.anyString(), any()))
        .thenReturn(List.of(FlatFareHoliday.builder().date("***********").build()));

    // THEN
    assertThrows(InternalServerException.class, () -> dynpConfigCacheService.isHoliday(reqDate));
  }

  @Test
  void givenNormalParam_whenGetObjectValueByPattern_thenReturnSuccess() {
    // WHEN
    when(cacheService.getKeysByPattern(Mockito.anyString()))
        .thenReturn(Set.of("COMPANY_HOLIDAY_KEY"));
    when(cacheService.getValue(Mockito.anyString(), any()))
        .thenReturn(FlatFareHoliday.builder().date("***********").build());

    // THEN
    FlatFareHoliday actual =
        dynpConfigCacheService.getObjectValueByPattern("keyString", FlatFareHoliday.class);
    assertEquals("***********", actual.getDate());
  }

  @Test
  void givenValidParamWithDataIsJSON_whenGetMultiMapConfigsAndSingleValueFromCache_thenReturnMap()
      throws JsonProcessingException {
    // GIVEN
    String data = "{\"key1\":\"\"value1\"}";
    Map<String, String> mockedMap = new HashMap<>();
    mockedMap.put("key1", "value1");
    String key1 = "key1";
    // WHEN
    when(cacheService.getKeysByPattern(any())).thenReturn(Set.of(key1));
    when(cacheService.getStringValue(key1)).thenReturn(data);
    when(objectMapper.readValue(data, Map.class)).thenReturn(mockedMap);
    // THEN
    Map<String, String> result =
        dynpConfigCacheService.getMultiMapConfigsAndSingleValueFromCache(key1);
    assertNotNull(result);
    assertEquals(1, result.size());
    assertTrue(result.containsValue(mockedMap.get("key1")));
    Map<String, String> expectedFallbackMap = new HashMap<>();
    expectedFallbackMap.put("key1", "value1");
    assertTrue(result.containsValue(expectedFallbackMap.get("key1")));
  }

  @Test
  void
      givenValidParamWithDataIsNotJSON_whenGetMultiMapConfigsAndSingleValueFromCache_thenReturnMap() {
    // GIVEN
    String data = "\"value1\"";
    Map<String, String> mockedMap = new HashMap<>();
    mockedMap.put("key1", "value1");
    String key1 = "key1";
    // WHEN
    when(cacheService.getKeysByPattern(any())).thenReturn(Set.of(key1));
    when(cacheService.getStringValue(key1)).thenReturn(data);
    // THEN
    Map<String, String> result =
        dynpConfigCacheService.getMultiMapConfigsAndSingleValueFromCache(key1);
    assertNotNull(result);
    assertEquals(1, result.size());
    assertTrue(result.containsValue(mockedMap.get("key1")));
    Map<String, String> expectedFallbackMap = new HashMap<>();
    expectedFallbackMap.put("key1", "value1");
    assertTrue(result.containsValue(expectedFallbackMap.get("key1")));
  }

  @Test
  void givenNullDataByKey_whenGetMultiMapConfigsAndSingleValueFromCache_thenReturnEmptyMap() {
    // GIVEN
    String key1 = "key1";
    // WHEN
    when(cacheService.getKeysByPattern(any())).thenReturn(Set.of(key1));
    when(cacheService.getStringValue(key1)).thenReturn(null);
    // THEN
    Map<String, String> result =
        dynpConfigCacheService.getMultiMapConfigsAndSingleValueFromCache(key1);
    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  @Test
  void givenInvalidJSONData_whenGetMultiMapConfigsAndSingleValueFromCache_thenThrowException()
      throws JsonProcessingException {
    // Arrange
    String keyPattern = "someKey";
    String keyCache = "someKeyCache";
    String invalidJson = "{invalidJson}";

    when(cacheService.getKeysByPattern(keyPattern)).thenReturn(Collections.singleton(keyCache));
    when(cacheService.getStringValue(keyCache)).thenReturn(invalidJson);
    when(objectMapper.readValue(invalidJson, Map.class))
        .thenThrow(new JsonProcessingException("Test Exception") {});

    // Act and Assert
    Executable methodCall =
        () -> dynpConfigCacheService.getMultiMapConfigsAndSingleValueFromCache(keyPattern);
    InternalServerException thrown = assertThrows(InternalServerException.class, methodCall);

    assertEquals(
        "Errors occurred while parsing JSON data for the following keys:\n" + keyCache + "\n",
        thrown.getMessage());
    verify(cacheService).getKeysByPattern(keyPattern);
    verify(cacheService).getStringValue(keyCache);
    verify(objectMapper).readValue(invalidJson, Map.class);
  }

  @Test
  void givenInvalidJSONData_whenHandleParsingErrors_thenThrowException() {
    // Arrange
    List<String> keyErrorList = Arrays.asList("key1", "key2");
    String expectedMessage =
        "Errors occurred while parsing JSON data for the following keys:\nkey1\nkey2\n";
    Method method;
    try {
      // Prepare the reflection setup outside the try block
      method =
          DynpConfigCacheServiceImpl.class.getDeclaredMethod("handleParsingErrors", List.class);
      method.setAccessible(true);
    } catch (NoSuchMethodException e) {
      fail("Method handleParsingErrors not found: " + e);
      return; // This return is necessary to prevent compilation error.
    }
    try {
      // Act
      method.invoke(dynpConfigCacheService, keyErrorList);
      fail("Expected an InternalServerException to be thrown");
    } catch (InvocationTargetException e) {
      // Assert
      Throwable cause = e.getCause();
      assertTrue(cause instanceof InternalServerException);
      assertEquals(expectedMessage, cause.getMessage());
    } catch (IllegalAccessException e) {
      fail("Illegal access to method: " + e);
    }
  }
}

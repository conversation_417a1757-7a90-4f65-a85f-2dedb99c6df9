package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.SurgeCalculationDto;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.SurgeCalculationStrategy;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.DemandSupplyConfigV2;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.NewPricingModelConfigEntity;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class SurgeCalculationV2StrategyTest {

  @Spy private SurgeCalculationV2Strategy surgeCalculationV2Strategy;

  @Test
  void getVersion_shouldReturnV2() {
    assertEquals(SurgeCalculationStrategy.V2, surgeCalculationV2Strategy.getVersion());
  }

  @Test
  void testDeSurge_shouldDecreaseSurgeValue() {
    SurgeCalculationDto surgeCalculationDto =
        SurgeCalculationDto.builder()
            .currentSurge(55)
            .demandConfig(
                DemandSupplyConfigV2.builder()
                    .surgeLow(-23)
                    .surgeHigh(50)
                    .stepNegative(5)
                    .stepPositive(5)
                    .demand15(0)
                    .unmet15(0)
                    .supply(0)
                    .build())
            .newPricingModelConfigEntity(
                NewPricingModelConfigEntity.builder()
                    .zoneId("18")
                    .additionalSurgeHigh(100)
                    .surgeHighTierRate(0.6)
                    .unmetRate1(0.3)
                    .unmetRate2(0.68)
                    .negativeDemandSupplyDownRate(0.9)
                    .build())
            .build();

    // when
    int finalSurge = 0;
    for (int i = 0; i < 100; i++) {
      finalSurge = surgeCalculationV2Strategy.calculate(surgeCalculationDto);
      surgeCalculationDto.setCurrentSurge(finalSurge);
    }

    // then
    assertEquals(-23, finalSurge);
  }

  @Test
  void calculate_whenUnmet15GreaterThanConfig_doIncreaseSurgeWithTheUpperBoundSurgeHighNew() {
    // given
    SurgeCalculationDto surgeCalculationDto =
        SurgeCalculationDto.builder()
            .currentSurge(10)
            .demandConfig(
                DemandSupplyConfigV2.builder()
                    .surgeLow(5)
                    .demand15(100)
                    .surgeHigh(20)
                    .stepNegative(1)
                    .stepPositive(2)
                    .unmet15(20.0)
                    .build())
            .newPricingModelConfigEntity(
                NewPricingModelConfigEntity.builder()
                    .surgeHighTierRate(0.5)
                    .unmetRate1(0.15)
                    .unmetRate2(0.2)
                    .build())
            .build();

    // when
    int surge = surgeCalculationV2Strategy.calculate(surgeCalculationDto);
    // then
    assertEquals(12, surge);
  }

  @Test
  void testSurge_whenExcessDemand15GreaterThanZeroAndSurgeLessThanZero_shouldSuccess() {
    // given
    SurgeCalculationDto surgeCalculationDto =
        SurgeCalculationDto.builder()
            .currentSurge(-5)
            .demandConfig(
                DemandSupplyConfigV2.builder()
                    .surgeLow(-23)
                    .excessDemand15(10)
                    .demand15(100)
                    .surgeHigh(20)
                    .stepNegative(1)
                    .stepPositive(2)
                    .unmet15(20.0)
                    .build())
            .newPricingModelConfigEntity(
                NewPricingModelConfigEntity.builder()
                    .surgeHighTierRate(0.5)
                    .unmetRate1(0.15)
                    .unmetRate2(0.2)
                    .build())
            .build();
    // when
    int surge = surgeCalculationV2Strategy.calculate(surgeCalculationDto);
    // then
    assertEquals(-4, surge);
  }

  @Test
  void calculate_whenUnmet15NotEqualsZeroAndPreviousUnmet15EqualsZero_doNotThrowException() {
    // given
    var currentSurge = -14;
    SurgeCalculationDto surgeCalculationDto =
        SurgeCalculationDto.builder()
            .currentSurge(currentSurge)
            .demandConfig(
                DemandSupplyConfigV2.builder()
                    .surgeLow(5)
                    .demand15(100)
                    .surgeHigh(20)
                    .stepNegative(1)
                    .stepPositive(2)
                    .build())
            .newPricingModelConfigEntity(NewPricingModelConfigEntity.builder().build())
            .build();

    var expectedSurge = 5;

    // when
    int result = surgeCalculationV2Strategy.calculate(surgeCalculationDto);
    // then
    assertEquals(
        expectedSurge,
        result,
        () -> "Expected surge = " + expectedSurge + " but got surge = " + result);
    verify(surgeCalculationV2Strategy, times(1))
        .clamp(
            anyInt(),
            eq(surgeCalculationDto.getSurgeLow()),
            eq(surgeCalculationDto.getSurgeHighNew()));
  }

  @Test
  void
      calculate_whenDemand15GreaterThanUnmetRate2AndSurgeGreaterThanSurgeHighRate_doNotThrowException() {
    // given
    int currentSurge = 14;
    SurgeCalculationDto surgeCalculationDto =
        SurgeCalculationDto.builder()
            .currentSurge(currentSurge)
            .demandConfig(
                DemandSupplyConfigV2.builder()
                    .surgeLow(5)
                    .demand15(1)
                    .unmet15(1.3)
                    .surgeHigh(1)
                    .stepNegative(1)
                    .stepPositive(2)
                    .build())
            .newPricingModelConfigEntity(
                NewPricingModelConfigEntity.builder()
                    .unmetRate2(1.5)
                    .surgeHighTierRate(0.0)
                    .build())
            .build();

    int expectedSurge = 5;

    // when
    int result = surgeCalculationV2Strategy.calculate(surgeCalculationDto);

    // then
    assertEquals(
        expectedSurge,
        result,
        () -> "Expected surge = " + expectedSurge + " but got surge = " + result);
    verify(surgeCalculationV2Strategy, times(1))
        .clamp(
            anyInt(),
            eq(surgeCalculationDto.getSurgeLow()),
            eq(surgeCalculationDto.getSurgeHighNew()));
  }

  @Test
  void
      calculate_whenCurrentSurgeIsLessThanZeroAndUnmetRateLessThanNegativeDemandSupplyDownRate_doNotThrowException() {
    // given
    int currentSurge = -14;
    SurgeCalculationDto surgeCalculationDto =
        SurgeCalculationDto.builder()
            .currentSurge(currentSurge)
            .demandConfig(
                DemandSupplyConfigV2.builder()
                    .surgeLow(5)
                    .demand15(0)
                    .unmet15(1.3)
                    .surgeHigh(1)
                    .stepNegative(1)
                    .stepPositive(2)
                    .build())
            .newPricingModelConfigEntity(
                NewPricingModelConfigEntity.builder()
                    .unmetRate2(1.5)
                    .surgeHighTierRate(0.0)
                    .negativeDemandSupplyDownRate(1.5)
                    .build())
            .build();

    int expectedSurge = 5;

    // when
    int result = surgeCalculationV2Strategy.calculate(surgeCalculationDto);

    // then
    assertEquals(
        expectedSurge,
        result,
        () -> "Expected surge = " + expectedSurge + " but got surge = " + result);
    verify(surgeCalculationV2Strategy, times(1))
        .clamp(
            anyInt(),
            eq(surgeCalculationDto.getSurgeLow()),
            eq(surgeCalculationDto.getSurgeHighNew()));
  }

  @Test
  void calculate_whenDefault_doNotThrowException() {
    // given
    int currentSurge = -14;
    SurgeCalculationDto surgeCalculationDto =
        SurgeCalculationDto.builder()
            .currentSurge(currentSurge)
            .demandConfig(
                DemandSupplyConfigV2.builder()
                    .surgeLow(5)
                    .demand15(0)
                    .unmet15(1.3)
                    .surgeHigh(1)
                    .stepNegative(1)
                    .stepPositive(2)
                    .build())
            .newPricingModelConfigEntity(
                NewPricingModelConfigEntity.builder()
                    .unmetRate2(1.5)
                    .surgeHighTierRate(0.0)
                    .negativeDemandSupplyDownRate(-1.5)
                    .build())
            .build();

    int expectedSurge = 5;

    // when
    int result = surgeCalculationV2Strategy.calculate(surgeCalculationDto);

    // then
    assertEquals(
        expectedSurge,
        result,
        () -> "Expected surge = " + expectedSurge + " but got surge = " + result);
    verify(surgeCalculationV2Strategy, times(1))
        .clamp(
            anyInt(),
            eq(surgeCalculationDto.getSurgeLow()),
            eq(surgeCalculationDto.getSurgeHighNew()));
  }
}

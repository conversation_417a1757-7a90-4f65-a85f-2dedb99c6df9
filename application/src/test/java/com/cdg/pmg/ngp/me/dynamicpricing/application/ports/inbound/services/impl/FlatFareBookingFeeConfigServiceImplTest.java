package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.DynpConfigCacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.FlatFareBookingFeeConfigService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.FlatFareBookingFeeRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FlatFareBookingFeeConfig;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class FlatFareBookingFeeConfigServiceImplTest {
  @Mock private FlatFareBookingFeeConfigService flatFareBookingFeeConfigService;

  @Mock private FlatFareBookingFeeRepository flatFareBookingFeeRepository;

  @Mock private DynpConfigCacheService dynpConfigCacheService;

  @BeforeEach
  void setUp() {
    flatFareBookingFeeConfigService =
        new FlatFareBookingFeeConfigServiceImpl(
            flatFareBookingFeeRepository, dynpConfigCacheService);
  }

  @Test
  void loadFlatFareBookingFeeConfig_isEmptyConfigs() {
    Mockito.when(flatFareBookingFeeRepository.getFlatFareBookingFeeConfigs())
        .then(config -> new ArrayList<>());
    flatFareBookingFeeConfigService.loadFlatFareBookingFeeConfig();
    Mockito.verify(dynpConfigCacheService, Mockito.times(0))
        .addListConfigsToCache(Mockito.anyString(), Mockito.anyList());
  }

  @Test
  void loadFlatFareBookingFeeConfig_isNotEmptyConfigs() {
    final var configs = List.of(new FlatFareBookingFeeConfig());
    Mockito.when(flatFareBookingFeeRepository.getFlatFareBookingFeeConfigs())
        .then(config -> configs);

    flatFareBookingFeeConfigService.loadFlatFareBookingFeeConfig();
    Mockito.verify(dynpConfigCacheService, Mockito.times(1))
        .addListConfigsToCache(Mockito.anyString(), Mockito.anyList());
  }
}

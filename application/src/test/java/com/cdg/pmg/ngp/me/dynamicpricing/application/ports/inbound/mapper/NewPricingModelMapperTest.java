package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.mapper;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.commands.NewPricingModelCommand;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.CMSConfigItem;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.CMSConfigList;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.NewPricingModelConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.DomainException;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.time.OffsetDateTime;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

class NewPricingModelMapperTest {

  private final NewPricingModelMapper mapper = Mappers.getMapper(NewPricingModelMapper.class);
  private final ObjectMapper objectMapper = new ObjectMapper();

  @Test
  void
      givenNewPricingModelCommand_whenMapToNewPricingModelConfigEntity_thenReturnNewPricingModelConfigEntity() {
    // Arrange
    var command = new NewPricingModelCommand();
    command.setIndex(2);
    command.setK1(1.0);
    command.setK2(2.0);
    command.setK3(3.0);
    command.setK4(4.0);
    command.setK5(5.0);
    command.setK6(6.0);
    command.setK7(7.0);
    command.setK8(8.0);
    command.setZoneId("01");
    command.setZonePriceVersion("V1");
    command.setStartDt(OffsetDateTime.now());
    command.setEndDt(OffsetDateTime.now());
    command.setUnmetRate1(0.0);
    command.setUnmetRate2(0.1);
    command.setAdditionalSurgeHigh(1);
    command.setCreatedBy("System");
    command.setUpdatedBy("System");
    command.setSurgeHighTierRate(1.9);
    command.setNegativeDemandSupplyDownRate(1.0);
    NewPricingModelConfigEntity newPricingModelConfigEntity =
        mapper.mapToNewPricingModelConfigEntity(command);
    Assertions.assertNotNull(newPricingModelConfigEntity);
    Assertions.assertEquals(2, newPricingModelConfigEntity.getIndex());
    Assertions.assertEquals(1.0, newPricingModelConfigEntity.getK1());
    Assertions.assertEquals(2.0, newPricingModelConfigEntity.getK2());
    Assertions.assertEquals(3.0, newPricingModelConfigEntity.getK3());
    Assertions.assertEquals(4.0, newPricingModelConfigEntity.getK4());
    Assertions.assertEquals(5.0, newPricingModelConfigEntity.getK5());
    Assertions.assertEquals(6.0, newPricingModelConfigEntity.getK6());
    Assertions.assertEquals(7.0, newPricingModelConfigEntity.getK7());
    Assertions.assertEquals(8.0, newPricingModelConfigEntity.getK8());
    Assertions.assertEquals("01", newPricingModelConfigEntity.getZoneId());
    Assertions.assertEquals("V1", newPricingModelConfigEntity.getZonePriceVersion());
    Assertions.assertNotNull(newPricingModelConfigEntity.getStartDt());
    Assertions.assertNotNull(newPricingModelConfigEntity.getEndDt());
    Assertions.assertEquals(0.0, newPricingModelConfigEntity.getUnmetRate1());
    Assertions.assertEquals(0.1, newPricingModelConfigEntity.getUnmetRate2());
    Assertions.assertEquals(1, newPricingModelConfigEntity.getAdditionalSurgeHigh());
    Assertions.assertEquals("System", newPricingModelConfigEntity.getCreatedBy());
    Assertions.assertEquals("System", newPricingModelConfigEntity.getUpdatedBy());
    Assertions.assertEquals(1.9, newPricingModelConfigEntity.getSurgeHighTierRate());
    Assertions.assertEquals(1.0, newPricingModelConfigEntity.getNegativeDemandSupplyDownRate());
  }

  @Test
  void
      givenJson_whenMapToNewPricingModelConfigEntities_thenReturnListOfNewPricingModelConfigEntity() {
    List<NewPricingModelConfigEntity> entities =
        mapper.mapToNewPricingModelConfigEntities(
            objectMapper,
            CMSConfigList.builder()
                .data(
                    List.of(
                        CMSConfigItem.builder()
                            .id(559L)
                            .key("Test")
                            .value(
                                "{\"id\":559,\"index\":1,\"zoneId\":\"03\",\"additionalSurgeHigh\":7,\"surgeHighTierRate\":3.0,\"unmetRate1\":1.0,\"unmetRate2\":88.0,\"negativeDemandSupplyDownRate\":21.0,\"createdBy\":\"SYSTEM\",\"updatedBy\":\"SYSTEM\" ,\"k1\":13.0,\"k2\":14.0,\"k3\":15.0,\"k4\":16.0,\"k5\":17.0,\"k6\":18.0,\"k7\":19.0,\"k8\":20.0,\"zonePriceVersion\":\"V3\"}")
                            .build()))
                .build());
    Assertions.assertEquals(1, entities.size());

    NewPricingModelConfigEntity entity = entities.get(0);
    Assertions.assertEquals(559L, entity.getId());
    Assertions.assertEquals(1, entity.getIndex());
    Assertions.assertEquals("03", entity.getZoneId());
    Assertions.assertEquals(7, entity.getAdditionalSurgeHigh());
    Assertions.assertEquals(3.0, entity.getSurgeHighTierRate());
    Assertions.assertEquals(1.0, entity.getUnmetRate1());
    Assertions.assertEquals(88.0, entity.getUnmetRate2());
    Assertions.assertEquals(21.0, entity.getNegativeDemandSupplyDownRate());
    Assertions.assertEquals("SYSTEM", entity.getCreatedBy());
    Assertions.assertEquals("SYSTEM", entity.getUpdatedBy());
    Assertions.assertEquals(13.0, entity.getK1());
    Assertions.assertEquals(14.0, entity.getK2());
    Assertions.assertEquals(15.0, entity.getK3());
    Assertions.assertEquals(16.0, entity.getK4());
    Assertions.assertEquals(17.0, entity.getK5());
    Assertions.assertEquals(18.0, entity.getK6());
    Assertions.assertEquals(19.0, entity.getK7());
    Assertions.assertEquals(20.0, entity.getK8());
    Assertions.assertEquals("V3", entity.getZonePriceVersion());
  }

  @Test
  void givenMalformedJson_whenParse_thenReturnNull() {
    var json = """
            {
              "index": 20
            """;
    NewPricingModelConfigEntity entity = mapper.parse(objectMapper, json);
    Assertions.assertNull(entity);
  }

  @Test
  void givenNewPricingModelEntity_whenToJson_thenReturnJsonString() {
    NewPricingModelConfigEntity newPricingModelConfigEntity =
        NewPricingModelConfigEntity.builder()
            .id(1L)
            .index(1)
            .negativeDemandSupplyDownRate(2.0)
            .build();

    String json = mapper.toJson(objectMapper, newPricingModelConfigEntity);
    // Assert the JSON string
    Assertions.assertNotNull(json);
    Assertions.assertTrue(json.contains("\"id\":1"));
    Assertions.assertTrue(json.contains("\"index\":1"));
    Assertions.assertTrue(json.contains("\"negativeDemandSupplyDownRate\":2.0"));
  }

  @Test
  void
      givenNewPricingModelEntity_whenNotRegisterJavaTimeModuleAndRunToJson_thenThrowsDomainException() {
    NewPricingModelConfigEntity newPricingModelConfigEntity =
        NewPricingModelConfigEntity.builder()
            .id(1L)
            .index(1)
            .negativeDemandSupplyDownRate(2.0)
            .updatedDt(OffsetDateTime.now())
            .build();
    Assertions.assertThrows(
        DomainException.class, () -> mapper.toJson(objectMapper, newPricingModelConfigEntity));
  }

  @Test
  void givenNonNullObject_whenRunNonNull_thenReturnTrue() {
    Assertions.assertTrue(mapper.nonNull(new Object()));
  }

  @Test
  void givenNullObject_whenRunNonNull_thenReturnFalse() {
    Assertions.assertFalse(mapper.nonNull(null));
  }
}

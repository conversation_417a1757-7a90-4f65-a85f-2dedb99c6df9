package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import static com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.DateUtils.DDMMYYYY_FORMAT;
import static org.junit.jupiter.api.Assertions.*;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.BookARideFareCalculationUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.BookARideConfigsConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.BookingFareConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.DynamicPricingTimeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FareUploadConfiguration;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.HourRateConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.VGProductFareBean;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.ProductTypeEnum;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/** Unit tests for {@link FareCalculationServiceImpl} */
@ExtendWith(MockitoExtension.class)
class FareCalculationServiceImplTest {

  @Mock private FareCalculationServiceImpl service;
  private final FareUploadConfiguration fareUploadConfiguration =
      FareUploadConfiguration.builder().build();
  private final Map<String, List<String>> productList = new HashMap<>();

  @BeforeEach
  void setUp() {
    final Date currentDate = new Date();
    service = new FareCalculationServiceImpl();
    LocalTime startTime = LocalTime.of(00, 00, 00);
    LocalTime endTime = LocalTime.of(23, 59, 59);
    HourRateConfig hourRateConfig = new HourRateConfig();
    hourRateConfig.setDaysOfWeekIncluded("MON,TUE,WED,THU,FRI,SAT,SUN,HOL");
    hourRateConfig.setStartTime(startTime);
    hourRateConfig.setEndTime(endTime);
    hourRateConfig.setRate(100.0);
    Long requestTime = currentDate.getTime();
    DynamicPricingTimeConfig dynamicPricingTimeConfig = new DynamicPricingTimeConfig();
    dynamicPricingTimeConfig.setStartTime(requestTime - 1000000000000L);
    dynamicPricingTimeConfig.setEndTime(requestTime + 100000L);
    Map<String, String> flatFareConfig;
    flatFareConfig = mockFlatFareConfig();
    flatFareConfig.put(BookARideConfigsConstant.DYNAMIC_PRICING_ENABLED, "Y");
    List<BookingFareConfig> bookingFareConfigList = mockBookingFareConfig();
    fareUploadConfiguration.setFlatFareConfig(flatFareConfig);
    fareUploadConfiguration.setHolidayConfig(
        List.of("2023-01-01 00:00:00, 2023-11-29 00:00:00, 2023-11-30 00:00:00"));
    fareUploadConfiguration.setPeakHourRateConfig(Map.of("0", hourRateConfig));
    fareUploadConfiguration.setDynamicPricingTimeConfig(Map.of("0", dynamicPricingTimeConfig));
    productList.put("0", List.of("COMFORT", "METERED"));
  }

  private Map<String, String> mockFlatFareConfig() {
    Map<String, String> flatFareConfig = new HashMap<>();
    flatFareConfig.put("LIVE_TRAFFIC_TIER_1_END_DIST", "5000");
    flatFareConfig.put("LIVE_TRAFFIC_TIER_2_END_DIST", "323232");
    flatFareConfig.put("LIVE_TRAFFIC_DYNAMIC_PRICING_PEAK_HOUR_RATE", "10101");
    flatFareConfig.put("LIVE_TRAFFIC_TOTAL_FARE_ESTIMATE_RT", "123");
    flatFareConfig.put("WAIT_TIME_TIER_1_END_DIST", "5000");
    flatFareConfig.put("WAIT_TIME_TIER_2_END_DIST", "323232");
    flatFareConfig.put("WAIT_TIME_DYNAMIC_PRICING_PEAK_HOUR_RATE", "10101");
    flatFareConfig.put("WAIT_TIME_TOTAL_FARE_ESTIMATE_RT", "123");
    flatFareConfig.put("EST_LIVE_FLAG_DOWN_RATE", "10");
    flatFareConfig.put("EST_LIVE_TIER_1_PER_COUNT_FARE", "30");
    flatFareConfig.put("EST_LIVE_TIER_1_END_DIST", "5000");
    flatFareConfig.put("EST_LIVE_TIER_2_END_DIST", "323232");
    flatFareConfig.put("EST_LIVE_DYNAMIC_PRICING_PEAK_HOUR_RATE", "10101");
    flatFareConfig.put("EST_LIVE_TOTAL_FARE_ESTIMATE_RT", "123");
    flatFareConfig.put("EST_WAIT_TIME_TIER_1_END_DIST", "5000");
    flatFareConfig.put("EST_WAIT_TIME_TIER_2_END_DIST", "323232");
    flatFareConfig.put("EST_WAIT_TIME_DYNAMIC_PRICING_PEAK_HOUR_RATE", "10101");
    flatFareConfig.put("DESURGE_MAX_CAP", "-50");
    flatFareConfig.put("DESURGE_MID_NIGHT_MAX_CAP", "12");
    flatFareConfig.put("DESURGE_PEAK_HOUR_MAX_CAP", "12");
    flatFareConfig.put("DYNAMIC_PRICING_TIER_1_PER_COUNT_FARE", "0.37");
    flatFareConfig.put("DYNAMIC_PRICING_TIER_1_START_DISTANCE", "0");
    flatFareConfig.put("DYNAMIC_PRICING_TIER_1_END_DISTANCE", "10000");
    flatFareConfig.put("DYNAMIC_PRICING_TIER_2_PER_COUNT_FARE", "0.47");
    flatFareConfig.put("DYNAMIC_PRICING_TIER_2_START_DISTANCE", "10001");
    flatFareConfig.put("DYNAMIC_PRICING_TIER_2_END_DISTANCE", "999999");
    flatFareConfig.put("DYNAMIC_PRICING_FLAG_DOWN_RATE", "3.5");
    flatFareConfig.put("TOTAL_FARE_ESTIMATE_LF", "1");
    flatFareConfig.put("TOTAL_FARE_ESTIMATE_RT", "1.15");
    flatFareConfig.put("DYNAMIC_PRICING_DURATION_RATE", "0.2");
    flatFareConfig.put("DYNAMIC_PRICING_PEAK_HOUR_RATE", "0.25");
    flatFareConfig.put("DYNAMIC_PRICING_MID_NIGHT_RATE", "0.5");
    flatFareConfig.put("LIVE_TRAFFIC_TIER_1_PER_COUNT_FARE", "0.37");
    flatFareConfig.put("LIVE_TRAFFIC_TIER_1_START_DISTANCE", "0");
    flatFareConfig.put("LIVE_TRAFFIC_TIER_1_END_DISTANCE", "1000");
    flatFareConfig.put("LIVE_TRAFFIC_TIER_2_PER_COUNT_FARE", "0.47");
    flatFareConfig.put("LIVE_TRAFFIC_TIER_2_START_DISTANCE", "500");
    flatFareConfig.put("LIVE_TRAFFIC_TIER_2_END_DISTANCE", "2000");
    flatFareConfig.put("LIVE_TRAFFIC_FLAG_DOWN_RATE", "3.5");
    flatFareConfig.put("LIVE_TRAFFIC_DURATION_RATE", "0.22");
    flatFareConfig.put("LIVE_TRAFFIC_MID_NIGHT_RATE_0", "0.25");
    flatFareConfig.put("LIVE_TRAFFIC_MID_NIGHT_DAYS_0", "WEEK_DAYS");
    flatFareConfig.put("LIVE_TRAFFIC_MID_NIGHT_START_TIME_0", "MID_NIGHT_START_TIME");
    flatFareConfig.put("LIVE_TRAFFIC_MID_NIGHT_END_TIME_0", "MID_NIGHT_END_TIME");
    flatFareConfig.put("LIVE_TRAFFIC_PEAK_HOUR_RATE_0", "0.25");
    flatFareConfig.put("LIVE_TRAFFIC_PEAK_HOUR_DAYS_0", "WEEK_DAYS");
    flatFareConfig.put("LIVE_TRAFFIC_PEAK_HOUR_START_TIME_0", "PEAK_HOUR_START_TIME");
    flatFareConfig.put("LIVE_TRAFFIC_PEAK_HOUR_END_TIME_0", "PEAK_HOUR_END_TIME");
    flatFareConfig.put("WAIT_TIME_TIER_1_PER_COUNT_FARE", "0.22");
    flatFareConfig.put("WAIT_TIME_TIER_1_START_DISTANCE", "1000");
    flatFareConfig.put("WAIT_TIME_TIER_1_END_DISTANCE", "20000");
    flatFareConfig.put("WAIT_TIME_TIER_2_PER_COUNT_FARE", "0.22");
    flatFareConfig.put("WAIT_TIME_TIER_2_START_DISTANCE", "100");
    flatFareConfig.put("WAIT_TIME_TIER_2_END_DISTANCE", "500");
    flatFareConfig.put("WAIT_TIME_FLAG_DOWN_RATE", "3.9");
    flatFareConfig.put("WAIT_TIME_RATE", "0.22");
    flatFareConfig.put("WAIT_TIME_PEAK_HOUR_RATE_0", "0.25");
    flatFareConfig.put("WAIT_TIME_PEAK_HOUR_DAYS_0", "WEEK_DAYS");
    flatFareConfig.put("WAIT_TIME_PEAK_HOUR_START_TIME_0", "MID_NIGHT_START_TIME");
    flatFareConfig.put("WAIT_TIME_PEAK_HOUR_END_TIME_0", "MID_NIGHT_END_TIME");
    flatFareConfig.put("WAIT_TIME_MID_NIGHT_RATE_0", "0.25");
    flatFareConfig.put("WAIT_TIME_MID_NIGHT_DAYS_0", "WEEK_DAYS");
    flatFareConfig.put("WAIT_TIME_MID_NIGHT_START_TIME_0", "PEAK_HOUR_START_TIME");
    flatFareConfig.put("WAIT_TIME_MID_NIGHT_END_TIME_0", "PEAK_HOUR_END_TIME");
    flatFareConfig.put("EST_WAIT_TIME_TIER_1_PER_COUNT_FARE", "0.22");
    flatFareConfig.put("EST_WAIT_TIME_TIER_1_START_DISTANCE", "1000");
    flatFareConfig.put("EST_WAIT_TIME_TIER_1_END_DISTANCE", "70000");
    flatFareConfig.put("EST_WAIT_TIME_TIER_2_PER_COUNT_FARE", "0.22");
    flatFareConfig.put("EST_WAIT_TIME_TIER_2_START_DISTANCE", "3000");
    flatFareConfig.put("EST_WAIT_TIME_TIER_2_END_DISTANCE", "9999");
    flatFareConfig.put("EST_WAIT_TIME_FLAG_DOWN_RATE", "3.9");
    flatFareConfig.put("EST_WAIT_TIME_TOTAL_FARE_ESTIMATE_LF", "1");
    flatFareConfig.put("EST_WAIT_TIME_TOTAL_FARE_ESTIMATE_RT", "1.15");
    flatFareConfig.put("EST_WAIT_TIME_RATE", "0.22");
    flatFareConfig.put("EST_WAIT_TIME_MID_NIGHT_RATE_0", "0.25");
    flatFareConfig.put("EST_WAIT_TIME_MID_NIGHT_DAYS_0", "WEEK_DAYS");
    flatFareConfig.put("EST_WAIT_TIME_MID_NIGHT_START_TIME_0", "MID_NIGHT_START_TIME");
    flatFareConfig.put("EST_WAIT_TIME_MID_NIGHT_END_TIME_0", "MID_NIGHT_END_TIME");
    flatFareConfig.put("EST_WAIT_TIME_PEAK_HOUR_RATE_0", "0.25");
    flatFareConfig.put("EST_WAIT_TIME_PEAK_HOUR_DAYS_0", "WEEK_DAYS");
    flatFareConfig.put("EST_WAIT_TIME_PEAK_HOUR_START_TIME_0", "PEAK_HOUR_START_TIME");
    flatFareConfig.put("EST_WAIT_TIME_PEAK_HOUR_END_TIME_0", "PEAK_HOUR_END_TIME");
    flatFareConfig.put("EST_LIVE_TRAFFIC_TIER_1_PER_COUNT_FARE", "0.37");
    flatFareConfig.put("EST_LIVE_TRAFFIC_TIER_1_START_DISTANCE", "0");
    flatFareConfig.put("EST_LIVE_TRAFFIC_TIER_1_END_DISTANCE", "9999");
    flatFareConfig.put("EST_LIVE_TRAFFIC_TIER_2_PER_COUNT_FARE", "0.47");
    flatFareConfig.put("EST_LIVE_TRAFFIC_TIER_2_START_DISTANCE", "0");
    flatFareConfig.put("EST_LIVE_TRAFFIC_TIER_2_END_DISTANCE", "100");
    flatFareConfig.put("EST_LIVE_TRAFFIC_FLAG_DOWN_RATE", "3.5");
    flatFareConfig.put("EST_LIVE_TRAFFIC_TOTAL_FARE_ESTIMATE_LF", "1");
    flatFareConfig.put("EST_LIVE_TRAFFIC_TOTAL_FARE_ESTIMATE_RT", "1.15");
    flatFareConfig.put("EST_LIVE_TRAFFIC_DURATION_RATE", "0.2");
    flatFareConfig.put("EST_LIVE_TRAFFIC_MID_NIGHT_RATE_0", "0.25");
    flatFareConfig.put("EST_LIVE_TRAFFIC_MID_NIGHT_DAYS_0", "WEEK_DAYS");
    flatFareConfig.put("EST_LIVE_TRAFFIC_MID_NIGHT_START_TIME_0", "MID_NIGHT_START_TIME");
    flatFareConfig.put("EST_LIVE_TRAFFIC_MID_NIGHT_END_TIME_0", "MID_NIGHT_END_TIME");
    flatFareConfig.put("EST_LIVE_TRAFFIC_PEAK_HOUR_RATE_0", "0.25");
    flatFareConfig.put("EST_LIVE_TRAFFIC_PEAK_HOUR_DAYS_0", "WEEK_DAYS");
    flatFareConfig.put("EST_LIVE_TRAFFIC_PEAK_HOUR_START_TIME_0", "PEAK_HOUR_START_TIME");
    flatFareConfig.put("EST_LIVE_TRAFFIC_PEAK_HOUR_END_TIME_0", "PEAK_HOUR_END_TIME");
    flatFareConfig.put("DYNAMIC_PRICING_VEH_GROUP_IDS", "0");
    return flatFareConfig;
  }

  private List<BookingFareConfig> mockBookingFareConfig() {
    return List.of(
        BookingFareConfig.builder()
            .pdtId(ProductTypeEnum.COMFORT_RIDE_PRODUCT.getProductCode())
            .vehTypeId("0")
            .tariffTypeCode(BookARideConfigsConstant.TARIFF_CJ_BOOKING_FEE)
            .fareAmt(8.0)
            .levyAmt(0.66)
            .startTime(LocalTime.of(00, 00, 00))
            .endTime(LocalTime.of(23, 59, 59))
            .applicableDays(List.of(BookARideConfigsConstant.HOL, "2023-01-01"))
            .build(),
        BookingFareConfig.builder()
            .pdtId(ProductTypeEnum.METER_FARE_PRODUCT.getProductCode())
            .vehTypeId("0")
            .tariffTypeCode(BookARideConfigsConstant.TARIFF_PDT_BOOKING_FEE)
            .fareAmt(7.0)
            .levyAmt(0.88)
            .startTime(LocalTime.of(00, 00, 00))
            .endTime(LocalTime.of(23, 59, 59))
            .applicableDays(List.of(BookARideConfigsConstant.HOL, "2023-01-01"))
            .build());
  }

  @Test
  void givenNoParams_whenCalculateFare_thenReturnAsMockData() throws ParseException {
    var now = new SimpleDateFormat("yyyy-MM-dd").parse("2023-01-01");
    service.calculateFare(now, fareUploadConfiguration, productList);
    Assertions.assertEquals(1, fareUploadConfiguration.getVgProductFareMap().size());
    Assertions.assertNotNull(fareUploadConfiguration.getVgProductFareMap().get(0).get("COMFORT"));
    Assertions.assertNotNull(fareUploadConfiguration.getVgProductFareMap().get(0).get("METERED"));
    Assertions.assertNotEquals(
        0d, fareUploadConfiguration.getVgProductFareMap().get(0).get("COMFORT").getFlagDown());
    Assertions.assertNotEquals(
        0d, fareUploadConfiguration.getVgProductFareMap().get(0).get("METERED").getFlagDown());
  }

  @Test
  void given_COMFORT_RIDE_PRODUCT_and_IS_LIVE_TRAFFICE_TRUE_whenCalculateFare_thenReturnAsExpected()
      throws ParseException {
    var now = new SimpleDateFormat("yyyy-MM-dd").parse("2023-01-01");
    fareUploadConfiguration.getFlatFareConfig().put("IS_LIVE_TRAFFIC", "Y");
    service.calculateFare(now, fareUploadConfiguration, productList);
    Assertions.assertEquals(1, fareUploadConfiguration.getVgProductFareMap().size());
    Assertions.assertNotNull(fareUploadConfiguration.getVgProductFareMap().get(0).get("COMFORT"));
    Assertions.assertNotNull(fareUploadConfiguration.getVgProductFareMap().get(0).get("METERED"));
    Assertions.assertNotEquals(
        0d, fareUploadConfiguration.getVgProductFareMap().get(0).get("COMFORT").getFlagDown());
    Assertions.assertNotEquals(
        0d, fareUploadConfiguration.getVgProductFareMap().get(0).get("METERED").getFlagDown());
  }

  // Test calculateRate method when isHoliday is true with currentDate is a holiday
  @Test
  void givenHolidayConfig_whenCalculateRate_thenReturnHolidayRate() throws ParseException {
    SimpleDateFormat sdf = new SimpleDateFormat(DDMMYYYY_FORMAT);

    Date reqDate = sdf.parse("25-12-2023"); // Christmas
    reqDate.setHours(10);

    List<String> holidaysList = Arrays.asList("25-12-2023 00:00:00", "01-01-2024 00:00:00");

    HourRateConfig config = new HourRateConfig();

    config.setDaysOfWeekIncluded("MON,TUE,WED,THU,FRI,SAT,SUN,HOL");

    config.setStartTime(
        LocalTime.of(reqDate.getHours() - 2, reqDate.getMinutes(), reqDate.getSeconds()));
    config.setEndTime(
        LocalTime.of(reqDate.getHours() + 1, reqDate.getMinutes(), reqDate.getSeconds()));
    config.setRate(10.0);

    Map<String, HourRateConfig> hourRateConfigMap = Map.of("", config);

    FareUploadConfiguration fareUploadConfiguration =
        FareUploadConfiguration.builder()
            .holidayConfig(holidaysList)
            .peakHourRateConfig(hourRateConfigMap)
            .build();

    service.calculateFare(reqDate, fareUploadConfiguration, productList);
    assertNotNull(fareUploadConfiguration.getVgProductFareMap());
  }

  @Test
  void givenFlatFareConfMap_whenSetFlatFareConfig_thenReturnFlatFareConfigValue() {
    // Arrange
    FareUploadConfiguration fareUploadConfiguration = FareUploadConfiguration.builder().build();
    String prefix = "COMFORT";
    String productId = "COMFORT";
    Date date = new Date();
    int vehicleGroupId = 0;
    boolean isSurgeRequired = true;

    Map<String, String> flatFareConfMap = new HashMap<>();
    flatFareConfMap.put(prefix + BookARideConfigsConstant.FLAG_DOWN_RATE, "10.0");
    flatFareConfMap.put(prefix + BookARideConfigsConstant.TIER_1_PER_COUNT_FARE, "5.0");

    fareUploadConfiguration.setFlatFareConfig(flatFareConfMap);

    BookARideFareCalculationUtils.setFlatFareConfig(
        prefix, fareUploadConfiguration, productId, date, vehicleGroupId, isSurgeRequired);

    assertNotNull(fareUploadConfiguration.getVgProductFareMap().get(vehicleGroupId));
    assertNotNull(fareUploadConfiguration.getVgProductFareMap().get(vehicleGroupId).get(productId));
    VGProductFareBean vGProductFareBean =
        fareUploadConfiguration.getVgProductFareMap().get(vehicleGroupId).get(productId);
    assertEquals(10.0, vGProductFareBean.getFlagDown());
    assertEquals(5.0, vGProductFareBean.getTier1Fare());
  }

  @Test
  void givenRate_whenReCalculate_thenReturnExpected() {
    // Arrange
    FareUploadConfiguration fareUploadConfiguration = FareUploadConfiguration.builder().build();
    String productId = "COMFORT";
    int vehicleGroupId = 0;
    double nonZeroRate = 0.1; // 10% for example

    // Assuming you have a method to initialize VGProductFareBean and add it to
    // fareUploadConfiguration
    initializeVGProductFareBean(fareUploadConfiguration, vehicleGroupId, productId);

    // Act & Assert for PEAK_HOUR
    BookARideFareCalculationUtils.reCalculate(
        fareUploadConfiguration,
        nonZeroRate,
        productId,
        vehicleGroupId,
        FlatfareConstants.PEAK_HOUR);
    VGProductFareBean vGProductFareBeanPeak =
        fareUploadConfiguration.getVgProductFareMap().get(vehicleGroupId).get(productId);
    assertTrue(vGProductFareBeanPeak.isPeakHour());
    assertFalse(vGProductFareBeanPeak.isMidNight());
    // ... assert other properties for PEAK_HOUR

    // Act & Assert for MIDNIGHT
    BookARideFareCalculationUtils.reCalculate(
        fareUploadConfiguration,
        nonZeroRate,
        productId,
        vehicleGroupId,
        FlatfareConstants.MIDNIGHT);
    VGProductFareBean vGProductFareBeanMidnight =
        fareUploadConfiguration.getVgProductFareMap().get(vehicleGroupId).get(productId);
    assertTrue(vGProductFareBeanMidnight.isPeakHour());
    assertTrue(vGProductFareBeanMidnight.isMidNight());
    // ... assert other properties for MIDNIGHT
  }

  @Test
  void givenZeroRate_whenReCalculate_thenReturnExpectedValue() {
    String productId = "COMFORT";
    int vehicleGroupId = 0;
    initializeVGProductFareBean(fareUploadConfiguration, vehicleGroupId, productId);
    VGProductFareBean vGProductFareBeanMidnight =
        fareUploadConfiguration.getVgProductFareMap().get(vehicleGroupId).get(productId);
    // Act & Assert for zero rate
    BookARideFareCalculationUtils.reCalculate(
        fareUploadConfiguration, 0, productId, vehicleGroupId, "SomeOtherPrefix");
    VGProductFareBean vGProductFareBeanZeroRate =
        fareUploadConfiguration.getVgProductFareMap().get(vehicleGroupId).get(productId);
    // Assert that values are not changed when rate is zero
    // ... assert properties for zero rate
    assertEquals(10.0, vGProductFareBeanMidnight.getFlagDown());
    assertEquals(5.0, vGProductFareBeanMidnight.getTier1Fare());

    assertEquals(51.0, vGProductFareBeanMidnight.getTier2Fare());

    assertEquals(44.0, vGProductFareBeanMidnight.getWaitTimeFare());
    assertFalse(vGProductFareBeanMidnight.isPeakHour());
    assertFalse(vGProductFareBeanMidnight.isMidNight());
  }

  private void initializeVGProductFareBean(
      FareUploadConfiguration fareUploadConfiguration, int vehicleGroupId, String productId) {
    VGProductFareBean vGProductFareBean = VGProductFareBean.builder().build();
    vGProductFareBean.setFlagDown(10.0);
    vGProductFareBean.setTier1Fare(5.0);
    vGProductFareBean.setTier2Fare(51.0);
    vGProductFareBean.setWaitTimeFare(44.0);
    vGProductFareBean.setPeakHour(false);
    vGProductFareBean.setMidNight(false);
    fareUploadConfiguration
        .getVgProductFareMap()
        .put(vehicleGroupId, Map.of(productId, vGProductFareBean));
  }

  @Test
  void givenDifferentProductIds_whenCalculateFare_thenProcessAccordingly() throws ParseException {
    Date date = new SimpleDateFormat("yyyy-MM-dd").parse("2023-01-01");
    int vehicleGroupId = 0;

    // Test with COMFORT product ID
    service.calculateFare(
        date, fareUploadConfiguration, Map.of(String.valueOf(vehicleGroupId), List.of("COMFORT")));
    assertNotNull(fareUploadConfiguration.getVgProductFareMap().get(vehicleGroupId).get("COMFORT"));

    // Test with METERED product ID
    service.calculateFare(
        date, fareUploadConfiguration, Map.of(String.valueOf(vehicleGroupId), List.of("METERED")));
    assertNotNull(fareUploadConfiguration.getVgProductFareMap().get(vehicleGroupId).get("METERED"));
  }

  @Test
  void givenMidNightPrefixes_whenReCalculate_thenAdjustFaresAccordingly() {
    String productId = "COMFORT";
    int vehicleGroupId = 0;
    double rate = 0.1; // 10% increase
    initializeVGProductFareBean(fareUploadConfiguration, vehicleGroupId, productId);

    final VGProductFareBean originalBean =
        fareUploadConfiguration
            .getVgProductFareMap()
            .get(vehicleGroupId)
            .get(productId); // Assuming clone method is implemented

    BookARideFareCalculationUtils.reCalculate(
        fareUploadConfiguration, rate, productId, vehicleGroupId, FlatfareConstants.MIDNIGHT);
    VGProductFareBean updatedBean =
        fareUploadConfiguration.getVgProductFareMap().get(vehicleGroupId).get(productId);

    // Assertions
    assertNotNull(updatedBean);
    assertEquals(10.0 * (1 + rate), updatedBean.getFlagDown());
    assertEquals(5 * (1 + rate), updatedBean.getTier1Fare());
    assertEquals(51.0 * (1 + rate), updatedBean.getTier2Fare());
    assertEquals(48.4, updatedBean.getWaitTimeFare());

    // Check if peak hour or midnight flags are unchanged
    assertEquals(originalBean.isPeakHour(), updatedBean.isPeakHour());
    assertEquals(originalBean.isMidNight(), updatedBean.isMidNight());
  }

  @Test
  void givenDifferentPrefixes_whenReCalculate_thenAdjustFaresAccordingly() {
    String productId = "COMFORT";
    int vehicleGroupId = 0;
    double rate = 0.1; // 10% increase
    initializeVGProductFareBean(fareUploadConfiguration, vehicleGroupId, productId);

    final VGProductFareBean originalBean =
        fareUploadConfiguration
            .getVgProductFareMap()
            .get(vehicleGroupId)
            .get(productId); // Assuming clone method is implemented

    BookARideFareCalculationUtils.reCalculate(
        fareUploadConfiguration, rate, productId, vehicleGroupId, "CUSTOM_PREFIX");
    VGProductFareBean updatedBean =
        fareUploadConfiguration.getVgProductFareMap().get(vehicleGroupId).get(productId);

    // Assertions
    assertNotNull(updatedBean);
    assertEquals(10.0 * (1 + rate), updatedBean.getFlagDown());
    assertEquals(5 * (1 + rate), updatedBean.getTier1Fare());
    assertEquals(51.0 * (1 + rate), updatedBean.getTier2Fare());
    assertEquals(48.4, updatedBean.getWaitTimeFare());

    // Check if peak hour or midnight flags are unchanged
    assertEquals(originalBean.isPeakHour(), updatedBean.isPeakHour());
    assertEquals(originalBean.isMidNight(), updatedBean.isMidNight());
  }

  @Test
  void givenPeakHourPrefixes_whenReCalculate_thenAdjustFaresAccordingly() {
    String productId = "COMFORT";
    int vehicleGroupId = 0;
    double rate = 0.1; // 10% increase
    initializeVGProductFareBean(fareUploadConfiguration, vehicleGroupId, productId);

    final VGProductFareBean originalBean =
        fareUploadConfiguration
            .getVgProductFareMap()
            .get(vehicleGroupId)
            .get(productId); // Assuming clone method is implemented

    BookARideFareCalculationUtils.reCalculate(
        fareUploadConfiguration, rate, productId, vehicleGroupId, FlatfareConstants.PEAK_HOUR);
    VGProductFareBean updatedBean =
        fareUploadConfiguration.getVgProductFareMap().get(vehicleGroupId).get(productId);

    // Assertions
    assertNotNull(updatedBean);
    assertEquals(10.0 * (1 + rate), updatedBean.getFlagDown());
    assertEquals(5 * (1 + rate), updatedBean.getTier1Fare());
    assertEquals(51.0 * (1 + rate), updatedBean.getTier2Fare());
    assertEquals(48.4, updatedBean.getWaitTimeFare());

    // Check if peak hour or midnight flags are unchanged
    assertEquals(originalBean.isPeakHour(), updatedBean.isPeakHour());
    assertEquals(originalBean.isMidNight(), updatedBean.isMidNight());
  }

  @Test
  void givenNullInputs_whenCalculateFare_thenHandleGracefully() {
    Date date = null;
    Map<String, List<String>> emptyProductList = new HashMap<>();

    assertDoesNotThrow(
        () -> service.calculateFare(date, fareUploadConfiguration, emptyProductList));
  }

  @Test
  void givenDynamicPricingEnabled_whenSetDpFareConfig_thenSetCorrectly() throws ParseException {
    Date date = new SimpleDateFormat("yyyy-MM-dd").parse("2023-01-01");
    String productId = "COMFORT";
    int vehicleGroupId = 0;
    fareUploadConfiguration
        .getFlatFareConfig()
        .put(BookARideConfigsConstant.DYNAMIC_PRICING_ENABLED, "Y");

    BookARideFareCalculationUtils.setDpFareConfig(
        date, fareUploadConfiguration, productId, vehicleGroupId);
    assertTrue(
        fareUploadConfiguration.getDynamicPricingTimeConfig().entrySet().stream()
            .anyMatch(
                entry ->
                    entry.getValue().getStartTime() <= date.getTime()
                        && entry.getValue().getEndTime() >= date.getTime()));
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.DpsProperties;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.CommonConstant;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Iterator;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

class CommonUtilsTest {

  @Test
  void givenNonNullParameters_whenCheckAnyNull_thenReturnFalse() {
    // GIVEN
    long km = 10L;
    long ett = 1000L;

    // THEN
    assertFalse(CommonUtils.anyNull(km, ett));
  }

  @Test
  void givenParametersWithNull_whenCheckAnyNull_thenReturnTrue() {
    // GIVEN
    long km = 10L;

    // THEN
    assertTrue(CommonUtils.anyNull(km, null));
  }

  @ParameterizedTest
  @CsvSource({"3.50, 3.50", "10.10, 10.50", "10.00, 10.00"})
  void givenInputAmounts_whenRoundUpToFiftyCent_returnRoundedToNearestFiftyCent(
      Double fare, Double expected) {
    // WHEN
    Double actual = CommonUtils.roundUpToFiftyCent(fare);

    // THEN
    assertEquals(expected, actual);
  }

  @Test
  void givenDecimalValue_whenRoundToTwo_thenReturnRoundedToTwoDecimals() {
    // GIVEN
    Double val = 2.23546;
    Double expected = 2.24;

    // WHEN
    Double actual = CommonUtils.roundToTwo(val);

    // THEN
    assertEquals(expected, actual);
  }

  @Test
  void givenDecimalValue_whenRoundToTwoBD_thenReturnBigDecimalRoundedToTwoDecimals() {
    // GIVEN
    Double val = 2.23546;
    BigDecimal expected = new BigDecimal("2.24");

    // WHEN
    BigDecimal actual = CommonUtils.roundToTwoBD(val);

    // THEN
    assertEquals(expected, actual);
  }

  @Test
  void givenPositiveDistanceInMeters_whenRoundMeterToKm_thenReturnDistanceInKm() {
    // GIVEN
    long distanceM = 1356;
    Double expected = 1.36;

    // WHEN
    Double actual = CommonUtils.roundMeterToKm(distanceM);

    // THEN
    assertEquals(expected, actual);
  }

  @Test
  void givenNegativeDistanceInMeters_whenRoundMeterToKm_thenReturnZeroKm() {
    // GIVEN
    long distanceM = -1000;
    Double expected = 0.0;

    // WHEN
    Double actual = CommonUtils.roundMeterToKm(distanceM);

    // THEN
    assertEquals(expected, actual);
  }

  @Test
  void
      givenDistancesBothWithin5KmOfEachOtherAndUnderMaxJourneyDist_whenCheckIfInBetweenFiveKM_thenReturnTrue() {
    // GIVEN
    Double distanceKM = 23.5;
    Double estimatedKM = 24.9;

    // THEN
    assertTrue(CommonUtils.inBetweenFiveKM(distanceKM, estimatedKM));
  }

  @Test
  void
      givenActualDistanceGreaterByMoreThan5KmThanEstimated_whenCheckIfInBetweenFiveKM_thenReturnFalse() {
    // GIVEN
    Double distanceKM = 24.5;
    Double estimatedKM = 23.5;

    // THEN
    assertFalse(CommonUtils.inBetweenFiveKM(distanceKM, estimatedKM));
  }

  @Test
  void givenBothDistancesAboveMaxJourneyDist_whenCheckIfInBetweenFiveKM_thenReturnTrue() {
    // GIVEN
    Double distanceKM = 30.5;
    Double estimatedKM = 25.5;

    // THEN
    assertTrue(CommonUtils.inBetweenFiveKM(distanceKM, estimatedKM));
  }

  @Test
  void
      givenDistanceAboveMaxJourneyAndMoreThan5KmGreaterThanEstimated_whenCheckIfInBetweenFiveKM_thenReturnFalse() {
    // GIVEN
    Double distanceKM = 28.5;
    Double estimatedKM = 23.5;

    // THEN
    assertFalse(CommonUtils.inBetweenFiveKM(distanceKM, estimatedKM));
  }

  @Test
  void givenNullParam_whenGenerateTripId_thenReturnEmpty() {
    assertEquals(StringUtils.EMPTY, CommonUtils.generateTripId(null));
  }

  @Test
  void givenNormalParam_whenGenerateTripId_thenReturnTripId() {
    assertNotNull(CommonUtils.generateTripId(new Date()));
  }

  @Test
  void givenNullParam_whenGenerateFareId_thenReturnEmpty() {
    assertEquals(StringUtils.EMPTY, CommonUtils.generateFareId(null, null));
  }

  @Test
  void givenNormalParam_whenGenerateFareId_thenReturnTripId() {
    assertNotNull(CommonUtils.generateFareId(new Date(), "mobile"));
  }

  @Test
  void givenNonNullFareId_whenGenMultiFareCacheKey_thenReturnCorrectKey() {
    // GIVEN
    String fareId = "1234";

    // WHEN
    String actual = CommonUtils.generateMultiFareCacheKey(fareId);

    // THEN
    String expected = "DYNAMIC_PRICING:MULTI_FARE:1234";
    assertEquals(expected, actual);
  }

  @Test
  void givenNullParam_whenGetReleaseVersion_thenReturnDefault() {
    // WHEN
    int actual = CommonUtils.getReleaseVersion(null);

    // THEN
    assertEquals(CommonConstant.RELEASE_VER_DEFAULT, actual);
  }

  @Test
  void givenNormalParam_whenGetReleaseVersion_thenReturn() {
    DpsProperties dpsProperties = new DpsProperties();
    dpsProperties.setApplicationRelease(CommonConstant.RELEASE_2);
    // WHEN
    int actual = CommonUtils.getReleaseVersion(dpsProperties);

    // THEN
    assertEquals(CommonConstant.RELEASE_2, actual);
  }

  @Test
  void givenIterator_whenGetIterableFromIterator_thenReturnIterable() {
    // Mock the Iterator
    Iterator<String> mockIterator = mock(Iterator.class);
    when(mockIterator.hasNext()).thenReturn(true, false);
    when(mockIterator.next()).thenReturn("Test");

    // Get an Iterable from the mocked Iterator
    Iterable<String> iterable = CommonUtils.getIterableFromIterator(mockIterator);

    // Verify the behavior and test the result
    assertNotNull(iterable);
    for (String item : iterable) {
      assertEquals("Test", item);
    }

    // Verify interactions with the mock
    verify(mockIterator, times(1)).next();
    verify(mockIterator, atLeastOnce()).hasNext();
  }

  @ParameterizedTest
  @CsvSource(
      value = {"30.0, 30.0", "'', null"},
      nullValues = "null")
  void givenNormalParam_whenStringToDoubleOrElseNull_thenReturn(String input, Double expected) {
    Double actual = CommonUtils.stringToDoubleOrElseNull(input);
    assertEquals(expected, actual);
  }

  @ParameterizedTest
  @CsvSource(
      value = {"'KEY_NAME_INDEX', 'INDEX'", "'', null"},
      nullValues = "null")
  void givenNormalParam_whenGetIndexConfigByKeyName_thenReturn(String input, String expected) {
    String actual = CommonUtils.getIndexConfigByKeyName(input);
    assertEquals(expected, actual);
  }

  @ParameterizedTest
  @CsvSource(
      value = {"'KEY_NAME_INDEX', 'KEY_NAME_'", "'', ''", "null, ''"},
      nullValues = "null")
  void givenNormalParam_whenGetConfigNameWithoutLastIndex_thenReturn(
      String input, String expected) {
    String actual = CommonUtils.getConfigNameWithoutLastIndex(input);
    assertEquals(expected, actual);
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;

import org.junit.jupiter.api.Test;

class ConfigurationKeyUtilsTest {
  @Test
  void givenCombinedString_whenExtractKeyPart_thenReturnExtractedKeyValue() {
    assertEquals("keyWithoutColon", ConfigurationKeyUtils.extractKeyPart("keyWithoutColon"));
    assertEquals("withColon", ConfigurationKeyUtils.extractKeyPart("key:withColon"));
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.flatfare.impl;

import static org.junit.jupiter.api.Assertions.*;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.*;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.FlatFareConfigService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.LocationSurchargeService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.DateUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.BookingFeeItem;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.PlatformFeeListRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.FareService;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.DomainException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class FlatFareManagerTest {

  @Mock private FlatFareManager flatFareManager;

  @Mock private FlatFareConfigService configService;

  @Mock private FareService fareService;

  @Mock private LocationSurchargeService locationSurchargeService;

  @BeforeEach
  void setUp() {
    flatFareManager = new FlatFareManager(configService, fareService, locationSurchargeService);
  }

  @Test
  void givenNormalParam_whenIsLimoType_thenReturnSuccess() {
    CommonConfigSet commonConfigSet =
        CommonConfigSet.builder().limoFlatFareVehIds(",1,10,100,").build();
    boolean actual = flatFareManager.isFlatLimoType(1, commonConfigSet);
    assertEquals(Boolean.TRUE, actual);
  }

  @Test
  void givenNormalParam_whenIsEstLimoType_thenReturnSuccess() {
    CommonConfigSet commonConfigSet =
        CommonConfigSet.builder().estLimoFlatFareVehIds(",1,10,100,").build();
    boolean actual = flatFareManager.isEstLimoType(1, commonConfigSet);
    assertEquals(Boolean.TRUE, actual);
  }

  @Test
  void givenNormalParam_whenIsEstFareType_thenReturnSuccess() {
    CommonConfigSet commonConfigSet = CommonConfigSet.builder().estFareVehIds(",1,10,100,").build();
    boolean actual = flatFareManager.isEstFareType(1, commonConfigSet);
    assertEquals(Boolean.TRUE, actual);
  }

  @Test
  void givenNormalParam_whenIsDynamicFareType_thenReturnSuccess() {
    CommonConfigSet commonConfigSet =
        CommonConfigSet.builder().dynamicPricingVehIds(",1,10,100,").build();
    boolean actual = flatFareManager.isDynamicFareType(1, commonConfigSet);
    assertEquals(Boolean.TRUE, actual);
  }

  @Test
  void givenNormalParam_whenShowMeterFareOnly_thenReturnSuccess() {
    CommonConfigSet commonConfigSet =
        CommonConfigSet.builder()
            .estFareVehIds(",1,10,100,")
            .dynamicPricingVehIds(",10,100,")
            .build();
    boolean actual = flatFareManager.isShowMeterFareOnly(1, commonConfigSet);
    assertEquals(Boolean.TRUE, actual);
  }

  @Test
  void givenNormalParam_whenShowFlatFareOnly_thenReturnSuccess() {
    CommonConfigSet commonConfigSet =
        CommonConfigSet.builder()
            .dynamicPricingVehIds(",1,10,100,")
            .estFareVehIds(",10,100,")
            .build();
    boolean actual = flatFareManager.isShowFlatFareOnly(1, commonConfigSet);
    assertEquals(Boolean.TRUE, actual);
  }

  @Test
  void givenNormalParam_whenIsAdvanceJobWhiteList_thenReturnSuccess() {
    CommonConfigSet commonConfigSet = CommonConfigSet.builder().advanceVehIds(",1,10,100,").build();
    boolean actual = flatFareManager.isAdvanceJobWhiteList(1, commonConfigSet);
    assertEquals(Boolean.TRUE, actual);
  }

  @Test
  void givenNormalParam_whenIsVehTypeIdListContain_thenReturnSuccess() {
    String config = ",1,10,100,";
    boolean actual = flatFareManager.isVehTypeIdListContain(1, config);
    assertEquals(Boolean.TRUE, actual);
  }

  @Test
  void givenValidVehTypeId_whenIsEstLimoType_thenReturnTrue() {
    CommonConfigSet commonConfigSet =
        CommonConfigSet.builder().estLimoFlatFareVehIds(",1,10,100,").build();
    boolean actual = flatFareManager.isEstLimoType(1, commonConfigSet);
    assertEquals(Boolean.TRUE, actual);
  }

  @Test
  void givenInvalidVehTypeId_whenIsEstLimoType_thenReturnFalse() {
    CommonConfigSet commonConfigSet =
        CommonConfigSet.builder().estLimoFlatFareVehIds(",1,10,100,").build();
    boolean actual = flatFareManager.isEstLimoType(2, commonConfigSet);
    assertEquals(Boolean.FALSE, actual);
  }

  @Test
  void givenNormalParam_whenFilterAdvanceVehId_thenReturnSuccess() {
    List<Integer> vehTypeIds = List.of(1, 10, 100, 107, 108);
    CommonConfigSet config = CommonConfigSet.builder().advanceVehIds(",1,10,100,").build();
    List<Integer> actual = flatFareManager.filterAdvanceVehId(vehTypeIds, config);
    List<Integer> expected = List.of(1, 10, 100);
    assertEquals(expected, actual);
  }

  @Test
  void givenNormalParam_whenGetCacheTimerMultiFlatFareInMinute_thenReturnSuccess() {
    String config = "9";
    int actual = flatFareManager.getCacheTimerMultiFlatFareInMinute(config);
    assertEquals(9, actual);
  }

  @Test
  void givenNormalParam_whenGetCacheTimerMultiFlatFareInMinute_thenReturnDefault() {
    String config = "string";
    int actual = flatFareManager.getCacheTimerMultiFlatFareInMinute(config);
    assertEquals(FlatfareConstants.CACHE_TIMER_MINS_MULTI_FLATFARE_DEFAULT, actual);
  }

  @Test
  void givenNormalParam_whenGetCacheTimerBreakDownFlatFareInMinute_thenReturnSuccess() {
    String config = "9";
    int actual = flatFareManager.getCacheTimerBreakDownFlatFareInMinute(config);
    assertEquals(9, actual);
  }

  @Test
  void givenNormalParam_whenGetCacheTimerBreakDownFlatFareInMinute_thenReturnDefault() {
    String config = "string";
    int actual = flatFareManager.getCacheTimerBreakDownFlatFareInMinute(config);
    assertEquals(FlatfareConstants.CACHE_TIMER_MINS_BREAKDOWN_FLATFARE_DEFAULT, actual);
  }

  @Test
  void givenIsNotDynamic_whenComputeSurgeLevel_thenReturnSuccess() {
    List<DriverSurgeLevelIndication> driverSurgeLevelIndications = new ArrayList<>();
    FlatFareVO flatFareVO = new FlatFareVO();
    int actual =
        flatFareManager.computeSurgeLevel(Boolean.FALSE, flatFareVO, driverSurgeLevelIndications);
    assertEquals(0, actual);
  }

  @Test
  void givenIsDynamic_whenComputeSurgeLevel_thenReturnSuccess() throws ParseException {
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy hh:mm:ss");

    String dateString = "09/20/2023 01:00:00";
    Date reqDate = df.parse(dateString);

    String effectFromtring = "09/20/2023 00:00:00";
    Date effectFromDate = df.parse(effectFromtring);

    String effectToString = "09/20/2023 02:00:00";
    Date effectToDate = df.parse(effectToString);

    FlatFareVO flatFareVO = new FlatFareVO();
    flatFareVO.setPricePerKm(10.0);
    flatFareVO.setFlatFareRequest(FlatFareRequest.builder().requestDate(reqDate).build());

    DriverSurgeLevelIndication driverSurgeLevelIndicationConfig =
        DriverSurgeLevelIndication.builder()
            .surgeLevel(1)
            .percFrom(9.0)
            .percTo(11.0)
            .effectTimeFromTs(effectFromDate.getTime())
            .effectTimeToTs(effectToDate.getTime())
            .colorHex("color")
            .build();

    int actual =
        flatFareManager.computeSurgeLevel(
            Boolean.TRUE, flatFareVO, List.of(driverSurgeLevelIndicationConfig));
    assertEquals(1, actual);
  }

  @Test
  void givenIsNotDynamic_whenComputeSurgeIndicator_thenReturnSuccess() {
    FlatFareVO flatFareVO = new FlatFareVO();
    int actual =
        flatFareManager.computeSurgeIndicator(
            Boolean.FALSE, flatFareVO, "anyString()", "anyString()");
    assertEquals(0, actual);
  }

  @Test
  void
      givenIsDynamicAndSurgePercGreaterThanThreshold_whenComputeSurgeIndicator_thenReturnSuccess() {
    FlatFareVO flatFareVO = new FlatFareVO();
    flatFareVO.setPricePerKm(10.0);

    int actual = flatFareManager.computeSurgeIndicator(Boolean.TRUE, flatFareVO, "10", "10");
    assertEquals(1, actual);
  }

  @Test
  void givenDynamicAndSurgePercLessThanThreshold_whenComputeSurgeIndicator_thenReturnSuccess() {
    FlatFareVO flatFareVO = new FlatFareVO();
    flatFareVO.setPricePerKm(10.0);

    int actual = flatFareManager.computeSurgeIndicator(Boolean.TRUE, flatFareVO, "11", "10");
    assertEquals(0, actual);
  }

  @Test
  void givenDynamicAndSurgePercLessThanThresholdZero_whenComputeSurgeIndicator_thenReturnSuccess() {
    FlatFareVO flatFareVO = new FlatFareVO();
    flatFareVO.setPricePerKm(9.0);

    int actual = flatFareManager.computeSurgeIndicator(Boolean.TRUE, flatFareVO, "11", "10");
    assertEquals(-1, actual);
  }

  @Test
  void givenIsNotDynamic_whenComputeSurgeIndicator_thenThrowsException() {
    FlatFareVO flatFareVO = new FlatFareVO();
    assertThrows(
        DomainException.class,
        () -> flatFareManager.computeSurgeIndicator(Boolean.TRUE, flatFareVO, null, null));
  }

  @Test
  void givenLimoType_whenComputeTotalFare_thenReturnInvalidFlatfare() {
    FlatFareRequest flatFareRequest = FlatFareRequest.builder().build();
    FlatFareConfigSet flatFareConfigSet = new FlatFareConfigSet();
    flatFareConfigSet.setPrefixKey(RedisKeyConstant.EST_LIMO_LIVE_TRAFFIC_KEY_PREFIX);
    assertThrows(
        DomainException.class,
        () -> flatFareManager.computeTotalFare(flatFareConfigSet, flatFareRequest));
  }

  @Test
  void givenStandardType_whenComputeTotalFare_thenReturnInvalidFlatfare() {
    FlatFareRequest flatFareRequest = FlatFareRequest.builder().build();
    FlatFareConfigSet flatFareConfigSet = new FlatFareConfigSet();
    flatFareConfigSet.setPrefixKey(RedisKeyConstant.LIVE_TRAFFIC_KEY_PREFIX);
    assertThrows(
        DomainException.class,
        () -> flatFareManager.computeTotalFare(flatFareConfigSet, flatFareRequest));
  }

  @Test
  void givenEstStandardType_whenComputeTotalFare_thenReturnInvalidFlatfare() {
    FlatFareRequest flatFareRequest = FlatFareRequest.builder().build();
    FlatFareConfigSet flatFareConfigSet = new FlatFareConfigSet();
    flatFareConfigSet.setPrefixKey(RedisKeyConstant.EST_LIVE_TRAFFIC_KEY_PREFIX);

    assertThrows(
        DomainException.class,
        () -> flatFareManager.computeTotalFare(flatFareConfigSet, flatFareRequest));
  }

  @Test
  void givenOtherType_whenComputeTotalFare_thenReturnInvalidFlatfare() {
    FlatFareRequest flatFareRequest = FlatFareRequest.builder().build();
    FlatFareConfigSet flatFareConfigSet = new FlatFareConfigSet();
    flatFareConfigSet.setPrefixKey("otherType");
    assertThrows(
        DomainException.class,
        () -> flatFareManager.computeTotalFare(flatFareConfigSet, flatFareRequest));
  }

  @Test
  void givenLimoType_whenComputeTotalFare_thenReturnSuccess() throws ParseException {
    // GIVEN
    FlatFareRequest flatFareRequest = getFlatFareRequest();

    FlatFareHoliday holiday = FlatFareHoliday.builder().date("2023-09-20 00:00:00").build();
    List<FlatFareHoliday> flatFareHolidayList = new ArrayList<>();
    flatFareHolidayList.add(holiday);

    // THEN
    FlatFareConfigSet flatFareConfigSet = getFlatFareConfigSet();
    flatFareConfigSet.setPrefixKey(RedisKeyConstant.EST_LIMO_LIVE_TRAFFIC_KEY_PREFIX);
    flatFareConfigSet.setBookingFeeList(initBookingFeeList());
    flatFareConfigSet.setAdditionalChargeList(new ArrayList<>());
    flatFareConfigSet.setEventSurgeAddressConfigList(new ArrayList<>());
    FlatFareVO flatFareVO = flatFareManager.computeTotalFare(flatFareConfigSet, flatFareRequest);

    assertTrue(
        flatFareVO.getFlagDown() == 0.5
            && flatFareVO.getTier1Fare() == 5.63
            && flatFareVO.getTier2Fare() == 8.13
            && flatFareVO.getWaitTimeFare() == 6.67
            && flatFareVO.getPeakHrFare() == 5.23
            && flatFareVO.getMidNightFare() == 5.23
            && FlatfareConstants.OWT_PDT_ID.equalsIgnoreCase(flatFareVO.getPdtId()));
  }

  @Test
  void givenNormalCondition_whenCreateBookingFeeListRequest_thenSuccess() throws ParseException {
    String startDateString = "09/20/2023";
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy");
    Date reqDate = df.parse(startDateString);

    FlatFareHoliday holiday = FlatFareHoliday.builder().date("2023-09-20 00:00:00").build();
    List<FlatFareHoliday> flatFareHolidayList = new ArrayList<>();
    flatFareHolidayList.add(holiday);

    FlatFareRequest flatFareRequest =
        FlatFareRequest.builder()
            .vehTypeIdList(List.of(1, 2))
            .jobType("jobType")
            .requestDate(reqDate)
            .build();

    assertDoesNotThrow(
        () ->
            flatFareManager.createBookingFeeListRequest(
                flatFareRequest, CommonConfigSet.builder().build(), flatFareHolidayList));
  }

  @Test
  void givenNormalCondition_whenInitBookingFeeRequestList_thenSuccess() throws ParseException {
    List<Integer> vehTypeIdList = List.of(0, 1);
    CommonConfigSet commonConfigSet = CommonConfigSet.builder().limoFlatFareVehIds(",1,").build();

    assertDoesNotThrow(
        () -> flatFareManager.initBookingFeeRequestList(vehTypeIdList, commonConfigSet));
  }

  @Test
  void givenLimo_whenCreatePlatformFeeListRequest_thenSuccess() {
    FlatFareRequest flatFareRequest =
        FlatFareRequest.builder()
            .vehTypeIdList(List.of(0, 1))
            .bookingChannel("BookingChannel")
            .build();
    CommonConfigSet commonConfigSet = CommonConfigSet.builder().limoFlatFareVehIds(",1,").build();

    PlatformFeeListRequest actual =
        flatFareManager.createPlatformFeeListRequest(flatFareRequest, commonConfigSet);
    assertEquals(6, actual.getPlatformFeeRequestList().size());
  }

  private List<LocationSurchargeConfig> getLocationSurchargeConfig() throws ParseException {
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy hh:mm:ss");
    String dateString = "09/20/2023 01:00:00";
    var reqDate = df.parse(dateString);

    LocalTime currentTime = DateUtils.convertToLocalTime(reqDate);
    return List.of(
        LocationSurchargeConfig.builder()
            .addressRef("1000")
            .startTime(currentTime)
            .endTime(currentTime)
            .productId("STD001")
            .chargeBy("PICKUP")
            .surchargeValue(0.3)
            .applicableDays("HOL")
            .dayIndicator("HOL")
            .locationName("Changi Airport")
            .build());
  }

  private List<BookingFare> getListBookingFareConfig() throws ParseException {
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy hh:mm:ss");
    String dateString = "09/20/2023 00:00:00";
    var reqDate = df.parse(dateString);

    LocalTime currentTime = DateUtils.convertToLocalTime(reqDate);

    String dateEndString = "09/20/2023 23:59:59";
    var reqEndDate = df.parse(dateEndString);

    LocalTime currentEndTime = DateUtils.convertToLocalTime(reqEndDate);
    return List.of(
        BookingFare.builder()
            .productId(FlatfareConstants.OWT_PDT_ID)
            .vehTypeId("100")
            .tariffTypeCode(FlatfareConstants.TARIFF_PDT)
            .fareAmt(10.0)
            .startTime(currentTime)
            .endTime(currentEndTime)
            .applicableDays("HOL")
            .build());
  }

  private FlatFareRequest getFlatFareRequest() throws ParseException {
    //    DateFormat df = new SimpleDateFormat("MM/dd/yyyy hh:mm:ss");
    //    String dateString = "09/20/2023 01:00:00";
    //    Date reqDate = df.parse(dateString);

    Date reqDateUTC = Date.from(Instant.parse("2023-09-19T17:00:00Z"));

    return FlatFareRequest.builder()
        .requestDate(reqDateUTC)
        .vehTypeId(1)
        .ett(1200)
        .routingDistance(14000)
        .originZoneId("01")
        .originAddressRef("1000")
        .originAddressLat(1.0000001)
        .originAddressLng(1.0000001)
        .destZoneId("02")
        .destAddressRef("2000")
        .destAddressLat(2.0000002)
        .destAddressLng(2.0000002)
        .intermediateZoneId("03")
        .intermediateAddrRef("3000")
        .intermediateAddrLat(3.0000003)
        .intermediateAddrLng(3.0000003)
        .build();
  }

  private FlatFareConfigSet getFlatFareConfigSet() throws ParseException {
    Map<String, String> peakHourRate0 = new HashMap<>();
    peakHourRate0.put("EST_LIMO_LIVE_TRAFFIC_PEAK_HOUR_DAYS_0", "MON,TUE,WED,THU,FRI,HOL");
    peakHourRate0.put("EST_LIMO_LIVE_TRAFFIC_PEAK_HOUR_END_TIME_0", "01:30:00");
    peakHourRate0.put("EST_LIMO_LIVE_TRAFFIC_PEAK_HOUR_RATE_0", "0.25");
    peakHourRate0.put("EST_LIMO_LIVE_TRAFFIC_PEAK_HOUR_START_TIME_0", "00:30:00");
    peakHourRate0.put("EST_LIMO_LIVE_TRAFFIC_PEAK_HOUR_STEPDOWN_10mins_0", "0.15");
    peakHourRate0.put("EST_LIMO_LIVE_TRAFFIC_PEAK_HOUR_STEPDOWN_15mins_0", "0.2");
    peakHourRate0.put("EST_LIMO_LIVE_TRAFFIC_PEAK_HOUR_STEPDOWN_5mins_0", "0.1");
    peakHourRate0.put("EST_LIMO_LIVE_TRAFFIC_PEAK_HOUR_STEPUP_10mins_0", "0.15");
    peakHourRate0.put("EST_LIMO_LIVE_TRAFFIC_PEAK_HOUR_STEPUP_15mins_0", "0.1");
    peakHourRate0.put("EST_LIMO_LIVE_TRAFFIC_PEAK_HOUR_STEPUP_5mins_0", "0.2");
    List<Map<String, String>> peakHourRateList = List.of(peakHourRate0);

    Map<String, String> midnightRate0 = new HashMap<>();
    midnightRate0.put("EST_LIMO_LIVE_TRAFFIC_MID_NIGHT_DAYS_0", "MON,TUE,WED,THU,FRI,HOL");
    midnightRate0.put("EST_LIMO_LIVE_TRAFFIC_MID_NIGHT_END_TIME_0", "01:30:00");
    midnightRate0.put("EST_LIMO_LIVE_TRAFFIC_MID_NIGHT_RATE_0", "0.25");
    midnightRate0.put("EST_LIMO_LIVE_TRAFFIC_MID_NIGHT_START_TIME_0", "00:30:00");
    midnightRate0.put("EST_LIMO_LIVE_TRAFFIC_MID_NIGHT_STEPDOWN_10mins_0", "0.15");
    midnightRate0.put("EST_LIMO_LIVE_TRAFFIC_MID_NIGHT_STEPDOWN_15mins_0", "0.2");
    midnightRate0.put("EST_LIMO_LIVE_TRAFFIC_MID_NIGHT_STEPDOWN_5mins_0", "0.1");
    midnightRate0.put("EST_LIMO_LIVE_TRAFFIC_MID_NIGHT_STEPUP_10mins_0", "0.15");
    midnightRate0.put("EST_LIMO_LIVE_TRAFFIC_MID_NIGHT_STEPUP_15mins_0", "0.1");
    midnightRate0.put("EST_LIMO_LIVE_TRAFFIC_MID_NIGHT_STEPUP_5mins_0", "0.2");
    List<Map<String, String>> midnightRateList = List.of(midnightRate0);

    String flagDownRate = "0.5";

    String tierPerCountFare = "400";
    String tierPerCountMeter = "0.25";
    String tierStartDistance = "1000";
    String tierEndDistance = "10000";
    TierFare tierFare =
        TierFare.builder()
            .perCountMeter(tierPerCountFare)
            .perCountFare(tierPerCountMeter)
            .startDistance(tierStartDistance)
            .endDistance(tierEndDistance)
            .build();

    String totalFareEstimateLF = "1";
    String totalFareEstimateRT = "1";
    EstimateRateConfig estimateRateConfig =
        EstimateRateConfig.builder()
            .totalFareEstimateLF(totalFareEstimateLF)
            .totalFareEstimateRT(totalFareEstimateRT)
            .build();

    String durationUnitConfig = "45";
    String durationRateConfig = "0.25";

    String maxFlatFareCap = "200.0";

    String startDateString = "09/20/2023";
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy");
    var reqDate = df.parse(startDateString);
    LocalTime currentTime = DateUtils.convertToLocalTime(reqDate);
    List<LocationSurchargeConfig> locationSurchargeConfigList =
        Stream.concat(
                initLocSurchargeNormalDay(currentTime).stream(),
                initLocSurchargeHOL(currentTime).stream())
            .toList();

    return FlatFareConfigSet.builder()
        .peakHoursRates(peakHourRateList)
        .midnightHoursRates(midnightRateList)
        .flagDownRate(flagDownRate)
        .tier1Fare(tierFare)
        .tier2Fare(tierFare)
        .estimateRateConfig(estimateRateConfig)
        .durationUnitConfig(durationUnitConfig)
        .durationRateConfig(durationRateConfig)
        .maxFlatFareCap(maxFlatFareCap)
        .locationSurchargeConfigList(locationSurchargeConfigList)
        .additionalChargeList(List.of(0.1, 0.2, 0.3, 0.4))
        .build();
  }

  private List<LocationSurchargeConfig> initLocSurchargeHOL(LocalTime currentTime) {
    var locItemHOL1 =
        LocationSurchargeConfig.builder()
            .addressRef("origin")
            .startTime(currentTime)
            .endTime(currentTime)
            .productId("FLAT-001")
            .chargeBy("PICKUP")
            .surchargeValue(0.3)
            .applicableDays("HOL")
            .dayIndicator("HOL")
            .locationName("Changi Airport")
            .build();
    var locItemHOL2 =
        LocationSurchargeConfig.builder()
            .addressRef("dest")
            .startTime(currentTime)
            .endTime(currentTime)
            .productId("STD001")
            .chargeBy("DEST")
            .surchargeValue(0.3)
            .applicableDays("HOL")
            .dayIndicator("HOL")
            .locationName("Changi Airport")
            .build();
    var locItemHOL3 =
        LocationSurchargeConfig.builder()
            .addressRef("inter")
            .startTime(currentTime)
            .endTime(currentTime)
            .productId("STD001")
            .chargeBy("DEST")
            .surchargeValue(0.3)
            .applicableDays("HOL")
            .dayIndicator("HOL")
            .locationName("Changi Airport")
            .build();

    return List.of(locItemHOL1, locItemHOL2, locItemHOL3);
  }

  private List<LocationSurchargeConfig> initLocSurchargeNormalDay(LocalTime currentTime) {
    var locItemHOL1 =
        LocationSurchargeConfig.builder()
            .addressRef("origin")
            .startTime(currentTime)
            .endTime(currentTime)
            .productId("FLAT-001")
            .chargeBy("PICKUP")
            .surchargeValue(1.9)
            .applicableDays("WED")
            .dayIndicator("WED")
            .locationName("Changi Airport")
            .build();
    var locItemHOL2 =
        LocationSurchargeConfig.builder()
            .addressRef("dest")
            .startTime(currentTime)
            .endTime(currentTime)
            .productId("STD001")
            .chargeBy("DEST")
            .surchargeValue(1.2)
            .applicableDays("WED")
            .dayIndicator("WED")
            .locationName("Changi Airport")
            .build();
    var locItemHOL3 =
        LocationSurchargeConfig.builder()
            .addressRef("inter")
            .startTime(currentTime)
            .endTime(currentTime)
            .productId("STD001")
            .chargeBy("DEST")
            .surchargeValue(1.2)
            .applicableDays("WED")
            .dayIndicator("WED")
            .locationName("Changi Airport")
            .build();

    return List.of(locItemHOL1, locItemHOL2, locItemHOL3);
  }

  private List<BookingFeeItem> initBookingFeeList() {
    List<BookingFeeItem> bookingFeeList = new ArrayList<>();
    bookingFeeList.add(buildBookingFeeItem("StandardFlatFare", "FLAT-001", 0, 3.3));
    bookingFeeList.add(buildBookingFeeItem("EstStandardFlatFare", "STD001", 0, 2.2));
    bookingFeeList.add(buildBookingFeeItem("LimoFlatFare", "OWT-001", 1, 50));
    bookingFeeList.add(buildBookingFeeItem("EstStandardFlatFare", "STD001", 1, 10));
    bookingFeeList.add(buildBookingFeeItem("LimoFlatFare", "OWT-001", 100, 10));
    return bookingFeeList;
  }

  private BookingFeeItem buildBookingFeeItem(
      String flatFareType, String productId, int vehicleTypeId, double bookingFee) {
    return BookingFeeItem.builder()
        .flatFareType(flatFareType)
        .productId(productId)
        .vehicleTypeId(vehicleTypeId)
        .bookingFee(bookingFee)
        .build();
  }
}

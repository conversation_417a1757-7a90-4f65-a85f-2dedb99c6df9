package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.flatfare.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareConfigSet;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareHoliday;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareVO;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.LocationSurchargeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.FlatFareConfigService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.LocationSurchargeService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.CommonUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.DateUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.BookingFeeItem;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.FareService;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.DomainException;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Stream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class LimoFlatFareComputeImplTest {
  @Mock private LimoFlatFareComputeImpl limoFlatFare;

  @Mock private FlatFareConfigService configService;

  @Mock private FareService fareService;

  @Mock private LocationSurchargeService locationSurchargeService;

  SimpleDateFormat dateFormat;

  @BeforeEach
  void setUp() {
    dateFormat = new SimpleDateFormat(DateUtils.YYYYMMDD_HHMMSS_FORMAT);
    FlatFareConfigSet flatFareConfigSet = new FlatFareConfigSet();
    flatFareConfigSet.setPrefixKey(RedisKeyConstant.EST_LIMO_LIVE_TRAFFIC_KEY_PREFIX);
    flatFareConfigSet.setBookingFeeList(initBookingFeeList());
    flatFareConfigSet.setAdditionalChargeList(List.of(0.1, 0.2, 0.3, 0.4));
    limoFlatFare =
        new LimoFlatFareComputeImpl(
            flatFareConfigSet, configService, fareService, locationSurchargeService);
  }

  @Test
  void givenHolidayConfig_whenCalLocSurcharges_thenReturnSuccess() throws ParseException {
    // GIVEN
    String startDateString = "09/20/2023";
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy");
    Date reqDate = df.parse(startDateString);
    LocalTime currentTime = DateUtils.convertToLocalTime(reqDate);

    FlatFareHoliday holiday = FlatFareHoliday.builder().date("2023-09-20 00:00:00").build();
    List<FlatFareHoliday> flatFareHolidayList = new ArrayList<>();
    flatFareHolidayList.add(holiday);

    FlatFareRequest request =
        FlatFareRequest.builder()
            .originAddressRef("origin")
            .destAddressRef("dest")
            .intermediateAddrRef("inter")
            .requestDate(reqDate)
            .build();
    FlatFareVO flatfareVO = new FlatFareVO();
    flatfareVO.setFlatFareRequest(request);

    FlatFareConfigSet flatFareConfigSet =
        FlatFareConfigSet.builder()
            .locationSurchargeConfigList(
                Stream.concat(
                        initLocSurchargeNormalDay(currentTime).stream(),
                        initLocSurchargeHOL(currentTime).stream())
                    .toList())
            .build();
    limoFlatFare.setConfigSet(flatFareConfigSet);

    // THEN
    limoFlatFare.calLocSurcharges(flatfareVO);
    var actual = CommonUtils.roundToTwo(flatfareVO.getTotalLocSurCharge());
    var expected = 0.6;
    assertEquals(expected, actual);
  }

  @Test
  void givenNormalDayConfig_whenCalLocSurcharges_thenReturnSuccess() throws ParseException {
    // GIVEN
    String startDateString = "09/20/2023";
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy");
    var reqDate = df.parse(startDateString);
    LocalTime currentTime = DateUtils.convertToLocalTime(reqDate);

    FlatFareHoliday holiday = FlatFareHoliday.builder().date("2023-09-22 00:00:00").build();
    List<FlatFareHoliday> flatFareHolidayList = new ArrayList<>();
    flatFareHolidayList.add(holiday);

    var request =
        FlatFareRequest.builder()
            .originAddressRef("origin")
            .destAddressRef("dest")
            .intermediateAddrRef("inter")
            .requestDate(reqDate)
            .build();
    var flatfareVO = new FlatFareVO();
    flatfareVO.setFlatFareRequest(request);

    FlatFareConfigSet flatFareConfigSet =
        FlatFareConfigSet.builder()
            .locationSurchargeConfigList(initLocSurchargeNormalDay(currentTime))
            .build();
    limoFlatFare.setConfigSet(flatFareConfigSet);

    // THEN
    limoFlatFare.calLocSurcharges(flatfareVO);
    var actual = CommonUtils.roundToTwo(flatfareVO.getTotalLocSurCharge());
    var expected = 2.4;
    assertEquals(expected, actual);
  }

  private List<LocationSurchargeConfig> initLocSurchargeHOL(LocalTime currentTime) {
    var locItemHOL1 =
        LocationSurchargeConfig.builder()
            .addressRef("origin")
            .startTime(currentTime)
            .endTime(currentTime)
            .productId("STD001")
            .chargeBy("PICKUP")
            .surchargeValue(0.3)
            .applicableDays("HOL")
            .dayIndicator("HOL")
            .locationName("Changi Airport")
            .build();
    var locItemHOL2 =
        LocationSurchargeConfig.builder()
            .addressRef("dest")
            .startTime(currentTime)
            .endTime(currentTime)
            .productId("STD001")
            .chargeBy("DEST")
            .surchargeValue(0.3)
            .applicableDays("HOL")
            .dayIndicator("HOL")
            .locationName("Changi Airport")
            .build();
    var locItemHOL3 =
        LocationSurchargeConfig.builder()
            .addressRef("inter")
            .startTime(currentTime)
            .endTime(currentTime)
            .productId("STD001")
            .chargeBy("DEST")
            .surchargeValue(0.3)
            .applicableDays("HOL")
            .dayIndicator("HOL")
            .locationName("Changi Airport")
            .build();

    return List.of(locItemHOL1, locItemHOL2, locItemHOL3);
  }

  private List<LocationSurchargeConfig> initLocSurchargeNormalDay(LocalTime currentTime) {
    var locItemHOL1 =
        LocationSurchargeConfig.builder()
            .addressRef("origin")
            .startTime(currentTime)
            .endTime(currentTime)
            .productId("STD001")
            .chargeBy("PICKUP")
            .surchargeValue(1.2)
            .applicableDays("WED")
            .dayIndicator("WED")
            .locationName("Changi Airport")
            .build();
    var locItemHOL2 =
        LocationSurchargeConfig.builder()
            .addressRef("dest")
            .startTime(currentTime)
            .endTime(currentTime)
            .productId("STD001")
            .chargeBy("DEST")
            .surchargeValue(1.2)
            .applicableDays("WED")
            .dayIndicator("WED")
            .locationName("Changi Airport")
            .build();
    var locItemHOL3 =
        LocationSurchargeConfig.builder()
            .addressRef("inter")
            .startTime(currentTime)
            .endTime(currentTime)
            .productId("STD001")
            .chargeBy("DEST")
            .surchargeValue(1.2)
            .applicableDays("WED")
            .dayIndicator("WED")
            .locationName("Changi Airport")
            .build();

    return List.of(locItemHOL1, locItemHOL2, locItemHOL3);
  }

  @Test
  void calAdditionalSurCharge_noParam_success() {
    FlatFareRequest request = FlatFareRequest.builder().vehTypeId(100).build();
    FlatFareVO flatFareVO = new FlatFareVO();
    Double expected = 1.0;
    flatFareVO.setFlatFareRequest(request);
    limoFlatFare.calAdditionalSurCharge(flatFareVO);
    var actual = flatFareVO.getAdditionalSurcharge();

    assertEquals(expected, actual);
  }

  @Test
  void calAdditionalSurCharge_noParam_throwException() {
    limoFlatFare.configSet.setAdditionalChargeList(null);
    FlatFareRequest request = FlatFareRequest.builder().vehTypeId(100).build();
    FlatFareVO flatFareVO = new FlatFareVO();
    flatFareVO.setFlatFareRequest(request);
    assertThrows(DomainException.class, () -> limoFlatFare.calAdditionalSurCharge(flatFareVO));
  }

  @Test
  void calEttFare_noParam_success() {
    var request = FlatFareRequest.builder().ett(1200L).build();

    FlatFareConfigSet flatFareConfigSet =
        FlatFareConfigSet.builder().durationUnitConfig("45").durationRateConfig("0.25").build();
    limoFlatFare.setConfigSet(flatFareConfigSet);

    var flatfareVO = new FlatFareVO();
    flatfareVO.setFlatFareRequest(request);
    limoFlatFare.calEttFare(flatfareVO);

    var actual = CommonUtils.roundToTwo(flatfareVO.getWaitTimeFare());
    var expected = 6.67;

    assertEquals(expected, actual);
  }

  @Test
  void calEttFare_noParam_ettZero() {
    var request = FlatFareRequest.builder().ett(1200L).build();

    FlatFareConfigSet flatFareConfigSet =
        FlatFareConfigSet.builder().durationUnitConfig("45").durationRateConfig("0").build();
    limoFlatFare.setConfigSet(flatFareConfigSet);

    var flatfareVO = new FlatFareVO();
    flatfareVO.setFlatFareRequest(request);
    limoFlatFare.calEttFare(flatfareVO);

    var actual = CommonUtils.roundToTwo(flatfareVO.getWaitTimeFare());
    var expected = 0.0;

    assertEquals(expected, actual);
  }

  @Test
  void calEttFare_noParam_error() {
    var request = FlatFareRequest.builder().ett(1200L).vehTypeId(1).build();
    var flatfareVO = new FlatFareVO();
    flatfareVO.setFlatFareRequest(request);

    assertThrows(DomainException.class, () -> limoFlatFare.calEttFare(flatfareVO));
  }

  @Test
  void givenActualPDTFlatFeeGreaterThenMaxCap_whenFinalizeTotalAmount_thenReturnSuccess() {
    // GIVEN
    FlatFareRequest request = FlatFareRequest.builder().vehTypeId(1).build();
    FlatFareConfigSet flatFareConfigSet =
        FlatFareConfigSet.builder().maxFlatFareCap("200.0").build();
    limoFlatFare.setConfigSet(flatFareConfigSet);

    FlatFareVO flatfareVO = new FlatFareVO();
    flatfareVO.setFlatFareRequest(request);
    flatfareVO.setActualPDTFlatFee(250.0);
    limoFlatFare.finalizeTotalAmount(flatfareVO);

    // THEN
    BigDecimal actual = flatfareVO.getTotalFare();
    BigDecimal expected = CommonUtils.roundToTwoBD(200.0);
    assertEquals(expected, actual);
  }

  @Test
  void givenActualPDTFlatFeeLessThenMaxCap_whenFinalizeTotalAmount_thenReturnSuccess() {
    // GIVEN
    FlatFareRequest request = FlatFareRequest.builder().vehTypeId(1).build();
    FlatFareConfigSet flatFareConfigSet =
        FlatFareConfigSet.builder().maxFlatFareCap("200.0").build();
    limoFlatFare.setConfigSet(flatFareConfigSet);

    FlatFareVO flatfareVO = new FlatFareVO();
    flatfareVO.setFlatFareRequest(request);
    flatfareVO.setActualPDTFlatFee(150.0);
    limoFlatFare.finalizeTotalAmount(flatfareVO);

    // THEN
    BigDecimal actual = flatfareVO.getTotalFare();
    BigDecimal expected = CommonUtils.roundToTwoBD(150.0);
    assertEquals(expected, actual);
  }

  @Test
  void givenActualPDTFlatFeeNull_whenFinalizeTotalAmount_thenReturnSuccess() {
    // GIVEN
    FlatFareRequest request = FlatFareRequest.builder().vehTypeId(1).build();
    FlatFareConfigSet flatFareConfigSet =
        FlatFareConfigSet.builder().maxFlatFareCap("200.0").build();
    limoFlatFare.setConfigSet(flatFareConfigSet);

    FlatFareVO flatfareVO = new FlatFareVO();
    flatfareVO.setFlatFareRequest(request);
    flatfareVO.setActualPDTFlatFee(null);
    limoFlatFare.finalizeTotalAmount(flatfareVO);

    BigDecimal actual = flatfareVO.getTotalFare();
    BigDecimal expected = CommonUtils.roundToTwoBD(0.0);

    // THEN
    assertEquals(expected, actual);
  }

  @Test
  void givenConfigNotDouble_whenFinalizeTotalAmount_thenThrowException() {
    // GIVEN
    FlatFareRequest request = FlatFareRequest.builder().vehTypeId(1).build();
    FlatFareConfigSet flatFareConfigSet =
        FlatFareConfigSet.builder().maxFlatFareCap("String").build();
    limoFlatFare.setConfigSet(flatFareConfigSet);
    FlatFareVO flatfareVO = new FlatFareVO();
    flatfareVO.setFlatFareRequest(request);

    // THEN
    assertThrows(DomainException.class, () -> limoFlatFare.finalizeTotalAmount(flatfareVO));
  }

  @Test
  void givenNormalConfig_whenCalAdditionalSurCharge_thenReturnSuccess() {
    // GIVEN
    FlatFareRequest request = FlatFareRequest.builder().vehTypeId(100).build();
    FlatFareVO flatFareVO = new FlatFareVO();
    flatFareVO.setFlatFareRequest(request);

    // THEN
    limoFlatFare.calAdditionalSurCharge(flatFareVO);
    var actual = flatFareVO.getAdditionalSurcharge();
    var expected = 1;
    assertEquals(expected, actual);
  }

  @Test
  void givenNullConfig_whenCalAdditionalSurCharge_thenThrowsException() {
    // GIVEN
    FlatFareRequest request = FlatFareRequest.builder().vehTypeId(100).build();
    FlatFareVO flatFareVO = new FlatFareVO();
    flatFareVO.setFlatFareRequest(request);
    limoFlatFare.configSet.setAdditionalChargeList(null);

    // THEN
    assertThrows(DomainException.class, () -> limoFlatFare.calAdditionalSurCharge(flatFareVO));
  }

  @Test
  void givenNormalCondition_whenCalBookingFee_thenReturnSuccess() throws ParseException {
    String dateTimeString = "2023-02-06 01:00:00";
    FlatFareVO flatfareVO = new FlatFareVO();
    flatfareVO.setFlatFareRequest(
        FlatFareRequest.builder()
            .vehTypeId(1)
            .requestDate(dateFormat.parse(dateTimeString))
            .build());
    flatfareVO.setPdtId("OWT-001");
    limoFlatFare.calBookingFee(flatfareVO);
    assertEquals(10, flatfareVO.getBookingFee());
    assertEquals(50, flatfareVO.getActualPDTFlatFee());
  }

  private List<BookingFeeItem> initBookingFeeList() {
    List<BookingFeeItem> bookingFeeList = new ArrayList<>();
    bookingFeeList.add(buildBookingFeeItem("StandardFlatFare", "FLAT-001", 0, 3.3));
    bookingFeeList.add(buildBookingFeeItem("EstStandardFlatFare", "STD001", 0, 2.2));
    bookingFeeList.add(buildBookingFeeItem("LimoFlatFare", "OWT-001", 1, 50));
    bookingFeeList.add(buildBookingFeeItem("EstStandardFlatFare", "STD001", 1, 10));
    return bookingFeeList;
  }

  private BookingFeeItem buildBookingFeeItem(
      String flatFareType, String productId, int vehicleTypeId, double bookingFee) {
    return BookingFeeItem.builder()
        .flatFareType(flatFareType)
        .productId(productId)
        .vehicleTypeId(vehicleTypeId)
        .bookingFee(bookingFee)
        .build();
  }
}

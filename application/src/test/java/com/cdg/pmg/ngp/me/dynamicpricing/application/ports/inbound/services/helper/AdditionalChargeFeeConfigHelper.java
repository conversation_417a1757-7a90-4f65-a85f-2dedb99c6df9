package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.helper;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.additionalcharge.DriverFeeAdditionalChargeProcessor;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.AdditionalChargeFeeConfigResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AdditionalChargeFeeConfigHelper {
  private AdditionalChargeFeeConfigHelper() {}

  public static final String DRIVER_FEE = "DRIVER_FEE";

  public static Map<String, List<AdditionalChargeFeeConfigResponse>> generateDriverFeeConfig() {

    Map<String, List<AdditionalChargeFeeConfigResponse>> driverFeeConfig = new HashMap<>();
    List<AdditionalChargeFeeConfigResponse> driverFeeConfigList = new ArrayList<>();
    driverFeeConfig.put(DRIVER_FEE, driverFeeConfigList);

    AdditionalChargeFeeConfigResponse chargeThreshold =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(DRIVER_FEE)
            .chargeKey(DriverFeeAdditionalChargeProcessor.CHARGE_THRESHOLD)
            .chargeValue(16.00)
            .chargeDescription(
                "DRIVER_FEE, charge_threshold, The threshold for trigger the additional charge")
            .build();

    AdditionalChargeFeeConfigResponse chargeLowerValue =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(DRIVER_FEE)
            .chargeKey(DriverFeeAdditionalChargeProcessor.CHARGE_LOWER_VALUE)
            .chargeValue(0.30)
            .chargeDescription(
                "DRIVER_FEE, charge_lower_value, The lower bond value for the additional charge")
            .build();

    AdditionalChargeFeeConfigResponse chargeUpperValue =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(DRIVER_FEE)
            .chargeKey(DriverFeeAdditionalChargeProcessor.CHARGE_UPPER_VALUE)
            .chargeValue(0.50)
            .chargeDescription(
                "DRIVER_FEE, charge_upper_value, The upper bond value for the additional charge")
            .build();

    AdditionalChargeFeeConfigResponse isCountInTotalfare =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(DRIVER_FEE)
            .chargeKey(DriverFeeAdditionalChargeProcessor.IS_COUNT_IN_TOTALFARE)
            .chargeValue(0.00)
            .chargeDescription(
                "DRIVER_FEE, is_count_in_totalfare, The additional charge value is counted into the total fare")
            .chargeFormula("true")
            .build();

    driverFeeConfigList.add(chargeThreshold);
    driverFeeConfigList.add(chargeLowerValue);
    driverFeeConfigList.add(chargeUpperValue);
    driverFeeConfigList.add(isCountInTotalfare);

    return driverFeeConfig;
  }

  public static Map<String, List<AdditionalChargeFeeConfigResponse>>
      generateDriverFeeConfigWithoutIsCountInTotalfare() {
    Map<String, List<AdditionalChargeFeeConfigResponse>> driverFeeConfig = new HashMap<>();
    List<AdditionalChargeFeeConfigResponse> driverFeeConfigList = new ArrayList<>();
    driverFeeConfig.put(DRIVER_FEE, driverFeeConfigList);

    AdditionalChargeFeeConfigResponse chargeThreshold =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(DRIVER_FEE)
            .chargeKey(DriverFeeAdditionalChargeProcessor.CHARGE_THRESHOLD)
            .chargeValue(16.00)
            .chargeDescription(
                "DRIVER_FEE, charge_threshold, The threshold for trigger the additional charge")
            .build();

    AdditionalChargeFeeConfigResponse chargeLowerValue =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(DRIVER_FEE)
            .chargeKey(DriverFeeAdditionalChargeProcessor.CHARGE_LOWER_VALUE)
            .chargeValue(0.30)
            .chargeDescription(
                "DRIVER_FEE, charge_lower_value, The lower bond value for the additional charge")
            .build();

    AdditionalChargeFeeConfigResponse chargeUpperValue =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(DRIVER_FEE)
            .chargeKey(DriverFeeAdditionalChargeProcessor.CHARGE_UPPER_VALUE)
            .chargeValue(0.50)
            .chargeDescription(
                "DRIVER_FEE, charge_upper_value, The upper bond value for the additional charge")
            .build();

    driverFeeConfigList.add(chargeThreshold);
    driverFeeConfigList.add(chargeLowerValue);
    driverFeeConfigList.add(chargeUpperValue);
    return driverFeeConfig;
  }

  public static Map<String, List<AdditionalChargeFeeConfigResponse>>
      generateDriverFeeConfigIsCountInTotalfareFalse() {
    Map<String, List<AdditionalChargeFeeConfigResponse>> driverFeeConfig = new HashMap<>();
    List<AdditionalChargeFeeConfigResponse> driverFeeConfigList = new ArrayList<>();
    driverFeeConfig.put(DRIVER_FEE, driverFeeConfigList);

    AdditionalChargeFeeConfigResponse chargeThreshold =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(DRIVER_FEE)
            .chargeKey(DriverFeeAdditionalChargeProcessor.CHARGE_THRESHOLD)
            .chargeValue(16.00)
            .chargeDescription(
                "DRIVER_FEE, charge_threshold, The threshold for trigger the additional charge")
            .build();

    AdditionalChargeFeeConfigResponse chargeLowerValue =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(DRIVER_FEE)
            .chargeKey(DriverFeeAdditionalChargeProcessor.CHARGE_LOWER_VALUE)
            .chargeValue(0.30)
            .chargeDescription(
                "DRIVER_FEE, charge_lower_value, The lower bond value for the additional charge")
            .build();

    AdditionalChargeFeeConfigResponse chargeUpperValue =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(DRIVER_FEE)
            .chargeKey(DriverFeeAdditionalChargeProcessor.CHARGE_UPPER_VALUE)
            .chargeValue(0.50)
            .chargeDescription(
                "DRIVER_FEE, charge_upper_value, The upper bond value for the additional charge")
            .build();

    AdditionalChargeFeeConfigResponse isCountInTotalfare =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(DRIVER_FEE)
            .chargeKey(DriverFeeAdditionalChargeProcessor.IS_COUNT_IN_TOTALFARE)
            .chargeValue(0.00)
            .chargeDescription(
                "DRIVER_FEE, is_count_in_totalfare, The additional charge value is counted into the total fare")
            .chargeFormula("false")
            .build();
    driverFeeConfigList.add(chargeThreshold);
    driverFeeConfigList.add(chargeLowerValue);
    driverFeeConfigList.add(chargeUpperValue);
    driverFeeConfigList.add(isCountInTotalfare);
    return driverFeeConfig;
  }

  public static Map<String, List<AdditionalChargeFeeConfigResponse>>
      generateDriverFeeConfigOnlyIsCountInTotalfareTrue() {
    Map<String, List<AdditionalChargeFeeConfigResponse>> driverFeeConfig = new HashMap<>();
    List<AdditionalChargeFeeConfigResponse> driverFeeConfigList = new ArrayList<>();
    driverFeeConfig.put(DRIVER_FEE, driverFeeConfigList);

    AdditionalChargeFeeConfigResponse isCountInTotalfare =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(DRIVER_FEE)
            .chargeKey(DriverFeeAdditionalChargeProcessor.IS_COUNT_IN_TOTALFARE)
            .chargeValue(0.00)
            .chargeDescription(
                "DRIVER_FEE, is_count_in_totalfare, The additional charge value is counted into the total fare")
            .chargeFormula("true")
            .build();
    driverFeeConfigList.add(isCountInTotalfare);
    return driverFeeConfig;
  }

  public static Map<String, List<AdditionalChargeFeeConfigResponse>>
      generateDriverFeeConfigWithEmptyValue() {
    Map<String, List<AdditionalChargeFeeConfigResponse>> driverFeeConfig = new HashMap<>();
    List<AdditionalChargeFeeConfigResponse> driverFeeConfigList = new ArrayList<>();
    driverFeeConfig.put(DRIVER_FEE, driverFeeConfigList);

    AdditionalChargeFeeConfigResponse chargeThreshold =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(DRIVER_FEE)
            .chargeKey(DriverFeeAdditionalChargeProcessor.CHARGE_THRESHOLD)
            .chargeDescription(
                "DRIVER_FEE, charge_threshold, The threshold for trigger the additional charge")
            .build();

    AdditionalChargeFeeConfigResponse chargeLowerValue =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(DRIVER_FEE)
            .chargeKey(DriverFeeAdditionalChargeProcessor.CHARGE_LOWER_VALUE)
            .chargeDescription(
                "DRIVER_FEE, charge_lower_value, The lower bond value for the additional charge")
            .build();

    AdditionalChargeFeeConfigResponse chargeUpperValue =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(DRIVER_FEE)
            .chargeKey(DriverFeeAdditionalChargeProcessor.CHARGE_UPPER_VALUE)
            .chargeDescription(
                "DRIVER_FEE, charge_upper_value, The upper bond value for the additional charge")
            .build();

    AdditionalChargeFeeConfigResponse isCountInTotalfare =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(DRIVER_FEE)
            .chargeKey(DriverFeeAdditionalChargeProcessor.IS_COUNT_IN_TOTALFARE)
            .chargeValue(0.00)
            .chargeDescription(
                "DRIVER_FEE, is_count_in_totalfare, The additional charge value is counted into the total fare")
            .chargeFormula("true")
            .build();
    driverFeeConfigList.add(chargeThreshold);
    driverFeeConfigList.add(chargeLowerValue);
    driverFeeConfigList.add(chargeUpperValue);
    driverFeeConfigList.add(isCountInTotalfare);
    return driverFeeConfig;
  }
}

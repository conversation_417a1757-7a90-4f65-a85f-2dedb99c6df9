package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import static com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants.LIMO_FLAT_FARE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.DynamicSurges;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareHoliday;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.mapper.DynamicSurgesMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.mapper.LocationSurchargeMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.DynpConfigCacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.FlatFareConfigService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.flatfare.impl.FlatFareManager;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.BookingFareUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.DateUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.BookingFeeRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.BookingFeeResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.CacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.FareService;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.BookARideConfigsConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.LocationSurchargeConfigEntity;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.time.LocalTime;
import java.time.OffsetDateTime;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * BookARideConfigServiceImplTest is a unit test class that ensures the proper functioning of the
 * BookARideConfigServiceImpl class. This class uses MockitoExtension to mock dependencies and
 * facilitate isolated testing of service logic. It simulates the retrieval of this data from a
 * mocked cache layer, verifying the service's ability to handle and process surge pricing data.
 * This test class is crucial for ensuring the reliability and correctness of the dynamic surge
 * pricing retrieval logic in the BookARideConfigService implementation, which is a key component in
 * the dynamic pricing module of the ride-booking system.
 */
@ExtendWith(MockitoExtension.class)
class BookARideConfigServiceImplTest {

  @Mock private BookARideConfigServiceImpl service;
  @Mock private DynpConfigCacheService dynpConfigCacheService;
  @Mock private CacheService cacheService;
  @Mock private FlatFareConfigService configService;
  @Mock private DynamicSurgesMapper dynamicSurgesMapper;
  @Mock private LocationSurchargeMapper locationSurchargeMapper;

  @Mock private FareService fareService;

  @Mock private FlatFareManager flatFareManager;

  private Method getProductListMethod;
  private Method getVehicleGroupListMethod;
  private Method determineFareTypeMethod;

  @BeforeEach
  void setUp() throws NoSuchMethodException {
    service =
        new BookARideConfigServiceImpl(
            dynpConfigCacheService,
            cacheService,
            dynamicSurgesMapper,
            configService,
            fareService,
            flatFareManager,
            locationSurchargeMapper);
    getProductListMethod =
        BookARideConfigServiceImpl.class.getDeclaredMethod("getProductList", Map.class);
    getProductListMethod.setAccessible(true);

    getVehicleGroupListMethod =
        BookARideConfigServiceImpl.class.getDeclaredMethod("getVehicleGroupList", Map.class);
    getVehicleGroupListMethod.setAccessible(true);

    determineFareTypeMethod =
        BookARideConfigServiceImpl.class.getDeclaredMethod(
            "determineFareType", String.class, String.class);
    determineFareTypeMethod.setAccessible(true);
  }

  @Test
  void givenHolidayConfigs_whenGetHolidaysFromCache_thenReturnListString() {
    // WHEN
    when(dynpConfigCacheService.getListObjectConfigFromCache(Mockito.any(), Mockito.any()))
        .thenReturn(List.of(new FlatFareHoliday()));
    // THEN
    assertNotNull(service.getHolidayConfigs());
  }

  @Test
  void givenMockCacheData_whenGetDynpSurges_thenReturnListDynamicSurgesEntity() {
    when(cacheService.getListValue(Mockito.any(), Mockito.any()))
        .thenReturn(List.of(new DynamicSurges()));
    assertNotNull(service.getDynpSurges());
  }

  @Test
  void
      givenLocationSurchargeConfigs_whenGetLocationSurchargeConfigsOnHoliday_thenReturnLocSurEntity() {
    com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.LocationSurchargeConfig
        locationSurchargeConfigDTO =
            new com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos
                .LocationSurchargeConfig();
    locationSurchargeConfigDTO.setLocationId(4);
    locationSurchargeConfigDTO.setLocationName("air port");
    locationSurchargeConfigDTO.setSurchargeValue(4.0);
    locationSurchargeConfigDTO.setChargeBy("PICKUP");
    locationSurchargeConfigDTO.setAddressRef("5394324");
    locationSurchargeConfigDTO.setDayIndicator("MON");
    locationSurchargeConfigDTO.setFareType("FLAT");
    locationSurchargeConfigDTO.setProductId("STD");
    locationSurchargeConfigDTO.setStartTime(LocalTime.of(0, 0, 0));
    locationSurchargeConfigDTO.setEndTime(LocalTime.of(23, 59, 59));

    LocationSurchargeConfigEntity locationSurchargeConfigEntity =
        new LocationSurchargeConfigEntity();
    locationSurchargeConfigEntity.setLocationId(4);
    locationSurchargeConfigEntity.setLocationName("air port");
    locationSurchargeConfigEntity.setSurchargeValue(4.0);
    locationSurchargeConfigEntity.setChargeBy("PICKUP");
    locationSurchargeConfigEntity.setAddressRef("5394324");
    locationSurchargeConfigEntity.setDayIndicator("MON");
    locationSurchargeConfigEntity.setFareType("FLAT");
    locationSurchargeConfigEntity.setProductId("STD");
    locationSurchargeConfigEntity.setStartTime("00:00:00");
    locationSurchargeConfigEntity.setEndTime("23:59:59");

    // Arrange
    Date currentDate = new Date(2023 - 1900, 11 - 1, 27);
    List<String> holidayList =
        Arrays.asList("27-11-2023 00:00:00", "01-05-2023 00:00:00", "25-12-2023 00:00:00");
    String dayInWeekReq = DateUtils.toDayOfWeekShortUpperCase(currentDate);
    List<com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.LocationSurchargeConfig>
        dayConfigs = List.of(locationSurchargeConfigDTO);
    when(configService.getLocationSurchargeConfig(
            FlatfareConstants.LOC_SURC_KEY_PREFIX + dayInWeekReq))
        .thenReturn(dayConfigs);
    when(locationSurchargeMapper.mapToLocationSurchargeConfigEntity(locationSurchargeConfigDTO))
        .thenReturn(locationSurchargeConfigEntity);
    // Act
    List<LocationSurchargeConfigEntity> result =
        service.getLocationSurchargeConfigs(currentDate, holidayList);
    // Assert
    assertNotNull(result);
    assertEquals(1, result.size());
  }

  @Test
  void
      givenLocationSurchargeConfigs_whenGetLocationSurchargeConfigsOnNotHoliday_thenReturnLocSurEntity() {
    com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.LocationSurchargeConfig
        locationSurchargeConfigDTO =
            new com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos
                .LocationSurchargeConfig();
    locationSurchargeConfigDTO.setLocationId(4);
    locationSurchargeConfigDTO.setLocationName("air port");
    locationSurchargeConfigDTO.setSurchargeValue(4.0);
    locationSurchargeConfigDTO.setChargeBy("PICKUP");
    locationSurchargeConfigDTO.setAddressRef("5394324");
    locationSurchargeConfigDTO.setDayIndicator("MON");
    locationSurchargeConfigDTO.setFareType("FLAT");
    locationSurchargeConfigDTO.setProductId("STD");
    locationSurchargeConfigDTO.setStartTime(LocalTime.of(0, 0, 0));
    locationSurchargeConfigDTO.setEndTime(LocalTime.of(23, 59, 59));

    LocationSurchargeConfigEntity locationSurchargeConfigEntity =
        new LocationSurchargeConfigEntity();
    locationSurchargeConfigEntity.setLocationId(4);
    locationSurchargeConfigEntity.setLocationName("air port");
    locationSurchargeConfigEntity.setSurchargeValue(4.0);
    locationSurchargeConfigEntity.setChargeBy("PICKUP");
    locationSurchargeConfigEntity.setAddressRef("5394324");
    locationSurchargeConfigEntity.setDayIndicator("MON");
    locationSurchargeConfigEntity.setFareType("FLAT");
    locationSurchargeConfigEntity.setProductId("STD");
    locationSurchargeConfigEntity.setStartTime("00:00:00");
    locationSurchargeConfigEntity.setEndTime("23:59:59");

    // Arrange
    Date currentDate = new Date(2023 - 1900, 11 - 1, 27);
    List<String> holidayList = Arrays.asList("01-05-2023 00:00:00", "25-12-2023 00:00:00");
    String dayInWeekReq = DateUtils.toDayOfWeekShortUpperCase(currentDate);
    List<com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.LocationSurchargeConfig>
        dayConfigs = List.of(locationSurchargeConfigDTO);
    when(configService.getLocationSurchargeConfig(
            FlatfareConstants.LOC_SURC_KEY_PREFIX + dayInWeekReq))
        .thenReturn(dayConfigs);
    when(locationSurchargeMapper.mapToLocationSurchargeConfigEntity(locationSurchargeConfigDTO))
        .thenReturn(locationSurchargeConfigEntity);
    // Act
    List<LocationSurchargeConfigEntity> result =
        service.getLocationSurchargeConfigs(currentDate, holidayList);
    // Assert
    assertNotNull(result);
    assertEquals(1, result.size());
  }

  @Test
  void givenLocationSurchargeConfigs_whenGetLocationSurchargeConfigs_thenReturnLocSurEntity() {
    com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.LocationSurchargeConfig
        locationSurchargeConfigDTO =
            new com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos
                .LocationSurchargeConfig();
    locationSurchargeConfigDTO.setLocationId(4);
    locationSurchargeConfigDTO.setLocationName("air port");
    locationSurchargeConfigDTO.setSurchargeValue(4.0);
    locationSurchargeConfigDTO.setChargeBy("PICKUP");
    locationSurchargeConfigDTO.setAddressRef("5394324");
    locationSurchargeConfigDTO.setDayIndicator("MON");
    locationSurchargeConfigDTO.setFareType("FLAT");
    locationSurchargeConfigDTO.setProductId("STD");
    locationSurchargeConfigDTO.setStartTime(LocalTime.of(0, 0, 0));
    locationSurchargeConfigDTO.setEndTime(LocalTime.of(23, 59, 59));

    LocationSurchargeConfigEntity locationSurchargeConfigEntity =
        new LocationSurchargeConfigEntity();
    locationSurchargeConfigEntity.setLocationId(4);
    locationSurchargeConfigEntity.setLocationName("air port");
    locationSurchargeConfigEntity.setSurchargeValue(4.0);
    locationSurchargeConfigEntity.setChargeBy("PICKUP");
    locationSurchargeConfigEntity.setAddressRef("5394324");
    locationSurchargeConfigEntity.setDayIndicator("MON");
    locationSurchargeConfigEntity.setFareType("FLAT");
    locationSurchargeConfigEntity.setProductId("STD");
    locationSurchargeConfigEntity.setStartTime("00:00:00");
    locationSurchargeConfigEntity.setEndTime("23:59:59");

    // Arrange
    Date currentDate = new Date(2023 - 1900, 11 - 1, 27);
    List<String> holidayList = Arrays.asList("01-05-2023 00:00:00", "25-12-2023 00:00:00");
    String dayInWeekReq = DateUtils.toDayOfWeekShortUpperCase(currentDate);
    List<com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.LocationSurchargeConfig>
        dayConfigs = List.of(locationSurchargeConfigDTO);
    when(configService.getLocationSurchargeConfig(
            FlatfareConstants.LOC_SURC_KEY_PREFIX + dayInWeekReq))
        .thenReturn(dayConfigs);
    when(locationSurchargeMapper.mapToLocationSurchargeConfigEntity(locationSurchargeConfigDTO))
        .thenReturn(locationSurchargeConfigEntity);
    // Act
    List<LocationSurchargeConfigEntity> result =
        service.getLocationSurchargeConfigs(currentDate, holidayList);
    // Assert
    assertNotNull(result);
    assertEquals(1, result.size());
  }

  @Test
  void givenMockMap_whenGetFlatFareConfigs_thenReturnFlatFareConfigsMap() {
    // GIVEN
    Map<String, String> map = new HashMap<>();
    map.put("key", "value");
    // WHEN
    when(dynpConfigCacheService.getMultiMapConfigsAndSingleValueFromCache(Mockito.any()))
        .thenReturn(map);
    // THEN
    assertNotNull(service.getFlatFareConfigs());
  }

  @Test
  void givenData_whenGetProductList_thenReturnExpected() throws Exception {
    // Arrange
    Map<String, String> bookARideConfigs = new HashMap<>();
    bookARideConfigs.put(BookARideConfigsConstant.BOOK_RIDE_PRODUCT, "Product1,Product2");

    String[] result = (String[]) getProductListMethod.invoke(service, bookARideConfigs);

    // Assert
    assertEquals(2, result.length);
    assertEquals("Product1", result[0]);
    assertEquals("Product2", result[1]);
  }

  @Test
  void givenData_whenGetVehicleGroupList_thenReturnExpected() throws Exception {
    // Arrange
    Map<String, String> bookARideConfigs = new HashMap<>();
    bookARideConfigs.put(BookARideConfigsConstant.BOOK_RIDE_VEHICLE_GROUP, "1,2,3");

    String[] result = (String[]) getVehicleGroupListMethod.invoke(service, bookARideConfigs);

    // Assert
    assertEquals(3, result.length);
    assertEquals("1", result[0]);
    assertEquals("2", result[1]);
    assertEquals("3", result[2]);
  }

  @Test
  void givenData_whenGetBookingFare_thenReturnExpected() {
    // Arrange
    String vehicleGroupId = "1";
    boolean isHoliday = false;
    OffsetDateTime requestDate = OffsetDateTime.now();
    String cdgProduct = "SomeProduct";

    BookingFeeResponse expectedResponse = new BookingFeeResponse();

    when(fareService.getBookingFee(any(BookingFeeRequest.class))).thenReturn(expectedResponse);
    BookingFeeRequest bookingFeeRequest =
        BookingFareUtils.createBookingFareRequest(
            vehicleGroupId,
            LIMO_FLAT_FARE,
            isHoliday,
            requestDate,
            cdgProduct,
            FlatfareConstants.JOB_TYPE_CJ);
    BookingFeeResponse result = fareService.getBookingFee(bookingFeeRequest);

    // Assert
    assertEquals(expectedResponse, result);
    verify(fareService).getBookingFee(any(BookingFeeRequest.class));
  }

  @Test
  void givenExceptionInDetermineFareType_whenCalled_thenLogError()
      throws InvocationTargetException, IllegalAccessException {
    // Arrange
    String vehicleGroupId = "invalid";
    String productId = "product";
    // Assert
    assertEquals(
        StringUtils.EMPTY, determineFareTypeMethod.invoke(service, vehicleGroupId, productId));
  }
}

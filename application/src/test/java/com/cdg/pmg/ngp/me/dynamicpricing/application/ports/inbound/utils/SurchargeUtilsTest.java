package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils;

import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.CALCULATE_BREAKDOWN_FARE_ERROR;
import static org.junit.jupiter.api.Assertions.*;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.EventSurgeAddressConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareHoliday;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareVO;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.LocationSurchargeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.LocationSurchargeConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.DomainException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Stream;
import org.junit.jupiter.api.Test;

class SurchargeUtilsTest {
  @Test
  void givenHolidayConfig_whenCalLocSurchargesForLimo_thenReturnSuccess() throws ParseException {
    // GIVEN
    String startDateString = "09/20/2023";
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy");
    Date reqDate = df.parse(startDateString);
    LocalTime currentTime = DateUtils.convertToLocalTime(reqDate);

    FlatFareHoliday holiday = FlatFareHoliday.builder().date("2023-09-20 00:00:00").build();
    List<FlatFareHoliday> flatFareHolidayList = new ArrayList<>();
    flatFareHolidayList.add(holiday);

    FlatFareRequest request =
        FlatFareRequest.builder()
            .originAddressRef("origin")
            .destAddressRef("dest")
            .intermediateAddrRef("inter")
            .requestDate(reqDate)
            .build();
    FlatFareVO flatfareVO = new FlatFareVO();
    flatfareVO.setFlatFareRequest(request);

    List<LocationSurchargeConfig> locationSurchargeConfigList =
        Stream.concat(
                initLocSurchargeNormalDay(currentTime).stream(),
                initLocSurchargeHOL(currentTime).stream())
            .toList();

    // THEN
    SurchargeUtils.calLocSurchargesForLimo(flatfareVO, locationSurchargeConfigList);
    var actual = CommonUtils.roundToTwo(flatfareVO.getTotalLocSurCharge());
    var expected = 0.6;
    assertEquals(expected, actual);
  }

  @Test
  void givenNormalDayConfig_whenCalLocSurchargesForLimo_thenReturnSuccess() throws ParseException {
    // GIVEN
    String startDateString = "09/20/2023";
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy");
    var reqDate = df.parse(startDateString);
    LocalTime currentTime = DateUtils.convertToLocalTime(reqDate);

    FlatFareHoliday holiday = FlatFareHoliday.builder().date("2023-09-22 00:00:00").build();
    List<FlatFareHoliday> flatFareHolidayList = new ArrayList<>();
    flatFareHolidayList.add(holiday);

    var request =
        FlatFareRequest.builder()
            .originAddressRef("origin")
            .destAddressRef("dest")
            .intermediateAddrRef("inter")
            .requestDate(reqDate)
            .build();
    var flatfareVO = new FlatFareVO();
    flatfareVO.setFlatFareRequest(request);

    List<LocationSurchargeConfig> locationSurchargeConfigList =
        initLocSurchargeNormalDay(currentTime);

    // THEN
    SurchargeUtils.calLocSurchargesForLimo(flatfareVO, locationSurchargeConfigList);
    var actual = CommonUtils.roundToTwo(flatfareVO.getTotalLocSurCharge());
    var expected = 2.4;
    assertEquals(expected, actual);
  }

  @Test
  void givenHolidayConfig_whenCalLocSurchargesForEstStandard_thenReturnSuccess()
      throws ParseException {
    // GIVEN
    String startDateString = "09/20/2023";
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy");
    var reqDate = df.parse(startDateString);
    LocalTime currentTime = DateUtils.convertToLocalTime(reqDate);

    FlatFareHoliday holiday = FlatFareHoliday.builder().date("2023-09-20 00:00:00").build();
    List<FlatFareHoliday> flatFareHolidayList = new ArrayList<>();
    flatFareHolidayList.add(holiday);

    var request =
        FlatFareRequest.builder()
            .originAddressRef("origin")
            .destAddressRef("dest")
            .intermediateAddrRef("inter")
            .requestDate(reqDate)
            .build();

    var flatfareVO = new FlatFareVO();
    flatfareVO.setFlatFareRequest(request);

    List<LocationSurchargeConfig> locationSurchargeConfigList =
        Stream.concat(
                initLocSurchargeNormalDay(currentTime).stream(),
                initLocSurchargeHOL(currentTime).stream())
            .toList();

    // THEN
    SurchargeUtils.calLocSurchargesForEstStandard(flatfareVO, locationSurchargeConfigList);
    var actual = CommonUtils.roundToTwo(flatfareVO.getTotalLocSurCharge());
    var expected = 0.9;
    assertEquals(expected, actual);
  }

  @Test
  void givenNormalDayConfig_whenCalLocSurchargesForEstStandard_thenReturnSuccess()
      throws ParseException {
    // GIVEN
    String startDateString = "09/20/2023";
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy");
    var reqDate = df.parse(startDateString);
    LocalTime currentTime = DateUtils.convertToLocalTime(reqDate);

    FlatFareHoliday holiday = FlatFareHoliday.builder().date("2023-09-22 00:00:00").build();
    List<FlatFareHoliday> flatFareHolidayList = new ArrayList<>();
    flatFareHolidayList.add(holiday);

    var request =
        FlatFareRequest.builder()
            .originAddressRef("origin")
            .destAddressRef("dest")
            .intermediateAddrRef("inter")
            .requestDate(reqDate)
            .build();
    var flatfareVO = new FlatFareVO();
    flatfareVO.setFlatFareRequest(request);

    List<LocationSurchargeConfig> locationSurchargeConfigList =
        initLocSurchargeNormalDay(currentTime);

    // THEN
    SurchargeUtils.calLocSurchargesForEstStandard(flatfareVO, locationSurchargeConfigList);
    var actual = CommonUtils.roundToTwo(flatfareVO.getTotalLocSurCharge());
    var expected = 3.6;
    assertEquals(expected, actual);
  }

  @Test
  void givenNullConfig_whenCalLocSurchargesForEstStandard_thenThrowException()
      throws ParseException {
    // GIVEN
    String startDateString = "09/20/2023";
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy");
    var reqDate = df.parse(startDateString);

    var request =
        FlatFareRequest.builder()
            .originAddressRef("origin")
            .destAddressRef("dest")
            .intermediateAddrRef("inter")
            .requestDate(reqDate)
            .build();

    var flatfareVO = new FlatFareVO();
    flatfareVO.setFlatFareRequest(request);

    // THEN
    try {
      SurchargeUtils.calLocSurchargesForEstStandard(flatfareVO, null);
    } catch (DomainException e) {
      assertEquals(CALCULATE_BREAKDOWN_FARE_ERROR.getErrorCode(), e.getErrorCode());
    }
  }

  @Test
  void givenHolidayConfig_whenCalLocSurchargesForStandard_thenReturnSuccess()
      throws ParseException {
    // GIVEN
    String startDateString = "09/20/2023";
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy");
    var reqDate = df.parse(startDateString);
    LocalTime currentTime = DateUtils.convertToLocalTime(reqDate);

    FlatFareHoliday holiday = FlatFareHoliday.builder().date("2023-09-20 00:00:00").build();
    List<FlatFareHoliday> flatFareHolidayList = new ArrayList<>();
    flatFareHolidayList.add(holiday);

    var request =
        FlatFareRequest.builder()
            .originAddressRef("origin")
            .destAddressRef("dest")
            .intermediateAddrRef("inter")
            .requestDate(reqDate)
            .build();

    var flatfareVO = new FlatFareVO();
    flatfareVO.setFlatFareRequest(request);

    List<LocationSurchargeConfig> locationSurchargeConfigList =
        Stream.concat(
                initLocSurchargeNormalDayStandard(currentTime).stream(),
                initLocSurchargeHOLStandard(currentTime).stream())
            .toList();

    // THEN
    SurchargeUtils.calLocSurchargesForStandard(flatfareVO, locationSurchargeConfigList);
    var actual = CommonUtils.roundToTwo(flatfareVO.getTotalLocSurCharge());
    var expected = 0.3;
    assertEquals(expected, actual);
  }

  @Test
  void givenNormalDayConfig_whenCalLocSurchargesForStandard_thenReturnSuccess()
      throws ParseException {
    // GIVEN
    String startDateString = "09/20/2023";
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy");
    var reqDate = df.parse(startDateString);
    LocalTime currentTime = DateUtils.convertToLocalTime(reqDate);

    FlatFareHoliday holiday = FlatFareHoliday.builder().date("2023-09-22 00:00:00").build();
    List<FlatFareHoliday> flatFareHolidayList = new ArrayList<>();
    flatFareHolidayList.add(holiday);

    var request =
        FlatFareRequest.builder()
            .originAddressRef("origin")
            .destAddressRef("dest")
            .intermediateAddrRef("inter")
            .requestDate(reqDate)
            .build();
    var flatfareVO = new FlatFareVO();
    flatfareVO.setFlatFareRequest(request);

    List<LocationSurchargeConfig> locationSurchargeConfigList =
        initLocSurchargeNormalDayStandard(currentTime);

    // THEN
    SurchargeUtils.calLocSurchargesForStandard(flatfareVO, locationSurchargeConfigList);
    var actual = CommonUtils.roundToTwo(flatfareVO.getTotalLocSurCharge());
    var expected = 1.9;
    assertEquals(expected, actual);
  }

  @Test
  void givenNullConfig_whenCalLocSurchargesForStandard_thenThrowException() throws ParseException {
    // GIVEN
    String startDateString = "09/20/2023";
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy");
    var reqDate = df.parse(startDateString);

    var request =
        FlatFareRequest.builder()
            .originAddressRef("origin")
            .destAddressRef("dest")
            .intermediateAddrRef("inter")
            .requestDate(reqDate)
            .build();

    var flatfareVO = new FlatFareVO();
    flatfareVO.setFlatFareRequest(request);

    // THEN
    try {
      SurchargeUtils.calLocSurchargesForStandard(flatfareVO, null);
    } catch (DomainException e) {
      assertEquals(CALCULATE_BREAKDOWN_FARE_ERROR.getErrorCode(), e.getErrorCode());
    }
  }

  @Test
  void givenNormalCondition_whenIsEventConfigMatchingDayAndMonthAndTimeEffective_thenTrue()
      throws ParseException {
    Date reqDate = initEventDate();
    EventSurgeAddressConfig chargeByPickUpFixedAmountConfig =
        EventSurgeAddressConfig.builder()
            .startTime(LocalTime.parse("00:00:00"))
            .endTime(LocalTime.parse("23:59:59"))
            .applicableDaysOfMonth(",19,20,21,22,")
            .applicableMonths(",8,9,10,11,")
            .chargeBy(FlatfareConstants.CHARGE_BY_PICKUP)
            .chargeType(FlatfareConstants.FIXED_AMOUNT)
            .chargeVal(0.5)
            .applicableAddresses(",1001,")
            .build();
    assertEquals(
        Boolean.TRUE,
        SurchargeUtils.isEventConfigMatchingDayAndMonthAndTimeEffective(
            chargeByPickUpFixedAmountConfig, reqDate));
  }

  @Test
  void
      givenNormalCondition_whenIsAddressRefMatchingWithConfigUsingFixAmountAndChangeByPickUp_thenTrue() {
    String addressRef = "1001";
    EventSurgeAddressConfig chargeByPickUpFixedAmountConfig =
        EventSurgeAddressConfig.builder()
            .startTime(LocalTime.parse("00:00:00"))
            .endTime(LocalTime.parse("23:59:59"))
            .applicableDaysOfMonth(",19,20,21,22,")
            .applicableMonths(",8,9,10,11,")
            .chargeBy(FlatfareConstants.CHARGE_BY_PICKUP)
            .chargeType(FlatfareConstants.FIXED_AMOUNT)
            .chargeVal(0.5)
            .applicableAddresses(",1001,")
            .build();
    assertEquals(
        Boolean.TRUE,
        SurchargeUtils.isAddressRefMatchingWithConfigUsingFixAmountAndChangeByPickUp(
            addressRef, chargeByPickUpFixedAmountConfig));
  }

  @Test
  void
      givenNormalCondition_whenIsAddressRefMatchingWithConfigUsingPercentageAndChangeByPickUp_thenTrue() {
    String addressRef = "1001";
    EventSurgeAddressConfig chargeByPickUpPercentConfig =
        EventSurgeAddressConfig.builder()
            .startTime(LocalTime.parse("00:00:00"))
            .endTime(LocalTime.parse("23:59:59"))
            .applicableDaysOfMonth(",19,20,21,22,")
            .applicableMonths(",8,9,10,11,")
            .chargeBy(FlatfareConstants.CHARGE_BY_PICKUP)
            .chargeType(FlatfareConstants.PERCENTAGE)
            .chargeVal(0.5)
            .applicableAddresses(",1001,")
            .build();
    assertEquals(
        Boolean.TRUE,
        SurchargeUtils.isAddressRefMatchingWithConfigUsingPercentageAndChangeByPickUp(
            addressRef, chargeByPickUpPercentConfig));
  }

  @Test
  void
      givenNormalCondition_whenIsAddressRefMatchingWithConfigUsingFixAmountAndChangeByDestination_thenTrue() {
    String addressRef = "1001";
    EventSurgeAddressConfig chargeByDestFixedAmountConfig =
        EventSurgeAddressConfig.builder()
            .startTime(LocalTime.parse("00:00:00"))
            .endTime(LocalTime.parse("23:59:59"))
            .applicableDaysOfMonth(",19,20,21,22,")
            .applicableMonths(",8,9,10,11,")
            .chargeBy(FlatfareConstants.CHARGE_BY_DEST)
            .chargeType(FlatfareConstants.FIXED_AMOUNT)
            .chargeVal(0.5)
            .applicableAddresses(",1001,")
            .build();
    assertEquals(
        Boolean.TRUE,
        SurchargeUtils.isAddressRefMatchingWithConfigUsingFixAmountAndChangeByDestination(
            addressRef, chargeByDestFixedAmountConfig));
  }

  @Test
  void
      givenNormalCondition_whenIsAddressRefMatchingWithConfigUsingPercentageAndChangeByDestination_thenTrue() {
    String addressRef = "1001";
    EventSurgeAddressConfig chargeByDestPercentConfig =
        EventSurgeAddressConfig.builder()
            .startTime(LocalTime.parse("00:00:00"))
            .endTime(LocalTime.parse("23:59:59"))
            .applicableDaysOfMonth(",19,20,21,22,")
            .applicableMonths(",8,9,10,11,")
            .chargeBy(FlatfareConstants.CHARGE_BY_DEST)
            .chargeType(FlatfareConstants.PERCENTAGE)
            .chargeVal(0.5)
            .applicableAddresses(",1001,")
            .build();
    assertEquals(
        Boolean.TRUE,
        SurchargeUtils.isAddressRefMatchingWithConfigUsingPercentageAndChangeByDestination(
            addressRef, chargeByDestPercentConfig));
  }

  private Date initEventDate() throws ParseException {
    String dateString = "09/20/2023 01:00:00";
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy hh:mm:ss");
    return df.parse(dateString);
  }

  private List<LocationSurchargeConfig> initLocSurchargeHOL(LocalTime currentTime) {
    var locItemHOL1 =
        LocationSurchargeConfig.builder()
            .addressRef("origin")
            .startTime(currentTime)
            .endTime(currentTime)
            .productId("STD001")
            .chargeBy("PICKUP")
            .surchargeValue(0.3)
            .applicableDays("HOL")
            .dayIndicator("HOL")
            .locationName("Changi Airport")
            .build();
    var locItemHOL2 =
        LocationSurchargeConfig.builder()
            .addressRef("dest")
            .startTime(currentTime)
            .endTime(currentTime)
            .productId("STD001")
            .chargeBy("DEST")
            .surchargeValue(0.3)
            .applicableDays("HOL")
            .dayIndicator("HOL")
            .locationName("Changi Airport")
            .build();
    var locItemHOL3 =
        LocationSurchargeConfig.builder()
            .addressRef("inter")
            .startTime(currentTime)
            .endTime(currentTime)
            .productId("STD001")
            .chargeBy("DEST")
            .surchargeValue(0.3)
            .applicableDays("HOL")
            .dayIndicator("HOL")
            .locationName("Changi Airport")
            .build();

    return List.of(locItemHOL1, locItemHOL2, locItemHOL3);
  }

  private List<LocationSurchargeConfig> initLocSurchargeNormalDay(LocalTime currentTime) {
    var locItemHOL1 =
        LocationSurchargeConfig.builder()
            .addressRef("origin")
            .startTime(currentTime)
            .endTime(currentTime)
            .productId("STD001")
            .chargeBy("PICKUP")
            .surchargeValue(1.2)
            .applicableDays("WED")
            .dayIndicator("WED")
            .locationName("Changi Airport")
            .build();
    var locItemHOL2 =
        LocationSurchargeConfig.builder()
            .addressRef("dest")
            .startTime(currentTime)
            .endTime(currentTime)
            .productId("STD001")
            .chargeBy("DEST")
            .surchargeValue(1.2)
            .applicableDays("WED")
            .dayIndicator("WED")
            .locationName("Changi Airport")
            .build();
    var locItemHOL3 =
        LocationSurchargeConfig.builder()
            .addressRef("inter")
            .startTime(currentTime)
            .endTime(currentTime)
            .productId("STD001")
            .chargeBy("DEST")
            .surchargeValue(1.2)
            .applicableDays("WED")
            .dayIndicator("WED")
            .locationName("Changi Airport")
            .build();

    return List.of(locItemHOL1, locItemHOL2, locItemHOL3);
  }

  private List<LocationSurchargeConfig> initLocSurchargeHOLStandard(LocalTime currentTime) {
    var locItemHOL1 =
        LocationSurchargeConfig.builder()
            .addressRef("origin")
            .startTime(currentTime)
            .endTime(currentTime)
            .productId("FLAT-001")
            .chargeBy("PICKUP")
            .surchargeValue(0.3)
            .applicableDays("HOL")
            .dayIndicator("HOL")
            .locationName("Changi Airport")
            .build();
    var locItemHOL2 =
        LocationSurchargeConfig.builder()
            .addressRef("dest")
            .startTime(currentTime)
            .endTime(currentTime)
            .productId("STD001")
            .chargeBy("DEST")
            .surchargeValue(0.3)
            .applicableDays("HOL")
            .dayIndicator("HOL")
            .locationName("Changi Airport")
            .build();
    var locItemHOL3 =
        LocationSurchargeConfig.builder()
            .addressRef("inter")
            .startTime(currentTime)
            .endTime(currentTime)
            .productId("STD001")
            .chargeBy("DEST")
            .surchargeValue(0.3)
            .applicableDays("HOL")
            .dayIndicator("HOL")
            .locationName("Changi Airport")
            .build();

    return List.of(locItemHOL1, locItemHOL2, locItemHOL3);
  }

  private List<LocationSurchargeConfig> initLocSurchargeNormalDayStandard(LocalTime currentTime) {
    var locItemHOL1 =
        LocationSurchargeConfig.builder()
            .addressRef("origin")
            .startTime(currentTime)
            .endTime(currentTime)
            .productId("FLAT-001")
            .chargeBy("PICKUP")
            .surchargeValue(1.9)
            .applicableDays("WED")
            .dayIndicator("WED")
            .locationName("Changi Airport")
            .build();
    var locItemHOL2 =
        LocationSurchargeConfig.builder()
            .addressRef("dest")
            .startTime(currentTime)
            .endTime(currentTime)
            .productId("STD001")
            .chargeBy("DEST")
            .surchargeValue(1.2)
            .applicableDays("WED")
            .dayIndicator("WED")
            .locationName("Changi Airport")
            .build();
    var locItemHOL3 =
        LocationSurchargeConfig.builder()
            .addressRef("inter")
            .startTime(currentTime)
            .endTime(currentTime)
            .productId("STD001")
            .chargeBy("DEST")
            .surchargeValue(1.2)
            .applicableDays("WED")
            .dayIndicator("WED")
            .locationName("Changi Airport")
            .build();

    return List.of(locItemHOL1, locItemHOL2, locItemHOL3);
  }

  @Test
  void givenNull_whenGroupConfigByDayInWeekThenAddress_thenReturnEmpty() {
    assertDoesNotThrow(() -> SurchargeUtils.groupConfigByDayInWeekThenAddress(null));
  }

  @Test
  void givenNormalCondition_whenGroupConfigByDayInWeekThenAddress_thenSuccess() {
    var result =
        SurchargeUtils.groupConfigByDayInWeekThenAddress(getLocationSurchargeConfigEntityList2());

    assertEquals(3, result.size());
    assertEquals(1, result.get("MON").size());
    assertEquals(1, result.get("TUE").size());
    assertEquals(1, result.get("WED").size());
  }

  @Test
  void givenNull_whenMergeGroupConfigByDayInWeekThenAddress_thenReturnEmpty() {
    assertDoesNotThrow(() -> SurchargeUtils.mergeGroupConfigByDayInWeekThenAddress(null, null));
  }

  @Test
  void givenNormalCondition_whenMergeGroupConfigByDayInWeekThenAddress_thenSuccess() {
    var map1 =
        SurchargeUtils.groupConfigByDayInWeekThenAddress(getLocationSurchargeConfigEntityList1());
    var map2 =
        SurchargeUtils.groupConfigByDayInWeekThenAddress(getLocationSurchargeConfigEntityList2());

    var result = SurchargeUtils.mergeGroupConfigByDayInWeekThenAddress(map1, map2);

    assertEquals(3, result.size());
    assertEquals(1, result.get("MON").size());
    assertEquals(1, result.get("TUE").size());
    assertEquals(1, result.get("WED").size());

    assertEquals(2, result.get("MON").get("111111").size());
    assertEquals(2, result.get("TUE").get("111111").size());
    assertEquals(1, result.get("WED").get("111111").size());
  }

  private List<LocationSurchargeConfigEntity> getLocationSurchargeConfigEntityList1() {
    LocationSurchargeConfigEntity locationSurchargeConfigEntity =
        new LocationSurchargeConfigEntity();
    locationSurchargeConfigEntity.setLocationId(1);
    locationSurchargeConfigEntity.setLocationName("air port");
    locationSurchargeConfigEntity.setSurchargeValue(4.0);
    locationSurchargeConfigEntity.setChargeBy("PICKUP");
    locationSurchargeConfigEntity.setAddressRef("5394324");
    locationSurchargeConfigEntity.setDayIndicator("MON");
    locationSurchargeConfigEntity.setFareType("FLAT");
    locationSurchargeConfigEntity.setProductId("STD");
    locationSurchargeConfigEntity.setStartTime("00:00:00");
    locationSurchargeConfigEntity.setEndTime("23:59:59");
    locationSurchargeConfigEntity.setAddressRef("111111");

    LocationSurchargeConfigEntity locationSurchargeConfigEntity2 =
        new LocationSurchargeConfigEntity();
    locationSurchargeConfigEntity2.setLocationId(5);
    locationSurchargeConfigEntity2.setLocationName("air port");
    locationSurchargeConfigEntity2.setSurchargeValue(4.0);
    locationSurchargeConfigEntity2.setChargeBy("PICKUP");
    locationSurchargeConfigEntity2.setAddressRef("5394324");
    locationSurchargeConfigEntity2.setDayIndicator("TUE");
    locationSurchargeConfigEntity2.setFareType("FLAT");
    locationSurchargeConfigEntity2.setProductId("STD");
    locationSurchargeConfigEntity2.setStartTime("00:00:00");
    locationSurchargeConfigEntity2.setEndTime("23:59:59");
    locationSurchargeConfigEntity2.setAddressRef("111111");

    return List.of(locationSurchargeConfigEntity, locationSurchargeConfigEntity2);
  }

  private List<LocationSurchargeConfigEntity> getLocationSurchargeConfigEntityList2() {
    LocationSurchargeConfigEntity locationSurchargeConfigEntity =
        new LocationSurchargeConfigEntity();
    locationSurchargeConfigEntity.setLocationId(1);
    locationSurchargeConfigEntity.setLocationName("air port");
    locationSurchargeConfigEntity.setSurchargeValue(4.0);
    locationSurchargeConfigEntity.setChargeBy("PICKUP");
    locationSurchargeConfigEntity.setAddressRef("5394324");
    locationSurchargeConfigEntity.setDayIndicator("TUE");
    locationSurchargeConfigEntity.setFareType("FLAT");
    locationSurchargeConfigEntity.setProductId("STD");
    locationSurchargeConfigEntity.setStartTime("00:00:00");
    locationSurchargeConfigEntity.setEndTime("23:59:59");
    locationSurchargeConfigEntity.setAddressRef("111111");

    LocationSurchargeConfigEntity locationSurchargeConfigEntity2 =
        new LocationSurchargeConfigEntity();
    locationSurchargeConfigEntity2.setLocationId(5);
    locationSurchargeConfigEntity2.setLocationName("air port");
    locationSurchargeConfigEntity2.setSurchargeValue(4.0);
    locationSurchargeConfigEntity2.setChargeBy("PICKUP");
    locationSurchargeConfigEntity2.setAddressRef("5394324");
    locationSurchargeConfigEntity2.setDayIndicator("MON");
    locationSurchargeConfigEntity2.setFareType("FLAT");
    locationSurchargeConfigEntity2.setProductId("STD");
    locationSurchargeConfigEntity2.setStartTime("00:00:00");
    locationSurchargeConfigEntity2.setEndTime("23:59:59");
    locationSurchargeConfigEntity2.setAddressRef("111111");

    LocationSurchargeConfigEntity locationSurchargeConfigEntity3 =
        new LocationSurchargeConfigEntity();
    locationSurchargeConfigEntity3.setLocationId(5);
    locationSurchargeConfigEntity3.setLocationName("air port");
    locationSurchargeConfigEntity3.setSurchargeValue(4.0);
    locationSurchargeConfigEntity3.setChargeBy("PICKUP");
    locationSurchargeConfigEntity3.setAddressRef("5394324");
    locationSurchargeConfigEntity3.setDayIndicator("WED");
    locationSurchargeConfigEntity3.setFareType("FLAT");
    locationSurchargeConfigEntity3.setProductId("STD");
    locationSurchargeConfigEntity3.setStartTime("00:00:00");
    locationSurchargeConfigEntity3.setEndTime("23:59:59");
    locationSurchargeConfigEntity3.setAddressRef("111111");

    return List.of(
        locationSurchargeConfigEntity,
        locationSurchargeConfigEntity2,
        locationSurchargeConfigEntity3);
  }
}

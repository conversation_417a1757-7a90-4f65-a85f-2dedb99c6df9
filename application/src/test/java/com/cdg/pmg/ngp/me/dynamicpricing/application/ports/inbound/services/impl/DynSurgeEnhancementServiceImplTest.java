package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.willDoNothing;
import static org.mockito.Mockito.*;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.SurgeCalculationDto;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.DemandSupplyStatisticsResponseV2;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.CompanyHolidayRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.DynamicSurgeRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.DynpSurgeLogsRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.PricingRangeRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.CacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.ConfigurationServiceOutboundPort;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.FleetAnalyticService;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.DemandSupplyConfigV2;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.DynamicSurgesEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.NewPricingModelConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.queries.PricingRangeCalDemandSurgeQueryResponse;
import java.lang.reflect.Field;
import java.time.OffsetDateTime;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.commons.util.ReflectionUtils;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class DynSurgeEnhancementServiceImplTest {

  @Spy @InjectMocks private DynSurgeEnhancementServiceImpl dynSurgeEnhancementService;

  @Mock private FleetAnalyticService fleetAnalyticService;
  @Mock private CacheService cacheService;
  @Mock private CompanyHolidayRepository companyHolidayRepository;
  @Mock private PricingRangeRepository pricingRangeRepository;
  @Mock private DynamicSurgeRepository dynamicSurgeRepository;
  @Mock private DynpSurgeLogsRepository dynpSurgeLogsRepository;
  @Spy private SurgeCalculationV1Strategy surgeCalculationV1Strategy;
  @Spy private SurgeCalculationV2Strategy surgeCalculationV2Strategy;
  @Spy private SurgeCalculationV2Point5Strategy surgeCalculationV2Point5Strategy;
  @Spy private SurgeCalculationV3Strategy surgeCalculationV3Strategy;
  @Mock private ConfigurationServiceOutboundPort configurationServiceOutboundPort;

  @Captor private ArgumentCaptor<List<DynamicSurgesEntity>> dynListArgumentCaptor;

  @Captor private ArgumentCaptor<SurgeCalculationDto> argumentCaptor;
  @Captor private ArgumentCaptor<List<DynamicSurgesEntity>> listArgumentCaptor;

  @BeforeEach
  void setUp() {}

  @Test
  void givenDemandSupplyStatisticsEmpty_whenCalculateDemandSupplySurge_doNothing() {
    // given
    given(fleetAnalyticService.getDemandSupplyStatisticsV2()).willReturn(Collections.emptyList());
    // when
    dynSurgeEnhancementService.calculateDemandSupplySurge();
    // then
    verifyNoInteractions(
        cacheService,
        companyHolidayRepository,
        pricingRangeRepository,
        dynamicSurgeRepository,
        dynpSurgeLogsRepository,
        surgeCalculationV1Strategy,
        surgeCalculationV2Strategy,
        surgeCalculationV3Strategy,
        surgeCalculationV2Point5Strategy);
  }

  @Test
  void givenDemandSupplyStatisticsNotEmpty_thenCalculateDemandSupplySurge_doNotThrowException()
      throws IllegalAccessException {
    // given
    DemandSupplyStatisticsResponseV2 demandSupplyStatistic =
        DemandSupplyStatisticsResponseV2.builder()
            .batchCounter(1)
            .demand15(2)
            .supply(3)
            .demand30(4)
            .demand60(5)
            .zoneId("01")
            .excessDemand15(6)
            .excessDemand30(7)
            .previousDemand15(8)
            .predictedDemand15(9)
            .predictedDemand30(10)
            .unmet15(0.8)
            .previousUnmet15(0.2)
            .build();
    List<DemandSupplyStatisticsResponseV2> demandSupplyStatistics = List.of(demandSupplyStatistic);
    var now = OffsetDateTime.now();
    var twoMinutesAgo = now.minusMinutes(2);
    var nextTwoMinutes = now.plusMinutes(2);
    NewPricingModelConfigEntity newPricingModel =
        NewPricingModelConfigEntity.builder()
            .zoneId("01")
            .zonePriceVersion("V3")
            .startDt(twoMinutesAgo)
            .endDt(nextTwoMinutes)
            .build();
    var newPricingModelEntities = List.of(newPricingModel);
    boolean isHoliday = false;

    PricingRangeCalDemandSurgeQueryResponse pricingRange =
        PricingRangeCalDemandSurgeQueryResponse.builder()
            .surgeLow(-14)
            .surgeHigh(40)
            .stepPositive(2)
            .stepNegative(3)
            .zoneId("01")
            .build();
    var pricingRanges = List.of(pricingRange);

    given(fleetAnalyticService.getDemandSupplyStatisticsV2()).willReturn(demandSupplyStatistics);
    given(configurationServiceOutboundPort.getNewPricingModelConfigEntities(true))
        .willReturn(newPricingModelEntities);
    doReturn(isHoliday).when(dynSurgeEnhancementService).getIsHolidayValueConfig();
    willDoNothing().given(dynamicSurgeRepository).removeInvalidDynSurges();

    given(pricingRangeRepository.getDynpConfigForDemandSurgeV2(isHoliday))
        .willReturn(pricingRanges);
    doReturn(
            List.of(
                DynamicSurgesEntity.builder().zoneId("01").surge(10).zonePriceModel("V1").build()))
        .when(dynSurgeEnhancementService)
        .getDynpSurges();

    // when
    dynSurgeEnhancementService.calculateDemandSupplySurge();

    // then
    verify(dynamicSurgeRepository).updateDynpSurgesV2(dynListArgumentCaptor.capture());
    verify(surgeCalculationV3Strategy).calculate(argumentCaptor.capture());
    var surgeCalculationDto = argumentCaptor.getValue();
    Field field =
        ReflectionUtils.findFields(
                surgeCalculationDto.getClass(),
                f -> f.getName().equals("demandConfig"),
                ReflectionUtils.HierarchyTraversalMode.TOP_DOWN)
            .get(0);
    field.setAccessible(true);

    DemandSupplyConfigV2 demandSupplyConfigV2 =
        (DemandSupplyConfigV2) field.get(surgeCalculationDto);
    assertNotNull(demandSupplyConfigV2, "Demand supply config should not be null");
    assertEquals(demandSupplyStatistic.getZoneId(), demandSupplyConfigV2.getZoneId());
    assertEquals(demandSupplyStatistic.getDemand15(), demandSupplyConfigV2.getDemand15());
    assertEquals(demandSupplyStatistic.getDemand30(), demandSupplyConfigV2.getDemand30());
    assertEquals(demandSupplyStatistic.getDemand60(), demandSupplyConfigV2.getDemand60());
    assertEquals(demandSupplyStatistic.getSupply(), demandSupplyConfigV2.getSupply());
    assertEquals(
        demandSupplyStatistic.getPredictedDemand15(), demandSupplyConfigV2.getPredictedDemand15());
    assertEquals(
        demandSupplyStatistic.getPredictedDemand30(), demandSupplyConfigV2.getPredictedDemand30());
    assertEquals(
        demandSupplyStatistic.getExcessDemand15(), demandSupplyConfigV2.getExcessDemand15());
    assertEquals(
        demandSupplyStatistic.getExcessDemand30(), demandSupplyConfigV2.getExcessDemand30());
    assertEquals(
        demandSupplyStatistic.getPreviousDemand15(), demandSupplyConfigV2.getPreviousDemand15());
    assertEquals(demandSupplyStatistic.getBatchCounter(), demandSupplyConfigV2.getBatchCounter());
    assertEquals(demandSupplyStatistic.getUnmet15(), demandSupplyConfigV2.getUnmet15());
    assertEquals(
        demandSupplyStatistic.getPreviousUnmet15(), demandSupplyConfigV2.getPreviousUnmet15());
    assertEquals(pricingRange.getSurgeLow(), demandSupplyConfigV2.getSurgeLow());
    assertEquals(pricingRange.getSurgeHigh(), demandSupplyConfigV2.getSurgeHigh());
    assertEquals(pricingRange.getStepPositive(), demandSupplyConfigV2.getStepPositive());
    assertEquals(pricingRange.getStepNegative(), demandSupplyConfigV2.getStepNegative());
  }

  @Test
  void givenDemandSupplyStatisticsEmpty_thenCalculateDemandSupplySurge_doNotThrowException() {
    // given
    DemandSupplyStatisticsResponseV2 demandSupplyStatistic =
        DemandSupplyStatisticsResponseV2.builder()
            .batchCounter(1)
            .demand15(2)
            .supply(3)
            .demand30(4)
            .demand60(5)
            .zoneId("01")
            .excessDemand15(6)
            .excessDemand30(7)
            .previousDemand15(8)
            .predictedDemand15(9)
            .predictedDemand30(10)
            .unmet15(0.8)
            .previousUnmet15(0.2)
            .build();
    List<DemandSupplyStatisticsResponseV2> demandSupplyStatistics = List.of(demandSupplyStatistic);
    var now = OffsetDateTime.now();
    var twoMinutesAgo = now.minusMinutes(2);
    var nextTwoMinutes = now.plusMinutes(2);
    NewPricingModelConfigEntity newPricingModel =
        NewPricingModelConfigEntity.builder()
            .zoneId("01")
            .zonePriceVersion("V3")
            .startDt(twoMinutesAgo)
            .endDt(nextTwoMinutes)
            .build();
    var newPricingModelEntities = List.of(newPricingModel);
    boolean isHoliday = false;

    PricingRangeCalDemandSurgeQueryResponse pricingRange =
        PricingRangeCalDemandSurgeQueryResponse.builder()
            .surgeLow(-14)
            .surgeHigh(40)
            .stepPositive(2)
            .stepNegative(3)
            .zoneId("01")
            .build();
    var pricingRanges = List.of(pricingRange);

    given(fleetAnalyticService.getDemandSupplyStatisticsV2()).willReturn(demandSupplyStatistics);
    given(configurationServiceOutboundPort.getNewPricingModelConfigEntities(true))
        .willReturn(newPricingModelEntities);
    doReturn(isHoliday).when(dynSurgeEnhancementService).getIsHolidayValueConfig();
    willDoNothing().given(dynamicSurgeRepository).removeInvalidDynSurges();

    given(pricingRangeRepository.getDynpConfigForDemandSurgeV2(isHoliday))
        .willReturn(pricingRanges);
    doReturn(Collections.emptyList()).when(dynSurgeEnhancementService).getDynpSurges();

    // when
    dynSurgeEnhancementService.calculateDemandSupplySurge();

    // then
    verify(dynamicSurgeRepository).updateDynpSurgesV2(listArgumentCaptor.capture());
    verify(dynamicSurgeRepository).removeInvalidDynSurges();
    List<DynamicSurgesEntity> entities = listArgumentCaptor.getValue();
    assertEquals(1, entities.size());
  }
}

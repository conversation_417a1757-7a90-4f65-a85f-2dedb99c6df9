package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.*;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.FlatFareAdjustmentConfRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.CacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FlatFareAdjustmentConfEntity;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class FlatFareAdjustmentConfServiceImplTest {
  @Mock private FlatFareAdjustmentConfService flatFareAdjustmentConfService;

  @Mock private FlatFareAdjustmentConfRepository flatFareAdjustmentConfRepository;
  @Mock private CacheService cacheService;

  @BeforeEach
  void setUp() {
    flatFareAdjustmentConfService =
        new FlatFareAdjustmentConfServiceImpl(flatFareAdjustmentConfRepository, cacheService);
  }

  @Test
  void test_loadFlatFareAdjustmentConf_success() {

    String key = "DYNAMIC_PRICING:FLAT_FARE:FLAT_FARE_ADJUSTMENT";
    List<FlatFareAdjustmentConfEntity> configList =
        Arrays.asList(new FlatFareAdjustmentConfEntity(), new FlatFareAdjustmentConfEntity());

    Mockito.when(flatFareAdjustmentConfRepository.getFlatFareAdjustmentConf())
        .thenReturn(configList);

    flatFareAdjustmentConfService.loadFlatFareAdjustmentConf();

    Mockito.verify(cacheService).deleteByKey(key);
    Mockito.verify(cacheService).setListValue(key, configList);
  }

  @Test
  void test_getFlatFareAdjustmentConf_success() {

    String key = "DYNAMIC_PRICING:FLAT_FARE:FLAT_FARE_ADJUSTMENT";
    int vehGrp = 0;
    List<FlatFareAdjustmentConfEntity> configList =
        Arrays.asList(
            FlatFareAdjustmentConfEntity.builder().vehGrp(1).fixedVal(3.0).build(),
            FlatFareAdjustmentConfEntity.builder().vehGrp(0).fixedVal(2.0).build());

    Mockito.when(cacheService.getListValue(key, FlatFareAdjustmentConfEntity.class))
        .thenReturn(configList);

    List<FlatFareAdjustmentConfEntity> result =
        flatFareAdjustmentConfService.getFlatFareAdjustmentConf();
    Assertions.assertEquals(2, result.size());
  }
}

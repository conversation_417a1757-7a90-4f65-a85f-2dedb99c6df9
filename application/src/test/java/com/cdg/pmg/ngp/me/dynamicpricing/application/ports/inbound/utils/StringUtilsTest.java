package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.Test;

class StringUtilsTest {
  @Test
  void givenCombinedString_whenExtractedQuoteString_thenReturnUnquote() {
    assertEquals("simpleString", StringUtils.unquote("simpleString"));
    assertEquals("quotedString", StringUtils.unquote("\"quotedString\""));
  }

  @Test
  void givenValidJson_whenTestIsJson_thenReturnTrue() {
    assertTrue(StringUtils.isJson("  {\"key\": \"value\"}  "));
  }

  @Test
  void givenValidJson_whenTestIsJson_thenReturnFalse() {
    assertFalse(StringUtils.isJson("[{\"value\"}]"));
  }
}

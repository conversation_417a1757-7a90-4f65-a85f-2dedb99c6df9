package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import static com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants.CHARGE_BY_PICKUP;
import static com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants.LOC_SURC_TYPE;
import static com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants.MON;
import static com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants.WED;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareHoliday;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.LocationSurchargeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.LocationSurchargeConfigRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.FlatFareConfigService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.DateUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.LocationSurchargeConfigRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.CacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.LocationSurchargeConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.queries.LocationSurchargeConfigQueryResponse;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class LocationSurchargeServiceImplTest {
  @InjectMocks LocationSurchargeServiceImpl locationSurchargeService;
  @Mock LocationSurchargeConfigRepository locationSurchargeConfigRepository;
  @Mock CacheService cacheService;
  @Mock FlatFareConfigService flatFareConfigService;

  @BeforeEach
  void init() {
    locationSurchargeService =
        new LocationSurchargeServiceImpl(
            locationSurchargeConfigRepository, cacheService, flatFareConfigService);
  }

  @Test
  void loadAllLocationSurchargeConfigs_isEmpty() {
    when(locationSurchargeConfigRepository.getLocationSurchargeConfigs(0))
        .then(config -> new LocationSurchargeConfigQueryResponse());
    locationSurchargeService.loadAllLocationSurchargeConfigs();
    Mockito.verify(cacheService, Mockito.times(0)).setValue(anyString(), Mockito.anyList());
  }

  @Test
  void loadAllLocationSurchargeConfigs_isNotEmpty() {
    final List<LocationSurchargeConfigEntity> configs = getLocationSurchargeConfigEntityList();
    final LocationSurchargeConfigQueryResponse emptyResponse =
        LocationSurchargeConfigQueryResponse.builder().configs(List.of()).build();
    final LocationSurchargeConfigQueryResponse response =
        LocationSurchargeConfigQueryResponse.builder().configs(configs).build();

    Mockito.when(locationSurchargeConfigRepository.getLocationSurchargeConfigs(0))
        .then(config -> response);
    Mockito.when(locationSurchargeConfigRepository.getLocationSurchargeConfigs(1))
        .thenReturn(emptyResponse);
    when(locationSurchargeConfigRepository.getLocationSurchargeConfigs(0)).then(config -> response);
    locationSurchargeService.loadAllLocationSurchargeConfigs();
    Mockito.verify(cacheService, Mockito.times(1)).setListValue(anyString(), Mockito.anyList());
  }

  private List<LocationSurchargeConfigEntity> getLocationSurchargeConfigEntityList() {
    LocationSurchargeConfigEntity locationSurchargeConfigEntity =
        new LocationSurchargeConfigEntity();
    locationSurchargeConfigEntity.setLocationId(1);
    locationSurchargeConfigEntity.setLocationName("air port");
    locationSurchargeConfigEntity.setSurchargeValue(4.0);
    locationSurchargeConfigEntity.setChargeBy("PICKUP");
    locationSurchargeConfigEntity.setAddressRef("5394324");
    locationSurchargeConfigEntity.setDayIndicator("MON");
    locationSurchargeConfigEntity.setFareType("FLAT");
    locationSurchargeConfigEntity.setProductId("STD");
    locationSurchargeConfigEntity.setStartTime("00:00:00");
    locationSurchargeConfigEntity.setEndTime("23:59:59");

    LocationSurchargeConfigEntity locationSurchargeConfigEntity2 =
        new LocationSurchargeConfigEntity();
    locationSurchargeConfigEntity2.setLocationId(5);
    locationSurchargeConfigEntity2.setLocationName("air port");
    locationSurchargeConfigEntity2.setSurchargeValue(4.0);
    locationSurchargeConfigEntity2.setChargeBy("PICKUP");
    locationSurchargeConfigEntity2.setAddressRef("5394324");
    locationSurchargeConfigEntity2.setDayIndicator("MON");
    locationSurchargeConfigEntity2.setFareType("FLAT");
    locationSurchargeConfigEntity2.setProductId("STD");
    locationSurchargeConfigEntity2.setStartTime("00:00:00");
    locationSurchargeConfigEntity2.setEndTime("23:59:59");

    return List.of(locationSurchargeConfigEntity, locationSurchargeConfigEntity2);
  }

  @Test
  void givenDestData_whenGetLocationSurchargeConfig_thenThrownException() {
    FlatFareHoliday holiday = FlatFareHoliday.builder().date("2024-09-20 00:00:00").build();
    List<FlatFareHoliday> flatFareHolidayList = new ArrayList<>();
    flatFareHolidayList.add(holiday);
    when(flatFareConfigService.getListHoliday()).thenReturn(flatFareHolidayList);
    LocationSurchargeConfigRequest request = initRequestPickup();
    request.setChargeBy("DEST");
    assertNull(locationSurchargeService.getLocationSurchargeConfig(request));
  }

  @Test
  void givenDaysInWeekData_whenGetLocationSurchargeConfig_thenReturnExpected() {

    FlatFareHoliday holiday = FlatFareHoliday.builder().date("2024-09-20 00:00:00").build();
    List<FlatFareHoliday> flatFareHolidayList = new ArrayList<>();
    flatFareHolidayList.add(holiday);
    when(flatFareConfigService.getListHoliday()).thenReturn(flatFareHolidayList);

    LocationSurchargeConfigRequest request = initRequestPickup();
    request.setChargeBy("PICKUP");

    when(cacheService.getListValue(anyString(), any()))
        .thenReturn(initLocMapPickUp(request.getAddressRef(), MON));

    assertDoesNotThrow(() -> locationSurchargeService.getLocationSurchargeConfig(request));
  }

  @Test
  void givenNotHolidayData_whenGetLocationSurchargeConfig_thenReturnExpected() {
    LocationSurchargeConfigRequest request = initRequestPickup();
    request.setChargeBy("PICKUP");

    FlatFareHoliday holiday = FlatFareHoliday.builder().date("2024-09-21 00:00:00").build();
    List<FlatFareHoliday> flatFareHolidayList = new ArrayList<>();
    flatFareHolidayList.add(holiday);
    when(flatFareConfigService.getListHoliday()).thenReturn(flatFareHolidayList);
    when(cacheService.getListValue(anyString(), any()))
        .thenReturn(initLocMapPickUp(request.getAddressRef(), MON));
    assertNotNull(locationSurchargeService.getLocationSurchargeConfig(request));
  }

  @Test
  void givenHolidayData_whenGetLocationSurchargeConfig_thenReturnExpected() {
    LocationSurchargeConfigRequest request = initRequestPickup();
    request.setChargeBy("PICKUP");

    FlatFareHoliday holiday = FlatFareHoliday.builder().date("2024-12-25 00:00:00").build();
    List<FlatFareHoliday> flatFareHolidayList = new ArrayList<>();
    flatFareHolidayList.add(holiday);
    when(flatFareConfigService.getListHoliday()).thenReturn(flatFareHolidayList);
    when(cacheService.getListValue(anyString(), any()))
        .thenReturn(initLocMapPickUp(request.getAddressRef(), WED));
    assertNotNull(locationSurchargeService.getLocationSurchargeConfig(request));
  }

  private LocationSurchargeConfigRequest initRequestPickup() {
    return LocationSurchargeConfigRequest.builder()
        .requestDate(OffsetDateTime.of(2024, 12, 25, 9, 3, 40, 0, ZoneOffset.of("+08:00")))
        .addressRef("106290")
        .chargeBy("PICKUP")
        .productId("STD001")
        .build();
  }

  private List<Object> initLocMapPickUp(String addressRef, String dayIndicator) {
    LocationSurchargeConfig locSur =
        LocationSurchargeConfig.builder()
            .addressRef("106290")
            .dayIndicator(FlatfareConstants.HOL)
            .chargeBy("PICKUP")
            .addressRef(addressRef)
            .dayIndicator(dayIndicator)
            .chargeBy(CHARGE_BY_PICKUP)
            .fareType(LOC_SURC_TYPE)
            .productId("STD001")
            .startTime(LocalTime.of(8, 0, 0))
            .endTime(LocalTime.of(23, 59, 59))
            .build();
    return Collections.singletonList(locSur);
  }

  @ParameterizedTest
  @CsvSource({"09/20/2023", "09/21/2023"})
  void givenNormalCondition_whenGetLocationSurchargeConfigList_thenSuccess(String reqDay)
      throws ParseException {
    // GIVEN
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy");
    var reqDate = df.parse(reqDay);

    FlatFareHoliday holiday = FlatFareHoliday.builder().date("2023-09-20 00:00:00").build();
    List<FlatFareHoliday> flatFareHolidayList = new ArrayList<>();
    flatFareHolidayList.add(holiday);

    // WHEN
    when(cacheService.getListValue(anyString(), any())).thenReturn(getLocationSurchargeConfig());

    // THEN
    assertNotNull(
        locationSurchargeService.getLocationSurchargeConfigList(
            reqDate, "address", "address", "address", flatFareHolidayList));
  }

  @ParameterizedTest
  @CsvSource({"09/20/2023", "09/21/2023"})
  void givenNullAddress_whenGetLocationSurchargeConfigList_thenSuccess(String reqDay)
      throws ParseException {
    // GIVEN
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy");
    var reqDate = df.parse(reqDay);

    FlatFareHoliday holiday = FlatFareHoliday.builder().date("2023-09-20 00:00:00").build();
    List<FlatFareHoliday> flatFareHolidayList = new ArrayList<>();
    flatFareHolidayList.add(holiday);

    // WHEN
    when(cacheService.getListValue(anyString(), any())).thenReturn(getLocationSurchargeConfig());

    // THEN
    assertNotNull(
        locationSurchargeService.getLocationSurchargeConfigList(
            reqDate, null, null, null, flatFareHolidayList));
  }

  private List<Object> getLocationSurchargeConfig() throws ParseException {
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy hh:mm:ss");
    String dateString = "09/20/2023 01:00:00";
    var reqDate = df.parse(dateString);

    LocalTime currentTime = DateUtils.convertToLocalTime(reqDate);
    return List.of(
        LocationSurchargeConfig.builder()
            .addressRef("1000")
            .startTime(currentTime)
            .endTime(currentTime)
            .productId("STD001")
            .chargeBy("PICKUP")
            .surchargeValue(0.3)
            .applicableDays("HOL")
            .dayIndicator("HOL")
            .locationName("Changi Airport")
            .build());
  }
}

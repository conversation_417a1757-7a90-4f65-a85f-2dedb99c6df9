package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareHoliday;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.Test;

class DateUtilsTest {

  public static Date createDate(int year, int month, int day, int hour, int minute) {
    Calendar calendar = Calendar.getInstance();
    calendar.set(year, month, day, hour, minute, 0);
    calendar.set(Calendar.MILLISECOND, 0);
    return calendar.getTime();
  }

  private static final String DDMMYYYY_FORMAT = "dd-MM-yyyy";

  @Test
  void givenValidTimeString_whenConvertToLocalTime_thenReturnLocalTime() {
    // GIVEN
    String time = "12:34:56";
    LocalTime expected = LocalTime.of(12, 34, 56);

    // WHEN
    LocalTime actual = DateUtils.convertToLocalTime(time);

    // THEN
    assertEquals(expected, actual);
  }

  @Test
  void givenEmptyString_whenConvertToLocalTime_thenReturnNull() {
    // GIVEN
    String emptyString = "";

    // WHEN
    LocalTime actual = DateUtils.convertToLocalTime(emptyString);

    // THEN
    assertNull(actual);
  }

  @Test
  void givenInvalidTimeString_whenConvertToLocalTime_thenReturnNull() {
    // GIVEN
    String emptyString = "1234:56";

    // WHEN
    LocalTime actual = DateUtils.convertToLocalTime(emptyString);

    // THEN
    assertNull(actual);
  }

  @Test
  void givenCurrentDate_whenConvertToLocalTime_thenReturnEquivalentLocalTime() {
    // GIVEN
    Date date = Date.from(Instant.parse("2023-09-20T10:10:10Z"));
    LocalTime expected = LocalTime.of(18, 10, 10);

    // WHEN
    LocalTime actual = DateUtils.convertToLocalTime(date);

    // THEN
    assertEquals(expected, actual);
  }

  @Test
  void givenNullDate_whenConvertToLocalTime_thenReturnNull() {
    // WHEN
    LocalTime actual = DateUtils.convertToLocalTime((Date) null);

    // THEN
    assertNull(actual);
  }

  @Test
  void givenCandidateTimeBetweenStartAndEndTimes_whenCheckIfBetween_thenReturnTrue() {
    // GIVEN
    LocalTime start = LocalTime.of(9, 0);
    LocalTime end = LocalTime.of(17, 0);

    // THEN
    assertTrue(DateUtils.isBetween(LocalTime.of(12, 0), start, end));
  }

  @Test
  void givenCandidateTimeOutsideOfStartAndEndTimes_whenCheckIfBetween_thenReturnFalse() {
    // GIVEN
    LocalTime start = LocalTime.of(9, 0);
    LocalTime end = LocalTime.of(17, 0);

    // THEN
    assertFalse(DateUtils.isBetween(LocalTime.of(18, 0), start, end));
    assertFalse(DateUtils.isBetween(LocalTime.of(8, 0), start, end));
  }

  @Test
  void givenCandidateTimeBetweenNightAndMorningTimes_whenCheckIfBetween_thenReturnTrue() {
    // GIVEN
    LocalTime start = LocalTime.of(20, 0);
    LocalTime end = LocalTime.of(6, 0);

    // THEN
    assertTrue(DateUtils.isBetween(LocalTime.of(23, 0), start, end));
    assertTrue(DateUtils.isBetween(LocalTime.of(3, 0), start, end));
  }

  @Test
  void givenCandidateTimeOutsideOfNightAndMorningTimes_whenCheckIfBetween_thenReturnFalse() {
    // GIVEN
    LocalTime start = LocalTime.of(20, 0);
    LocalTime end = LocalTime.of(6, 0);

    // THEN
    assertFalse(DateUtils.isBetween(LocalTime.of(19, 0), start, end));
    assertFalse(DateUtils.isBetween(LocalTime.of(7, 0), start, end));
  }

  @Test
  void givenCandidateDateBetweenStartAndEndDate_whenCheckIfBetween_thenReturnTrue() {
    // GIVEN
    Date start = createDate(2023, 2, 10, 0, 0);
    Date end = createDate(2023, 10, 10, 0, 0);
    Date candidate = createDate(2023, 9, 29, 0, 0);

    // THEN
    assertTrue(DateUtils.isBetween(candidate, start, end));
  }

  @Test
  void givenDateOutsideOfStartDateAndEndDate_whenCheckIfBetween_thenReturnFalse() {
    // GIVEN
    Date start = createDate(2023, 2, 10, 0, 0);
    Date end = createDate(2023, 10, 10, 0, 0);
    Date candidate1 = createDate(2023, 1, 31, 0, 0);
    Date candidate2 = createDate(2023, 1, 12, 0, 0);

    // THEN
    assertFalse(DateUtils.isBetween(candidate1, start, end));
    assertFalse(DateUtils.isBetween(candidate2, start, end));
  }

  @Test
  void givenDateWithinRangeOfStartAndEndLocalDates_whenCheckIfBetween_thenReturnTrue() {
    // GIVEN
    LocalDate start = LocalDate.of(2023, 4, 21);
    LocalDate end = LocalDate.of(2023, 10, 21);
    Date candidate = createDate(2023, Calendar.SEPTEMBER, 10, 10, 10);

    // THEN
    assertTrue(DateUtils.isBetween(candidate, start, end));
  }

  @Test
  void givenDateOutsideOfStartAndEndLocalDates_whenCheckIfBetween_thenReturnFalse() {
    // GIVEN
    LocalDate start = LocalDate.of(2023, 4, 21);
    LocalDate end = LocalDate.of(2023, 10, 21);
    Date candidate1 = createDate(2023, Calendar.MARCH, 10, 10, 10);
    Date candidate2 = createDate(2023, Calendar.DECEMBER, 10, 10, 10);

    // THEN
    assertFalse(DateUtils.isBetween(candidate1, start, end));
    assertFalse(DateUtils.isBetween(candidate2, start, end));
  }

  @Test
  void givenDateWithinRangeOfEndAndStartLocalDates_whenCheckIfBetween_thenReturnTrue() {
    // GIVEN
    LocalDate end = LocalDate.of(2023, 10, 10);
    LocalDate start = LocalDate.of(2023, 2, 10);
    Date candidate = Date.from(Instant.parse("2023-10-10T02:10:00Z"));

    // THEN
    assertTrue(DateUtils.isBetween(candidate, start, end));
  }

  @Test
  void givenDateOutsideOfEndAndStartLocalDates_whenCheckIfBetween_thenReturnFalse() {
    // GIVEN
    LocalDate start = LocalDate.of(2023, 10, 21);
    LocalDate end = LocalDate.of(2023, 2, 21);
    Date candidate1 = createDate(2023, Calendar.SEPTEMBER, 10, 10, 10);
    Date candidate2 = createDate(2023, Calendar.MARCH, 10, 10, 10);

    // THEN
    assertFalse(DateUtils.isBetween(candidate1, start, end));
    assertFalse(DateUtils.isBetween(candidate2, start, end));
  }

  @Test
  void givenValidDate_whenConvertToDayOfWeekShortUpperCase_thenReturnUpperCaseDay() {
    // GIVEN
    Date date = createDate(2023, Calendar.FEBRUARY, 20, 10, 10);
    String expected = "MON";

    // WHEN
    String actual = DateUtils.toDayOfWeekShortUpperCase(date);

    // THEN
    assertEquals(expected, actual);
  }

  @Test
  void givenNullDate_whenConvertToDayOfWeekShortUpperCase_thenReturnEmptyString() {
    // GIVEN
    String expected = "";

    // WHEN
    String actual = DateUtils.toDayOfWeekShortUpperCase(null);

    // THEN
    assertEquals(expected, actual);
  }

  @Test
  void givenValidDate_whenGetHourOfDate_thenReturnHour() {
    // GIVEN
    Date date = Date.from(Instant.parse("2023-09-20T22:00:00Z"));
    int expected = 6;

    // WHEN
    int actual = DateUtils.getHourOfDate(date);

    // THEN
    assertEquals(expected, actual);
  }

  @Test
  void givenValidDate_whenFormatToDayMonthYear_thenReturnFormattedString() {
    // GIVEN
    Date date = createDate(2023, Calendar.FEBRUARY, 20, 10, 20);
    String expected = "20-02-2023";

    // WHEN
    String actual = DateUtils.toddMMyyyy(date);

    // THEN
    assertEquals(expected, actual);
  }

  @Test
  void givenValidDateTimeString_whenFormatToDayMonthYear_thenReturnFormattedString()
      throws ParseException {
    // GIVEN
    String daytime = "2023-02-20 14:30:00";
    String expected = "20-02-2023";

    // WHEN
    String actual = DateUtils.toddMMyyyy(daytime);

    // THEN
    assertEquals(expected, actual);
  }

  @Test
  void givenDateString_whenStringToDDmmYYYY_thenDoesNotThrow() {
    String requestDate = "2024-01-18T09:03:40.822Z";
    assertDoesNotThrow(() -> DateUtils.stringToDDmmYYYY(requestDate));
  }

  @Test
  void givenInvalidDateString_whenStringToDDmmYYYY_thenReturnNull() {
    String requestDate = "invalidRequestDate";
    assertNull(DateUtils.stringToDDmmYYYY(requestDate));
  }

  @Test
  void givenValidDate_whenToDayOfMonth_thenReturnStringDay() {
    // GIVEN
    Date date = Date.from(Instant.parse("2023-09-20T22:00:00Z"));
    String expected = "21";

    // WHEN
    String actual = DateUtils.toDayOfMonth(date);

    // THEN
    assertEquals(expected, actual);
  }

  @Test
  void givenValidDate_whenToMonth_thenReturnStringMonth() {
    // GIVEN
    Date date = Date.from(Instant.parse("2023-09-20T22:00:00Z"));
    String expected = "9";

    // WHEN
    String actual = DateUtils.toMonth(date);

    // THEN
    assertEquals(expected, actual);
  }

  @Test
  void givenInt_whenMinuteToMillisecond_thenReturnLong() {
    // GIVEN
    int minutes = 2;
    long expected = 120000L;

    // WHEN
    long actual = DateUtils.minuteToMillisecond(minutes);

    // THEN
    assertEquals(expected, actual);
  }

  @Test
  void givenHoliday_whenIsHolidaySingTime_thenReturnTrue() {
    FlatFareHoliday holiday = FlatFareHoliday.builder().date("2023-09-20 00:00:00").build();
    List<FlatFareHoliday> flatFareHolidayList = new ArrayList<>();
    flatFareHolidayList.add(holiday);

    // Sing time = reqDateUTC + 8
    Date reqDateUTC = Date.from(Instant.parse("2023-09-19T22:00:00Z"));

    assertTrue(DateUtils.isHolidaySingTime(reqDateUTC, flatFareHolidayList));
  }

  @Test
  void givenNotHoliday_whenIsHolidaySingTime_thenReturnFalse() {
    FlatFareHoliday holiday = FlatFareHoliday.builder().date("2023-09-20 00:00:00").build();
    List<FlatFareHoliday> flatFareHolidayList = new ArrayList<>();
    flatFareHolidayList.add(holiday);

    // Sing time = reqDateUTC + 8
    Date reqDateUTC = Date.from(Instant.parse("2023-09-20T22:00:00Z"));

    assertFalse(DateUtils.isHolidaySingTime(reqDateUTC, flatFareHolidayList));
  }

  @Test
  void givenNormalParam_whenMinuteToSecond_thenReturnSuccess() {
    assertEquals(60, DateUtils.minuteToSecond(1));
  }

  @Test
  void givenDate_whenCheckIsHoliday_thenReturnTrue() throws ParseException {
    SimpleDateFormat sdf = new SimpleDateFormat(DDMMYYYY_FORMAT);
    Date reqDate = sdf.parse("25-12-2023");
    List<String> holidaysList = Arrays.asList("2023-12-25 00:00:00", "01-01-2024 00:00:00");
    assertTrue(
        DateUtils.isHolidaySingTime(reqDate, DateUtils.mapToListFlatFareHoliday(holidaysList)));
  }

  @Test
  void givenApplicableDays_whenCheckForHoliday_thenReturnTrue() {
    String applicableDays = "MON,HOL";

    assertTrue(DateUtils.isApplicableDaysContainsHOL(applicableDays));
  }
}

import org.gradle.api.tasks.testing.logging.TestExceptionFormat
import org.gradle.api.tasks.testing.logging.TestLogEvent

plugins {
  java
  jacoco
  id("io.spring.dependency-management") version "1.0.15.RELEASE"
}

dependencyManagement {
  imports { mavenBom("org.springframework.boot:spring-boot-dependencies:3.1.3") }
}

group = "com.cdg.pmg.ngp.me.dynamicpricing.application"

version = "0.0.1-SNAPSHOT"

java.sourceCompatibility = JavaVersion.VERSION_17

jacoco { toolVersion = "0.8.8" }

repositories {
  mavenCentral()
  mavenLocal()
}

dependencies {
  implementation(project(":domain"))

  implementation(libs.jackson.annotations)
  implementation(libs.jackson.datatype)
  implementation(libs.slf4j.api)
  implementation(libs.apache.commons.lang)
  implementation(libs.jakarta.annotation.api)
  implementation(libs.jakarta.inject)
  implementation(libs.guava)

  // Spring Transaction support
  implementation("org.springframework:spring-tx")

  // lombok compile and processor
  compileOnly(libs.lombok)
  annotationProcessor(libs.lombok)
  annotationProcessor(libs.lombok.mapstruct.binding)

  // lombok compile and processor
  compileOnly(libs.lombok)
  annotationProcessor(libs.lombok)
  annotationProcessor(libs.lombok.mapstruct.binding)

  implementation(libs.mapstruct)
  annotationProcessor(libs.mapstruct.processor)

  // Test implementation
  testImplementation(platform(libs.junit.bom))
  testImplementation(libs.junit.jupiter)
  testImplementation(libs.mockito.junit.jupiter)
}

// ============= MapStruct Setting =====================
// Get mapstruct to using jakarta instead of javax
tasks.withType<JavaCompile>().configureEach {
  options.encoding = "UTF-8"
  options.compilerArgs.add("-Amapstruct.defaultComponentModel=jakarta")
  options.compilerArgs.add("-Amapstruct.unmappedTargetPolicy=IGNORE")
}

// ================= Jacoco Settings ================
tasks.jacocoTestReport {
  dependsOn(tasks.test) // tests are required to run before generating the report
  reports {
    xml.required.set(true)
    html.required.set(true)
    csv.required.set(false)
    xml.outputLocation.set(layout.buildDirectory.file("reports/jacoco/unit/testReport.xml"))
    html.outputLocation.set(layout.buildDirectory.dir("reports/jacoco/unit/html"))
  }
}

// ================== Unit Tests Settings ==================
tasks.withType<Test> {
  useJUnitPlatform()
  testLogging {
    events(
        TestLogEvent.PASSED, TestLogEvent.FAILED, TestLogEvent.STANDARD_ERROR, TestLogEvent.SKIPPED)
    exceptionFormat = TestExceptionFormat.FULL
    showExceptions = true
    showCauses = true
    showStackTraces = true
  }
  reports {
    html.required.set(true)
    junitXml.apply {
      // isOutputPerTestCase = true // defaults to false
      mergeReruns.set(true) // defaults to false
    }
  }
  outputs.upToDateWhen { false }
  addTestListener(
      object : TestListener {
        override fun beforeSuite(suite: TestDescriptor) {}
        override fun beforeTest(testDescriptor: TestDescriptor) {}
        override fun afterTest(testDescriptor: TestDescriptor, result: TestResult) {}
        override fun afterSuite(suite: TestDescriptor, result: TestResult) {
          if (suite.parent == null) {
            println("----")
            println("Suite name: ${suite.name}")
            println("Test result: ${result.resultType}")
            println(
                "Test summary: ${result.testCount} tests, " +
                    "${result.successfulTestCount} succeeded, " +
                    "${result.failedTestCount} failed, " +
                    "${result.skippedTestCount} skipped")
          }
        }
      })
}

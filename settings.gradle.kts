rootProject.name = "me-dynamicpricing"

include("techframework", "application", "domain")

dependencyResolutionManagement {
  versionCatalogs {
    create("libs") {
      version("spring-boot", "3.1.3")
      version("spring", "6.0.11")
      version("spring-cloud", "4.1.0")
      version("spring-integration", "6.0.5")
      version("aws-spring-cloud", "3.0.0")
      version("jakarta-servlet-api", "6.0.0")
      version("jakarta.persistence-api", "3.1.0")
      version("jakarta-validation", "3.0.2")
      version("jakarta-inject", "2.0.1")
      version("jakarta-annotation-api", "2.0.0")
      version("postgresql", "42.6.0")
      version("liquibase-core", "4.16.1")
      version("liquibase-groovy-dsl", "3.0.2")
      version("picocli", "4.6.1")
      version("micrometer", "1.11.5")
      version("micrometer-tracing-bridge-otel", "1.0.4")
      version("logbook", "3.0.0-RC.2")
      version("logback-json", "0.1.5")
      version("apache-commons-lang", "3.13.0")
      version("commons-io", "2.11.0")
      version("jackson", "2.14.2")
      version("git-commit-id", "5.0.0")
      version("lombok", "1.18.26")
      version("mapstruct", "1.5.5.Final")
      version("lombok-mapstruct-binding", "0.2.0")
      version("spring-data-redis", "3.0.5")
      version("lettuce", "6.2.4.RELEASE")
      version("springdoc-openapi", "2.1.0")
      version("therapi-javadoc", "0.15.0")
      version("openapi-jackson-databind-nullable", "0.2.6")
      version("feign-micrometer", "13.0")
      version("protobuf", "3.25.1")
      version("guava", "32.0.1-jre")
      version("spring-kafka", "3.0.6")
      version("slf4j-api", "2.0.6")
      version("junit", "5.10.0")
      version("mockito-junit-jupiter", "5.6.0")
      version("testcontainers", "1.19.0")
      version("testcontainers-redis", "1.6.4")
      version("retrofit", "2.9.0")

      // Spring Boot
      library("spring-boot-starter-web", "org.springframework.boot", "spring-boot-starter-web")
          .versionRef("spring-boot")
      library(
              "spring-boot-starter-validation",
              "org.springframework.boot",
              "spring-boot-starter-validation")
          .versionRef("spring-boot")
      library(
              "spring-boot-configuration-processor",
              "org.springframework.boot",
              "spring-boot-configuration-processor")
          .versionRef("spring-boot")
      library(
              "spring-boot-starter-actuator",
              "org.springframework.boot",
              "spring-boot-starter-actuator")
          .versionRef("spring-boot")
      library(
              "spring-boot-starter-integration",
              "org.springframework.boot",
              "spring-boot-starter-integration")
          .versionRef("spring-boot")
      library(
              "spring-boot-starter-data-jpa",
              "org.springframework.boot",
              "spring-boot-starter-data-jpa")
          .versionRef("spring-boot")

      // Spring
      library("spring-core", "org.springframework", "spring-core").versionRef("spring")

      // Spring Cloud
      library(
              "spring-cloud-starter-config",
              "org.springframework.cloud",
              "spring-cloud-starter-config")
          .versionRef("spring-cloud")
      library(
              "spring-cloud-starter-bus-kafka",
              "org.springframework.cloud",
              "spring-cloud-starter-bus-kafka")
          .versionRef("spring-cloud")
      library(
              "spring-cloud-starter-openfeign",
              "org.springframework.cloud",
              "spring-cloud-starter-openfeign")
          .versionRef("spring-cloud")

      // Integration for upload file by SFTP
      library(
              "spring-integration-sftp",
              "org.springframework.integration",
              "spring-integration-sftp")
          .versionRef("spring-integration")
      library(
              "spring-integration-file",
              "org.springframework.integration",
              "spring-integration-file")
          .versionRef("spring-integration")

      // AWS Parameter Store for externalized configuration
      library("spring-cloud-aws-starter", "io.awspring.cloud", "spring-cloud-aws-starter")
          .versionRef("aws-spring-cloud")
      library(
              "spring-cloud-aws-starter-parameter-store",
              "io.awspring.cloud",
              "spring-cloud-aws-starter-parameter-store")
          .versionRef("aws-spring-cloud")
      library(
              "spring-cloud-aws-starter-secrets-manager",
              "io.awspring.cloud",
              "spring-cloud-aws-starter-secrets-manager")
          .versionRef("aws-spring-cloud")

      // Jakarta Servlet API - Since from Spring Boot 3, javax namespace was changed to jakarta
      library("jakarta-servlet-api", "jakarta.servlet", "jakarta.servlet-api")
          .versionRef("jakarta-servlet-api")
      library("jakarta-persistence-api", "jakarta.persistence", "jakarta.persistence-api")
          .versionRef("jakarta.persistence-api")
      library("jakarta-validation", "jakarta.validation", "jakarta.validation-api")
          .versionRef("jakarta-validation")
      library("jakarta-annotation-api", "jakarta.annotation", "jakarta.annotation-api")
          .versionRef("jakarta-annotation-api")
      library("jakarta-inject", "jakarta.inject", "jakarta.inject-api").versionRef("jakarta-inject")

      // Database
      library("postgresql", "org.postgresql", "postgresql").versionRef("postgresql")

      // Database Version Control with Liquibase
      library("liquibase-core", "org.liquibase", "liquibase-core").versionRef("liquibase-core")
      library("liquibase-groovy-dsl", "org.liquibase", "liquibase-groovy-dsl")
          .versionRef("liquibase-groovy-dsl")
      library("picocli", "info.picocli", "picocli").versionRef("picocli")

      // Monitoring and Logging
      library("micrometer-registry-prometheus", "io.micrometer", "micrometer-registry-prometheus")
          .versionRef("micrometer")
      library("micrometer-core", "io.micrometer", "micrometer-core").versionRef("micrometer")
      library("micrometer-tracing-bridge-otel", "io.micrometer", "micrometer-tracing-bridge-otel")
          .versionRef("micrometer-tracing-bridge-otel")
      library("logbook-spring-boot-starter", "org.zalando", "logbook-spring-boot-starter")
          .versionRef("logbook")
      library("logback-json-classic", "ch.qos.logback.contrib", "logback-json-classic")
          .versionRef("logback-json")
      library("logback-jackson", "ch.qos.logback.contrib", "logback-jackson")
          .versionRef("logback-json")

      // Version Control Integration
      library("git-commit-id-maven-plugin", "io.github.git-commit-id", "git-commit-id-maven-plugin")
          .versionRef("git-commit-id")

      // Code Generation and Processing
      library("lombok", "org.projectlombok", "lombok").versionRef("lombok")
      library("lombok-mapstruct-binding", "org.projectlombok", "lombok-mapstruct-binding")
          .versionRef("lombok-mapstruct-binding")
      library("mapstruct", "org.mapstruct", "mapstruct").versionRef("mapstruct")
      library("mapstruct-processor", "org.mapstruct", "mapstruct-processor").versionRef("mapstruct")

      // Caching
      library("spring-data-redis", "org.springframework.data", "spring-data-redis")
          .versionRef("spring-data-redis")
      library("lettuce-core", "io.lettuce", "lettuce-core").versionRef("lettuce")

      // API Documentation
      library(
              "springdoc-openapi-starter-webmvc-ui",
              "org.springdoc",
              "springdoc-openapi-starter-webmvc-ui")
          .versionRef("springdoc-openapi")
      library("therapi-runtime-javadoc", "com.github.therapi", "therapi-runtime-javadoc")
          .versionRef("therapi-javadoc")
      library("openapi-jackson-databind-nullable", "org.openapitools", "jackson-databind-nullable")
          .versionRef("openapi-jackson-databind-nullable")

      // HTTP Client
      library("feign-micrometer", "io.github.openfeign", "feign-micrometer")
          .versionRef("feign-micrometer")

      // Common
      library("apache-commons-lang", "org.apache.commons", "commons-lang3")
          .versionRef("apache-commons-lang")
      library("commons-io", "commons-io", "commons-io").versionRef("commons-io")

      // Jackson
      library("jackson-core", "com.fasterxml.jackson.core", "jackson-core").versionRef("jackson")
      library("jackson-annotations", "com.fasterxml.jackson.core", "jackson-annotations")
          .versionRef("jackson")
      library("jackson-databind", "com.fasterxml.jackson.core", "jackson-databind")
          .versionRef("jackson")
      library("jackson-datatype", "com.fasterxml.jackson.datatype", "jackson-datatype-jsr310")
          .versionRef("jackson")

      // protobuf
      library("protobuf-java", "com.google.protobuf", "protobuf-java").versionRef("protobuf")
      library("protoc", "com.google.protobuf", "protoc").versionRef("protobuf")

      // Guava
      library("guava", "com.google.guava", "guava").versionRef("guava")

      // Log
      library("slf4j-api", "org.slf4j", "slf4j-api").versionRef("slf4j-api")

      // Test Libraries
      library("spring-boot-starter-test", "org.springframework.boot", "spring-boot-starter-test")
          .versionRef("spring-boot")
      library("junit-bom", "org.junit", "junit-bom").versionRef("junit")
      library("junit-jupiter", "org.junit.jupiter", "junit-jupiter").versionRef("junit")
      library("junit-jupiter-api", "org.junit.jupiter", "junit-jupiter-api").versionRef("junit")
      library("mockito-junit-jupiter", "org.mockito", "mockito-junit-jupiter")
          .versionRef("mockito-junit-jupiter")
      library("testcontainers", "org.testcontainers", "testcontainers").versionRef("testcontainers")
      library("testcontainers-postgresql", "org.testcontainers", "postgresql")
          .versionRef("testcontainers")
      library("testcontainers-kafka", "org.testcontainers", "kafka").versionRef("testcontainers")
      library("testcontainers-junit-jupiter", "org.testcontainers", "junit-jupiter")
          .versionRef("testcontainers")
      library(
              "testcontainers-wiremock",
              "org.wiremock.integrations.testcontainers",
              "wiremock-testcontainers-module")
          .version("1.0-alpha-13")
      library("retrofit", "com.squareup.retrofit2", "retrofit").versionRef("retrofit")
      library("retrofit-jackson", "com.squareup.retrofit2", "converter-jackson")
          .versionRef("retrofit")
      library("kafka-test", "org.springframework.kafka", "spring-kafka-test")
          .versionRef("spring-kafka")
      library("testcontainers-redis", "com.redis.testcontainers", "testcontainers-redis")
          .versionRef("testcontainers-redis")
      library(
              "spring-integration-test",
              "org.springframework.integration",
              "spring-integration-test")
          .versionRef("spring-integration")
    }
  }
}

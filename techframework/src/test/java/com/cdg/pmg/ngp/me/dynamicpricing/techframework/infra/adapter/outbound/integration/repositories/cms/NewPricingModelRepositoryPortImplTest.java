package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.cms;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.mapper.NewPricingModelMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.CMSConfigItem;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.CMSServiceOutboundPort;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.NewPricingModelConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs.SpringSecurityAuditorAware;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mapstruct.factory.Mappers;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class NewPricingModelRepositoryPortImplTest {

  @Mock CMSServiceOutboundPort cmsServiceOutboundPort;

  NewPricingModelRepositoryPortImpl newPricingModelRepositoryPort;
  @Mock SpringSecurityAuditorAware springSecurityAuditorAware;
  @Mock ObjectMapper objectMapper;
  @Spy NewPricingModelMapper newPricingModelMapper = Mappers.getMapper(NewPricingModelMapper.class);
  @Captor ArgumentCaptor<String> stringArgumentCaptor;

  @BeforeEach
  void init() {
    newPricingModelRepositoryPort =
        new NewPricingModelRepositoryPortImpl(
            springSecurityAuditorAware,
            newPricingModelMapper,
            objectMapper,
            cmsServiceOutboundPort);
  }

  @Test
  void whenUpdate_thenDoNotThrows() {
    String updatedBy = "ADMIN";
    when(springSecurityAuditorAware.getCurrentAuditor()).thenReturn(Optional.of(updatedBy));
    String json = """
            {
              "id": "test"
            }
            """;
    doReturn(json).when(newPricingModelMapper).toJson(any(), any());

    var newPricingModelEntity = NewPricingModelConfigEntity.builder().build();
    newPricingModelRepositoryPort.update(newPricingModelEntity);

    verify(cmsServiceOutboundPort, Mockito.times(1))
        .updateCMSConfig(any(), stringArgumentCaptor.capture());
    var jsonResponse = stringArgumentCaptor.getValue();
    Assertions.assertEquals(json, jsonResponse);
  }

  @Test
  void whenCreate_thenReturnCreatedNewPricingModelEntity() {
    String createdBy = "ADMIN";
    int expectedIndex = 1;
    when(springSecurityAuditorAware.getCurrentAuditor()).thenReturn(Optional.of(createdBy));
    var entity = new NewPricingModelConfigEntity();
    when(cmsServiceOutboundPort.createCmsConfig(any()))
        .thenReturn(CMSConfigItem.builder().id(Long.valueOf(expectedIndex)).build());

    newPricingModelRepositoryPort.create(entity, expectedIndex);
    verify(cmsServiceOutboundPort).createCmsConfig(any());
  }

  @Test
  void whenGetListNewPricingModelConfigEntityInCms_thenReturnNewPricingModelConfigEntityList() {
    doReturn(List.of(new NewPricingModelConfigEntity()))
        .when(newPricingModelMapper)
        .mapToNewPricingModelConfigEntities(any(), any());
    List<NewPricingModelConfigEntity> result =
        newPricingModelRepositoryPort.getListNewPricingModelConfigEntityInCms();
    verify(newPricingModelMapper).mapToNewPricingModelConfigEntities(any(), any());
    Assertions.assertEquals(1, result.size());
    ;
  }
}

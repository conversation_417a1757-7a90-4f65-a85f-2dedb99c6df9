package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class DatabaseConfigTest {
  @InjectMocks private DatabaseConfig databaseConfig;

  @Test
  void testInitDatabaseConfiguration() {
    databaseConfig = new DatabaseConfig();
  }
}

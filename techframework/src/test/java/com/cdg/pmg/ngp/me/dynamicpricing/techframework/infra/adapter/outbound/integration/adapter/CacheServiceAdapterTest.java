package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.adapter;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.redis.RedisService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/** The type Cache service impl test. */
@ExtendWith(MockitoExtension.class)
class CacheServiceAdapterTest {
  /** The Cache service. */
  @Mock CacheServiceAdapter cacheService;
  /** The Redis service. */
  @Mock RedisService redisService;

  private Set<String> listKeys;
  private static final String KEY = "KEY";
  private static final String VALUE = "VALUE";
  private static final String KEY_MAP = "KEY_MAP";
  private static final String KEY_PATTERN = "KEY_PATTERN";

  /** Sets up. */
  @BeforeEach
  void setUp() {
    cacheService = new CacheServiceAdapter(redisService);
    listKeys = new HashSet<>();
    listKeys.add("KEY1");
    listKeys.add("KEY2");
  }

  /** Gets value return value do not throw exception. */
  @Test
  void givenKey_whenGetValue_thenReturnValue() {
    when(redisService.getValue(any(), any())).thenReturn(new Object());
    final var response = cacheService.getValue(VALUE, Object.class);
    assertNotNull(response);
  }

  /** Sets value set successfully do not throw exception. */
  @Test
  void givenKeyValue_whenSetValue_thenNotThrowException() {
    final CacheServiceAdapter cacheServiceSpy = spy(cacheService);
    cacheServiceSpy.setValue(KEY, VALUE);
    verify(cacheServiceSpy, times(1)).setValue(KEY, VALUE);
  }

  /** Sets value with int expire duration set successfully do not throw exception. */
  @Test
  void givenKeyValueWithIntExpireDuration_whenSetValue_thenNotThrowException() {
    final CacheServiceAdapter cacheServiceSpy = spy(cacheService);
    cacheServiceSpy.setValue(KEY, VALUE, 10);
    verify(cacheServiceSpy, times(1)).setValue(KEY, VALUE, 10);
  }

  /** Sets value with long expire duration set successfully do not throw exception. */
  @Test
  void givenKeyValueWithLongExpireDuration_whenSetValue_thenNotThrowException() {
    final CacheServiceAdapter cacheServiceSpy = spy(cacheService);
    cacheServiceSpy.setValue(KEY, VALUE, 10L);
    verify(cacheServiceSpy, times(1)).setValue(KEY, VALUE, 10L);
  }

  /** Gets keys by pattern return list keys do not throw exception. */
  @Test
  void givenPatternKey_whenGetKeysByPattern_thenReturnListKeys() {
    when(redisService.getKeysByPattern(KEY_PATTERN)).thenReturn(listKeys);
    final var response = cacheService.getKeysByPattern(KEY_PATTERN);
    assertEquals(listKeys.size(), response.size());
  }

  /** Gets keys by pattern, suffix, prefix return list keys do not throw exception. */
  @Test
  void givenPatternSuffixPrefixKey_whenGetKeysByPattern_thenReturnListKeys() {
    when(redisService.getKeysByPattern(
            RedisKeyConstant.WILDCARD.concat(KEY_PATTERN).concat(RedisKeyConstant.WILDCARD)))
        .thenReturn(listKeys);
    final var response =
        cacheService.getKeysByPattern(
            KEY_PATTERN, RedisKeyConstant.WILDCARD, RedisKeyConstant.WILDCARD);
    assertEquals(listKeys.size(), response.size());
  }

  /** Delete by key not throw exception. */
  @Test
  void givenKey_whenDeleteByKey_thenNotThrowException() {
    final CacheServiceAdapter cacheServiceSpy = spy(cacheService);
    cacheServiceSpy.deleteByKey(KEY);
    verify(cacheServiceSpy, times(1)).deleteByKey(KEY);
  }

  /** Set int expire time by key and not throw exception. */
  @Test
  void givenKeyIntExpireTime_whenSetExpire_thenNotThrowException() {
    final CacheServiceAdapter cacheServiceSpy = spy(cacheService);
    cacheServiceSpy.setExpire(KEY, 10);
    verify(cacheServiceSpy, times(1)).setExpire(KEY, 10);
  }

  /** Set long expire time by key and not throw exception. */
  @Test
  void givenKeyLongExpireTime_whenSetExpire_thenNotThrowException() {
    final CacheServiceAdapter cacheServiceSpy = spy(cacheService);
    cacheServiceSpy.setExpire(KEY, 10L);
    verify(cacheServiceSpy, times(1)).setExpire(KEY, 10L);
  }

  /** Get Multi Value Map by key, value map and set of keys by pattern. */
  @Test
  void givenKeyValueSetKeysByPattern_whenGetMultiValueMap_thenReturnListMap() {
    when(redisService.getMultiValueMap(listKeys, String.class, String.class))
        .thenReturn(List.of(new HashMap<>()));
    final var response = cacheService.getMultiValueMap(listKeys, String.class, String.class);
    assertNotNull(response);
  }

  /** Get Multi Value List by type of list and set of keys by pattern. */
  @Test
  void givenTypeAndSetKeysByPattern_whenGetMultiValueList_thenReturnList() {
    when(redisService.getMultiValueList(listKeys, String.class))
        .thenReturn(List.of(new ArrayList<>()));
    final var response = cacheService.getMultiValueList(listKeys, String.class);
    assertNotNull(response);
  }

  /** Get List Value by key */
  @Test
  void givenTypeAndKey_whenGetListValue_thenReturnList() {
    when(redisService.getListValue(KEY, String.class)).thenReturn(List.of(VALUE));
    final var response = cacheService.getListValue(KEY, String.class);
    assertEquals(1, response.size());
  }

  /** Get Map Value by key */
  @Test
  void givenKeyCacheAndKeyValueMap_whenGetListMap_thenReturnMap() {
    when(redisService.getMapValue(KEY, String.class, String.class))
        .thenReturn(Map.of(KEY_MAP, VALUE));
    final var response = cacheService.getMapValue(KEY, String.class, String.class);
    assertEquals(VALUE, response.get(KEY_MAP));
  }

  @Test
  void givenValidKey_whenDeleteByKeys_thenDeleteInRedis() {
    final CacheServiceAdapter cacheServiceSpy = spy(cacheService);
    cacheServiceSpy.deleteByKeys(Set.of(KEY));
    // THEN
    verify(cacheServiceSpy, times(1)).deleteByKeys(Set.of(KEY));
  }

  @Test
  void givenValidKey_whenSetListValue_thenNotThrowException() {
    final CacheServiceAdapter cacheServiceSpy = spy(cacheService);
    cacheServiceSpy.setListValue(KEY, List.of(VALUE));
    // THEN
    verify(cacheServiceSpy, times(1)).setListValue(KEY, List.of(VALUE));
  }

  @Test
  void givenValidParam_whenGetDataJsonByKey_thenReturnJsonData() {
    String key1 = "key1";
    String dataJson1 = "{\"data1\"}";
    // WHEN
    when(redisService.getStringValueByKey(key1)).thenReturn(dataJson1);

    // THEN
    String result = cacheService.getStringValue(key1);
    assertNotNull(result);
    assertEquals(dataJson1, result);
    verify(redisService).getStringValueByKey(key1);
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa;

import static org.mockito.Mockito.when;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.FlatFareConfigRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FlatFareConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.FlatFareConfigJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.FlatFareConfigJPARepository;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class FlatFareConfigRepositoryImplTest {
  @Mock private FlatFareConfigRepository flatFareConfigRepository;
  @Mock private FlatFareConfigJPARepository flatFareConfigJPARepository;

  @BeforeEach
  void setUp() {
    flatFareConfigRepository = new FlatFareConfigRepositoryImpl(flatFareConfigJPARepository);
  }

  @Test
  void getAllFlatFareConfig_isEmptyConfig() {
    Mockito.when(flatFareConfigJPARepository.getAllValidFlatFareConfig()).then(config -> List.of());
    final List<FlatFareConfig> actual = flatFareConfigRepository.getAllFlatFareConfig();
    final List<FlatFareConfig> expected = new ArrayList<>();
    Assertions.assertEquals(expected, actual);
  }

  @Test
  void getAllFlatFareConfig_isNotEmptyConfig() {
    final List<FlatFareConfigJPA> resultQuery = List.of(new FlatFareConfigJPA());
    Mockito.when(flatFareConfigJPARepository.getAllValidFlatFareConfig())
        .then(config -> resultQuery);
    final List<FlatFareConfig> actual = flatFareConfigRepository.getAllFlatFareConfig();

    final FlatFareConfigJPA config = resultQuery.get(0);
    final boolean expected =
        actual.stream()
            .allMatch(
                actualConf ->
                    Objects.equals(actualConf.getParamKey(), config.getParamKey())
                        && Objects.equals(actualConf.getParamValue(), config.getParamValue()));
    Assertions.assertTrue(expected);
  }

  @Test
  void getConfByParamkeyFlatFare() {
    final List<String> expected = new ArrayList<>();
    when(flatFareConfigJPARepository.getConfByParamkeyFlatFare(Mockito.anyString()))
        .thenReturn(expected);

    final List<String> actual =
        flatFareConfigRepository.getConfByParamkeyFlatFare(Mockito.anyString());
    Assertions.assertEquals(expected, actual);
  }

  @Test
  void getConfByIdentifyForCalDemandSurge_isEmptyConfig() {
    Mockito.when(
            flatFareConfigJPARepository.getConfByIdentifyForCalDemandSurge(Mockito.anyString()))
        .then(config -> List.of());
    final List<FlatFareConfig> actual =
        flatFareConfigRepository.getConfByIdentifyForCalDemandSurge(Mockito.anyString());
    final List<FlatFareConfig> expected = new ArrayList<>();
    Assertions.assertEquals(expected, actual);
  }

  @Test
  void getConfByIdentifyForCalDemandSurge_isNotEmptyConfig() {
    final List<FlatFareConfigJPA> resultQuery = List.of(new FlatFareConfigJPA());
    Mockito.when(
            flatFareConfigJPARepository.getConfByIdentifyForCalDemandSurge(Mockito.anyString()))
        .then(config -> resultQuery);
    final List<FlatFareConfig> actual =
        flatFareConfigRepository.getConfByIdentifyForCalDemandSurge(Mockito.anyString());

    final FlatFareConfigJPA config = resultQuery.get(0);
    final boolean expected =
        actual.stream()
            .allMatch(
                actualConf ->
                    Objects.equals(actualConf.getParamKey(), config.getParamKey())
                        && Objects.equals(actualConf.getParamValue(), config.getParamValue()));
    Assertions.assertTrue(expected);
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.sftp;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs.SftpConfigParams;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs.sftp.UploadConfiguration;
import java.io.File;
import java.nio.file.Paths;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.expression.common.LiteralExpression;
import org.springframework.integration.file.remote.session.SessionFactory;
import org.springframework.integration.sftp.outbound.SftpMessageHandler;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHandler;
import org.springframework.messaging.support.GenericMessage;
import org.springframework.messaging.support.MessageBuilder;

@ExtendWith(MockitoExtension.class)
class UploadConfigurationTest {

  @Mock private UploadConfiguration uploadConfiguration;
  @Mock private SftpConfigParams sftpConfigParams;

  @BeforeEach
  void setUp() {
    uploadConfiguration = new UploadConfiguration(sftpConfigParams);
  }

  @Test
  void givenDefaultSessionFactory_whenGetSessionFactoryWithNoParams_thenReturnMockData() {
    when(sftpConfigParams.getHost()).thenReturn("");
    when(sftpConfigParams.getUser()).thenReturn("");
    when(sftpConfigParams.getPrivateKey()).thenReturn("");

    SessionFactory sessionFactory = uploadConfiguration.defaultSessionFactory();
    assertNotNull(sessionFactory);
  }

  @Test
  void givenNoParam_whenCallUploadHandler_thenReturnNotNull() {
    when(sftpConfigParams.getPrivateKey()).thenReturn("testPrivateKey");
    MessageHandler messageHandler = uploadConfiguration.createFileReplacingUploadHandler();
    Assertions.assertNotNull(messageHandler);
  }

  @Test
  void givenDefaultSessionFactory_whenCallUploadHandler_thenReturnMockData() {
    when(sftpConfigParams.getHost()).thenReturn("");
    when(sftpConfigParams.getUser()).thenReturn("");
    when(sftpConfigParams.getSftpDir()).thenReturn("/");
    var url = "partnerupload.google.com";
    when(sftpConfigParams.getPrivateKey()).thenReturn(String.valueOf(Paths.get(url)));

    SessionFactory sessionFactory = uploadConfiguration.defaultSessionFactory();

    SftpMessageHandler handler = new SftpMessageHandler(sessionFactory);
    handler.setRemoteDirectoryExpression(new LiteralExpression(sftpConfigParams.getSftpDir()));
    handler.setFileNameGenerator(
        message -> {
          if (message.getPayload() instanceof File) {
            return ((File) message.getPayload()).getName();
          } else {
            throw new IllegalArgumentException("Invalid file format");
          }
        });

    var uploadHandler = uploadConfiguration.createFileReplacingUploadHandler();
    Assertions.assertNotEquals(handler, uploadHandler);
    assertNotNull(sessionFactory);
  }

  @Test
  void givenInvalidFile_whenCallUploadHandler_thenThrowException() {
    // Arrange
    when(sftpConfigParams.getHost()).thenReturn("localhost");
    when(sftpConfigParams.getUser()).thenReturn("test-user");
    when(sftpConfigParams.getPrivateKey()).thenReturn("testPrivateKey");
    when(sftpConfigParams.getSftpDir()).thenReturn("/test");

    // Act and Assert
    // Test that the handler throws an exception when trying to process invalid payload
    // The connection will fail first, but this tests the overall error handling
    assertThrows(
        Exception.class,
        () -> {
          MessageHandler handler = uploadConfiguration.createFileReplacingUploadHandler();
          Message<String> message = new GenericMessage<>("InvalidPayload");
          handler.handleMessage(message);
        });
  }

  @Test
  void givenFile_whenCallUploadHandlerWithFilePayload_thenReturnFileName() {
    when(sftpConfigParams.getSftpDir()).thenReturn("/sftpDirectory");
    when(sftpConfigParams.getPrivateKey()).thenReturn("testPrivateKey");
    File testFile = new File("testFile");
    Message<File> fileMessage = MessageBuilder.withPayload(testFile).build();
    MessageHandler handler = uploadConfiguration.createFileReplacingUploadHandler();
    handler.handleMessage(fileMessage);
    assertEquals(fileMessage.getPayload(), testFile);
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa;

import static com.cdg.pmg.ngp.me.dynamicpricing.constants.DomainConstant.HOL;
import static com.cdg.pmg.ngp.me.dynamicpricing.constants.DomainConstant.MON;
import static com.cdg.pmg.ngp.me.dynamicpricing.constants.DomainConstant.TEN_AM;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.PricingRangeConfigRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.PricingRangeConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.NotFoundException;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.PricingRangeConfigJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.mappers.PricingRangeConfigEntityMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.PricingRangeConfigJPARepository;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

/** The type Pricing range config repository impl test. */
@ExtendWith(MockitoExtension.class)
class PricingRangeConfigRepositoryImplTest {
  @Mock private PricingRangeConfigRepository pricingRangeConfigRepository;
  @Mock private PricingRangeConfigJPARepository pricingRangeConfigJPARepository;
  @Mock private PricingRangeConfigEntityMapper pricingRangeConfigEntityMapper;

  private PricingRangeConfigJPA pricingRangeConfigJPA;
  private PricingRangeConfigEntity pricingRangeConfigEntity;

  /** Init repository and mock data. */
  @BeforeEach
  void initRepositoryAndMockData() {
    pricingRangeConfigRepository =
        new PricingRangeConfigRepositoryImpl(
            pricingRangeConfigJPARepository, pricingRangeConfigEntityMapper);

    pricingRangeConfigJPA = new PricingRangeConfigJPA();
    pricingRangeConfigJPA.setDynamicPricingRangeId(1);
    pricingRangeConfigJPA.setDay(MON);

    pricingRangeConfigEntity =
        PricingRangeConfigEntity.builder().pricingRangeId(1).day(MON).build();
  }

  /** Given no argument when get pricing range configs then return successfully. */
  @Test
  void givenNoArgument_whenGetPricingRangeConfigs_thenReturnSuccessfully() {
    List<PricingRangeConfigJPA> listPricingRangeConfigJPA = new ArrayList<>();
    listPricingRangeConfigJPA.add(pricingRangeConfigJPA);
    when(pricingRangeConfigJPARepository.getPricingRangeConfigs())
        .thenReturn(listPricingRangeConfigJPA);

    when(pricingRangeConfigEntityMapper.mapPricingRangeConfigJPAToPricingRangeConfig(Mockito.any()))
        .thenReturn(pricingRangeConfigEntity);

    var actual = pricingRangeConfigRepository.getPricingRangeConfigs().get(0).getDay();
    assertEquals(MON, actual);
  }

  /** Given valid input when insert pricing range config then return successfully. */
  @Test
  void givenValidInput_whenInsertPricingRangeConfig_thenReturnSuccessfully() {
    when(pricingRangeConfigEntityMapper.mapPricingRangeConfigEntityToPricingRangeConfigJPA(
            Mockito.any()))
        .thenReturn(pricingRangeConfigJPA);

    when(pricingRangeConfigJPARepository.save(Mockito.any())).thenReturn(pricingRangeConfigJPA);

    when(pricingRangeConfigEntityMapper.mapPricingRangeConfigJPAToPricingRangeConfig(Mockito.any()))
        .thenReturn(pricingRangeConfigEntity);

    var actual =
        pricingRangeConfigRepository.insertPricingRangeConfig(pricingRangeConfigEntity).getDay();
    assertEquals(MON, actual);
  }

  /** Given valid input when update pricing range config then return successfully. */
  @Test
  void givenValidInput_whenUpdatePricingRangeConfig_thenReturnSuccessfully() {
    when(pricingRangeConfigJPARepository.findById(Mockito.any()))
        .thenReturn(Optional.of(pricingRangeConfigJPA));

    PricingRangeConfigJPA updatedPricingRangeConfigJPA = new PricingRangeConfigJPA();
    updatedPricingRangeConfigJPA.setDynamicPricingRangeId(1);
    updatedPricingRangeConfigJPA.setDay(HOL);
    when(pricingRangeConfigJPARepository.save(Mockito.any()))
        .thenReturn(updatedPricingRangeConfigJPA);

    pricingRangeConfigEntity.setDay(HOL);
    when(pricingRangeConfigEntityMapper.mapPricingRangeConfigJPAToPricingRangeConfig(Mockito.any()))
        .thenReturn(pricingRangeConfigEntity);

    PricingRangeConfigEntity newPricingRangeConfigEntity = new PricingRangeConfigEntity();
    newPricingRangeConfigEntity.setPricingRangeId(1);
    newPricingRangeConfigEntity.setDay(HOL);
    var actual =
        pricingRangeConfigRepository
            .updatePricingRangeConfig(newPricingRangeConfigEntity, pricingRangeConfigEntity)
            .getDay();
    assertEquals(HOL, actual);
  }

  /** Given valid input when update pricing range config then throw not found exception. */
  @Test
  void givenValidInput_whenUpdatePricingRangeConfig_thenThrowNotFoundException() {
    when(pricingRangeConfigJPARepository.findById(Mockito.any())).thenReturn(Optional.empty());

    PricingRangeConfigEntity newPricingRangeConfigEntity = new PricingRangeConfigEntity();
    newPricingRangeConfigEntity.setPricingRangeId(1);
    newPricingRangeConfigEntity.setDay(HOL);
    assertThrows(
        NotFoundException.class,
        () ->
            pricingRangeConfigRepository.updatePricingRangeConfig(
                newPricingRangeConfigEntity, pricingRangeConfigEntity));
  }

  /** Given valid input get pricing range configs by day and hour then return successfully. */
  @Test
  void givenValidInput_getPricingRangeConfigsByDayAndHour_thenReturnSuccessfully() {
    List<PricingRangeConfigJPA> listPricingRangeConfigJPA = new ArrayList<>();
    listPricingRangeConfigJPA.add(pricingRangeConfigJPA);
    when(pricingRangeConfigJPARepository.getPricingRangeConfigsByDayAndHour(
            Mockito.any(), Mockito.any()))
        .thenReturn(listPricingRangeConfigJPA);

    when(pricingRangeConfigEntityMapper.mapPricingRangeConfigJPAToPricingRangeConfig(Mockito.any()))
        .thenReturn(pricingRangeConfigEntity);
    var actual =
        pricingRangeConfigRepository
            .getPricingRangeConfigsByDayAndHour(MON, TEN_AM)
            .get(0)
            .getDay();
    assertEquals(MON, actual);
  }
}

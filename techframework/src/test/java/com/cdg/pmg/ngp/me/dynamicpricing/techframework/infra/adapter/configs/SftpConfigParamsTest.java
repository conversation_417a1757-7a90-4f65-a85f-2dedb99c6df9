package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs;

import static org.junit.jupiter.api.Assertions.assertEquals;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class SftpConfigParamsTest {

  @Mock private SftpConfigParams sftpConfigParams;

  @BeforeEach
  void setUp() {
    sftpConfigParams = new SftpConfigParams();
    sftpConfigParams.setSftpDir("/");
    sftpConfigParams.setUser("testUser");
    sftpConfigParams.setHost("testHost");
    sftpConfigParams.setPrivateKey("testPrivateKey");
  }

  @Test
  void testSftpConfigParams() {
    assertEquals("testHost", sftpConfigParams.getHost());
    assertEquals("testUser", sftpConfigParams.getUser());
    assertEquals("/", sftpConfigParams.getSftpDir());
    assertEquals("testPrivateKey", sftpConfigParams.getPrivateKey());
  }
}

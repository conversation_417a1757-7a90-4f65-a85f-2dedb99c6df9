package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.FareTypeConfigRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FareTypeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.FareTypeConfigJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.custom.FareTypeConfigJPACustom;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.mappers.FareTypeConfigEntityMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.FareTypeConfigJPARepository;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class FareTypeConfigRepositoryImplTest {

  @Mock private FareTypeConfigRepository fareTypeConfigRepository;
  @Mock private FareTypeConfigJPARepository fareTypeConfigJPARepository;
  @Mock private FareTypeConfigEntityMapper fareTypeConfigEntityMapper;
  @Mock private FareTypeConfigJPACustom fareTypeConfigJPACustom;

  private FareTypeConfigJPA fareTypeConfigJPA;
  private FareTypeConfig fareTypeConfig;
  private FareTypeConfigJPA updatedFareTypeConfig;

  @BeforeEach
  void init() {
    fareTypeConfigRepository =
        new FareTypeConfigRepositoryImpl(fareTypeConfigJPARepository, fareTypeConfigEntityMapper);
    fareTypeConfigJPA = new FareTypeConfigJPA();
    fareTypeConfigJPA.setFareType("DYNP_MIN_CAP");
    fareTypeConfigJPA.setDay("MON");
    fareTypeConfigJPA.setHour("10");

    fareTypeConfig =
        FareTypeConfig.builder().fareType("DYNP_MIN_CAP").day("MON").hour("10").build();

    updatedFareTypeConfig = new FareTypeConfigJPA();
    updatedFareTypeConfig.setFareType("DYNP_MIN_CAP");
    updatedFareTypeConfig.setDay("FRI");
    updatedFareTypeConfig.setHour("10");
  }

  @Test
  void getParamConfigByListFareType_validInput_returnOK() {
    List<FareTypeConfigJPA> fareTypeConfigEntityList = new ArrayList<>();
    fareTypeConfigEntityList.add(fareTypeConfigJPA);
    when(fareTypeConfigJPARepository.getFareTypeConfigByListFareType(Mockito.any()))
        .thenReturn(fareTypeConfigEntityList);

    when(fareTypeConfigEntityMapper.mapFareTypeConfigEntityToFareTypeConfig(Mockito.any()))
        .thenReturn(fareTypeConfig);

    var expected = "MON";
    Set<String> listFareType = new HashSet<>();
    listFareType.add("DYNP_MIN_CAP");
    var actual =
        fareTypeConfigRepository.getParamConfigByListFareType(listFareType).get(0).getDay();
    assertEquals(expected, actual);
  }

  @Test
  void createFareTypeConfig_validInput_returnOK() {
    when(fareTypeConfigEntityMapper.mapFareTypeConfigToFareTypeConfigEntity(Mockito.any()))
        .thenReturn(fareTypeConfigJPA);

    when(fareTypeConfigJPARepository.save(Mockito.any())).thenReturn(fareTypeConfigJPA);

    when(fareTypeConfigEntityMapper.mapFareTypeConfigEntityToFareTypeConfig(Mockito.any()))
        .thenReturn(fareTypeConfig);

    var expected = "MON";
    var actual = fareTypeConfigRepository.createFareTypeConfig(fareTypeConfig).getDay();
    assertEquals(expected, actual);
  }

  @Test
  void updateFareTypeConfig() {
    when(fareTypeConfigJPARepository.findByFareType(Mockito.any())).thenReturn(fareTypeConfigJPA);

    when(fareTypeConfigJPARepository.save(Mockito.any())).thenReturn(updatedFareTypeConfig);

    fareTypeConfig.setDay("FRI");
    when(fareTypeConfigEntityMapper.mapFareTypeConfigEntityToFareTypeConfig(Mockito.any()))
        .thenReturn(fareTypeConfig);

    var expected = "FRI";
    var fareTypeConfigInput =
        FareTypeConfig.builder().fareType("DYNP_MIN_CAP").day("FRI").hour("10").build();
    var actual = fareTypeConfigRepository.updateFareTypeConfig(fareTypeConfigInput).getDay();
    assertEquals(expected, actual);
  }

  @Test
  void getFareTypeConfigs_isEmptyConfig() {
    final List<FareTypeConfigJPACustom> queryResult = new ArrayList<>();
    when(fareTypeConfigJPARepository.getFareTypeConfig()).thenReturn(queryResult);
    final List<FareTypeConfig> expected = new ArrayList<>();
    final List<FareTypeConfig> actual = fareTypeConfigRepository.getFareTypeConfigs();
    assertEquals(expected, actual);
  }

  @Test
  void getFareTypeConfigs_isNotEmptyConfig() {
    final List<FareTypeConfigJPACustom> resultQuery = List.of(fareTypeConfigJPACustom);
    when(fareTypeConfigJPARepository.getFareTypeConfig()).thenReturn(resultQuery);
    final List<FareTypeConfig> actual = fareTypeConfigRepository.getFareTypeConfigs();
    final FareTypeConfigJPACustom conf = resultQuery.get(0);
    final boolean expected =
        actual.stream()
            .allMatch(
                actualConf ->
                    Objects.equals(actualConf.getFareTypeId(), conf.getFareTypeId())
                        && Objects.equals(actualConf.getFareType(), conf.getFareType())
                        && Objects.equals(actualConf.getDay(), conf.getDay())
                        && Objects.equals(actualConf.getHour(), conf.getHour())
                        && Objects.equals(actualConf.getFixedValue(), conf.getFixedValue())
                        && Objects.equals(actualConf.getDefaultFixed(), conf.getDefaultFixed())
                        && Objects.equals(actualConf.getPercentValue(), conf.getPercentValue())
                        && Objects.equals(actualConf.getStartDate(), conf.getStartDate())
                        && Objects.equals(actualConf.getEndDate(), conf.getEndDate())
                        && Objects.equals(actualConf.getVehGrp(), conf.getVehGrp())
                        && Objects.equals(actualConf.getDayPriority(), conf.getDayPriority())
                        && Objects.equals(actualConf.getCreatedDate(), conf.getCreatedDate())
                        && Objects.equals(actualConf.getUpdatedDate(), conf.getUpdatedDate())
                        && Objects.equals(
                            actualConf.getDefaultPercent(), conf.getDefaultPercent()));
    assertTrue(expected);
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa;

import static org.mockito.Mockito.when;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.S2CellJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.S2CellJPARepository;
import java.util.Collections;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class S2CellJPARepositoryTest {
  @Mock private S2CellJPARepository s2CellJPARepository;

  private S2CellJPA s2CellJPA;

  @BeforeEach
  void setUp() {
    s2CellJPA =
        S2CellJPA.builder()
            .s2CellId("s2CellId")
            .s2CellDesc("s2CellDesc")
            .s2CellLatitude(1.0)
            .s2CellLocationId("s2CellLocationId")
            .s2CellTokenId("s2CellTokenId")
            .s2CellLevel(1)
            .s2CellSeqId(1L)
            .s2CellLocDesc("s2CellLocDesc")
            .s2CellZoneId("s2CellZoneId")
            .s2CellLongitude(1.0)
            .build();
  }

  @Test
  void givenS2CellJPA_whenGetAllS2CellJPA_thenReturnS2CellData() {
    when(s2CellJPARepository.findAll()).thenReturn(Collections.singletonList(s2CellJPA));
    Assertions.assertEquals(s2CellJPARepository.findAll(), Collections.singletonList(s2CellJPA));
  }

  @Test
  void givenEmptyData_whenGetAllS2CellJPA_thenReturnNull() {
    when(s2CellJPARepository.findAll()).thenReturn(null);
    Assertions.assertNull(s2CellJPARepository.findAll());
  }
}

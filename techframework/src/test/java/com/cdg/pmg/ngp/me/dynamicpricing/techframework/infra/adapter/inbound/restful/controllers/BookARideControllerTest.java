package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.controllers;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.BookARideService;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.BookARideResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
class BookARideControllerTest {
  @Mock private BookARideController bookARideController;

  @Mock private BookARideService bookARideService;

  @BeforeEach
  void setUp() {
    bookARideController = new BookARideController(bookARideService);
  }

  @Test
  void givenCall_whenDoTaskScheduler_thenReturnSuccess() {
    // Arrange
    doNothing().when(bookARideService).uploadFile();

    // Act
    ResponseEntity<BookARideResponse> response = bookARideController.uploadFileScheduler();

    // Assert
    assertNotNull(response);
    assertEquals(200, response.getStatusCodeValue());
    assertNotNull(response.getBody());
    assertEquals("Trigger success doTask", response.getBody().getData());
    assertNotNull(response.getBody().getTraceId());
    assertNotNull(response.getBody().getTimestamp());

    // Verify the interaction with fareUploadScheduler
    verify(bookARideService, times(1)).uploadFile();
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.services;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.mapper.NewPricingModelMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.CacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.NewPricingModelConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.BadRequestException;
import com.cdg.pmg.ngp.me.dynamicpricing.outbound.ZoneInfoRepositoryOutboundPort;
import com.cdg.pmg.ngp.me.dynamicpricing.service.NewPricingModelService;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs.DynamicSurgeProperties;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs.properties.RefreshableProperties;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.mapper.surgecomputation.DynamicSurgeConfigMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.properties.NewPricingModelConfigList;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ConfigurationOutboundPortImplTest {

  @Mock private NewPricingModelConfigList newPricingModelConfigList;

  @Mock private NewPricingModelMapper newPricingModelMapper;

  @Spy private ObjectMapper objectMapper;
  @Mock private NewPricingModelService newPricingModelService;
  @Mock private CacheService cacheService;
  @Mock private ZoneInfoRepositoryOutboundPort zoneInfoRepositoryOutboundPort;
  @Mock private RefreshableProperties refreshableProperties;
  @Mock private DynamicSurgeProperties dynamicSurgeProperties;
  @Mock private DynamicSurgeConfigMapper dynamicSurgeConfigMapper;

  @InjectMocks private ConfigurationOutboundPortImpl configurationOutboundPortImpl;

  @Test
  void getNewPricingModelConfigEntity_shouldReturnListOfNewPricingModelConfigEntities() {
    // Arrange
    List<String> jsonList = getNewPricingModelJsonList();
    when(newPricingModelConfigList.getItems()).thenReturn(jsonList);

    NewPricingModelConfigEntity expectedEntity =
        NewPricingModelConfigEntity.builder().id(0L).zoneId("01").index(0).build();
    when(newPricingModelMapper.parse(any(ObjectMapper.class), any(String.class)))
        .thenReturn(expectedEntity);
    when(cacheService.getValue(any(), any())).thenReturn(List.of("01"));
    // Act
    List<NewPricingModelConfigEntity> result =
        configurationOutboundPortImpl.getNewPricingModelConfigEntities(true);

    // Assert
    assertEquals(1, result.size());
    assertEquals(expectedEntity, result.get(0));
  }

  @Test
  void whenValidateNewPricingModelNewPricingModelEntityFail_thenExcludeFromResult() {
    List<String> jsonList = getNewPricingModelJsonList();
    when(newPricingModelConfigList.getItems()).thenReturn(jsonList);
    doThrow(new BadRequestException("Dummy", 0L))
        .when(newPricingModelService)
        .validateIgnoreZoneCheck(any());

    NewPricingModelConfigEntity expectedEntity =
        NewPricingModelConfigEntity.builder().id(0L).zoneId("01").index(0).build();
    when(newPricingModelMapper.parse(any(ObjectMapper.class), any(String.class)))
        .thenReturn(expectedEntity);
    when(cacheService.getValue(any(), any())).thenReturn(List.of("01"));

    // Act
    List<NewPricingModelConfigEntity> result =
        configurationOutboundPortImpl.getNewPricingModelConfigEntities(true);
    assertEquals(0, result.size());
  }

  @Test
  void whenValidateNewPricingModelDynamicPricingIsNull_thenExcludeFromResult() {
    // Arrange
    List<String> jsonList = getNewPricingModelJsonList();
    when(newPricingModelConfigList.getItems()).thenReturn(jsonList);
    when(newPricingModelMapper.parse(any(ObjectMapper.class), any(String.class))).thenReturn(null);
    // Act
    List<NewPricingModelConfigEntity> result =
        configurationOutboundPortImpl.getNewPricingModelConfigEntities(true);

    // Assert
    assertEquals(0, result.size());
  }

  @Test
  void givenNoParam_whenLoadBookARideConfigurations_thenReturnSuccess() {
    // GIVEN
    Map<String, String> result = new HashMap<>();
    result.put("key", "value");
    // WHEN
    when(refreshableProperties.getBookARideConfigProperties()).thenReturn(result);
    // THEN
    assertDoesNotThrow(() -> configurationOutboundPortImpl.loadBookARideConfigurations());
  }

  @Test
  void whenZoneIdIsInvalid_thenExcludeFromResult() {
    List<String> jsonList = getNewPricingModelJsonList();
    when(newPricingModelConfigList.getItems()).thenReturn(jsonList);
    NewPricingModelConfigEntity expectedEntity =
        NewPricingModelConfigEntity.builder().id(0L).index(0).build();
    when(newPricingModelMapper.parse(any(ObjectMapper.class), any(String.class)))
        .thenReturn(expectedEntity);
    when(cacheService.getValue(any(), any())).thenReturn(List.of("02"));

    // Act
    List<NewPricingModelConfigEntity> result =
        configurationOutboundPortImpl.getNewPricingModelConfigEntities(true);
    assertEquals(0, result.size());
  }

  @Test
  void whenNotFoundZoneIdsFromCache_thenRetrieveZoneIdsFromDatabase() {
    // Arrange
    when(newPricingModelConfigList.getItems()).thenReturn(List.of());

    // Act
    configurationOutboundPortImpl.getNewPricingModelConfigEntities(true);

    // Assert
    verify(zoneInfoRepositoryOutboundPort).findAllZones();
  }

  private static @NotNull List<String> getNewPricingModelJsonList() {
    List<String> jsonList = new ArrayList<>();
    jsonList.add(
        "{\"id\":0,\"index\":0,\"zoneId\":\"01\",\"startDt\":\"2023-06-04T00:00:00Z\",\"endDt\":\"2023-06-04T00:00:00Z\",\"additionalSurgeHigh\":0,\"surgeHighTierRate\":0.0,\"unmetRate1\":0.0,\"unmetRate2\":0.0,\"negativeDemandSupplyDownRate\":0.0,\"createdBy\":\"\",\"createdDt\":\"2023-06-04T00:00:00Z\",\"updatedBy\":\"\",\"updatedDt\":\"2023-06-04T00:00:00Z\",\"k1\":0.0,\"k2\":0.0,\"k3\":0.0,\"k4\":0.0,\"k5\":0.0,\"k6\":0.0,\"k7\":0.0,\"k8\":0.0,\"zonePriceVersion\":\"\"}");
    return jsonList;
  }
}

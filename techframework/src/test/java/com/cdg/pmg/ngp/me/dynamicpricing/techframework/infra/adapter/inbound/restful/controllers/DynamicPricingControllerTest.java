package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.controllers;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.MultiFareResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.additionalcharge.AdditionalChargeFeeData;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.DemandSupplyService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.DynamicPricingService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.farecounter.RequestCounterService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.H3RegionComputeRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.H3RegionComputeResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.AddressService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.ConfigurationServiceOutboundPort;
import com.cdg.pmg.ngp.me.dynamicpricing.commands.StoreFareBreakdownCommandRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.commands.StoreFareBreakdownCommandResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.GeneratedRouteEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.LatLng;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.ValidateFareEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.DynamicPricingSurgeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.BadRequestException;
import com.cdg.pmg.ngp.me.dynamicpricing.queries.MultiFareRequestQuery;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper.DynamicPricingMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.*;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
class DynamicPricingControllerTest {

  @InjectMocks private DynamicPricingController controller;
  @Mock private DynamicPricingService dynamicPricingService;
  @Mock private DemandSupplyService demandSupplyService;
  @Mock private DynamicPricingMapper mapper;
  @Mock private RequestCounterService requestCounterService;
  @Mock private AddressService addressService;
  @Mock private ConfigurationServiceOutboundPort configurationServiceOutboundPort;

  @Test
  void givenNormalRequest_whenStoreFareBreakdown_thenReturnOK() {
    // WHEN
    when(mapper.mapToStoreFareBreakdownRequestQuery(any()))
        .thenReturn(new StoreFareBreakdownCommandRequest());
    when(dynamicPricingService.storeFareBreakdownDetail(any()))
        .thenReturn(new StoreFareBreakdownCommandResponse());
    when(mapper.mapToStoreFareBreakdownInboundResponseData(any()))
        .thenReturn(new StoreFareBreakdownInboundResponseData());

    // THEN
    ResponseEntity<StoreFareBreakdownInboundResponse> responseEntity =
        controller.storeFareBreakdown(new StoreFareBreakdownInboundRequest());
    assertNotNull(responseEntity);
  }

  @Test
  void givenGetEstimatedFareInboundRequest_whenGetEstimatedFare_thenReturnOK() {
    // GIVEN
    H3RegionComputeRequest regionComputeRequest =
        new H3RegionComputeRequest(1.324103125052384, 103.94436932842119);
    MultiFareRequestQuery mockedMultiFareRequestQuery = new MultiFareRequestQuery();
    GetEstimatedFareInboundRequest inboundRequest = new GetEstimatedFareInboundRequest();
    MultiFareResponse mockedMultiFareResponse = new MultiFareResponse();
    GetEstimatedFareResponse expectedResponse = new GetEstimatedFareResponse();
    GetEstimatedFareInboundResponse mockedGetEstimatedFareInboundResponse =
        new GetEstimatedFareInboundResponse();
    expectedResponse.data(mockedGetEstimatedFareInboundResponse);
    DynamicPricingSurgeConfig dynamicPricingSurgeConfig = new DynamicPricingSurgeConfig();

    // WHEN
    when(mapper.mapToH3RegionComputeRequest(any())).thenReturn(regionComputeRequest);
    when(addressService.resolveH3Region(regionComputeRequest))
        .thenReturn(Optional.of(new H3RegionComputeResponse()));
    when(mapper.mapToEstFareRequestQuery(any())).thenReturn(mockedMultiFareRequestQuery);
    when(dynamicPricingService.getMultiFare(any())).thenReturn(mockedMultiFareResponse);
    when(mapper.mapToGetEstimatedFareInboundResponse(any()))
        .thenReturn(mockedGetEstimatedFareInboundResponse);
    when(configurationServiceOutboundPort.getDynamicPricingSurgeConfig())
        .thenReturn(dynamicPricingSurgeConfig);
    ResponseEntity<GetEstimatedFareResponse> responseEntity =
        controller.getMultiFare(inboundRequest);
    // THEN
    assertNotNull(responseEntity);
    assertNotNull(responseEntity.getBody());
    assertEquals(expectedResponse.getData(), responseEntity.getBody().getData());
  }

  @Test
  void givenFareId_whenGetMultiFareById_thenReturnMultiFareResponse() {
    // Given
    String fareId = "sampleFareId";
    MultiFareResponse mockedMultiFareResponse = new MultiFareResponse();
    GetEstimatedFareResponse expectedResponse = new GetEstimatedFareResponse();
    GetEstimatedFareInboundResponse mockedGetEstimatedFareInboundResponse =
        new GetEstimatedFareInboundResponse();
    expectedResponse.data(mockedGetEstimatedFareInboundResponse);

    // When
    when(dynamicPricingService.getMultiFareByFareId(fareId)).thenReturn(mockedMultiFareResponse);
    when(mapper.mapToGetEstimatedFareInboundResponse(any()))
        .thenReturn(mockedGetEstimatedFareInboundResponse);
    ResponseEntity<GetEstimatedFareResponse> responseEntity = controller.getMultiFareById(fareId);

    // Then
    assertNotNull(responseEntity);
    assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
    assertNotNull(responseEntity.getBody());
    assertEquals(expectedResponse.getData(), responseEntity.getBody().getData());
  }

  @Test
  void givenInvalidRequest_whenVerifyFare_thenReturnFalse() {
    final ValidateFareRequest verifyFareRequest = new ValidateFareRequest();
    final ValidateFareEntity request = new ValidateFareEntity();

    Mockito.when(mapper.mapToValidateFareEntity(verifyFareRequest)).thenReturn(request);
    Mockito.when(dynamicPricingService.validateFare(request)).thenReturn(false);

    final boolean actual =
        Objects.requireNonNull(controller.verifyFare(verifyFareRequest).getBody())
            .getData()
            .getIsValidFare();
    Assertions.assertFalse(actual);
  }

  @Test
  void givenValidRequest_whenVerifyFare_thenReturnTrue() {
    final ValidateFareRequest verifyFareRequest = new ValidateFareRequest();
    final ValidateFareEntity request = new ValidateFareEntity();

    Mockito.when(mapper.mapToValidateFareEntity(verifyFareRequest)).thenReturn(request);
    Mockito.when(dynamicPricingService.validateFare(request)).thenReturn(true);

    final boolean actual =
        Objects.requireNonNull(controller.verifyFare(verifyFareRequest).getBody())
            .getData()
            .getIsValidFare();
    Assertions.assertTrue(actual);
  }

  @Test
  void givenTripId_whenGetGeneratedRoute_thenReturnSuccessfully() {
    GeneratedRouteEntity generatedRouteEntity =
        GeneratedRouteEntity.builder()
            .pickupPoint(LatLng.builder().lat(1.324103125052384).lng(103.94436932842119).build())
            .destinationPoint(
                LatLng.builder().lat(1.3346444203582726).lng(103.93534846724433).build())
            .intermediatePoint(
                LatLng.builder().lat(1.3646444203582726).lng(103.96534846724433).build())
            .distance(5L)
            .duration(1200L)
            .build();
    Mockito.when(dynamicPricingService.getGeneratedRouteByTripId(Mockito.any()))
        .thenReturn(generatedRouteEntity);
    GeneratedRoute data = new GeneratedRoute();
    com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.LatLng latLngModel =
        new com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.LatLng();
    latLngModel.setLat(BigDecimal.valueOf(1.324103125052384));
    latLngModel.setLng(BigDecimal.valueOf(103.94436932842119));
    data.setPickupPoint(latLngModel);
    latLngModel.setLat(BigDecimal.valueOf(1.3346444203582726));
    latLngModel.setLng(BigDecimal.valueOf(103.93534846724433));
    data.setIntermediatePoint(latLngModel);
    latLngModel.setLat(BigDecimal.valueOf(1.3646444203582726));
    latLngModel.setLng(BigDecimal.valueOf(103.96534846724433));
    data.setDestinationPoint(latLngModel);
    data.setDistance(BigDecimal.valueOf(5L));
    data.setDuration(BigDecimal.valueOf(1200L));
    Mockito.when(mapper.mapGeneratedRouteEntityToGeneratedRoute(Mockito.any())).thenReturn(data);
    var actual =
        Objects.requireNonNull(controller.getGeneratedRoute("trip-id").getBody())
            .getData()
            .getDuration();
    assertEquals(BigDecimal.valueOf(1200L), actual);
  }

  @Test
  void givenTripIdIsNull_whenGetGeneratedRoute_thenThrowBadRequestException() {
    assertThrows(BadRequestException.class, () -> controller.getGeneratedRoute(null));
  }

  @Test
  void whenUpdateDynamicSurge_thenUpdatedSuccess() {
    final var response = controller.updateDynamicSurge();
    assertTrue(response.getStatusCode().is2xxSuccessful());
  }

  @Test
  void givenValidRequest_whenSearchFareBreakdown_thenReturnSuccess() {
    final SearchFareBreakdownInboundRequest request = new SearchFareBreakdownInboundRequest();
    request.setFareId("fareId");
    request.setBookingId("bookingId");
    request.setTripId("tripId");

    final var response = controller.searchFareBreakdown(request);
    assertTrue(response.getStatusCode().is2xxSuccessful());
  }

  @Test
  void whenUpdateDynamicSurgeV2_thenUpdatedSuccess() {
    final var response = controller.updateDynamicSurgeV2();
    assertTrue(response.getStatusCode().is2xxSuccessful());
  }

  @Test
  void getAdditionalChargeFeesByCondition_Success_withData() {
    List<AdditionalChargeFeeData> expect =
        List.of(
            new AdditionalChargeFeeData(
                1,
                "DRIVER_FEE",
                BigDecimal.valueOf(0.5),
                BigDecimal.valueOf(17),
                BigDecimal.valueOf(0.5),
                BigDecimal.valueOf(0.3)),
            new AdditionalChargeFeeData(
                2,
                "OTHER_FEE",
                BigDecimal.valueOf(1),
                BigDecimal.valueOf(17),
                BigDecimal.valueOf(2),
                BigDecimal.valueOf(1)));

    when(dynamicPricingService.getAdditionalChargeFeesByCondition(any(), any(), any()))
        .thenReturn(expect);

    ResponseEntity<AdditionalChargeFeesResponse> response =
        controller.getAdditionalChargeFeesByCondition("fareId", 1, "productTypeId");
    assertTrue(response.getStatusCode().is2xxSuccessful());

    AdditionalChargeFeesResponse body = response.getBody();
    assertNotNull(body);

    List<AdditionalChargeFee> actualDataList = body.getData();
    assertNotNull(actualDataList);
    assertNotEquals(0, actualDataList.size());

    AdditionalChargeFee actualDriverFee = actualDataList.get(0);
    assertEquals(expect.get(0).getChargeId(), actualDriverFee.getChargeId());
    assertEquals(expect.get(0).getChargeType(), actualDriverFee.getChargeType());
    assertEquals(expect.get(0).getChargeAmt(), actualDriverFee.getChargeAmt());
    assertEquals(expect.get(0).getChargeThreshold(), actualDriverFee.getChargeThreshold());
    assertEquals(expect.get(0).getChargeUpperLimit(), actualDriverFee.getChargeUpperLimit());
    assertEquals(expect.get(0).getChargeLowerLimit(), actualDriverFee.getChargeLowerLimit());

    AdditionalChargeFee actualOtherFee = actualDataList.get(1);
    assertEquals(expect.get(1).getChargeId(), actualOtherFee.getChargeId());
    assertEquals(expect.get(1).getChargeType(), actualOtherFee.getChargeType());
    assertEquals(expect.get(1).getChargeAmt(), actualOtherFee.getChargeAmt());
    assertEquals(expect.get(1).getChargeThreshold(), actualOtherFee.getChargeThreshold());
    assertEquals(expect.get(1).getChargeUpperLimit(), actualOtherFee.getChargeUpperLimit());
    assertEquals(expect.get(1).getChargeLowerLimit(), actualOtherFee.getChargeLowerLimit());
  }
}

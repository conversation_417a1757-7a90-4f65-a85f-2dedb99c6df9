package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.custom.LocationSurchargeJPACustom;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.core.namedparam.SqlParameterSource;

@ExtendWith(MockitoExtension.class)
class LocationSurchargeJPARepositoryTest {
  @Mock private LocationSurchargeJPARepository locationSurchargeJPARepository;

  @Mock private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

  @BeforeEach
  void setup() {
    locationSurchargeJPARepository = new LocationSurchargeJPARepository(namedParameterJdbcTemplate);
  }

  @SuppressWarnings("unchecked")
  @Test
  void givenNormalParam_whenGetLocationSurchargeConfigs_thenReturnList() {
    // GIVEN
    List<LocationSurchargeJPACustom> expected =
        List.of(LocationSurchargeJPACustom.builder().build());
    // WHEN
    when(namedParameterJdbcTemplate.query(
            any(), any(SqlParameterSource.class), any(BeanPropertyRowMapper.class)))
        .thenReturn(List.of(LocationSurchargeJPACustom.builder().build()));
    List<LocationSurchargeJPACustom> actual =
        locationSurchargeJPARepository.getLocationSurchargeConfigs(3000, 0);

    // THEM
    assertEquals(actual.size(), expected.size());
  }
}

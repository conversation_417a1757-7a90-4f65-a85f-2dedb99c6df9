package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.adapter;

import static org.mockito.Mockito.when;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.DemandSupplyStatisticsResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.DemandSupplyStatisticsResponseV2;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.FleetAnalyticService;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos.DemandSupplyStatisticsOutbound;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos.DemandSupplyStatisticsOutboundResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos.DemandSupplyStatisticsOutboundV2;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos.DemandSupplyStatisticsOutboundV2Response;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.mapper.FleetAnalOutboundMapper;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
class FleetAnalyticServiceAdapterTest {

  @Mock FleetAnalyticService fleetAnalyticService;

  @Mock FleetAnalyticOutboundAdapter fleetAnalyticOutboundAdapter;

  @Mock FleetAnalOutboundMapper mapper;

  @BeforeEach
  void setUp() {
    fleetAnalyticService = new FleetAnalyticServiceAdapter(fleetAnalyticOutboundAdapter, mapper);
  }

  @Test
  void givenNullDemandSupply_whenDemandSuppyStatistics_thenReturnEmptyList() {
    when(fleetAnalyticOutboundAdapter.getDemandSupplyStatistics()).thenReturn(null);
    final List<DemandSupplyStatisticsResponse> actual =
        fleetAnalyticService.getDemandSuppyStatistics();
    Assertions.assertEquals(actual, new ArrayList<>());
  }

  @Test
  void givenNotNullDemandSupply_whenDemandSuppyStatistics_thenReturnList() {
    final DemandSupplyStatisticsOutbound response = new DemandSupplyStatisticsOutbound();
    final List<DemandSupplyStatisticsOutbound> data = List.of(response);
    final DemandSupplyStatisticsOutboundResponse dosResponse =
        new DemandSupplyStatisticsOutboundResponse();
    dosResponse.setData(data);
    when(fleetAnalyticOutboundAdapter.getDemandSupplyStatistics())
        .thenReturn(ResponseEntity.of(Optional.of(dosResponse)));
    final List<DemandSupplyStatisticsResponse> actual =
        fleetAnalyticService.getDemandSuppyStatistics();
    final boolean expected =
        actual.stream()
            .allMatch(
                actualConf ->
                    Objects.equals(actualConf.getZoneId(), response.getZoneId())
                        && Objects.equals(actualConf.getSupply(), response.getSupply())
                        && Objects.equals(
                            actualConf.getPreviousDemand(), response.getPreviousDemand())
                        && Objects.equals(
                            actualConf.getPredictedDemand(), response.getPredictedDemand())
                        && Objects.equals(actualConf.getExcessDemand(), response.getExcessDemand())
                        && Objects.equals(actualConf.getRecentDemand(), response.getRecentDemand())
                        && Objects.equals(
                            actualConf.getBatchCounter(), response.getBatchCounter()));
    Assertions.assertTrue(expected);
  }

  @Test
  void givenNullDemandSupply_whenDemandSuppyStatisticsV2_thenReturnEmptyList() {
    when(fleetAnalyticOutboundAdapter.getDemandSupplyStatisticsV2()).thenReturn(null);
    final List<DemandSupplyStatisticsResponseV2> actual =
        fleetAnalyticService.getDemandSupplyStatisticsV2();
    Assertions.assertEquals(actual, new ArrayList<>());
  }

  @Test
  void givenNotNullDemandSupply_whenDemandSuppyStatisticsV2_thenReturnList() {
    final DemandSupplyStatisticsOutboundV2 response = new DemandSupplyStatisticsOutboundV2();
    final List<DemandSupplyStatisticsOutboundV2> data = List.of(response);
    final DemandSupplyStatisticsOutboundV2Response dosResponse =
        new DemandSupplyStatisticsOutboundV2Response();
    dosResponse.setData(data);

    when(fleetAnalyticOutboundAdapter.getDemandSupplyStatisticsV2())
        .thenReturn(ResponseEntity.of(Optional.of(dosResponse)));
    final List<DemandSupplyStatisticsResponseV2> actual =
        fleetAnalyticService.getDemandSupplyStatisticsV2();
    final boolean expected =
        actual.stream()
            .allMatch(
                actualConf ->
                    Objects.equals(actualConf.getZoneId(), response.getZoneId())
                        && Objects.equals(actualConf.getDemand15(), response.getDemand15())
                        && Objects.equals(actualConf.getDemand30(), response.getDemand30())
                        && Objects.equals(actualConf.getDemand60(), response.getDemand60())
                        && Objects.equals(
                            actualConf.getPredictedDemand15(), response.getPredictedDemand15())
                        && Objects.equals(
                            actualConf.getPredictedDemand30(), response.getPredictedDemand30())
                        && Objects.equals(actualConf.getSupply(), response.getSupply())
                        && Objects.equals(
                            actualConf.getExcessDemand15(), response.getExcessDemand15())
                        && Objects.equals(
                            actualConf.getExcessDemand30(), response.getExcessDemand30())
                        && Objects.equals(
                            actualConf.getPreviousDemand15(), response.getPreviousDemand15())
                        && Objects.equals(actualConf.getBatchCounter(), response.getBatchCounter())
                        && Objects.equals(actualConf.getUnmet15(), response.getPreviousUnmet15()));

    Assertions.assertTrue(expected);
  }
}

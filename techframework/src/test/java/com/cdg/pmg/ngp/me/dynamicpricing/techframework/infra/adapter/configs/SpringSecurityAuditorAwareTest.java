package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs;

import static com.cdg.pmg.ngp.me.dynamicpricing.constants.CommonConstant.SYSTEM_USER;
import static org.junit.jupiter.api.Assertions.assertEquals;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

@ExtendWith(MockitoExtension.class)
class SpringSecurityAuditorAwareTest {
  @InjectMocks private SpringSecurityAuditorAware auditorAware;
  @Mock private ServletRequestAttributes requestAttributes;
  @Mock private HttpServletRequest request;

  @BeforeEach
  void initSpringSecurityAuditorAware() {
    auditorAware = new SpringSecurityAuditorAware();
    requestAttributes = new ServletRequestAttributes(request);
  }

  @Test
  void givenUserId_whenGetCurrentAuditor_thenReturnUserIdFromHeader() {
    String expectedUserId = "user123";
    Mockito.when(request.getHeader("X-User-Id")).thenReturn(expectedUserId);
    RequestContextHolder.setRequestAttributes(requestAttributes);
    assertEquals(Optional.of(expectedUserId), auditorAware.getCurrentAuditor());
  }

  @Test
  void givenUserIdNull_whenGetCurrentAuditor_thenReturnSystemUser() {
    Mockito.when(request.getHeader("X-User-Id")).thenReturn(null);
    RequestContextHolder.setRequestAttributes(requestAttributes);
    assertEquals(Optional.of(SYSTEM_USER), auditorAware.getCurrentAuditor());
  }

  @Test
  void givenRequestIsAbsent_whenGetCurrentAuditor_thenReturnSystemUser() {
    RequestContextHolder.resetRequestAttributes();
    assertEquals(Optional.of(SYSTEM_USER), auditorAware.getCurrentAuditor());
  }
}

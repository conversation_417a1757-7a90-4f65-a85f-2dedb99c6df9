package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.verify;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.mappers.NewPricingModelConfigChangeMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.NewPricingModelConfigChangeJPARepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mapstruct.factory.Mappers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class NewPricingModelConfigChangeRepositoryImplTest {

  @Spy
  private NewPricingModelConfigChangeMapper newPricingModelConfigChangeMapper =
      Mappers.getMapper(NewPricingModelConfigChangeMapper.class);

  @Mock private NewPricingModelConfigChangeJPARepository newPricingModelConfigChangeJPARepository;

  @InjectMocks
  private NewPricingModelConfigChangeRepositoryImpl newPricingModelConfigChangeRepository;

  @Test
  void
      givenAListOfNewPricingModelConfigChangeEntities_whenPerformSaveAll_ConfigModelChanges_thenShouldSuccess() {
    // Act
    newPricingModelConfigChangeRepository.saveAllPricingConfigModelChanges(anyList());

    // Assert
    verify(newPricingModelConfigChangeJPARepository).saveAll(anyList());
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.S2CellEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.S2CellJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.mappers.S2CellEntityMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.S2CellJPARepository;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class S2CellRepositoryImplTest {
  @Mock private S2CellRepositoryImpl s2CellRepositoryImpl;

  @Mock private S2CellJPARepository s2CellJPARepository;

  @Mock private S2CellEntityMapper mapper;

  private S2CellJPA s2CellJPA;

  private S2CellEntity s2CellEntity;

  @BeforeEach
  void setUp() {
    s2CellRepositoryImpl = new S2CellRepositoryImpl(s2CellJPARepository, mapper);
    s2CellJPA =
        S2CellJPA.builder()
            .s2CellId("s2CellId")
            .s2CellDesc("s2CellDesc")
            .s2CellLatitude(1.0)
            .s2CellLocationId("s2CellLocationId")
            .s2CellTokenId("s2CellTokenId")
            .s2CellLevel(1)
            .s2CellSeqId(1L)
            .s2CellLocDesc("s2CellLocDesc")
            .s2CellZoneId("s2CellZoneId")
            .s2CellLongitude(1.0)
            .build();
    s2CellEntity =
        S2CellEntity.builder()
            .s2CellId("s2CellId")
            .s2CellDesc("s2CellDesc")
            .s2CellLatitude(1.0)
            .s2CellLocationId("s2CellLocationId")
            .s2CellTokenId("s2CellTokenId")
            .s2CellLevel(1)
            .s2CellSeqId(1L)
            .s2CellLocDesc("s2CellLocDesc")
            .s2CellZoneId("s2CellZoneId")
            .s2CellLongitude(1.0)
            .build();
  }

  @Test
  void givenS2CellJPA_whenGetAllS2Cell_thenReturnS2CellEntity() {
    when(s2CellJPARepository.findAll()).thenReturn(List.of(s2CellJPA));
    when(mapper.mapS2CellJpaToEntity(s2CellJPA)).thenReturn(s2CellEntity);
    assertEquals(List.of(s2CellEntity), s2CellRepositoryImpl.getAllS2Cell());
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.FlatFareAdjustmentConfRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FlatFareAdjustmentConfEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.FlatFareAdjustmentConfJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.FlatFareAdjustmentConfJPARepository;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class FlatFareAdjustmentConfRepositoryImplTest {
  @Mock private FlatFareAdjustmentConfRepository flatFareAdjustmentConfRepository;
  @Mock private FlatFareAdjustmentConfJPARepository flatFareAdjustmentConfJPARepository;

  @BeforeEach
  void setUp() {
    flatFareAdjustmentConfRepository =
        new FlatFareAdjustmentConfRepositoryImpl(flatFareAdjustmentConfJPARepository);
  }

  @Test
  void test_getFlatFareAdjustmentConf_success() {

    final List<FlatFareAdjustmentConfJPA> configs =
        List.of(
            FlatFareAdjustmentConfJPA.builder().id(1).vehGrp(0).fixedVal(2.0).perVal(30.0).build());

    Mockito.when(flatFareAdjustmentConfJPARepository.findAllByIsEnabledIsTrue())
        .thenReturn(configs);

    final List<FlatFareAdjustmentConfEntity> actual =
        flatFareAdjustmentConfRepository.getFlatFareAdjustmentConf();

    final List<FlatFareAdjustmentConfEntity> expected =
        List.of(
            FlatFareAdjustmentConfEntity.builder()
                .id(1)
                .vehGrp(0)
                .fixedVal(2.0)
                .perVal(30.0)
                .build());
    Assertions.assertEquals(expected.get(0).getId(), actual.get(0).getId());
    Assertions.assertEquals(expected.get(0).getVehGrp(), actual.get(0).getVehGrp());
    Assertions.assertEquals(expected.get(0).getFixedVal(), actual.get(0).getFixedVal());
    Assertions.assertEquals(expected.get(0).getPerVal(), actual.get(0).getPerVal());
  }

  @Test
  void test_getFlatFareAdjustmentConf_empty() {

    final List<FlatFareAdjustmentConfJPA> configs = new ArrayList<>();

    Mockito.when(flatFareAdjustmentConfJPARepository.findAllByIsEnabledIsTrue())
        .thenReturn(configs);

    final List<FlatFareAdjustmentConfEntity> actual =
        flatFareAdjustmentConfRepository.getFlatFareAdjustmentConf();

    final List<FlatFareAdjustmentConfEntity> expected = new ArrayList<>();

    Assertions.assertEquals(expected, actual);
  }
}

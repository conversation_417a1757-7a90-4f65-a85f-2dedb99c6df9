package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.adapter;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.CMSConfigItem;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.CMSConfigList;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.CMSConfigRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.CreateCMSConfigRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs.SpringSecurityAuditorAware;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
class CMSServiceAdapterTest {

  @Mock CmsOutboundAdapter cmsOutboundAdapter;

  @Mock private SpringSecurityAuditorAware springSecurityAuditorAware;
  @InjectMocks private CMSServiceAdapter cmsServiceAdapter;

  @Test
  void testGetCMSBySearchText() {
    ReflectionTestUtils.setField(cmsServiceAdapter, "serviceName", "ME-PRICING");
    String searchText = "test";
    ResponseEntity<CMSConfigList> responseEntity =
        new ResponseEntity<>(CMSConfigList.builder().build(), HttpStatus.OK);
    when(cmsOutboundAdapter.getCmsNewPricingModelConfig(anyString(), any(), anyInt()))
        .thenReturn(responseEntity);

    CMSConfigList result = cmsServiceAdapter.getCMSBySearchText(searchText);

    assertEquals(responseEntity.getBody(), result);
    verify(cmsOutboundAdapter, times(1))
        .getCmsNewPricingModelConfig(searchText, "ME-PRICING", Integer.MAX_VALUE);
  }

  @Test
  void testUpdateCMSConfig() {
    ReflectionTestUtils.setField(cmsServiceAdapter, "serviceName", "ME-PRICING");
    Long id = 1L;
    String value = "test value";
    String updatedBy = "test user";
    when(springSecurityAuditorAware.getCurrentAuditor())
        .thenReturn(java.util.Optional.of(updatedBy));

    cmsServiceAdapter.updateCMSConfig(id, value);

    verify(cmsOutboundAdapter, times(1)).updateCmsConfig(eq(id), any(CMSConfigRequest.class));
  }

  @Test
  void testCreateCmsConfig() {
    ReflectionTestUtils.setField(cmsServiceAdapter, "serviceName", "ME-PRICING");
    CreateCMSConfigRequest createCmsConfigRequest =
        CreateCMSConfigRequest.builder().value("test value").build();
    String updatedBy = "test user";
    CMSConfigItem cmsConfigItem = CMSConfigItem.builder().id(1L).build();
    ResponseEntity<CMSConfigItem> responseEntity =
        new ResponseEntity<>(cmsConfigItem, HttpStatus.CREATED);
    when(springSecurityAuditorAware.getCurrentAuditor())
        .thenReturn(java.util.Optional.of(updatedBy));
    when(cmsOutboundAdapter.createCmsConfig(any(CreateCMSConfigRequest.class)))
        .thenReturn(responseEntity);

    CMSConfigItem result = cmsServiceAdapter.createCmsConfig(createCmsConfigRequest);

    assertEquals(cmsConfigItem, result);
    verify(cmsOutboundAdapter, times(1)).createCmsConfig(any(CreateCMSConfigRequest.class));
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.infra.adapter.configs.properties;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs.properties.RefreshableProperties;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * RefreshablePropertiesTest is a unit test class designed to ensure the correct behavior of the
 * RefreshableProperties class. It leverages the MockitoExtension for mocking dependencies
 * and @TestPropertySource for loading properties from the 'application.properties' file. The setUp
 * method initializes the RefreshableProperties object with predefined values using
 * ReflectionTestUtils, simulating the environment in which the class would typically run. This
 * ensures that the dynamic pricing system's configuration handling is reliable and functions as
 * expected.
 */
@ExtendWith(MockitoExtension.class)
@TestPropertySource("classpath:application.properties")
class RefreshablePropertiesTest {

  @Mock private RefreshableProperties refreshableProperties;

  @BeforeEach
  void setUp() {
    refreshableProperties = new RefreshableProperties();
    ReflectionTestUtils.setField(refreshableProperties, "bookRideSchedulingMin", "3");
    ReflectionTestUtils.setField(refreshableProperties, "feedId", "comfortdelgro");
    ReflectionTestUtils.setField(refreshableProperties, "currencyCode", "SGD");
    ReflectionTestUtils.setField(refreshableProperties, "bookRideVehicleGroup", "0");
    ReflectionTestUtils.setField(refreshableProperties, "bookRideProduct", "COMFORT,METERED");
    ReflectionTestUtils.setField(refreshableProperties, "comfortLocalisedName", "ComfortRIDE");
    ReflectionTestUtils.setField(refreshableProperties, "meteredLocalisedName", "Metered Fare");
    ReflectionTestUtils.setField(refreshableProperties, "comfortInternalName", "FLAT001");
    ReflectionTestUtils.setField(refreshableProperties, "meteredInternalName", "STD001");
    ReflectionTestUtils.setField(refreshableProperties, "waitingTimeSeconds", "1");
    ReflectionTestUtils.setField(refreshableProperties, "fileName", "comfortdelgro");
    ReflectionTestUtils.setField(
        refreshableProperties, "comfortLowRangeEstimateMultiplierValue", "0.9");
    ReflectionTestUtils.setField(
        refreshableProperties, "meteredLowRangeEstimateMultiplierValue", "1");
    ReflectionTestUtils.setField(
        refreshableProperties, "comfortHighRangeEstimateMultiplierValue", "1");
    ReflectionTestUtils.setField(
        refreshableProperties, "meteredHighRangeEstimateMultiplierValue", "1.15");
    ReflectionTestUtils.setField(refreshableProperties, "minimumFixed", "4.2");
    ReflectionTestUtils.setField(refreshableProperties, "bookRideProdCategory", "5");
    ReflectionTestUtils.setField(refreshableProperties, "carIconId", "comfortdelgro-taxi");
  }

  @Test
  void givenMockData_whenGetBookARideConfigs_thenReturnBookARideConfigPropertiesMap() {
    Map<String, String> configMap = refreshableProperties.getBookARideConfigProperties();

    assertEquals("3", configMap.get("BOOK_RIDE_SCHEDULING_MIN"));
    assertEquals("comfortdelgro", configMap.get("FEED_ID"));
    assertEquals("SGD", configMap.get("CURRENCY_CODE"));
    assertEquals("0", configMap.get("BOOK_RIDE_VEHICLE_GROUP"));
    assertEquals("COMFORT,METERED", configMap.get("BOOK_RIDE_PRODUCT"));
    assertEquals("ComfortRIDE", configMap.get("COMFORT_LOCALISED_NAME"));
    assertEquals("Metered Fare", configMap.get("METERED_LOCALISED_NAME"));
    assertEquals("FLAT001", configMap.get("COMFORT_INTERNAL_NAME"));
    assertEquals("STD001", configMap.get("METERED_INTERNAL_NAME"));
    assertEquals("1", configMap.get("WAITING_TIME_SECONDS"));
    assertEquals("comfortdelgro", configMap.get("FILE_NAME"));
    assertEquals("0.9", configMap.get("COMFORT_LOW_RANGE_ESTIMATE_MULTIPLIER_VALUE"));
    assertEquals("1", configMap.get("METERED_LOW_RANGE_ESTIMATE_MULTIPLIER_VALUE"));
    assertEquals("1", configMap.get("COMFORT_HIGH_RANGE_ESTIMATE_MULTIPLIER_VALUE"));
    assertEquals("1.15", configMap.get("METERED_HIGH_RANGE_ESTIMATE_MULTIPLIER_VALUE"));
    assertEquals("4.2", configMap.get("MINIMUM_FIXED"));
    assertEquals("5", configMap.get("BOOK_RIDE_PROD_CATEGORY"));
    assertEquals("comfortdelgro-taxi", configMap.get("CAR_ICON_ID"));
  }
}

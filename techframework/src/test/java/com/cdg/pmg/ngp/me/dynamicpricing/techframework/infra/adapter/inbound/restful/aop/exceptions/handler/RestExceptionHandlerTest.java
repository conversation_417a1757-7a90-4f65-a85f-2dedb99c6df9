package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.aop.exceptions.handler;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.Response;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.BadRequestException;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.DomainException;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.InternalServerException;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.NotFoundException;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.TimeoutException;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.aop.exceptions.BaseException;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.aop.exceptions.FieldValidationException;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.language.ResourceLanguage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockHttpServletRequest;

@ExtendWith(MockitoExtension.class)
class RestExceptionHandlerTest {
  @Mock private RestExceptionHandler exceptionHandler;
  @Mock private ResourceLanguage language;

  private MockHttpServletRequest httpServletRequest = new MockHttpServletRequest();

  @BeforeEach
  void setUp() {
    exceptionHandler = new RestExceptionHandler(language);
  }

  @Test
  void givenFieldValidationException__whenHandleException__returnServerError() {
    // GIVEN
    final FieldValidationException exception =
        new FieldValidationException("Server exception", 500L);
    Mockito.mock(FieldValidationException.class);
    final Response<Object> expected = Response.fail("Server exception", 500L);
    // WHEN
    // THEN
    final Response<Object> result = exceptionHandler.handleException(exception);
    // ASSERT
    assertEquals(expected.getError().getCode(), result.getError().getCode());
  }

  @Test
  void givenBaseException__whenHandleException__returnServerError() {
    // GIVEN
    final BaseException exception =
        new BaseException("Invalid", 500L, HttpStatus.INTERNAL_SERVER_ERROR);
    final Response<Object> expected = Response.fail("Invalid", 500L);
    // WHEN
    // THEN
    final ResponseEntity<Response<Object>> result = exceptionHandler.handleException(exception);
    // ASSERT
    assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, result.getStatusCode());
  }

  @Test
  void givenDomainExceptionException__whenHandleException__returnServerError() {
    // GIVEN
    final DomainException exception =
        new BaseException("Invalid", 500L, HttpStatus.INTERNAL_SERVER_ERROR);
    final Response<Object> expected = Response.fail("Invalid", 500L);
    // WHEN
    // THEN
    final ResponseEntity<Response<Object>> result = exceptionHandler.handleException(exception);
    // ASSERT
    assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, result.getStatusCode());
  }

  @Test
  void givenInternalServerException__whenHandleException__returnServerError() {
    // GIVEN
    final InternalServerException exception =
        new InternalServerException("Invalid", 500L, HttpStatus.INTERNAL_SERVER_ERROR);
    final Response<Object> expected = Response.fail("Invalid", 500L);
    // WHEN
    // THEN
    final ResponseEntity<Response<Object>> result = exceptionHandler.handleException(exception);
    // ASSERT
    assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, result.getStatusCode());
  }

  @Test
  void givenNotFoundException__whenHandleException__returnServerError() {
    // GIVEN
    final NotFoundException exception =
        new NotFoundException("Invalid", 500L, HttpStatus.NOT_FOUND);
    final Response<Object> expected = Response.fail("Invalid", 500L);
    // WHEN
    // THEN
    final ResponseEntity<Response<Object>> result = exceptionHandler.handleException(exception);
    // ASSERT
    assertEquals(HttpStatus.NOT_FOUND, result.getStatusCode());
  }

  @Test
  void givenBadRequestException__whenHandleException__returnServerError() {
    // GIVEN
    final BadRequestException exception =
        new BadRequestException("Invalid", 400L, HttpStatus.BAD_REQUEST);
    final Response<Object> expected = Response.fail("Invalid", 400L);
    // WHEN
    // THEN
    final ResponseEntity<Response<Object>> result = exceptionHandler.handleException(exception);
    // ASSERT
    assertEquals(HttpStatus.BAD_REQUEST, result.getStatusCode());
  }

  @Test
  void givenTimeoutException__whenHandleException__returnServerError() {
    // GIVEN
    final TimeoutException exception =
        new TimeoutException("Invalid", 408L, HttpStatus.INTERNAL_SERVER_ERROR);
    final Response<Object> expected = Response.fail("Invalid", 408L);
    // WHEN
    // THEN
    final ResponseEntity<Response<Object>> result = exceptionHandler.handleException(exception);
    // ASSERT
    assertEquals(HttpStatus.REQUEST_TIMEOUT, result.getStatusCode());
  }
}

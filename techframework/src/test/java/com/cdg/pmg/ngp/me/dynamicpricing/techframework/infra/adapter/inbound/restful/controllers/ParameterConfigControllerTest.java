package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.controllers;

import static com.cdg.pmg.ngp.me.dynamicpricing.constants.ConfigKeyConstant.DYNP_MIN_CAP;
import static com.cdg.pmg.ngp.me.dynamicpricing.constants.DomainConstant.MON;
import static com.cdg.pmg.ngp.me.dynamicpricing.constants.DomainConstant.TEN_AM;
import static com.cdg.pmg.ngp.me.dynamicpricing.constants.DomainConstant.TEN_HOUR;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.FareTypeConfigService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.PricingRangeConfigService;
import com.cdg.pmg.ngp.me.dynamicpricing.commands.FareTypeConfigCommand;
import com.cdg.pmg.ngp.me.dynamicpricing.commands.PricingRangeConfigCommand;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FareTypeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.PricingRangeConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper.FareTypeConfigMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper.PricingRangeConfigMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.ConfigRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.FareTypeConfigRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.FareTypeConfigResponseData;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.PricingRangeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.PricingRangeConfigRequest;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

/** The type Parameter config controller test. */
@ExtendWith(MockitoExtension.class)
class ParameterConfigControllerTest {
  @InjectMocks private ParameterConfigController controller;
  @Mock private FareTypeConfigService fareTypeConfigService;
  @Mock private FareTypeConfigMapper fareTypeConfigMapper;
  @Mock private PricingRangeConfigService pricingRangeConfigService;
  @Mock private PricingRangeConfigMapper pricingRangeConfigMapper;

  private FareTypeConfig fareTypeConfig;
  private com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.FareTypeConfig fareTypeConfigModel;
  private FareTypeConfigCommand command;
  private FareTypeConfigResponseData fareTypeConfigResponseData;
  private PricingRangeConfigEntity pricingRangeConfigEntity;
  private PricingRangeConfig pricingRangeConfig;

  /** Init controller and mock data. */
  @BeforeEach
  void initControllerAndMockData() {
    fareTypeConfig =
        FareTypeConfig.builder().fareType(DYNP_MIN_CAP).day(MON).hour(TEN_HOUR).build();

    fareTypeConfigModel =
        new com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.FareTypeConfig();
    fareTypeConfigModel.setFareType(DYNP_MIN_CAP);
    fareTypeConfigModel.setDay(MON);
    fareTypeConfigModel.setHour(TEN_HOUR);

    command =
        FareTypeConfigCommand.builder().fareType(DYNP_MIN_CAP).day(MON).hour(TEN_HOUR).build();

    fareTypeConfigResponseData = new FareTypeConfigResponseData();
    fareTypeConfigResponseData.setFareType(DYNP_MIN_CAP);
    fareTypeConfigResponseData.setDay(MON);
    fareTypeConfigResponseData.setHour(TEN_HOUR);
    pricingRangeConfigEntity =
        PricingRangeConfigEntity.builder().pricingRangeId(1).hour(TEN_AM).build();
    pricingRangeConfig = new PricingRangeConfig();
    pricingRangeConfig.setPricingRangeId(BigDecimal.valueOf(1));
    pricingRangeConfig.setHour(TEN_AM);
  }

  /** Gets param config by list fare type has valid input return ok. */
  @Test
  void getParamConfigByListFareType_hasValidInput_returnOK() {
    List<FareTypeConfig> fareTypeConfigs = new ArrayList<>();
    fareTypeConfigs.add(fareTypeConfig);
    when(fareTypeConfigService.getParamConfigsByListFareType(Mockito.any()))
        .thenReturn(fareTypeConfigs);

    List<com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.FareTypeConfig> data =
        new ArrayList<>();
    data.add(fareTypeConfigModel);
    when(fareTypeConfigMapper.mapFareTypeConfigDomainToFareTypeConfigTech(Mockito.any()))
        .thenReturn(data);

    List<String> listFareType = new ArrayList<>();
    listFareType.add(DYNP_MIN_CAP);
    var actual =
        Objects.requireNonNull(controller.getParamConfigsByListFareType(listFareType).getBody())
            .getData()
            .get(0)
            .getDay();
    assertEquals(MON, actual);
  }

  /**
   * Update fare type config has valid input return ok.
   *
   * @throws ParseException the parse exception
   */
  @Test
  void updateFareTypeConfig_hasValidInput_returnOK() throws ParseException {
    when(fareTypeConfigMapper.mapFareTypeConfigRequestToFareTypeConfigCommand(Mockito.any()))
        .thenReturn(command);

    when(fareTypeConfigService.insertOrUpdateFareTypeConfig(Mockito.any()))
        .thenReturn(fareTypeConfig);

    when(fareTypeConfigMapper.mapFareTypeConfigToFareTypeConfigResponseData(Mockito.any()))
        .thenReturn(fareTypeConfigResponseData);

    FareTypeConfigRequest fareTypeConfigRequest = new FareTypeConfigRequest();
    fareTypeConfigRequest.setFareType(DYNP_MIN_CAP);
    ConfigRequest configRequest = new ConfigRequest();
    configRequest.setDay(MON);
    configRequest.setHour(TEN_HOUR);
    fareTypeConfigRequest.setConfigRequest(configRequest);
    var actual =
        Objects.requireNonNull(
                controller.insertOrUpdateFareTypeConfig(fareTypeConfigRequest).getBody())
            .getData()
            .get(0)
            .getDay();
    assertEquals(MON, actual);
  }

  /** Given no param when get pricing range configs then return successfully. */
  @Test
  void givenNoParam_whenGetPricingRangeConfigs_thenReturnSuccessfully() {
    List<PricingRangeConfigEntity> pricingRangeConfigEntities = new ArrayList<>();
    pricingRangeConfigEntities.add(pricingRangeConfigEntity);
    when(pricingRangeConfigService.getPricingRangeConfigs()).thenReturn(pricingRangeConfigEntities);

    when(pricingRangeConfigMapper.mapPricingRangeConfigEntityToPricingRangeConfig(Mockito.any()))
        .thenReturn(pricingRangeConfig);

    var actual =
        Objects.requireNonNull(controller.getPricingRangeConfigs().getBody())
            .getData()
            .get(0)
            .getHour();
    assertEquals(TEN_AM, actual);
  }

  /** Given valid in put when insert or update pricing range config then return successfully. */
  @Test
  void givenValidInPut_whenInsertOrUpdatePricingRangeConfig_thenReturnSuccessfully() {
    PricingRangeConfigCommand pricingRangeConfigCommand =
        PricingRangeConfigCommand.builder().hour(TEN_AM).build();
    when(pricingRangeConfigMapper.mapPricingRangeConfigRequestToPricingRangeConfigCommand(
            Mockito.any()))
        .thenReturn(pricingRangeConfigCommand);

    when(pricingRangeConfigService.insertOrUpdatePricingRangeConfig(Mockito.any()))
        .thenReturn(pricingRangeConfigEntity);

    when(pricingRangeConfigMapper.mapPricingRangeConfigEntityToPricingRangeConfig(Mockito.any()))
        .thenReturn(pricingRangeConfig);

    PricingRangeConfigRequest pricingRangeConfigRequest = new PricingRangeConfigRequest();
    pricingRangeConfigRequest.setHour(TEN_AM);
    var actual =
        Objects.requireNonNull(
                controller.insertOrUpdatePricingRangeConfig(pricingRangeConfigRequest).getBody())
            .getData()
            .get(0)
            .getHour();
    assertEquals(TEN_AM, actual);
  }
}

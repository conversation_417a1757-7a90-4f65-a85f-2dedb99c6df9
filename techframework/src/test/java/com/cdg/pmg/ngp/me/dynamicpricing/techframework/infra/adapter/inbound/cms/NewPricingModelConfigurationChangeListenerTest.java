package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.cms;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.NewPricingConfigChangeTrackingService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.ConfigurationServiceOutboundPort;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.NewPricingModelConfigChangeEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.NewPricingModelConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.EntityChangeTypeEnum;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs.DynamicSurgeProperties;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.mappers.NewPricingModelConfigChangeMapper;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mapstruct.factory.Mappers;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
class NewPricingModelConfigurationChangeListenerTest {

  @Mock private NewPricingConfigChangeTrackingService newPricingConfigChangeTrackingService;
  @Mock private ConfigurationServiceOutboundPort configurationOutboundPort;
  @Mock private DynamicSurgeProperties dynamicSurgeProperties;

  @Spy
  private NewPricingModelConfigChangeMapper newPricingModelConfigChangeMapper =
      Mappers.getMapper(NewPricingModelConfigChangeMapper.class);

  @Captor
  private ArgumentCaptor<List<NewPricingModelConfigChangeEntity>>
      newPricingModelConfigChangeEntitiesCaptor;

  @InjectMocks @Spy
  private NewPricingModelConfigurationChangeListener pricingModelConfigurationChangeListener;

  @Test
  void testInit() {
    // Arrange
    when(dynamicSurgeProperties.getFleetAnalyticDemand30()).thenReturn("30");
    when(dynamicSurgeProperties.getFleetAnalyticDemand60()).thenReturn("60");
    when(dynamicSurgeProperties.getDosIncludeJobStatuses()).thenReturn("FAILED");
    when(dynamicSurgeProperties.getDosExcludeBookingChannels()).thenReturn("GOJEK");
    when(dynamicSurgeProperties.getStd001Weight()).thenReturn("0.7");

    // Act
    pricingModelConfigurationChangeListener.init();

    // Assert
    verify(configurationOutboundPort).getNewPricingModelConfigEntities(false);
  }

  @Test
  void givenAPreviousPricingConfigEntities_whenPerformUpdateAnEntity_thenCaptureTheUpdatedEntity() {
    NewPricingModelConfigEntity newPricingModelConfigEntity =
        NewPricingModelConfigEntity.builder().id(1L).zoneId("01").build();
    NewPricingModelConfigEntity updatedPricingModelConfigEntity =
        NewPricingModelConfigEntity.builder().id(1L).zoneId("02").build();

    ReflectionTestUtils.setField(
        pricingModelConfigurationChangeListener,
        "previousEntities",
        List.of(newPricingModelConfigEntity));
    when(configurationOutboundPort.getNewPricingModelConfigEntities(false))
        .thenReturn(List.of(updatedPricingModelConfigEntity));
    when(dynamicSurgeProperties.getFleetAnalyticDemand30()).thenReturn("30");
    when(dynamicSurgeProperties.getFleetAnalyticDemand60()).thenReturn("60");
    when(dynamicSurgeProperties.getDosIncludeJobStatuses()).thenReturn("FAILED");
    when(dynamicSurgeProperties.getDosExcludeBookingChannels()).thenReturn("GOJEK");
    when(dynamicSurgeProperties.getStd001Weight()).thenReturn("0.7");
    ReflectionTestUtils.setField(
        pricingModelConfigurationChangeListener, "previousSurgeProperties", dynamicSurgeProperties);
    // Act
    pricingModelConfigurationChangeListener.listen();

    // Assert
    verify(newPricingConfigChangeTrackingService)
        .saveAll(newPricingModelConfigChangeEntitiesCaptor.capture());
    List<NewPricingModelConfigChangeEntity> changeEntities =
        newPricingModelConfigChangeEntitiesCaptor.getValue();
    assertEquals(2, changeEntities.size());

    assertEquals(1L, changeEntities.get(0).getConfigId());
    assertEquals(EntityChangeTypeEnum.UPDATE, changeEntities.get(0).getEntityChangeType());
    assertEquals("01", changeEntities.get(0).getZoneId());

    assertEquals(1L, changeEntities.get(1).getConfigId());
    assertEquals(EntityChangeTypeEnum.UPDATE, changeEntities.get(0).getEntityChangeType());
    assertEquals("02", changeEntities.get(1).getZoneId());
  }

  @Test
  void givenAPreviousPricingConfigEntities_whenPerformAddAnEntity_thenCaptureTheUpdatedEntity() {
    NewPricingModelConfigEntity updatedPricingModelConfigEntity =
        NewPricingModelConfigEntity.builder().id(1L).zoneId("01").build();

    ReflectionTestUtils.setField(
        pricingModelConfigurationChangeListener, "previousEntities", List.of());
    when(configurationOutboundPort.getNewPricingModelConfigEntities(false))
        .thenReturn(List.of(updatedPricingModelConfigEntity));
    when(dynamicSurgeProperties.getFleetAnalyticDemand30()).thenReturn("30");
    when(dynamicSurgeProperties.getFleetAnalyticDemand60()).thenReturn("60");
    when(dynamicSurgeProperties.getDosIncludeJobStatuses()).thenReturn("FAILED");
    when(dynamicSurgeProperties.getDosExcludeBookingChannels()).thenReturn("GOJEK");
    when(dynamicSurgeProperties.getStd001Weight()).thenReturn("0.7");
    ReflectionTestUtils.setField(
        pricingModelConfigurationChangeListener, "previousSurgeProperties", dynamicSurgeProperties);

    // Act
    pricingModelConfigurationChangeListener.listen();

    // Assert
    verify(newPricingConfigChangeTrackingService)
        .saveAll(newPricingModelConfigChangeEntitiesCaptor.capture());
    List<NewPricingModelConfigChangeEntity> changeEntities =
        newPricingModelConfigChangeEntitiesCaptor.getValue();
    assertEquals(1, changeEntities.size());

    assertEquals(1L, changeEntities.get(0).getConfigId());
    assertEquals(EntityChangeTypeEnum.ADD_NEW, changeEntities.get(0).getEntityChangeType());
    assertEquals("01", changeEntities.get(0).getZoneId());
  }

  @Test
  void givenAPreviousPricingConfigEntities_whenPerformDeleteAnEntity_thenCaptureTheUpdatedEntity() {
    NewPricingModelConfigEntity newPricingModelConfigEntity =
        NewPricingModelConfigEntity.builder().id(1L).zoneId("01").build();

    ReflectionTestUtils.setField(
        pricingModelConfigurationChangeListener,
        "previousEntities",
        List.of(newPricingModelConfigEntity));
    when(configurationOutboundPort.getNewPricingModelConfigEntities(false)).thenReturn(List.of());
    when(dynamicSurgeProperties.getFleetAnalyticDemand30()).thenReturn("30");
    when(dynamicSurgeProperties.getFleetAnalyticDemand60()).thenReturn("60");
    when(dynamicSurgeProperties.getDosIncludeJobStatuses()).thenReturn("FAILED");
    when(dynamicSurgeProperties.getDosExcludeBookingChannels()).thenReturn("GOJEK");
    when(dynamicSurgeProperties.getStd001Weight()).thenReturn("0.7");
    ReflectionTestUtils.setField(
        pricingModelConfigurationChangeListener, "previousSurgeProperties", dynamicSurgeProperties);

    // Act
    pricingModelConfigurationChangeListener.listen();

    // Assert
    verify(newPricingConfigChangeTrackingService)
        .saveAll(newPricingModelConfigChangeEntitiesCaptor.capture());
    List<NewPricingModelConfigChangeEntity> changeEntities =
        newPricingModelConfigChangeEntitiesCaptor.getValue();
    assertEquals(1, changeEntities.size());

    assertEquals(1L, changeEntities.get(0).getConfigId());
    assertEquals(EntityChangeTypeEnum.DELETE, changeEntities.get(0).getEntityChangeType());
    assertEquals("01", changeEntities.get(0).getZoneId());
  }
}

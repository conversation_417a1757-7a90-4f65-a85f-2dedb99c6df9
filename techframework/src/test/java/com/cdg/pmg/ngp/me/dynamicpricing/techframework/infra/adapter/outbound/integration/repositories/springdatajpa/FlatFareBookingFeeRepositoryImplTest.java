package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.FlatFareBookingFeeRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FlatFareBookingFeeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.custom.FlatFareBookingFeeJPACustom;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.FlatFareBookingFeeJPARepository;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class FlatFareBookingFeeRepositoryImplTest {
  @Mock private FlatFareBookingFeeRepository flatFareBookingFeeRepository;

  @Mock private FlatFareBookingFeeJPARepository flatFareBookingFeeJPARepository;

  @Mock private FlatFareBookingFeeJPACustom flatFareBookingFeeJPACustom;

  @BeforeEach
  void setUp() {
    flatFareBookingFeeRepository =
        new FlatFareBookingFeeRepositoryImpl(flatFareBookingFeeJPARepository);
  }

  @Test
  void getFlatFareBookingFeeConfigs_isEmptyConfig() {
    final List<FlatFareBookingFeeJPACustom> resultQuery = new ArrayList<>();
    Mockito.when(flatFareBookingFeeJPARepository.getFlatFareBookingFeeConfigs())
        .thenReturn(resultQuery);
    final List<FlatFareBookingFeeConfig> actual =
        flatFareBookingFeeRepository.getFlatFareBookingFeeConfigs();
    final List<FlatFareBookingFeeConfig> expected = new ArrayList<>();
    Assertions.assertEquals(expected, actual);
  }

  @Test
  void getFlatFareBookingFeeConfigs_isNotEmptyConfig() {
    final List<FlatFareBookingFeeJPACustom> resultQuery = List.of(flatFareBookingFeeJPACustom);
    Mockito.when(flatFareBookingFeeJPARepository.getFlatFareBookingFeeConfigs())
        .thenReturn(resultQuery);
    final List<FlatFareBookingFeeConfig> actual =
        flatFareBookingFeeRepository.getFlatFareBookingFeeConfigs();

    final FlatFareBookingFeeJPACustom conf = resultQuery.get(0);
    final boolean expected =
        actual.stream()
            .allMatch(
                actualConf ->
                    Objects.equals(actualConf.getProductId(), conf.getProductId())
                        && Objects.equals(actualConf.getVehTypeId(), conf.getVehTypeId())
                        && Objects.equals(actualConf.getTariffTypeCode(), conf.getTariffTypeCode())
                        && Objects.equals(actualConf.getFareAmt(), conf.getFareAmt())
                        && Objects.equals(actualConf.getLevyAmt(), conf.getLevyAmt())
                        && Objects.equals(actualConf.getStartTime(), conf.getStartTime())
                        && Objects.equals(actualConf.getEndTime(), conf.getEndTime())
                        && Objects.equals(
                            actualConf.getApplicableDays(), conf.getApplicableDays()));
    Assertions.assertTrue(expected);
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.redis.impl;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.anySet;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.EstimatedFare;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.InternalServerException;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs.properties.RedisAppProperties;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

@ExtendWith(MockitoExtension.class)
class RedisServiceImplTest {
  private final String TEST_KEY = "testKey";
  private final String DATA_JSON =
      """
 {
    "flatAndMeterFareForLimo": null,
    "flatfareForStandard": null,
    "meterFareForStandard": null,
    "dynamicPricingForStandard": null
 }
 """;
  private final int INT_EXPIRE_DURATION = 100;
  private final int INT_DEFAULT_EXPIRE_DURATION = 0;
  private final long LONG_EXPIRE_DURATION = 100;

  @Mock private StringRedisTemplate redisTemplate;
  @Mock private ObjectMapper objectMapper;
  @Mock private RedisAppProperties redisAppProperties;
  @Mock private ValueOperations<String, String> valueOperations;
  @Mock private ListOperations<String, String> listOperations;
  private RedisServiceImpl redisService;
  private EstimatedFare testObject;

  @BeforeEach
  public void setup() {
    redisService = new RedisServiceImpl(redisTemplate, objectMapper, redisAppProperties);
    testObject = new EstimatedFare();
    lenient().when(redisTemplate.opsForValue()).thenReturn(valueOperations);
    lenient().when(redisTemplate.opsForList()).thenReturn(listOperations);
  }

  // Helper method
  private <T> TypeReference<T> anyTypeReference() {
    return any();
  }

  @Test
  void givenKeyAndObject_whenSetValue_thenRedisStoresSerializedData()
      throws JsonProcessingException {
    // WHEN
    when(objectMapper.writeValueAsString(any())).thenReturn(DATA_JSON);

    // THEN
    redisService.setValue(TEST_KEY, testObject);

    verify(objectMapper, times(1)).writeValueAsString(testObject);
    verify(redisTemplate.opsForValue(), times(1)).set(TEST_KEY, DATA_JSON);
  }

  @Test
  void givenInvalidObject_whenSetValue_thenThrowsInternalServerException()
      throws JsonProcessingException {
    // WHEN
    when(objectMapper.writeValueAsString(any())).thenThrow(JsonProcessingException.class);

    // THEN
    assertThrows(
        InternalServerException.class, () -> redisService.setValue(TEST_KEY, EstimatedFare.class));

    verify(objectMapper, times(1)).writeValueAsString(any());
    verify(redisTemplate.opsForValue(), never()).set(anyString(), anyString());
  }

  @Test
  void givenValidObjectAndIntDuration_whenSetValue_thenStoreInRedisWithIntDuration()
      throws JsonProcessingException {
    // WHEN
    when(objectMapper.writeValueAsString(any())).thenReturn(DATA_JSON);

    // THEN
    redisService.setValue(TEST_KEY, testObject, INT_EXPIRE_DURATION);

    verify(objectMapper, times(1)).writeValueAsString(testObject);
    verify(redisTemplate.opsForValue(), times(1))
        .set(TEST_KEY, DATA_JSON, INT_EXPIRE_DURATION, TimeUnit.SECONDS);
  }

  @Test
  void givenInvalidJsonAndIntDuration_whenSetValue_thenThrowsInternalServerException()
      throws JsonProcessingException {
    // WHEN
    when(objectMapper.writeValueAsString(any())).thenThrow(JsonProcessingException.class);

    // THEN
    assertThrows(
        InternalServerException.class,
        () -> redisService.setValue(TEST_KEY, Object.class, INT_EXPIRE_DURATION));

    verify(objectMapper, times(1)).writeValueAsString(any());
    verify(redisTemplate.opsForValue(), never())
        .set(TEST_KEY, DATA_JSON, INT_EXPIRE_DURATION, TimeUnit.SECONDS);
  }

  @Test
  void givenValidObjectAndLongDuration_whenSetValue_thenStoreInRedisWithLongDuration()
      throws JsonProcessingException {
    // GIVEN
    long expireDuration = 5L;

    // WHEN
    when(objectMapper.writeValueAsString(any())).thenReturn(DATA_JSON);

    // THEN
    redisService.setValue(TEST_KEY, testObject, expireDuration);

    verify(objectMapper, times(1)).writeValueAsString(testObject);
    verify(redisTemplate.opsForValue(), times(1))
        .set(TEST_KEY, DATA_JSON, expireDuration, TimeUnit.SECONDS);
  }

  @Test
  void givenInvalidJsonAndLongDuration_whenSetValue_thenThrowsInternalServerException()
      throws JsonProcessingException {
    // WHEN
    when(objectMapper.writeValueAsString(any())).thenThrow(JsonProcessingException.class);

    // THEN
    assertThrows(
        InternalServerException.class,
        () -> redisService.setValue(TEST_KEY, testObject, LONG_EXPIRE_DURATION));

    verify(objectMapper, times(1)).writeValueAsString(any());
    verify(redisTemplate.opsForValue(), never())
        .set(TEST_KEY, DATA_JSON, LONG_EXPIRE_DURATION, TimeUnit.SECONDS);
  }

  @Test
  void givenValidParam_whenGetValue_thenReturnExpectedObject() throws JsonProcessingException {
    // WHEN
    when(valueOperations.get(any())).thenReturn(DATA_JSON);
    when(objectMapper.readValue(anyString(), ArgumentMatchers.<Class<EstimatedFare>>any()))
        .thenReturn(testObject);
    EstimatedFare expectedResult = testObject;

    // THEN
    EstimatedFare actualResult = redisService.getValue(TEST_KEY, EstimatedFare.class);
    assertEquals(expectedResult, actualResult);
  }

  @Test
  void givenInvalidJsonWithLongDuration_whenGetValue_thenThrowInternalServerException()
      throws JsonProcessingException {
    // WHEN
    when(valueOperations.get(anyString())).thenReturn(DATA_JSON);
    when(objectMapper.readValue(anyString(), ArgumentMatchers.<Class<EstimatedFare>>any()))
        .thenThrow(JsonProcessingException.class);

    // THEN
    assertThrows(
        InternalServerException.class, () -> redisService.getValue(TEST_KEY, EstimatedFare.class));
  }

  @Test
  void givenValidKey_whenDeleteByKey_thenDeleteInRedis() {
    // WHEN
    redisService.deleteByKey(TEST_KEY);

    // THEN
    verify(redisTemplate, times(1)).delete(TEST_KEY);
  }

  @Test
  void givenValidKeyAndLongDuration_whenSetExpire_thenSetRedisExpiry() {
    // WHEN
    redisService.setExpire(TEST_KEY, LONG_EXPIRE_DURATION);

    // THEN
    verify(redisTemplate, times(1)).expire(any(), anyLong(), eq(TimeUnit.SECONDS));
  }

  @Test
  void givenValidKeyAndNegativeLongDuration_whenSetExpire_thenDefaultExpiryUsed() {
    // GIVEN
    long expireDuration = -60L;
    // WHEN
    when(redisAppProperties.getDefaultExpiredTimeInSecond())
        .thenReturn(INT_DEFAULT_EXPIRE_DURATION);

    redisService.setExpire(TEST_KEY, expireDuration);

    // THEN
    verify(redisTemplate, times(1))
        .expire(TEST_KEY, redisAppProperties.getDefaultExpiredTimeInSecond(), TimeUnit.SECONDS);
  }

  @Test
  void givenValidKeyAndIntDuration_whenSetExpire_thenSetRedisExpiry() {
    // WHEN
    redisService.setExpire(TEST_KEY, INT_EXPIRE_DURATION);

    // THEN
    verify(redisTemplate, times(1)).expire(TEST_KEY, INT_EXPIRE_DURATION, TimeUnit.SECONDS);
  }

  @Test
  void givenValidKeyAndNegativeIntDuration_whenSetExpire_thenDefaultExpiryUsed() {
    // GIVEN
    int expireDuration = -60;

    // WHEN
    when(redisAppProperties.getDefaultExpiredTimeInSecond())
        .thenReturn(INT_DEFAULT_EXPIRE_DURATION);

    redisService.setExpire(TEST_KEY, expireDuration);

    // THEN
    verify(redisTemplate, times(1))
        .expire(TEST_KEY, redisAppProperties.getDefaultExpiredTimeInSecond(), TimeUnit.SECONDS);
  }

  @Test
  void givenValidKeySet_whenGetMultiValueList_thenReturnListsOfObjects() {
    Set<String> requestKeys = Set.of("key1");
    List<List<Object>> result = redisService.getMultiValueList(requestKeys, Object.class);
    int expectedSize = requestKeys.size();
    assertEquals(expectedSize, result.size());
  }

  @Test
  void givenSetOfKeysAndInvalidObject_whenGetMultiValueMap_thenReturnExpectedListOfMap()
      throws JsonProcessingException {
    // GIVEN
    Set<String> requestKeys = Set.of("key1");
    String json1 = "{\"keyA\": \"valueA\"}";
    int expectedSize = requestKeys.size();

    // WHEN
    when(redisTemplate.opsForValue().multiGet(anySet())).thenReturn(List.of(json1));
    when(objectMapper.readValue(anyString(), anyTypeReference()))
        .thenReturn(Map.of("keyA", "valueA"));

    // THEN
    List<Map<String, String>> result =
        redisService.getMultiValueMap(requestKeys, String.class, String.class);

    assertEquals(expectedSize, result.size());
    assertEquals("valueA", result.get(0).get("keyA"));
  }

  @Test
  void givenSetOfKeysAndInvalidObject_whenGetMultiValueMap_thenThrowsInternalServerException()
      throws JsonProcessingException {
    // GIVEN
    Set<String> keys = Set.of("key1", "key2");
    String sampleJson = "{\"keyA\": \"valueA\"}";

    // WHEN
    when(valueOperations.multiGet(anySet())).thenReturn(Collections.singletonList(sampleJson));
    when(objectMapper.readValue(anyString(), anyTypeReference()))
        .thenThrow(JsonProcessingException.class);

    // THEN
    assertThrows(
        InternalServerException.class,
        () -> redisService.getMultiValueMap(keys, Object.class, Object.class));
  }

  @Test
  void givenNullPattern_whenGetKeysByPattern_thenReturnEmptySet() {
    // WHEN
    Set<String> result = redisService.getKeysByPattern(null);

    // THEN
    assertTrue(result.isEmpty());
  }

  @Test
  void givenValidPatternButNoMatchingKeys_whenGetKeysByPattern_thenReturnEmptySet() {
    // WHEN
    when(redisTemplate.scan(any(ScanOptions.class))).thenReturn(mock(Cursor.class));

    // THEN
    Set<String> result = redisService.getKeysByPattern(TEST_KEY);

    assertTrue(result.isEmpty());
  }

  @Test
  void givenValidKeyAndJson_whenGetListValue_thenReturnExpectedList()
      throws JsonProcessingException {
    // GIVEN
    List<String> expectedResult = Collections.singletonList("");

    // WHEN
    when(redisTemplate.opsForList().range(TEST_KEY, 0, -1)).thenReturn(List.of(""));
    when(objectMapper.readValue("", String.class)).thenReturn("");

    // THEN
    List<String> actualResult = redisService.getListValue(TEST_KEY, String.class);
    assertEquals(expectedResult, actualResult);
  }

  @Test
  void givenTestKeyWithNoDataFromRedis_whenGetListValue_thenReturnEmptyList() {
    // WHEN
    when(redisTemplate.opsForList().range(TEST_KEY, 0, -1)).thenReturn(null);

    // THEN
    List<EstimatedFare> actualResult = redisService.getListValue(TEST_KEY, EstimatedFare.class);
    assertTrue(actualResult.isEmpty());
  }

  @Test
  void givenTestKeyWithInvalidJsonDataFromRedis_whenGetListValue_thenThrowsInternalServerException()
      throws JsonProcessingException {
    // WHEN
    when(redisTemplate.opsForList().range(TEST_KEY, 0, -1)).thenReturn(List.of(""));
    when(objectMapper.readValue("", String.class)).thenThrow(JsonProcessingException.class);

    // THEN
    assertThrows(
        InternalServerException.class, () -> redisService.getListValue(TEST_KEY, String.class));
  }

  @Test
  void givenValidKeyAndJson_whenSetListValue_thenNotThrowException() {
    // THEN
    assertDoesNotThrow(() -> redisService.setListValue(TEST_KEY, List.of("")));
  }

  @Test
  void givenValidKeyAndJson_whenSetListValue_thenThrowException() throws JsonProcessingException {
    when(objectMapper.writeValueAsString(anyString())).thenThrow(JsonProcessingException.class);
    // THEN
    assertThrows(
        InternalServerException.class, () -> redisService.setListValue(TEST_KEY, List.of("")));
  }

  @Test
  void givenValidKeyAndJson_whenGetMapValue_thenReturnExpectedMap() throws JsonProcessingException {
    // GIVEN
    Map<String, EstimatedFare> expectedResult = new HashMap<>();
    expectedResult.put("testKey1", new EstimatedFare());

    // WHEN
    when(valueOperations.get(anyString())).thenReturn(DATA_JSON);
    when(objectMapper.readValue(anyString(), anyTypeReference())).thenReturn(expectedResult);
    when(objectMapper.convertValue(any(), eq(EstimatedFare.class))).thenReturn(new EstimatedFare());

    // THEN
    Map<String, EstimatedFare> actualResult =
        redisService.getMapValue(TEST_KEY, String.class, EstimatedFare.class);
    assertEquals(expectedResult, actualResult);
  }

  @Test
  void givenTestKeyWithNoDataFromRedis_whenGetMapValue_thenReturnEmptyMap() {
    // GIVEN
    Map<String, EstimatedFare> expectedResult = new HashMap<>();

    // WHEN
    when(valueOperations.get(anyString())).thenReturn(null);

    // THEN
    Map<String, EstimatedFare> actualResult =
        redisService.getMapValue(TEST_KEY, String.class, EstimatedFare.class);
    assertEquals(expectedResult, actualResult);
  }

  @Test
  void givenTestKeyWithInvalidJsonDataFromRedis_whenGetMapValue_thenThrowsInternalServerException()
      throws JsonProcessingException {
    // WHEN
    when(valueOperations.get(anyString())).thenReturn(DATA_JSON);
    when(objectMapper.readValue(anyString(), anyTypeReference()))
        .thenThrow(JsonProcessingException.class);

    // THEN
    assertThrows(
        InternalServerException.class,
        () -> redisService.getMapValue(TEST_KEY, String.class, Class.class));
  }

  @Test
  void givenValidKey_whenDeleteByKeys_thenDeleteInRedis() {
    // WHEN
    redisService.deleteByKeys(Set.of(TEST_KEY));

    // THEN
    verify(redisTemplate, times(1)).delete(Set.of(TEST_KEY));
  }

  @Test
  void givenValidParam_whenGetDataJsonByKey_thenDataJson() {
    // GIVEN
    String key1 = "key1";
    String dataJson1 = "{\"data1\"}";
    // WHEN

    when(redisTemplate.opsForValue().get(key1)).thenReturn(dataJson1);

    // THEN
    String result = redisService.getStringValueByKey(key1);
    assertNotNull(result);
    assertTrue(result.equalsIgnoreCase(dataJson1));
  }
}

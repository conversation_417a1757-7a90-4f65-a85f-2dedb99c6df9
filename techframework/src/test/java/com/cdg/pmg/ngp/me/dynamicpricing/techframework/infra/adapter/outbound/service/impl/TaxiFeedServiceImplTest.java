package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.service.impl;

import static org.junit.jupiter.api.Assertions.assertNotEquals;

import com.cdg.pmg.ngp.me.dynamicpricing.constants.BookARideConfigsConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FareUploadConfiguration;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.LocationSurchargeConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.S2CellEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.VGProductFareBean;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.TaxiFeed;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.utils.TaxiFeedUtils;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class TaxiFeedServiceImplTest {
  @Mock private TaxiFeedServiceImpl taxiFeedService;

  @Mock private TaxiFeedUtils taxiFeedHelper;

  private Map<String, String> bookARideConfig = new HashMap<>();

  private final FareUploadConfiguration fareUploadConfiguration =
      FareUploadConfiguration.builder().build();
  private final Map<String, List<String>> productList = new HashMap<>();

  @BeforeEach
  void setUp() {

    taxiFeedService = new TaxiFeedServiceImpl();

    LocalTime now = LocalTime.of(5, 00, 00);

    List<S2CellEntity> s2CellEntityList =
        List.of(
            S2CellEntity.builder()
                .s2CellSeqId(Long.parseLong("1"))
                .s2CellId("3592184792021467136")
                .s2CellTokenId("31da01094")
                .s2CellLatitude(Double.parseDouble("1.2190221494"))
                .s2CellLongitude(Double.parseDouble("103.6058864422"))
                .s2CellLevel(Integer.parseInt("15"))
                .s2CellZoneId("93")
                .s2CellLocationId("1")
                .s2CellDesc("GUL N180")
                .s2CellLocDesc("Jurong Island N11")
                .build(),
            S2CellEntity.builder()
                .s2CellSeqId(Long.parseLong("2"))
                .s2CellId("3592184796316434432")
                .s2CellTokenId("31da010a4")
                .s2CellLatitude(Double.parseDouble("1.2166692299"))
                .s2CellLongitude(Double.parseDouble("103.6087797593"))
                .s2CellLevel(Integer.parseInt("15"))
                .s2CellZoneId("93")
                .s2CellLocationId("2")
                .s2CellDesc("GUL N178")
                .s2CellLocDesc("Jurong Island N10")
                .build());
    List<LocationSurchargeConfigEntity> locationSurChargeConfigList =
        List.of(
            LocationSurchargeConfigEntity.builder()
                .fareType("LOC_SURC")
                .dayIndicator("HOL")
                .startTime(String.valueOf(now.minusHours(1)))
                .endTime(String.valueOf(now.plusHours(1)))
                .chargeBy("PICKUP")
                .surchargeValue(1.0)
                .locationId(1)
                .locationName("")
                .addressRef("")
                .productId("FLAT-001")
                .build(),
            LocationSurchargeConfigEntity.builder()
                .fareType("EV_SURGE")
                .dayIndicator("HOL")
                .startTime(String.valueOf(now.minusHours(1)))
                .endTime(String.valueOf(now.plusHours(1)))
                .chargeBy("PICKUP")
                .locationId(2)
                .locationName("")
                .addressRef("")
                .productId("STD001")
                .build());

    Map<Integer, List<LocationSurchargeConfigEntity>> locationSurChargeConfig = new HashMap<>();
    locationSurChargeConfig.put(1, locationSurChargeConfigList);
    VGProductFareBean vgProductFareBean =
        VGProductFareBean.builder()
            .vehicleGroupId("0")
            .productId("COMFORT")
            .isSurgeRequired(true)
            .flagDown(0.0)
            .tier1Fare(0.0)
            .tier2Fare(0.0)
            .tier1Start(1.0)
            .tier2Start(1.0)
            .tier1End(3.0)
            .tier2End(3.0)
            .tier1PerCountMeter(1000.0)
            .tier2PerCountMeter(1010.0)
            .waitTimeFare(1.0)
            .bookingFee(0.0)
            .bookingFee(8.0)
            .build();

    bookARideConfig = getBookARideConfigsMap();
    fareUploadConfiguration.setS2CellList(s2CellEntityList);
    fareUploadConfiguration.setLocationSurChargeConfig(locationSurChargeConfig);
    fareUploadConfiguration.setVgProductFareMap(Map.of(0, Map.of("COMFORT", vgProductFareBean)));
    fareUploadConfiguration.setSurgeConfig(Map.of("93", 1));
    productList.put("0", List.of("COMFORT"));
  }

  @Test
  void givenNoParams_whenBuildTaxiFeed_thenReturnMockData() {
    TaxiFeed result =
        taxiFeedService.buildTaxiFeed(bookARideConfig, fareUploadConfiguration, productList);
    Assertions.assertEquals("comfortdelgro", result.getMetaData().getFeedId());
    Assertions.assertEquals(3, result.getAreasCount());
    assertNotEquals(0, result.getAreasCount());
    assertNotEquals(0, result.getProductsCount());
  }

  private static Map<String, String> getBookARideConfigsMap() {
    Map<String, String> configMap = new HashMap<>();
    configMap.put(BookARideConfigsConstant.BOOK_RIDE_SCHEDULING_MIN, "3");
    configMap.put(BookARideConfigsConstant.FEED_ID, "comfortdelgro");
    configMap.put(BookARideConfigsConstant.CURRENCY_CODE, "SGD");
    configMap.put(BookARideConfigsConstant.BOOK_RIDE_VEHICLE_GROUP, "0");
    configMap.put(BookARideConfigsConstant.BOOK_RIDE_PRODUCT, "COMFORT,METERED");
    configMap.put(BookARideConfigsConstant.COMFORT_LOCALISED_NAME, "ComfortRIDE");
    configMap.put(BookARideConfigsConstant.METERED_LOCALISED_NAME, "Metered Fare");
    configMap.put(BookARideConfigsConstant.COMFORT_INTERNAL_NAME, "FLAT001");
    configMap.put(BookARideConfigsConstant.METERED_INTERNAL_NAME, "STD001");
    configMap.put(BookARideConfigsConstant.WAITING_TIME_SECONDS, "1");
    configMap.put(BookARideConfigsConstant.FILE_NAME, "comfortdelgro");
    configMap.put("COMFORT_" + BookARideConfigsConstant.LOW_RANGE_ESTIMATE_MULTIPLIER_VALUE, "0.9");
    configMap.put("METERED_" + BookARideConfigsConstant.LOW_RANGE_ESTIMATE_MULTIPLIER_VALUE, "1");
    configMap.put("COMFORT_" + BookARideConfigsConstant.HIGH_RANGE_ESTIMATE_MULTIPLIER_VALUE, "1");
    configMap.put(
        "METERED_" + BookARideConfigsConstant.HIGH_RANGE_ESTIMATE_MULTIPLIER_VALUE, "1.15");
    configMap.put(BookARideConfigsConstant.MINIMUM_FIXED, "4.2");
    configMap.put(BookARideConfigsConstant.BOOK_RIDE_PROD_CATEGORY, "5");
    configMap.put(BookARideConfigsConstant.CAR_ICON_ID, "comfortdelgro-taxi");
    return configMap;
  }
}

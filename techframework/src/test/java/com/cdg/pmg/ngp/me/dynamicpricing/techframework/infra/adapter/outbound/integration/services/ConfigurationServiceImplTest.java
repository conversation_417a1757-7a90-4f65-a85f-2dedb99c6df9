package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.services;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.DpsProperties;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs.SystemParamProperties;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.mapper.ConfigurationMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ConfigurationServiceImplTest {
  @InjectMocks private ConfigurationServiceImpl configurationService;
  @Mock private SystemParamProperties systemParamProperties;
  @Mock private ConfigurationMapper configurationMapper;

  @BeforeEach
  public void setUp() {
    configurationService = new ConfigurationServiceImpl(systemParamProperties, configurationMapper);
  }

  @Test
  void givenNormalCondition_whenGetDpsProperties_thenReturnSuccess() {
    // GIVEN
    final DpsProperties configuration = new DpsProperties();
    configuration.setApplicationRelease(2);
    // WHEN
    when(configurationMapper.toDpsProperties(systemParamProperties)).thenReturn(configuration);
    // THEN
    final DpsProperties result = configurationService.getDpsProperties();
    // ASSERT
    assertEquals(configuration, result);
  }
}

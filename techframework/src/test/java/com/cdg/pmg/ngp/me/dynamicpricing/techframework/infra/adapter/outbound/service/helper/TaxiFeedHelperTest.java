package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.service.helper;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.FareUploadConfiguration;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.InternalServerException;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.utils.TaxiFeedUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class TaxiFeedHelperTest {
  @Mock private TaxiFeedUtils taxiFeedHelper;

  @Mock private FareUploadConfiguration fareUploadConfiguration;

  @Test
  void givenFareUploadConfiguration_whenBuildTaxiAreas_thenInternalServerExceptionIsThrown() {
    // Given
    when(fareUploadConfiguration.getS2CellList()).thenThrow(new RuntimeException("Mock exception"));
    // When & Then
    assertThrows(
        InternalServerException.class,
        () -> taxiFeedHelper.buildTaxiAreas(fareUploadConfiguration));
  }
}

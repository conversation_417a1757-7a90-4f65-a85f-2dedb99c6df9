package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.mapper;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.GenerateRouteResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos.GenerateRouteOutboundResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos.PolylineOutboundResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos.RouteOutboundResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.mapper.AddressOutboundMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.mapper.AddressOutboundMapperImpl;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
/** Address Outbound Mapper test. */
class AddressOutboundMapperTest {

  @Mock AddressOutboundMapper mapper;

  @BeforeEach
  void setUp() {
    mapper = new AddressOutboundMapperImpl();
  }

  @Test
  void givenData_whenMapToGenerateRouteResponse_thenReturnExpected() {
    GenerateRouteResponse generateRouteResponse =
        GenerateRouteResponse.builder()
            .distanceMeters(1L)
            .description("description")
            .duration("duration")
            .staticDuration("staticDuration")
            .encodedPolyline("encodedPolyline")
            .build();
    GenerateRouteOutboundResponse generateRouteOutboundResponse =
        GenerateRouteOutboundResponse.builder()
            .routes(
                List.of(
                    RouteOutboundResponse.builder()
                        .distanceMeters(1L)
                        .description("description")
                        .duration("duration")
                        .staticDuration("staticDuration")
                        .polyline(
                            PolylineOutboundResponse.builder()
                                .encodedPolyline("encodedPolyline")
                                .build())
                        .build()))
            .build();
    assertEquals(
        generateRouteResponse, mapper.mapToGenerateRouteResponse(generateRouteOutboundResponse));
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa;

import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.ERROR_MAP_TO_ROUTE_INFO;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.FlatFareBreakDownRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FareBreakdownDetailEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.RouteInfo;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.BadRequestException;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.InternalServerException;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.NotFoundException;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.FlatFareBreakdownDetailJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.custom.FareBreakdownJPACustom;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.mappers.FlatFareBreakdownEntityMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.FlatFareBreakdownJPARepository;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class FlatFareBreakdownRepositoryImplTest {
  @Mock private FlatFareBreakDownRepository flatFareBreakDownRepository;
  @Mock private FlatFareBreakdownJPARepository flatFareBreakdownJPARepository;
  @Mock private FlatFareBreakdownEntityMapper breakdownEntityMapper;
  @Mock private FareBreakdownJPACustom fareBreakdownJPACustom;

  @BeforeEach
  void setUp() {
    flatFareBreakDownRepository =
        new FlatFareBreakdownRepositoryImpl(flatFareBreakdownJPARepository, breakdownEntityMapper);
  }

  @Test
  void givenNormalParam_whenCreateFareBreakdown_thenSuccess() {
    when(breakdownEntityMapper.mapToFlatFareBreakdownDetailJpa(Mockito.any()))
        .thenReturn(new FlatFareBreakdownDetailJPA());
    when(flatFareBreakdownJPARepository.save(Mockito.any()))
        .thenReturn(new FlatFareBreakdownDetailJPA());
    assertDoesNotThrow(
        () -> flatFareBreakDownRepository.createFareBreakdown(new FareBreakdownDetailEntity()));
  }

  @Test
  void givenFareIdAlreadyExisted_whenIsExisted_thenTrue() {
    when(flatFareBreakdownJPARepository.findByBookingId(Mockito.any()))
        .thenReturn(new FlatFareBreakdownDetailJPA());
    assertTrue(flatFareBreakDownRepository.isExisted(Mockito.anyString()));
  }

  @Test
  void givenFareIdNotExisted_whenIsExisted_thenFalse() {
    when(flatFareBreakdownJPARepository.findByBookingId(Mockito.any())).thenReturn(null);
    assertFalse(flatFareBreakDownRepository.isExisted(Mockito.anyString()));
  }

  @Test
  void givenNormalParam_whenCreateFareBreakdown_thenInternalServerException() {
    when(breakdownEntityMapper.mapToFlatFareBreakdownDetailJpa(Mockito.any()))
        .thenReturn(new FlatFareBreakdownDetailJPA());
    when(flatFareBreakdownJPARepository.save(Mockito.any()))
        .thenThrow(new IllegalArgumentException());
    FareBreakdownDetailEntity request = new FareBreakdownDetailEntity();
    assertThrows(
        InternalServerException.class,
        () -> flatFareBreakDownRepository.createFareBreakdown(request));
  }

  @Test
  void givenTripIdIsBlank_whenGetGeneratedRouteByTripId_thenThrowBadRequestException() {
    assertThrows(
        BadRequestException.class, () -> flatFareBreakDownRepository.getGeneratedRouteByTripId(""));
  }

  @Test
  void givenTripId_whenGetGeneratedRouteByTripId_thenReturnSuccessfully() {
    FlatFareBreakdownDetailJPA flatFareBreakdownDetailJPA = new FlatFareBreakdownDetailJPA();
    flatFareBreakdownDetailJPA.setFlatFareBreakdownId(1);
    flatFareBreakdownDetailJPA.setEtt(1200L);
    Mockito.when(flatFareBreakdownJPARepository.findByTripId(Mockito.any()))
        .thenReturn(List.of(flatFareBreakdownDetailJPA));
    RouteInfo routeInfo = RouteInfo.builder().ett(1200L).build();
    Mockito.when(breakdownEntityMapper.mapFlatFareBreakdownDetailJPAToRouteInfo(Mockito.any()))
        .thenReturn(routeInfo);
    var actual = flatFareBreakDownRepository.getGeneratedRouteByTripId("trip-id").getEtt();
    assertEquals(1200L, actual);
  }

  @Test
  void
      givenFlatFareBreakdownDetailJPAIsNull_whenGetGeneratedRouteByTripId_thenThrowNotFoundException() {
    Mockito.when(flatFareBreakdownJPARepository.findByTripId(Mockito.any())).thenReturn(null);
    assertThrows(
        NotFoundException.class,
        () -> flatFareBreakDownRepository.getGeneratedRouteByTripId("trip-id"));
  }

  @Test
  void
      givenMapToRouteInfoHasError_whenGetGeneratedRouteByTripId_thenThrowInternalServerException() {
    FlatFareBreakdownDetailJPA flatFareBreakdownDetailJPA = new FlatFareBreakdownDetailJPA();
    flatFareBreakdownDetailJPA.setFlatFareBreakdownId(1);
    flatFareBreakdownDetailJPA.setEtt(1200L);
    Mockito.when(flatFareBreakdownJPARepository.findByTripId(Mockito.any()))
        .thenReturn(List.of(flatFareBreakdownDetailJPA));
    Mockito.when(breakdownEntityMapper.mapFlatFareBreakdownDetailJPAToRouteInfo(Mockito.any()))
        .thenThrow(
            new InternalServerException(
                ERROR_MAP_TO_ROUTE_INFO.getMessage(), ERROR_MAP_TO_ROUTE_INFO.getErrorCode()));
    assertThrows(
        InternalServerException.class,
        () -> flatFareBreakDownRepository.getGeneratedRouteByTripId("trip-id"));
  }

  @Test
  void givenRequestNull_whenSearchFareBreakdown_thenThrowBadRequestException() {
    assertThrows(
        BadRequestException.class,
        () -> flatFareBreakDownRepository.searchFareBreakdown(null, null, null));
  }

  @Test
  void givenRequestValid_whenSearchFareBreakdown_thenNotThrowException() {
    Mockito.when(
            flatFareBreakdownJPARepository.searchFareBreakdown("fareId", "tripId", "bookingId"))
        .thenReturn(List.of(fareBreakdownJPACustom));

    assertDoesNotThrow(
        () -> flatFareBreakDownRepository.searchFareBreakdown("fareId", "tripId", "bookingId"));
  }
}

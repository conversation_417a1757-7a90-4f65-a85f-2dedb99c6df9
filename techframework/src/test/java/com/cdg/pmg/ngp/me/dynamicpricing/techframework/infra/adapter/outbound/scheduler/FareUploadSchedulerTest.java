package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.scheduler;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.BookARideConfigService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.S2CellService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl.FareCalculationServiceImpl;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.ConfigurationServiceOutboundPort;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.BookARideConfigsConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.DynamicSurgesEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FareUploadConfiguration;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.LocationSurchargeConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.S2CellEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.InternalServerException;
import com.cdg.pmg.ngp.me.dynamicpricing.queries.S2CellListConfigQueryResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.TaxiFeed;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs.sftp.UploadGateway;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.service.TaxiFeedService;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * FareUploadSchedulerTest is designed to validate the functionality of the FareUploadScheduler
 * class. It utilizes MockitoExtension for mocking dependencies and setting up the testing
 * environment. This test class primarily focuses on the internal private methods of
 * FareUploadScheduler. They ensure that the FareUploadScheduler accurately processes and manages
 * fare-related data, which is critical for the dynamic pricing and scheduling aspects of the
 * ride-booking system. These tests are essential for maintaining the reliability and accuracy of
 * the fare upload scheduling process.
 */
@ExtendWith(MockitoExtension.class)
class FareUploadSchedulerTest {
  @Mock private FareUploadScheduler fareUploadScheduler;

  @Mock private BookARideConfigService bookARideConfigService;

  @Mock private S2CellService s2Service;

  @Mock private FareCalculationServiceImpl fareCalculationService;

  @Mock private TaxiFeedService taxiFeedService;

  @Mock private UploadGateway uploadGateway;
  @Mock private ConfigurationServiceOutboundPort configurationServiceOutboundPort;

  private Method loadS2CellListCacheMethod;

  private Method loadSurgeConfigurationMethod;

  private Method buildProductListMethod;

  private Method loadLocationSurChargeConfigurationMethod;

  private Method loadFlatFareConfigurationMethod;

  private S2CellEntity s2Cell;

  private S2CellListConfigQueryResponse s2CellListConfigQueryResponse;

  @BeforeEach
  void setUp() throws NoSuchMethodException {
    MockitoAnnotations.initMocks(this);
    fareUploadScheduler =
        new FareUploadScheduler(
            bookARideConfigService,
            s2Service,
            fareCalculationService,
            taxiFeedService,
            uploadGateway,
            configurationServiceOutboundPort);

    buildProductListMethod =
        FareUploadScheduler.class.getDeclaredMethod("buildProductList", Map.class);
    buildProductListMethod.setAccessible(true);

    loadSurgeConfigurationMethod =
        FareUploadScheduler.class.getDeclaredMethod("loadSurgeConfiguration", Map.class);
    loadSurgeConfigurationMethod.setAccessible(true);

    loadLocationSurChargeConfigurationMethod =
        FareUploadScheduler.class.getDeclaredMethod(
            "loadLocationSurChargeConfiguration", Map.class, List.class, Date.class);
    loadLocationSurChargeConfigurationMethod.setAccessible(true);

    loadFlatFareConfigurationMethod =
        FareUploadScheduler.class.getDeclaredMethod("loadFlatFareConfiguration", Map.class);
    loadFlatFareConfigurationMethod.setAccessible(true);

    loadS2CellListCacheMethod =
        FareUploadScheduler.class.getDeclaredMethod("loadS2CellListCache", List.class);
    loadS2CellListCacheMethod.setAccessible(true);

    s2Cell =
        S2CellEntity.builder()
            .s2CellId("s2CellId")
            .s2CellDesc("s2CellDesc")
            .s2CellLatitude(1.0)
            .s2CellLocationId("1")
            .s2CellTokenId("s2CellTokenId")
            .s2CellLevel(1)
            .s2CellSeqId(1L)
            .s2CellLocDesc("s2CellLocDesc")
            .s2CellZoneId("s2CellZoneId")
            .s2CellLongitude(1.0)
            .build();
    s2CellListConfigQueryResponse =
        S2CellListConfigQueryResponse.builder().s2CellList(List.of(s2Cell)).build();
  }

  @Test
  void givenBookRideConfig_whenBuildProductList_thenReturnProductList() throws Exception {
    // Given
    Map<String, String> bookRideConfig = new HashMap<>();
    bookRideConfig.put(BookARideConfigsConstant.BOOK_RIDE_VEHICLE_GROUP, "group1,group2");
    bookRideConfig.put(BookARideConfigsConstant.BOOK_RIDE_PRODUCT, "product1,product2");

    // When
    Map<String, List<String>> result =
        (Map<String, List<String>>)
            buildProductListMethod.invoke(fareUploadScheduler, bookRideConfig);

    // Then
    assertEquals(2, result.size());
    assertEquals(List.of("product1", "product2"), result.get("group1"));
    assertEquals(List.of("product1", "product2"), result.get("group2"));
  }

  @Test
  void givenHoliday_whenGetHolidayConfigs_thenReturnHolidayConfigs() throws Exception {
    // Given
    List<String> holidayConfigurations = new ArrayList<>();
    List<String> expectedHolidayConfigurations =
        new ArrayList<>(List.of("2019-10-10", "2018-10-10"));
    // When
    when(bookARideConfigService.getHolidayConfigs()).thenReturn(expectedHolidayConfigurations);
    fareUploadScheduler.loadHolidayConfiguration(holidayConfigurations);
    // Then
    assertEquals(expectedHolidayConfigurations.size(), holidayConfigurations.size());
  }

  @Test
  void givenHoliday_whenLoadFareUpload_thenReturnFareUploadConfig() throws Exception {
    // Given
    Date reqDate = new Date();
    reqDate.setYear(2023);
    reqDate.setMonth(10);
    reqDate.setDate(10);
    List<String> holidayConfigurations = List.of("2022-10-10", "2023-10-10");
    List<String> expectedHolidayConfigurations = List.of("2019-10-10", "2018-10-10");
    // When
    when(bookARideConfigService.getHolidayConfigs()).thenReturn(holidayConfigurations);
    when(s2Service.getS2CellList()).thenReturn(s2CellListConfigQueryResponse);
    FareUploadConfiguration result = fareUploadScheduler.loadFareUploadConfigurations(reqDate);

    // Then
    assertNotNull(result);
    assertEquals(expectedHolidayConfigurations.size(), holidayConfigurations.size());
  }

  @Test
  void givenMockMap_whenLoadSurgeConfigs_thenReturnSurgesConfigs() throws Exception {
    // Given
    DynamicSurgesEntity dynamicSurgesEntity = new DynamicSurgesEntity();
    dynamicSurgesEntity.setZoneId("zoneId-1");
    dynamicSurgesEntity.setSurge(1);
    Map<String, Integer> surgeConfigs = new HashMap<>();
    List<DynamicSurgesEntity> expectedSurgeConfigurations = new ArrayList<>();
    expectedSurgeConfigurations.add(dynamicSurgesEntity);
    // When
    when(bookARideConfigService.getDynpSurges()).thenReturn(expectedSurgeConfigurations);
    loadSurgeConfigurationMethod.invoke(fareUploadScheduler, surgeConfigs);
    // Then
    assertEquals(dynamicSurgesEntity.getSurge(), surgeConfigs.get("zoneId-1"));
  }

  @Test
  void givenSurgeConfigs_whenLoadFareUpload_thenReturnFareUploadConfig() throws Exception {
    // Given
    Date reqDate = new Date();
    reqDate.setYear(2023);
    reqDate.setMonth(10);
    reqDate.setDate(10);

    // Given
    DynamicSurgesEntity dynamicSurgesEntity = new DynamicSurgesEntity();
    dynamicSurgesEntity.setZoneId("zoneId-1");
    dynamicSurgesEntity.setSurge(1);
    List<DynamicSurgesEntity> expectedSurgeConfigurations = new ArrayList<>();
    expectedSurgeConfigurations.add(dynamicSurgesEntity);
    // When
    when(bookARideConfigService.getDynpSurges()).thenReturn(expectedSurgeConfigurations);
    when(s2Service.getS2CellList()).thenReturn(s2CellListConfigQueryResponse);

    FareUploadConfiguration result = fareUploadScheduler.loadFareUploadConfigurations(reqDate);

    // Then
    assertNotNull(result);
  }

  @Test
  void givenMockData_whenLoadLocationSurchargeConfig_thenReturnLocationSurchargeConfigs()
      throws Exception {
    // Given
    LocationSurchargeConfigEntity locationSurchargeConfigEntity =
        new LocationSurchargeConfigEntity();
    locationSurchargeConfigEntity.setLocationId(4);
    locationSurchargeConfigEntity.setLocationName("air port");
    locationSurchargeConfigEntity.setSurchargeValue(4.0);
    locationSurchargeConfigEntity.setChargeBy("PICKUP");
    locationSurchargeConfigEntity.setAddressRef("5394324");
    locationSurchargeConfigEntity.setDayIndicator("MON");
    locationSurchargeConfigEntity.setFareType("FLAT");
    locationSurchargeConfigEntity.setProductId("STD");
    locationSurchargeConfigEntity.setStartTime("00:00:00");
    locationSurchargeConfigEntity.setEndTime("23:59:59");
    Date currentDate = new Date(2023 - 1900, 11 - 1, 27);
    List<String> holidayList = Arrays.asList("27-11-2023", "01-05-2023", "25-12-2023");

    Map<String, List<LocationSurchargeConfigEntity>> locationSurChargeConfig = new HashMap<>();
    // When
    when(bookARideConfigService.getLocationSurchargeConfigs(currentDate, holidayList))
        .thenReturn(List.of(locationSurchargeConfigEntity));
    loadLocationSurChargeConfigurationMethod.invoke(
        fareUploadScheduler, locationSurChargeConfig, holidayList, currentDate);

    // Then
    assertNotNull(locationSurChargeConfig);
  }

  @Test
  void givenMockData_whenLoadFlatFareConfiguration_thenReturnFlatFareConfiguration()
      throws Exception {
    // Given
    Map<String, String> flatFareConfiguration = new HashMap<>();
    Map<String, String> expectedFlatFareConfig = new HashMap<>();
    expectedFlatFareConfig.put("key1", "value1");
    expectedFlatFareConfig.put("key2", "value2");
    // When
    when(bookARideConfigService.getFlatFareConfigs()).thenReturn(expectedFlatFareConfig);
    loadFlatFareConfigurationMethod.invoke(fareUploadScheduler, flatFareConfiguration);
    // Then
    assertEquals(expectedFlatFareConfig.size(), flatFareConfiguration.size());
    for (Map.Entry<String, String> entry : expectedFlatFareConfig.entrySet()) {
      assertTrue(flatFareConfiguration.containsKey(entry.getKey()));
      assertEquals(entry.getValue(), flatFareConfiguration.get(entry.getKey()));
    }
  }

  @Test
  void givenMockS2Cell_whenLoadS2CellListCache_thenReturnS2CellList()
      throws InvocationTargetException, IllegalAccessException {
    when(s2Service.getS2CellList()).thenReturn(s2CellListConfigQueryResponse);
    List<S2CellEntity> result = new ArrayList<>();
    loadS2CellListCacheMethod.invoke(fareUploadScheduler, result);
    assertEquals(1, result.size());
  }

  @Test
  void givenMockS2Cell_whenLoadFareUploadConfigurations_thenReturnFareUploadConfiguration() {
    when(s2Service.getS2CellList()).thenReturn(s2CellListConfigQueryResponse);
    assertNotNull(fareUploadScheduler.loadFareUploadConfigurations(new Date()));
  }

  @Test
  void givenData_whenLoadFareUpload_thenReturnMidnightRatesConfigNotNull() {
    // Given
    Date reqDate = new Date();
    reqDate.setYear(2023);
    reqDate.setMonth(10);
    reqDate.setDate(10);
    when(s2Service.getS2CellList()).thenReturn(s2CellListConfigQueryResponse);
    FareUploadConfiguration result = fareUploadScheduler.loadFareUploadConfigurations(reqDate);
    assertNotNull(result);
    assertNotNull(result.getMidNightRateConfig());
    assertNotNull(result.getMidNightRateConfig());
  }

  @Test
  void givenParams_whenLoadFareUploadConfigurations_thenReturnData() {
    Date reqDate = new Date();
    reqDate.setYear(2023);
    reqDate.setMonth(10);
    reqDate.setDate(10);
    when(s2Service.getS2CellList()).thenReturn(s2CellListConfigQueryResponse);
    Map<Integer, Integer> endpointSurchargeAreas = new HashMap<>();
    endpointSurchargeAreas.put(0, 1);
    endpointSurchargeAreas.put(1, 0);
    // Create a sample FareUploadConfiguration
    FareUploadConfiguration expectedConfig =
        FareUploadConfiguration.builder()
            .flatFareConfig(new HashMap<>())
            .dynamicPricingTimeConfig(new HashMap<>())
            .endPointSurchargeAreas(endpointSurchargeAreas)
            .s2CellList(s2Service.getS2CellList().getS2CellList())
            .build();
    FareUploadConfiguration result = fareUploadScheduler.loadFareUploadConfigurations(reqDate);
    // Assertions
    assertNotNull(result);
    assertEquals(expectedConfig, result);
  }

  @Test
  void givenMockTaxiFeed_whenBuildTaxiFeed_thenReturnNoExceptions() {
    when(taxiFeedService.buildTaxiFeed(any(), any(), any()))
        .thenReturn(TaxiFeed.newBuilder().build());
    Map<String, String> bookRideConfig = new HashMap<>();
    bookRideConfig.put(BookARideConfigsConstant.BOOK_RIDE_VEHICLE_GROUP, "0");
    bookRideConfig.put(BookARideConfigsConstant.BOOK_RIDE_PRODUCT, "COMFORT");
    bookRideConfig.put(BookARideConfigsConstant.FILE_NAME, "comfortdelgro");
    when(s2Service.getS2CellList()).thenReturn(s2CellListConfigQueryResponse);
    when(configurationServiceOutboundPort.loadBookARideConfigurations()).thenReturn(bookRideConfig);
    FareUploadScheduler scheduler =
        new FareUploadScheduler(
            bookARideConfigService,
            s2Service,
            fareCalculationService,
            taxiFeedService,
            uploadGateway,
            configurationServiceOutboundPort);
    assertDoesNotThrow(scheduler::uploadFile);
  }

  @Test
  void givenError_whenUploadFile_thenThrowInternalServerException() {
    // Given
    Map<String, String> bookRideConfig = new HashMap<>();
    bookRideConfig.put(BookARideConfigsConstant.BOOK_RIDE_VEHICLE_GROUP, "0");
    bookRideConfig.put(BookARideConfigsConstant.BOOK_RIDE_PRODUCT, "COMFORT");
    bookRideConfig.put(BookARideConfigsConstant.FILE_NAME, "comfortdelgro");
    when(s2Service.getS2CellList()).thenReturn(s2CellListConfigQueryResponse);
    when(configurationServiceOutboundPort.loadBookARideConfigurations()).thenReturn(bookRideConfig);
    when(taxiFeedService.buildTaxiFeed(any(), any(), any()))
        .thenReturn(TaxiFeed.newBuilder().build());
    doThrow(new RuntimeException("Error in uploading file")).when(uploadGateway).upload(any());

    // When & Then
    InternalServerException exception =
        assertThrows(InternalServerException.class, () -> fareUploadScheduler.uploadFile());
    assertEquals("Error in uploading Book-A-Ride file to FTP", exception.getMessage());
  }

  @Test
  void givenMockTaxiFeed_whenBuildTaxiFeed_thenReturnExceptions() {
    when(taxiFeedService.buildTaxiFeed(any(), any(), any())).thenReturn(null);
    Map<String, String> bookRideConfig = new HashMap<>();
    bookRideConfig.put(BookARideConfigsConstant.BOOK_RIDE_VEHICLE_GROUP, "0");
    bookRideConfig.put(BookARideConfigsConstant.BOOK_RIDE_PRODUCT, "COMFORT");
    when(s2Service.getS2CellList()).thenReturn(s2CellListConfigQueryResponse);
    when(configurationServiceOutboundPort.loadBookARideConfigurations()).thenReturn(bookRideConfig);
    FareUploadScheduler scheduler =
        new FareUploadScheduler(
            bookARideConfigService,
            s2Service,
            fareCalculationService,
            taxiFeedService,
            uploadGateway,
            configurationServiceOutboundPort);
    // Act & Assert
    assertThrows(NullPointerException.class, () -> scheduler.uploadFile());
  }
}

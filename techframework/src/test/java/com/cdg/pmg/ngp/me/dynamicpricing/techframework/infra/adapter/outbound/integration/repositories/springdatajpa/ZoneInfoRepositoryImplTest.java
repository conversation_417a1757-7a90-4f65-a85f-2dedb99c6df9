package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa;

import static org.mockito.Mockito.when;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.ZoneInfoJPARepository;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ZoneInfoRepositoryImplTest {

  @InjectMocks private ZoneInfoRepositoryImpl zoneInfoRepositoryOutboundPort;

  @Mock private ZoneInfoJPARepository zoneInfoJPARepository;

  @Test
  void givenZoneID_whenCheckZoneIdExists_thenExpectTrue() {
    // Arrange
    String expectedZoneId = "01";
    when(zoneInfoJPARepository.checkZoneIdExists(expectedZoneId)).thenReturn(true);

    // Act
    boolean zoneIdExists = zoneInfoRepositoryOutboundPort.checkZoneIdExists(expectedZoneId);

    // Assert
    Assertions.assertTrue(zoneIdExists);
  }

  @Test
  void testLoadAllZonesId() {
    // Arrange
    List<String> expectedResult = Collections.singletonList("01");
    when(zoneInfoJPARepository.findAllZoneIds()).thenReturn(expectedResult);

    // Act
    List<String> allZoneIds = zoneInfoRepositoryOutboundPort.findAllZones();

    // Assert
    Assertions.assertEquals(expectedResult, allZoneIds);
  }
}

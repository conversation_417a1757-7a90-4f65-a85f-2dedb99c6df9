package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.DateUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.CompanyHolidayRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.CompanyHolidayJPARepository;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CompanyHolidayRepositoryImplTest {
  @Mock private CompanyHolidayRepository companyHolidayRepository;

  @Mock private CompanyHolidayJPARepository companyHolidayJPARepository;

  private DateFormat df;

  @BeforeEach
  void setUp() {
    companyHolidayRepository = new CompanyHolidayRepositoryImpl(companyHolidayJPARepository);
    df = new SimpleDateFormat(DateUtils.YYYYMMDD_HHMMSS_FORMAT);
  }

  @Test
  void getCompanyHolidays() {
    final List<String> expected = new ArrayList<>();
    when(companyHolidayJPARepository.getCompanyHolidays()).thenReturn(expected);

    final List<String> actual = companyHolidayRepository.getCompanyHolidays();
    assertEquals(expected, actual);
  }

  @Test
  void getCurrentHoliday() {
    final String expected = df.format(new Date());
    when(companyHolidayJPARepository.getCurrentHoliday()).thenReturn(expected);

    final String actual = companyHolidayRepository.getCurrentHoliday();
    assertEquals(expected, actual);
  }
}

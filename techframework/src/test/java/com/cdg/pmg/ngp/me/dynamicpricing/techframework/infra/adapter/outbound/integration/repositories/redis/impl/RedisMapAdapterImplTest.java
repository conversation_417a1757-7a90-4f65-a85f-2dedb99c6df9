package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.redis.impl;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import java.time.Duration;
import java.time.Instant;
import java.util.Date;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.support.collections.RedisMap;

@ExtendWith(MockitoExtension.class)
class RedisMapAdapterImplTest {
  @Mock private RedisMapAdapterImpl redisMapAdapter;
  @Mock private RedisMap<String, String> redisMap;

  @BeforeEach
  public void setup() {
    redisMapAdapter = new RedisMapAdapterImpl(redisMap);
  }

  @Test
  void givenNothing_whenGetMap_thenSuccess() {
    // WHEN
    ConcurrentMap<String, String> actual = redisMapAdapter.getMap();

    // THEN
    assertNotNull(actual);
  }

  @Test
  void givenDuration_whenExpire_thenReturnFalse() {
    // WHEN
    boolean actual = redisMapAdapter.expire(Duration.ZERO);

    // THEN
    assertFalse(actual);
  }

  @Test
  void givenNull_whenExpire_thenReturnFalse() {
    // WHEN
    boolean actual = redisMapAdapter.expire(null);

    // THEN
    assertFalse(actual);
  }

  @Test
  void givenInstant_whenExpireAt_thenReturnFalse() {
    // WHEN
    boolean actual = redisMapAdapter.expireAt(Instant.now());

    // THEN
    assertFalse(actual);
  }

  @Test
  void givenDate_whenExpireAt_thenReturnFalse() {
    // WHEN
    boolean actual = redisMapAdapter.expireAt(new Date());

    // THEN
    assertFalse(actual);
  }

  @Test
  void givenTimeOutAndUnit_whenExpire_thenReturnFalse() {
    // WHEN
    boolean actual = redisMapAdapter.expire(1000L, TimeUnit.SECONDS);

    // THEN
    assertFalse(actual);
  }
}

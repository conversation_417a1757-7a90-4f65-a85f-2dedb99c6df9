package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.CBDAddressConfigJPARepository;
import java.util.ArrayList;
import java.util.HashSet;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CBDAddressConfigRepositoryImplTest {

  @InjectMocks private CBDAddressConfigRepositoryImpl cbdAddressConfigRepository;
  @Mock private CBDAddressConfigJPARepository cbdAddressConfigJPARepository;

  @Test
  void givenNormalCondition_whenInsertCBDAddress_thenSuccess() {
    cbdAddressConfigRepository.insertCBDAddress(new HashSet<>());
    Mockito.verify(cbdAddressConfigJPARepository, Mockito.times(1))
        .insertBatchOnConflict(Mockito.any());
  }

  @Test
  void givenNormalCondition_whenDeleteCBDAddress_thenSuccess() {
    cbdAddressConfigRepository.deleteCBDAddress(new HashSet<>());
    Mockito.verify(cbdAddressConfigJPARepository, Mockito.times(1))
        .deleteBatchByAddressRef(Mockito.any());
  }

  @Test
  void givenNormalCondition_whenGetLocConfigByAddressRefList_thenSuccess() {
    cbdAddressConfigRepository.getLocConfigByAddressRefList(new ArrayList<>());
    Mockito.verify(cbdAddressConfigJPARepository, Mockito.times(1))
        .getLocConfigByAddressRefList(Mockito.any());
  }
}

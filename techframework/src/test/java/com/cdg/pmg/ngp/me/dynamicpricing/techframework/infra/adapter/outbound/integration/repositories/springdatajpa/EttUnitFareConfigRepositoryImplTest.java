package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.EttUnitFareConfigRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.EttUnitFareConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.queries.EttUnitFareConfigQueryResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.custom.EttUnitFareJPACustom;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.custom.LocationSurchargeJPACustom;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.EttUnitFareJPARepository;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class EttUnitFareConfigRepositoryImplTest {
  @Mock EttUnitFareConfigRepository ettUnitFareConfigRepository;
  @Mock EttUnitFareJPARepository ettUnitFareJPARepository;
  @Mock EttUnitFareJPACustom ettUnitFareJPACustom;

  @BeforeEach
  void init() {
    ettUnitFareConfigRepository = new EttUnitFareConfigRepositoryImpl(ettUnitFareJPARepository);
  }

  @Test
  void givenEmptyEtt_whenGetEttUnitFareConfigs_thenReturnEmpty() {
    final List<LocationSurchargeJPACustom> resultQuery = new ArrayList<>();
    Mockito.when(ettUnitFareJPARepository.getEttUnitFareConfigs()).then(config -> resultQuery);
    final EttUnitFareConfigQueryResponse actual =
        ettUnitFareConfigRepository.getEttUnitFareConfigs();
    final List<EttUnitFareConfig> expected = new ArrayList<>();
    Assertions.assertEquals(expected, actual.getConfigs());
  }

  @Test
  void givenListEtt_whenGetEttUnitFareConfigs_thenReturnListEtt() {
    final List<EttUnitFareJPACustom> resultQuery = List.of(ettUnitFareJPACustom);
    Mockito.when(ettUnitFareJPARepository.getEttUnitFareConfigs()).then(config -> resultQuery);
    final EttUnitFareConfigQueryResponse actual =
        ettUnitFareConfigRepository.getEttUnitFareConfigs();

    final EttUnitFareJPACustom conf = resultQuery.get(0);
    final boolean expected =
        actual.getConfigs().stream()
            .allMatch(
                actualConf ->
                    Objects.equals(actualConf.getZoneIds(), conf.getZoneIds())
                        && Objects.equals(actualConf.getDayOfWeek(), conf.getDayOfWeek())
                        && Objects.equals(actualConf.getEkm(), conf.getEkm())
                        && Objects.equals(actualConf.getHr(), conf.getHr())
                        && Objects.equals(actualConf.getChargeBy(), conf.getChargeBy())
                        && Objects.equals(actualConf.getTaxiType(), conf.getTaxiType())
                        && Objects.equals(actualConf.getRate(), conf.getRate()));
    Assertions.assertTrue(expected);
  }
}

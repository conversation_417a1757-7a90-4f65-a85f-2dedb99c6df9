/*
package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.controllers;


@ExtendWith(MockitoExtension.class)
class S2CellControllerTest {
  @Mock private S2CellController s2CellController;

  @Mock private S2CellService s2CellService;

  @BeforeEach
  void setUp() {
    s2CellController = new S2CellController(s2CellService);
  }

  @Test
  void givenCall_whenFetchCache_thenReturnOK() {
    // Mock the behavior of s2CellService.fetchCacheS2Cell()
    when(s2CellService.fetchCacheS2Cell()).thenReturn(RedisKeyConstant.CACHE_CLEAR_SUCCESS_MESSAGE);

    // Act
    ResponseEntity<String> responseEntity = s2CellController.fetchCache();

    // Assert
    assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
    assertEquals(RedisKeyConstant.CACHE_CLEAR_SUCCESS_MESSAGE, responseEntity.getBody());
  }
}
*/

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.PricingRangeRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.queries.PricingRangeCalDemandSurgeQueryResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.custom.PricingRangeCalDemandSurgeJPACustom;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.PricingRangeJPARepository;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class PricingRangeRepositoryImplTest {
  @Mock PricingRangeRepository pricingRangeRepository;

  @Mock PricingRangeJPARepository pricingRangeJPARepository;

  @Mock private PricingRangeCalDemandSurgeJPACustom pricingRangeCalDemandSurgeJPACustom;

  @BeforeEach
  void setUp() {
    pricingRangeRepository = new PricingRangeRepositoryImpl(pricingRangeJPARepository);
  }

  @Test
  void givenEmptyConfig_whenGetDynpConfigForDemandSurge_thenReturnEmpty() {
    final List<PricingRangeCalDemandSurgeJPACustom> resultQuery = new ArrayList<>();
    when(pricingRangeJPARepository.getPricingRangeConfigs(Mockito.anyBoolean()))
        .thenReturn(resultQuery);
    final List<PricingRangeCalDemandSurgeQueryResponse> actual =
        pricingRangeRepository.getDynpConfigForDemandSurge(Mockito.anyBoolean());
    final List<PricingRangeCalDemandSurgeQueryResponse> expected = new ArrayList<>();
    assertEquals(expected, actual);
  }

  @Test
  void givenListConfigs_whenGetDynpConfigForDemandSurge_thenReturnList() {
    final List<PricingRangeCalDemandSurgeJPACustom> resultQuery =
        List.of(pricingRangeCalDemandSurgeJPACustom);
    when(pricingRangeJPARepository.getPricingRangeConfigs(Mockito.anyBoolean()))
        .thenReturn(resultQuery);
    final List<PricingRangeCalDemandSurgeQueryResponse> actual =
        pricingRangeRepository.getDynpConfigForDemandSurge(Mockito.anyBoolean());

    final PricingRangeCalDemandSurgeJPACustom conf = resultQuery.get(0);
    final boolean expected =
        actual.stream()
            .allMatch(
                actualConf ->
                    Objects.equals(actualConf.getSurgeLow(), conf.getSurgeLow())
                        && Objects.equals(actualConf.getSurgeHigh(), conf.getSurgeHigh())
                        && Objects.equals(actualConf.getStepPositive(), conf.getStepPositive())
                        && Objects.equals(actualConf.getStepNegative(), conf.getStepNegative())
                        && Objects.equals(actualConf.getZoneId(), conf.getZoneId()));
    Assertions.assertTrue(expected);
  }

  @Test
  void whenGetDynpConfigForDemandSurgeV2_thenReturnMappedResponses() {
    // Arrange
    boolean isHoliday = true;

    List<PricingRangeCalDemandSurgeJPACustom> expectedConfigs = new ArrayList<>();
    PricingRangeCalDemandSurgeJPACustom config1 =
        new PricingRangeCalDemandSurgeJPACustom() {
          @Override
          public Integer getSurgeLow() {
            return 1;
          }

          @Override
          public Integer getSurgeHigh() {
            return 1;
          }

          @Override
          public Integer getStepPositive() {
            return 1;
          }

          @Override
          public Integer getStepNegative() {
            return 1;
          }

          @Override
          public String getZoneId() {
            return "1";
          }

          @Override
          public String getDay() {
            return "1";
          }

          @Override
          public String getHour() {
            return "1";
          }
        };
    expectedConfigs.add(config1);

    when(pricingRangeJPARepository.getPricingRangeConfigsV2(isHoliday)).thenReturn(expectedConfigs);

    // Act
    List<PricingRangeCalDemandSurgeQueryResponse> responses =
        pricingRangeRepository.getDynpConfigForDemandSurgeV2(isHoliday);

    // Assert
    assertEquals(1, responses.size());
    assertEquals(config1.getZoneId(), responses.get(0).getZoneId());
  }

  @Test
  void whenDynpConfigForDemandSurgeV2ConfigsAreEmpty_thenReturnEmptyList() {
    // Arrange
    boolean isHoliday = true;

    when(pricingRangeJPARepository.getPricingRangeConfigsV2(isHoliday))
        .thenReturn(new ArrayList<>());

    // Act
    List<PricingRangeCalDemandSurgeQueryResponse> responses =
        pricingRangeRepository.getDynpConfigForDemandSurgeV2(isHoliday);

    // Assert
    assertEquals(0, responses.size());
  }
}

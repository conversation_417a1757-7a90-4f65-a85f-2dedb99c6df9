package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.DynamicSurgesEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.DynamicSurgeJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.DynamicSurgeNgpJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.DynamicSurgeJPARepository;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.DynamicSurgeNgpJPARepository;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.mapper.DynamicSurgeMapper;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mapstruct.factory.Mappers;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class DynamicSurgeRepositoryImplTest {
  @Mock private DynamicSurgeRepositoryImpl dynamicSurgeRepository;
  @Mock private DynamicSurgeJPARepository dynamicSurgeJPARepository;
  @Mock private DynamicSurgeNgpJPARepository dynamicSurgeNgpJPARepository;
  @Mock private EntityManager entityManager;
  private final DynamicSurgeMapper dynamicSurgeMapper = Mappers.getMapper(DynamicSurgeMapper.class);

  @BeforeEach
  public void setup() {
    dynamicSurgeRepository =
        new DynamicSurgeRepositoryImpl(
            dynamicSurgeJPARepository,
            dynamicSurgeNgpJPARepository,
            dynamicSurgeMapper,
            entityManager);
  }

  @Test
  void givenNothing_whenGetDynpSurges_then_returnNotEmptyArrayList() {
    // WHEN
    when(dynamicSurgeJPARepository.getDynpSurges())
        .thenReturn(
            List.of(
                DynamicSurgeJPA.builder()
                    .surge(10)
                    .surgeLow(10)
                    .surgeHigh(100)
                    .demandRecent(1)
                    .demandPrevious(1)
                    .demandPredicted(1)
                    .supply(0)
                    .prevSurge(0)
                    .batchKey(1)
                    .build()));
    List<DynamicSurgesEntity> actual = dynamicSurgeRepository.getDynpSurges();

    // THEN
    assertFalse(actual.isEmpty());
  }

  @Test
  void givenNothing_whenGetDynpSurges_then_returnEmptyArrayList() {
    // WHEN
    when(dynamicSurgeJPARepository.getDynpSurges()).thenReturn(List.of());
    List<DynamicSurgesEntity> actual = dynamicSurgeRepository.getDynpSurges();

    // THEN
    assertTrue(actual.isEmpty());
  }

  @Test
  void givenListEntities_whenUpdateDynpSurges_thenSaveAll() {
    // WHEN
    dynamicSurgeRepository.updateDynpSurges(List.of(DynamicSurgesEntity.builder().build()));

    // THEN
    Mockito.verify(dynamicSurgeNgpJPARepository, Mockito.times(1)).saveAll(Mockito.anyList());
  }

  @Test
  void whenGetNGPDynpSurges_thenListDynamicSurgesEntity() {
    // WHEN
    when(dynamicSurgeNgpJPARepository.getDynpSurges())
        .thenReturn(
            List.of(
                DynamicSurgeNgpJPA.builder()
                    .surge(10)
                    .surgeLow(10)
                    .surgeHigh(100)
                    .demandRecent(1)
                    .demandPrevious(1)
                    .demandPredicted(1)
                    .demand15(1)
                    .demandPredicted15(1)
                    .unmet15(1.0)
                    .previousUnmet15(1.0)
                    .supply(0)
                    .prevSurge(0)
                    .build()));
    List<DynamicSurgesEntity> actual = dynamicSurgeRepository.getNGPDynpSurges();

    // THEN
    assertFalse(actual.isEmpty());
  }

  @Test
  void whenGetNGPDynpSurges_thenReturnEmptyList() {
    // WHEN
    when(dynamicSurgeNgpJPARepository.getDynpSurges()).thenReturn(Collections.emptyList());
    List<DynamicSurgesEntity> actual = dynamicSurgeRepository.getNGPDynpSurges();

    // THEN
    assertTrue(actual.isEmpty());
  }

  @Test
  void whenRemoveInvalidDynSurges_thenNotThrowException() {
    // WHEN
    doNothing().when(dynamicSurgeNgpJPARepository).removeInvalidDynSurges();
    dynamicSurgeRepository.removeInvalidDynSurges();

    // THEN
    verify(dynamicSurgeNgpJPARepository, times(1)).removeInvalidDynSurges();
  }

  @Test
  void whenUpdateDynpSurgesV2_shouldSuccess() {
    // Arrange
    var entities = buildSurges();
    Query mock = mock(Query.class);
    when(entityManager.createNativeQuery(anyString())).thenReturn(mock);

    // Act
    dynamicSurgeRepository.updateDynpSurgesV2(entities);

    // Assert
    verify(entityManager).createNativeQuery(anyString());
    verify(mock).executeUpdate();
  }

  @Test
  void whenUpdateDynpSurgesV2WithEmptyEntities_shouldSuccess() {
    // Arrange
    List<DynamicSurgesEntity> entities = List.of();

    // Act
    dynamicSurgeRepository.updateDynpSurgesV2(entities);

    // Assert
    verify(entityManager, times(0)).createNativeQuery(anyString());
  }

  private List<DynamicSurgesEntity> buildSurges() {
    DynamicSurgesEntity surge = new DynamicSurgesEntity();

    surge.setZoneId("56");
    surge.setSurge(1);
    surge.setSurgeLow(2);
    surge.setSurgeHigh(3);
    surge.setDemandRecent(4);
    surge.setDemandPrevious(5);
    surge.setDemandPredicted(6);
    surge.setSupply(7);
    surge.setExcessDemand(8);
    surge.setLastUpdDt(
        Timestamp.from(LocalDateTime.of(2024, 8, 21, 1, 2).toInstant(ZoneOffset.UTC)));
    surge.setPrevSurge(9);
    surge.setBatchKey(10);
    surge.setZonePriceModel("V11");
    surge.setDemandPredicted(12);
    surge.setExcessDemand15(13);
    surge.setUnmet15(14);
    surge.setPreviousUnmet15(15);
    surge.setDemand15(16);
    surge.setPredictedDemand15(17);
    DynamicSurgesEntity surge2 = new DynamicSurgesEntity();

    surge2.setZoneId("58");
    surge2.setSurge(1);
    surge2.setSurgeLow(2);
    surge2.setSurgeHigh(3);
    surge2.setDemandRecent(4);
    surge2.setDemandPrevious(5);
    surge2.setDemandPredicted(6);
    surge2.setSupply(7);
    surge2.setExcessDemand(8);
    surge2.setLastUpdDt(
        Timestamp.from(LocalDateTime.of(2024, 8, 21, 1, 2).toInstant(ZoneOffset.UTC)));
    surge2.setPrevSurge(9);
    surge2.setBatchKey(10);
    surge2.setZonePriceModel("V11");
    surge2.setDemandPredicted(12);
    surge2.setExcessDemand15(13);
    surge2.setUnmet15(14);
    surge2.setPreviousUnmet15(15);
    surge2.setDemand15(16);
    surge2.setPredictedDemand15(17);
    return List.of(surge, surge2);
  }
}

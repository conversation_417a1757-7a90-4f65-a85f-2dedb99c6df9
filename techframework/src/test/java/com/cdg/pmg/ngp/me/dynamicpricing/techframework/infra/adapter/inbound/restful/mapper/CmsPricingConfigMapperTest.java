package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.NewPricingModelConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.NewPricingModelConfigListResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.NewPricingModelConfigResponse;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CmsPricingConfigMapperTest {

  @Spy CmsPricingConfigMapper cmsPricingConfigMapper;

  @Test
  void toNewPricingModelItemResponse() {
    NewPricingModelConfigEntity pricingModelConfigEntity = new NewPricingModelConfigEntity();
    pricingModelConfigEntity.setZoneId("01");
    NewPricingModelConfigResponse newPricingModelConfigResponse =
        new NewPricingModelConfigResponse();
    newPricingModelConfigResponse.setZoneId("01");
    doReturn(newPricingModelConfigResponse)
        .when(cmsPricingConfigMapper)
        .toNewPricingModelResponse(any());

    NewPricingModelConfigListResponse result =
        cmsPricingConfigMapper.toNewPricingModelItemResponse(List.of(pricingModelConfigEntity));
    assertEquals(List.of(newPricingModelConfigResponse), result.getItems());
  }
}

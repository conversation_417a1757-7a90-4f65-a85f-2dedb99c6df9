package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.adapter;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.BookingFeeItem;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.BookingFeeListRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.BookingFeeListResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.BookingFeeRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.BookingFeeResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.PlatformFeeIdentify;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.PlatformFeeListRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.PlatformFeeRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.PlatformFeeResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.FareService;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.InternalServerException;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos.*;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.mapper.FareOutboundMapper;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
class FareServiceAdapterTest {
  @Mock private FareService fareService;
  @Mock private FareOutboundAdapter fareOutboundAdapter;
  @Mock private FareOutboundMapper fareOutboundMapper;

  private PlatformFeeOutboundRequest platformFeeOutboundRequest;
  private ResponseEntity<PlatformFeeOutboundResponse> platformFeeResponse;
  private ResponseEntity<PlatformFeeListOutboundResponse> platformFeeListResponse;
  private List<PlatformFeeResponse> platformFee;
  private PlatformFeeRequest platformFeeRequest;

  private PlatformFeeListRequest platformFeeListRequest;
  private PlatformFeeListOutboundRequest platformFeeListOutboundRequest;

  @BeforeEach
  void initServiceAndMockData() {
    fareService = new FareServiceAdapter(fareOutboundAdapter, fareOutboundMapper);

    platformFeeOutboundRequest =
        PlatformFeeOutboundRequest.builder()
            .bookingChannel("channel")
            .productId("pdt01")
            .vehicleGroupId(String.valueOf(1))
            .build();

    PlatformFeeOutbound platformFeeOutbound = PlatformFeeOutbound.builder().id(1L).build();
    PlatformFeeOutboundResponse platformFeeOutboundResponse =
        PlatformFeeOutboundResponse.builder().data(List.of(platformFeeOutbound)).build();
    platformFeeResponse =
        new ResponseEntity<>(platformFeeOutboundResponse, HttpStatusCode.valueOf(200));

    PlatformFeeListOutboundResponse platformFeeListOutboundResponse =
        PlatformFeeListOutboundResponse.builder()
            .platformFeeList(List.of(platformFeeOutbound))
            .build();
    platformFeeListResponse =
        new ResponseEntity<>(platformFeeListOutboundResponse, HttpStatusCode.valueOf(200));

    platformFee = List.of(PlatformFeeResponse.builder().id(1L).build());

    platformFeeRequest =
        PlatformFeeRequest.builder()
            .bookingChannel("channel")
            .productId("pdt01")
            .vehicleTypeId(String.valueOf(1))
            .build();

    platformFeeListRequest =
        PlatformFeeListRequest.builder()
            .bookingChannel("channel")
            .platformFeeRequestList(
                List.of(
                    PlatformFeeIdentify.builder().productId("pdt01").vehicleGroupId("1").build()))
            .build();
    platformFeeListOutboundRequest =
        PlatformFeeListOutboundRequest.builder()
            .bookingChannel("channel")
            .platformFeeRequestList(
                List.of(
                    PlatformFeeIdentifyOutbound.builder()
                        .productId("pdt01")
                        .vehicleGroupId("1")
                        .build()))
            .build();
  }

  @Test
  void givenValidInput_whenGetPlatformFee_thenReturnSuccessfully() {
    when(fareOutboundAdapter.getPlatformFee(
            Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
        .thenReturn(platformFeeResponse);

    when(fareOutboundMapper.mapToPlatformFeeResponse(Mockito.any())).thenReturn(platformFee);

    var actual = fareService.getPlatformFee(platformFeeRequest).get(0).getId();
    assertEquals(1, actual);
  }

  @Test
  void givenPlatformFeeResponseIsNull_whenGetPlatformFee_thenReturnEmpty() {
    ResponseEntity<PlatformFeeOutboundResponse> response =
        new ResponseEntity<>(null, HttpStatusCode.valueOf(200));

    when(fareOutboundAdapter.getPlatformFee(
            Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
        .thenReturn(response);

    assertDoesNotThrow(() -> fareService.getPlatformFee(platformFeeRequest));
  }

  @Test
  void givenDataIsNull_whenGetPlatformFee_thenEmpty() {
    ResponseEntity<PlatformFeeOutboundResponse> response =
        new ResponseEntity<>(null, HttpStatusCode.valueOf(200));
    when(fareOutboundAdapter.getPlatformFee(
            Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
        .thenReturn(response);

    assertDoesNotThrow(() -> fareService.getPlatformFee(platformFeeRequest));
  }

  @Test
  void givenValidInput_whenGetPlatformFeeByList_thenReturnSuccessfully() {
    when(fareOutboundMapper.mapToPlatformFeeListOutboundRequest(Mockito.any()))
        .thenReturn(platformFeeListOutboundRequest);
    when(fareOutboundAdapter.getPlatformFeeByList(Mockito.any()))
        .thenReturn(platformFeeListResponse);
    when(fareOutboundMapper.mapToPlatformFeeResponse(Mockito.any())).thenReturn(platformFee);

    Long actual = fareService.getPlatformFeeByList(platformFeeListRequest).get(0).getId();
    assertEquals(1, actual);
  }

  @Test
  void givenPlatformFeeResponseIsNull_whenGetPlatformFeeByList_thenReturnEmpty() {
    ResponseEntity<PlatformFeeListOutboundResponse> response =
        new ResponseEntity<>(null, HttpStatusCode.valueOf(200));
    when(fareOutboundAdapter.getPlatformFeeByList(Mockito.any())).thenReturn(response);
    assertDoesNotThrow(() -> fareService.getPlatformFeeByList(platformFeeListRequest));
  }

  @Test
  void givenBookingFee_whenCallAPISuccess_thenReturnResponse() {
    // Arrange
    BookingFeeRequest bookingFeeRequest = BookingFeeRequest.builder().build();
    BookingFeeOutboundRequest bookingFeeOutBoundRequest =
        BookingFeeOutboundRequest.builder().build();
    BookingFeeOutboundResponse bookingFeeOutBoundResponse = new BookingFeeOutboundResponse();
    ResponseEntity<BookingFeeOutboundResponse> response =
        new ResponseEntity<>(bookingFeeOutBoundResponse, HttpStatusCode.valueOf(200));

    BookingFeeResponse expectedResponse = new BookingFeeResponse();

    when(fareOutboundMapper.mapToBookingFeeOutBoundRequest(bookingFeeRequest))
        .thenReturn(bookingFeeOutBoundRequest);
    when(fareOutboundAdapter.getBookingFee(bookingFeeOutBoundRequest)).thenReturn(response);
    when(fareOutboundMapper.mapToBookingFeeResponse(bookingFeeOutBoundResponse))
        .thenReturn(expectedResponse);

    // Act
    BookingFeeResponse result = fareService.getBookingFee(bookingFeeRequest);

    // Assert
    assertNotNull(result);
    assertEquals(expectedResponse, result);
  }

  @Test
  void givenBookingFee_whenGetBookingFee_thenThrowInternalServerException() {
    // Arrange
    BookingFeeRequest bookingFeeRequest = BookingFeeRequest.builder().build();
    BookingFeeOutboundRequest bookingFeeOutBoundRequest =
        BookingFeeOutboundRequest.builder().build();

    when(fareOutboundMapper.mapToBookingFeeOutBoundRequest(bookingFeeRequest))
        .thenReturn(bookingFeeOutBoundRequest);
    when(fareOutboundAdapter.getBookingFee(bookingFeeOutBoundRequest))
        .thenThrow(new RuntimeException("Test exception"));

    // Act & Assert
    assertThrows(InternalServerException.class, () -> fareService.getBookingFee(bookingFeeRequest));
  }

  @Test
  void givenNormalCondition_whenGetBookingFeeByList_thenReturnSuccess() {
    // Arrange
    List<BookingFeeItemOutbound> bookingFeeItemOutboundList =
        List.of(
            BookingFeeItemOutbound.builder().vehicleTypeId(1).build(),
            BookingFeeItemOutbound.builder().vehicleTypeId(2).build());
    List<BookingFeeItem> bookingFeeItemList =
        List.of(
            BookingFeeItem.builder().vehicleTypeId(1).build(),
            BookingFeeItem.builder().vehicleTypeId(2).build());
    BookingFeeListRequest bookingFeeRequest =
        BookingFeeListRequest.builder().bookingFeeRequestList(bookingFeeItemList).build();

    BookingFeeListOutboundRequest bookingFeeOutBoundRequest =
        BookingFeeListOutboundRequest.builder()
            .bookingFeeRequestList(bookingFeeItemOutboundList)
            .build();

    BookingFeeListOutboundResponse bookingFeeListOutboundResponse =
        BookingFeeListOutboundResponse.builder().build();
    ResponseEntity<BookingFeeListOutboundResponse> response =
        new ResponseEntity<>(bookingFeeListOutboundResponse, HttpStatusCode.valueOf(200));

    BookingFeeListResponse expectedResponse = new BookingFeeListResponse();

    when(fareOutboundMapper.mapToBookingFeeListOutboundRequest(bookingFeeRequest))
        .thenReturn(bookingFeeOutBoundRequest);
    when(fareOutboundAdapter.getBookingFeeByList(bookingFeeOutBoundRequest)).thenReturn(response);
    when(fareOutboundMapper.mapToBookingFeeListResponse(bookingFeeListOutboundResponse))
        .thenReturn(expectedResponse);

    // Act
    BookingFeeListResponse result = fareService.getBookingFeeByList(bookingFeeRequest);

    // Assert
    assertNotNull(result);
    assertEquals(expectedResponse, result);
  }

  @Test
  void givenError_whenGetBookingFeeByList_thenThrowInternalServerException() {
    // Arrange
    List<BookingFeeItemOutbound> bookingFeeItemOutboundList =
        List.of(
            BookingFeeItemOutbound.builder().vehicleTypeId(1).build(),
            BookingFeeItemOutbound.builder().vehicleTypeId(2).build());
    List<BookingFeeItem> bookingFeeItemList =
        List.of(
            BookingFeeItem.builder().vehicleTypeId(1).build(),
            BookingFeeItem.builder().vehicleTypeId(2).build());
    BookingFeeListRequest bookingFeeRequest =
        BookingFeeListRequest.builder().bookingFeeRequestList(bookingFeeItemList).build();
    BookingFeeListOutboundRequest bookingFeeOutBoundRequest =
        BookingFeeListOutboundRequest.builder()
            .bookingFeeRequestList(bookingFeeItemOutboundList)
            .build();

    when(fareOutboundMapper.mapToBookingFeeListOutboundRequest(bookingFeeRequest))
        .thenReturn(bookingFeeOutBoundRequest);
    when(fareOutboundAdapter.getBookingFeeByList(bookingFeeOutBoundRequest))
        .thenThrow(new RuntimeException("Test exception"));

    // Act & Assert
    assertThrows(
        InternalServerException.class, () -> fareService.getBookingFeeByList(bookingFeeRequest));
  }
}

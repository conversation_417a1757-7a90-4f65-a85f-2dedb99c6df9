package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.LocationSurchargeConfigRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.queries.LocationSurchargeConfigQueryResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.custom.LocationSurchargeJPACustom;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.LocationSurchargeJPARepository;
import java.util.List;
import java.util.Objects;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

@ExtendWith(MockitoExtension.class)
class LocationSurchargeConfigEntityRepositoryImplTest {
  @Mock LocationSurchargeConfigRepository locationSurchargeConfigRepository;
  @Mock LocationSurchargeJPARepository locationSurchargeJPARepository;
  @Mock LocationSurchargeJPACustom locationSurchargeJPACustom;
  @Mock Page<LocationSurchargeJPACustom> pageLocSurcharge;

  @BeforeEach
  void init() {
    locationSurchargeConfigRepository =
        new LocationSurchargeConfigRepositoryImpl(locationSurchargeJPARepository);
    pageLocSurcharge = new PageImpl<>(List.of(locationSurchargeJPACustom));
  }

  @Test
  void getLocationSurchargeConfigs_isEmpty() {

    Mockito.when(locationSurchargeJPARepository.getLocationSurchargeConfigs(30000, 0))
        .thenReturn(List.of());
    final LocationSurchargeConfigQueryResponse actual =
        locationSurchargeConfigRepository.getLocationSurchargeConfigs(0);

    final LocationSurchargeJPACustom conf =
        pageLocSurcharge.get().findFirst().orElse(locationSurchargeJPACustom);
    final boolean expected =
        actual.getConfigs().stream()
            .allMatch(
                actualConf ->
                    Objects.equals(actualConf.getLocationId(), conf.getLocationId())
                        && Objects.equals(actualConf.getLocationName(), conf.getLocationName())
                        && Objects.equals(actualConf.getAddressRef(), conf.getAddressRef())
                        && Objects.equals(actualConf.getFareType(), conf.getFareType())
                        && Objects.equals(actualConf.getChargeBy(), conf.getChargeBy())
                        && Objects.equals(actualConf.getSurchargeValue(), conf.getSurchargeValue())
                        && Objects.equals(actualConf.getProductId(), conf.getProductId())
                        && Objects.equals(actualConf.getStartTime(), conf.getStartTime())
                        && Objects.equals(actualConf.getEndTime(), conf.getEndTime())
                        && Objects.equals(actualConf.getDayIndicator(), conf.getDayIndicator()));
    Assertions.assertTrue(expected);
  }
}

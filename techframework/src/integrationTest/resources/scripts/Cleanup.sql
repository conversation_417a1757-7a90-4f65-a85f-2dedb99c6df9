TRUNCATE TABLE company_holiday CASCADE;
TRUNCATE TABLE config_key_value_change CASCADE;
TRUNCATE TABLE dynp_pricing_range CASCADE;
TRUNCATE TABLE dynp_surge_logs CASCADE;
TRUNCATE TABLE dynp_surges CASCADE;
TRUNCATE TABLE dynp_surges_ngp CASCADE;
TRUNCATE TABLE dynp_zone_mapping CASCADE;
TRUNCATE TABLE ett_unit_fare CASCADE;
TRUNCATE TABLE fare_type_conf CASCADE;
TRUNCATE TABLE fare_veh_grp_conf CASCADE;
TRUNCATE TABLE flat_fare_breakdown_detail CASCADE;
TRUNCATE TABLE flat_fare_conf CASCADE;
TRUNCATE TABLE loc_address CASCADE;
TRUNCATE TABLE loc_surcharge CASCADE;
TRUNCATE TABLE location CASCADE;
TRUNCATE TABLE new_pricing_model_config_change CASCADE;
TRUNCATE TABLE ph_type_code CASCADE;
TRUNCATE TABLE product CASCADE;
TRUNCATE TABLE s2cell CASCADE;
TRUNCATE TABLE surge_computation_region_model_distribution CASCADE;
TRUNCATE TABLE surge_computation_region_model_distribution_audit CASCADE;
TRUNCATE TABLE surge_computation_models CASCADE;
TRUNCATE TABLE surge_computation_models_audit CASCADE;
TRUNCATE TABLE surge_computation_static_time_based_configurations CASCADE;
TRUNCATE TABLE surge_computation_static_time_based_configurations_audit CASCADE;
TRUNCATE TABLE surge_computation_static_region_based_configurations CASCADE;
TRUNCATE TABLE surge_computation_static_region_based_configurations_audit CASCADE;
TRUNCATE TABLE zone_info CASCADE;
TRUNCATE TABLE surge_computation_model_api_logs CASCADE;
TRUNCATE TABLE surge_computation_model_surges CASCADE;
TRUNCATE TABLE ml_create_booking_request_agg_stats CASCADE;
TRUNCATE TABLE ml_get_fare_request_agg_stats CASCADE;
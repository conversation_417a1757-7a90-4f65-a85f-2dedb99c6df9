insert into dynp_surges (zone_id, surge, surge_low, surge_high, demand_recent, demand_previous, demand_predicted, supply, excess_demand, last_upd_dt, prev_surge, batch_key, created_dt, created_by, updated_dt, updated_by, time_slot_type, zone_price_model, demand_predicted_15, excess_demand_15, unmet_m1, unmet_m2, q_m1)
values  ('63', -15, -15, -30, 0, 0, 0, 0, 0, '2024-12-24 01:58:20.000000', -15, 674616, '2024-08-20 10:34:02.909408', 'system', null, null, 'OFFPEAK', 'v2.5', 0, 0, 0, 0, 0),
        ('73', -15, -15, -30, 0, 0, 0, 0, 0, '2024-12-24 01:58:20.000000', -15, 674616, '2024-08-20 10:34:02.909408', 'system', null, null, 'OFFPEAK', 'v2', 0, 0, 0, 0, 0),
        ('39', -15, -15, -30, 0, 0, 0, 0, 0, '2024-12-24 01:58:20.000000', -15, 674616, '2024-08-20 10:34:02.909408', 'system', null, null, 'OFFPEAK', 'v2.5', 0, 0, 0, 0, 0),
        ('74', -15, -15, -30, 0, 0, 0, 0, 0, '2024-12-24 01:58:20.000000', -15, 674616, '2024-08-20 10:34:02.909408', 'system', null, null, 'OFFPEAK', 'v2.5', 0, 0, 0, 0, 0),
        ('99', -15, -15, -30, 0, 0, 0, 0, 0, '2024-12-24 01:58:20.000000', -15, 674616, '2024-08-20 10:34:02.909408', 'system', null, null, 'OFFPEAK', 'v2', 0, 0, 0, 0, 0);
spring.cloud.config.enabled=false

# Database configuration for tests
# These will be overridden by the dynamic properties in IntegrationTestBase
spring.datasource.writer.url=${spring.datasource.url}
spring.datasource.writer.username=${spring.datasource.username}
spring.datasource.writer.password=${spring.datasource.password}
spring.datasource.writer.driver-class-name=org.postgresql.Driver

spring.datasource.reader.url=${spring.datasource.url}
spring.datasource.reader.username=${spring.datasource.username}
spring.datasource.reader.password=${spring.datasource.password}
spring.datasource.reader.driver-class-name=org.postgresql.Driver
#!/bin/bash

# Set database connection details
DB_NAME="ngp"
# Use configurable schema name for better portability and backward compatibility
SCHEMA_NAME="${TEST_DATABASE_SCHEMA:-ngp_me_dynamic_prc}"
DB_USER="postgres"
DB_PASSWORD="postgres"

# Connect to the database
PGPASSWORD="$DB_PASSWORD" psql -U "$DB_USER" -d "$DB_NAME" <<EOF

-- Create the schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS $SCHEMA_NAME;


EOF

echo "Schema '$SCHEMA_NAME' created in database '$DB_NAME'."

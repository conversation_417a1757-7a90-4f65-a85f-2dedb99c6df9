{"request": {"method": "POST", "url": "/v1.0/fleet-analytic/region/cal-demand-supply"}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "jsonBody": {"data": [{"regionId": 1, "comfortRideDemand": 120, "meterDemand": 80, "comfortRideUnmetDemand": 30, "meterUnmetDemand": 20}, {"regionId": 2, "comfortRideDemand": 95, "meterDemand": 60, "comfortRideUnmetDemand": 25, "meterUnmetDemand": 15}, {"regionId": 3, "comfortRideDemand": 46, "meterDemand": 78, "comfortRideUnmetDemand": 20, "meterUnmetDemand": 31}]}}, "priority": 2}
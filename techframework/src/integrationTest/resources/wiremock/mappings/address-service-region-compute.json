{"request": {"method": "POST", "url": "/v1.0/h3-regions/compute"}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "jsonBody": {"data": [{"regionVersion": "1.0.0", "regionId": 1, "regionName": "Sample Region", "lat": 1.2345, "lng": 103.1234, "boundary": [{"lat": 1.2345, "lng": 103.1234}, {"lat": 1.2355, "lng": 103.1244}]}, {"regionVersion": "1.0.0", "regionId": 2, "regionName": "Another Region", "lat": 2.3456, "lng": 104.2345, "boundary": [{"lat": 2.3456, "lng": 104.2345}, {"lat": 2.3466, "lng": 104.2355}]}]}}}
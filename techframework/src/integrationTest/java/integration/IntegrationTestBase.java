package integration;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.TechFrameworkApplication;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.redis.testcontainers.RedisContainer;
import integration.containers.*;
import integration.containers.base.SchemaRegistryContainer;
import integration.rest.DynamicPricingServiceApi;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;
import okhttp3.OkHttpClient;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.testcontainers.containers.KafkaContainer;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.wiremock.integrations.testcontainers.WireMockContainer;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;

@ExtendWith(SpringExtension.class)
@ActiveProfiles("test")
@SpringBootTest(
    classes = TechFrameworkApplication.class,
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@AutoConfigureMockMvc
@Testcontainers
public abstract class IntegrationTestBase {

  @LocalServerPort protected int port;

  @Autowired protected ObjectMapper objectMapper;

  protected DynamicPricingServiceApi dynamicPricingServiceApi;

  @BeforeAll
  public void setup() {
    TimeZone.setDefault(TimeZone.getTimeZone("UTC"));

    OkHttpClient okHttpClient =
        new OkHttpClient.Builder()
            .connectTimeout(60, TimeUnit.SECONDS)
            .readTimeout(60, TimeUnit.SECONDS)
            .writeTimeout(60, TimeUnit.SECONDS)
            .build();

    String baseUrl = "http://localhost:" + port;
    Retrofit retrofit =
        new Retrofit.Builder()
            .baseUrl(baseUrl)
            .client(okHttpClient)
            .addConverterFactory(JacksonConverterFactory.create(objectMapper))
            .build();

    dynamicPricingServiceApi = retrofit.create(DynamicPricingServiceApi.class);
  }

  @DynamicPropertySource
  public static void registerProperties(DynamicPropertyRegistry registry) {

    PostgreSQLContainer<?> postgreSQLContainer = SingletonPostgreSQLContainer.getInstance();
    RedisContainer redisContainer = SingletonRedisContainer.getInstance();
    WireMockContainer wiremockContainer = SingleWireMockContainer.getInstance();
    KafkaContainer kafkaContainer = SingletonKafkaContainer.getInstance();
    SchemaRegistryContainer schemaRegistryContainer =
        SingletonSchemaRegistryContainer.getInstance();

    // Use configurable schema name for better portability and backward compatibility
    String testSchemaName = System.getProperty("test.database.schema", "ngp_me_dynamic_prc");

    // Configure legacy datasource properties
    registry.add(
        "spring.datasource.url",
        () -> postgreSQLContainer.getJdbcUrl() + "&currentSchema=" + testSchemaName);
    registry.add("spring.datasource.username", postgreSQLContainer::getUsername);
    registry.add("spring.datasource.password", postgreSQLContainer::getPassword);

    // Configure writer datasource properties
    registry.add(
        "spring.datasource.writer.url",
        () -> postgreSQLContainer.getJdbcUrl() + "&currentSchema=" + testSchemaName);
    registry.add("spring.datasource.writer.username", postgreSQLContainer::getUsername);
    registry.add("spring.datasource.writer.password", postgreSQLContainer::getPassword);
    registry.add("spring.datasource.writer.driver-class-name", () -> "org.postgresql.Driver");

    // Configure reader datasource properties
    registry.add(
        "spring.datasource.reader.url",
        () -> postgreSQLContainer.getJdbcUrl() + "&currentSchema=" + testSchemaName);
    registry.add("spring.datasource.reader.username", postgreSQLContainer::getUsername);
    registry.add("spring.datasource.reader.password", postgreSQLContainer::getPassword);
    registry.add("spring.datasource.reader.driver-class-name", () -> "org.postgresql.Driver");

    registry.add("spring.liquibase.enabled", () -> true);

    registry.add("spring.data.redis.host", redisContainer::getHost);
    registry.add("spring.data.redis.port", redisContainer::getFirstMappedPort);
    registry.add("app.redis.ssl", () -> false);

    registry.add("spring.kafka.bootstrap-servers", kafkaContainer::getBootstrapServers);
    registry.add("spring.kafka.properties.schema-registry-url", schemaRegistryContainer::getUrl);
    registry.add("spring.kafka.properties.auto-register-schemas", () -> true);

    registry.add(
        "spring.cloud.openfeign.client.config.addressClient.url", wiremockContainer::getBaseUrl);
    registry.add(
        "spring.cloud.openfeign.client.config.fareClient.url", wiremockContainer::getBaseUrl);
    registry.add(
        "spring.cloud.openfeign.client.config.fleetAnalyticClient.url",
        wiremockContainer::getBaseUrl);
    registry.add(
        "spring.cloud.openfeign.client.config.cmsClient.url", wiremockContainer::getBaseUrl);
    registry.add(
        "spring.cloud.openfeign.client.config.weatherRetrievalClient.url",
        wiremockContainer::getBaseUrl);
    registry.add(
        "spring.cloud.openfeign.client.config.surgeComputationModelClient.url",
        wiremockContainer::getBaseUrl);
  }
}

package integration.containers;

import integration.containers.base.SingletonContainerNetwork;
import lombok.extern.slf4j.Slf4j;
import org.testcontainers.containers.BindMode;
import org.testcontainers.utility.DockerImageName;
import org.wiremock.integrations.testcontainers.WireMockContainer;

@Slf4j
public class SingleWireMockContainer {

  private static final WireMockContainer container;

  private static final DockerImageName imageName =
      DockerImageName.parse("wiremock/wiremock:3.6.0")
          .asCompatibleSubstituteFor("wiremock/wiremock");

  static {
    container =
        new WireMockContainer(imageName)
            .withNetwork(SingletonContainerNetwork.getInstance())
            .withClasspathResourceMapping(
                "wiremock/mappings", "/home/<USER>/mappings", BindMode.READ_ONLY)
            .withCliArg("--verbose")
            .withLogConsumer(outputFrame -> log.info(outputFrame.getUtf8String()));
    container.start();
  }

  private SingleWireMockContainer() {
    // Prevent instantiation
  }

  public static WireMockContainer getInstance() {
    return container;
  }
}

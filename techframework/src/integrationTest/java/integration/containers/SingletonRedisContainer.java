package integration.containers;

import com.redis.testcontainers.RedisContainer;
import integration.containers.base.SingletonContainerNetwork;
import lombok.extern.slf4j.Slf4j;
import org.testcontainers.utility.DockerImageName;

@Slf4j
public class SingletonRedisContainer {

  private static final RedisContainer container;

  private static final DockerImageName imageName =
      DockerImageName.parse("public.ecr.aws/docker/library/redis:7.0.7-alpine")
          .asCompatibleSubstituteFor("redis");

  static {
    container =
        new RedisContainer(imageName)
            .withNetwork(SingletonContainerNetwork.getInstance())
            .withEnv("REDIS_DISABLE_COMMANDS", "FLUSHDB,FLUSHALL")
            .withEnv("REDIS_PASSWORD", "admin");

    container.start();
  }

  private SingletonRedisContainer() {
    // Prevent instantiation
  }

  public static RedisContainer getInstance() {
    return container;
  }
}

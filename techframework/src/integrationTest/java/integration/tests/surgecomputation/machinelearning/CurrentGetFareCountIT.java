package integration.tests.surgecomputation.machinelearning;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.MlGetFareRequestAggStatsService;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.RegionFareCountAggregateResult;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.MlGetFareRequestAggStatsJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.surgecomputation.MlGetFareRequestAggStatsRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.CurrentGetFareCountResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.FareCountAggregateResult;
import integration.IntegrationTestBase;
import java.io.IOException;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.jdbc.Sql;
import retrofit2.Response;

/**
 * Integration test for the /v1.0/ml/current-get-fare-count endpoint.
 * Tests the machine learning endpoint that retrieves current get fare count for all H3 regions.
 */
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
@Sql(scripts = "/scripts/Cleanup.sql", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
public class CurrentGetFareCountIT extends IntegrationTestBase {

  private final MlGetFareRequestAggStatsRepository mlGetFareRequestAggStatsRepository;

  @MockBean private MlGetFareRequestAggStatsService mlGetFareRequestAggStatsService;

  @BeforeEach
  void setUp() {
    // Clean up any existing data
    mlGetFareRequestAggStatsRepository.deleteAll();
  }

  @Test
  void testGetCurrentGetFareCount_withNoData_shouldReturnEmptyList() throws IOException {
    // Arrange
    when(mlGetFareRequestAggStatsService.getCurrentGetFareCount())
        .thenReturn(List.of());

    // Act
    Response<CurrentGetFareCountResponse> response =
        dynamicPricingServiceApi.getCurrentGetFareCount().execute();

    // Assert
    assertTrue(response.isSuccessful());
    assertNotNull(response.body());
    assertNotNull(response.body().getData());
    assertTrue(response.body().getData().isEmpty());
  }

  @Test
  void testGetCurrentGetFareCount_withSingleRegion_shouldReturnCorrectData() throws IOException {
    // Arrange
    Long regionId = 123456789L;
    Integer fareCount = 100;
    
    RegionFareCountAggregateResult mockResult = 
        new RegionFareCountAggregateResult(regionId, fareCount);
    
    when(mlGetFareRequestAggStatsService.getCurrentGetFareCount())
        .thenReturn(List.of(mockResult));

    // Act
    Response<CurrentGetFareCountResponse> response =
        dynamicPricingServiceApi.getCurrentGetFareCount().execute();

    // Assert
    assertTrue(response.isSuccessful());
    assertNotNull(response.body());
    assertNotNull(response.body().getData());
    assertEquals(1, response.body().getData().size());

    FareCountAggregateResult result = response.body().getData().get(0);
    assertEquals(regionId, result.getRegionId());
    assertEquals(fareCount, result.getGetFareCount());
  }

  @Test
  void testGetCurrentGetFareCount_withMultipleRegions_shouldReturnAllData() throws IOException {
    // Arrange
    List<RegionFareCountAggregateResult> mockResults = List.of(
        new RegionFareCountAggregateResult(123456789L, 100),
        new RegionFareCountAggregateResult(987654321L, 50),
        new RegionFareCountAggregateResult(555666777L, 0)
    );
    
    when(mlGetFareRequestAggStatsService.getCurrentGetFareCount())
        .thenReturn(mockResults);

    // Act
    Response<CurrentGetFareCountResponse> response =
        dynamicPricingServiceApi.getCurrentGetFareCount().execute();

    // Assert
    assertTrue(response.isSuccessful());
    assertNotNull(response.body());
    assertNotNull(response.body().getData());
    assertEquals(3, response.body().getData().size());

    List<FareCountAggregateResult> results = response.body().getData();
    
    // Verify first region
    FareCountAggregateResult result1 = results.get(0);
    assertEquals(123456789L, result1.getRegionId());
    assertEquals(100, result1.getGetFareCount());
    
    // Verify second region
    FareCountAggregateResult result2 = results.get(1);
    assertEquals(987654321L, result2.getRegionId());
    assertEquals(50, result2.getGetFareCount());
    
    // Verify third region (zero count)
    FareCountAggregateResult result3 = results.get(2);
    assertEquals(555666777L, result3.getRegionId());
    assertEquals(0, result3.getGetFareCount());
  }

  @Test
  void testGetCurrentGetFareCount_withZeroFareCount_shouldReturnZero() throws IOException {
    // Arrange
    Long regionId = 123456789L;
    Integer fareCount = 0;
    
    RegionFareCountAggregateResult mockResult = 
        new RegionFareCountAggregateResult(regionId, fareCount);
    
    when(mlGetFareRequestAggStatsService.getCurrentGetFareCount())
        .thenReturn(List.of(mockResult));

    // Act
    Response<CurrentGetFareCountResponse> response =
        dynamicPricingServiceApi.getCurrentGetFareCount().execute();

    // Assert
    assertTrue(response.isSuccessful());
    assertNotNull(response.body());
    assertNotNull(response.body().getData());
    assertEquals(1, response.body().getData().size());

    FareCountAggregateResult result = response.body().getData().get(0);
    assertEquals(regionId, result.getRegionId());
    assertEquals(0, result.getGetFareCount());
  }

  @Test
  void testGetCurrentGetFareCount_withLargeFareCount_shouldHandleCorrectly() throws IOException {
    // Arrange
    Long regionId = 123456789L;
    Integer largeFareCount = 999999;
    
    RegionFareCountAggregateResult mockResult = 
        new RegionFareCountAggregateResult(regionId, largeFareCount);
    
    when(mlGetFareRequestAggStatsService.getCurrentGetFareCount())
        .thenReturn(List.of(mockResult));

    // Act
    Response<CurrentGetFareCountResponse> response =
        dynamicPricingServiceApi.getCurrentGetFareCount().execute();

    // Assert
    assertTrue(response.isSuccessful());
    assertNotNull(response.body());
    assertNotNull(response.body().getData());
    assertEquals(1, response.body().getData().size());

    FareCountAggregateResult result = response.body().getData().get(0);
    assertEquals(regionId, result.getRegionId());
    assertEquals(largeFareCount, result.getGetFareCount());
  }

  @Test
  void testGetCurrentGetFareCount_performanceTest() throws IOException {
    // Arrange
    List<RegionFareCountAggregateResult> mockResults = List.of(
        new RegionFareCountAggregateResult(123456789L, 100),
        new RegionFareCountAggregateResult(987654321L, 50),
        new RegionFareCountAggregateResult(555666777L, 75),
        new RegionFareCountAggregateResult(111222333L, 25),
        new RegionFareCountAggregateResult(444555666L, 150)
    );
    
    when(mlGetFareRequestAggStatsService.getCurrentGetFareCount())
        .thenReturn(mockResults);

    // Act - Call the endpoint multiple times to test performance
    long startTime = System.currentTimeMillis();

    for (int i = 0; i < 10; i++) {
      Response<CurrentGetFareCountResponse> response =
          dynamicPricingServiceApi.getCurrentGetFareCount().execute();
      assertTrue(response.isSuccessful());
      assertEquals(5, response.body().getData().size());
    }

    long endTime = System.currentTimeMillis();
    long totalTime = endTime - startTime;

    // Assert - Should be reasonably fast for ML monitoring calls
    assertTrue(
        totalTime < 10000,
        "Endpoint should be fast for ML monitoring calls, took: " + totalTime + "ms");
  }

  @Test
  void testGetCurrentGetFareCount_withNullFareCount_shouldHandleGracefully() throws IOException {
    // Arrange
    Long regionId = 123456789L;
    Integer nullFareCount = null;
    
    RegionFareCountAggregateResult mockResult = 
        new RegionFareCountAggregateResult(regionId, nullFareCount);
    
    when(mlGetFareRequestAggStatsService.getCurrentGetFareCount())
        .thenReturn(List.of(mockResult));

    // Act
    Response<CurrentGetFareCountResponse> response =
        dynamicPricingServiceApi.getCurrentGetFareCount().execute();

    // Assert
    assertTrue(response.isSuccessful());
    assertNotNull(response.body());
    assertNotNull(response.body().getData());
    assertEquals(1, response.body().getData().size());

    FareCountAggregateResult result = response.body().getData().get(0);
    assertEquals(regionId, result.getRegionId());
    assertNull(result.getGetFareCount());
  }
}

package integration.tests.surgecomputation.standardinputs;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.GetStandardInputsResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.StandardInput;
import integration.IntegrationTestBase;
import org.junit.jupiter.api.Test;

/**
 * Integration tests for the Standard Inputs functionality. This test class verifies that all
 * standard input services are properly registered and can be retrieved through the API endpoint.
 */
public class StandardInputsApiIT extends IntegrationTestBase {

  /** Test that the standard inputs API endpoint returns the expected data. */
  @Test
  void getStandardInputs_shouldReturnAllStandardInputs() throws Exception {
    // Act - Use the DynamicPricingServiceApi from IntegrationTestBase
    GetStandardInputsResponse response =
        dynamicPricingServiceApi.getStandardInputs().execute().body();

    // Assert
    assertNotNull(response);
    assertNotNull(response.getData());
    assertEquals(5, response.getData().size(), "Expected 5 standard inputs in the response");

    // Verify that all expected standard inputs are present
    boolean hasComfortRideDemand = false;
    boolean hasComfortRideUnmetDemand = false;
    boolean hasMeterDemand = false;
    boolean hasMeterUnmetDemand = false;
    boolean hasAverageIntensity = false;

    for (StandardInput input : response.getData()) {
      if ("ComfortRideDemand".equals(input.getName())) {
        hasComfortRideDemand = true;
        assertEquals("ComfortRide Demand by region", input.getDescription());
      } else if ("ComfortRideUnmetDemand".equals(input.getName())) {
        hasComfortRideUnmetDemand = true;
        assertEquals("ComfortRide Unmet Demand by region", input.getDescription());
      } else if ("MeterDemand".equals(input.getName())) {
        hasMeterDemand = true;
        assertEquals("Meter Demand by region", input.getDescription());
      } else if ("MeterUnmetDemand".equals(input.getName())) {
        hasMeterUnmetDemand = true;
        assertEquals("Meter Unmet Demand by region", input.getDescription());
      } else if ("AverageIntensity".equals(input.getName())) {
        hasAverageIntensity = true;
        assertEquals("Rainfall intensity by region", input.getDescription());
      }
    }

    assertTrue(hasComfortRideDemand, "ComfortRideDemand should be in the response");
    assertTrue(hasComfortRideUnmetDemand, "ComfortRideUnmetDemand should be in the response");
    assertTrue(hasMeterDemand, "MeterDemand should be in the response");
    assertTrue(hasMeterUnmetDemand, "MeterUnmetDemand should be in the response");
    assertTrue(hasAverageIntensity, "AverageIntensity should be in the response");
  }
}

package integration.tests.surgecomputation.farebreakdown;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareVOPart;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.MultiFareResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.CommonUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.CacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FareBreakdownDetailEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.SurgeAreaTypeEnum;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.MlCreateBookingRequestAggStatsJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.surgecomputation.MlCreateBookingRequestAggStatsJPARepository;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.StoreFareBreakdownInboundRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.StoreFareBreakdownInboundResponse;
import integration.IntegrationTestBase;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;
import retrofit2.Response;

@RequiredArgsConstructor(onConstructor_ = {@Autowired})
@Sql(scripts = "/scripts/Cleanup.sql", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
public class StoreFareBreakdownIT extends IntegrationTestBase {

  private static final String FARE_ID = "test-fare-id-12345";
  private static final Integer VEHICLE_TYPE_ID = 100;
  private static final String BOOKING_ID = "12345";
  public static final String PICKUP_ADDRESS_REF = "123";
  public static final String DEST_ADDRESS_REF = "456";

  private final CacheService cacheService;
  private final MlCreateBookingRequestAggStatsJPARepository
      mlCreateBookingRequestAggStatsJPARepository;

  @AfterEach
  void cleanup() {
    String fareBreakdownKey = CommonUtils.generateFareBreakdownKey(FARE_ID, VEHICLE_TYPE_ID);
    cacheService.deleteByKey(fareBreakdownKey);

    final String multiFareCacheKey = CommonUtils.generateMultiFareCacheKey(FARE_ID);
    cacheService.deleteByKey(multiFareCacheKey);
  }

  @BeforeEach
  void setUp() {
    final String fareBreakdownKey = CommonUtils.generateFareBreakdownKey(FARE_ID, VEHICLE_TYPE_ID);

    FareBreakdownDetailEntity fareBreakdownDetailEntity =
        FareBreakdownDetailEntity.builder()
            .fareId(FARE_ID)
            .pickupAddressRef(PICKUP_ADDRESS_REF)
            .pickupAddressLat(1.3521)
            .pickupAddressLng(103.8198)
            .destAddressRef(DEST_ADDRESS_REF)
            .destAddressLat(1.3644)
            .destAddressLng(103.9915)
            .requestDate(new Timestamp(System.currentTimeMillis()))
            .flagDownRate(3.5)
            .tier1Fare(0.5)
            .tier2Fare(0.5)
            .waitTimeFare(0.0)
            .peakHrFare(0.0)
            .midNightFare(0.0)
            .bookingFee(2.5)
            .routingDistance(10000L)
            .ett(1200L)
            .dpSurgePercent(1.2)
            .dpBaseFareForSurge(10.0)
            .dpFinalFare(12.0)
            .meteredBaseFare(10.0)
            .totalFare(BigDecimal.valueOf(12))
            .estimatedFareLF(BigDecimal.valueOf(10))
            .estimatedFareRT(BigDecimal.valueOf(20))
            .meterPlatformFeeLower(10.0)
            .meterPlatformFeeUpper(20.0)
            .flatPlatformFee(5.0)
            .areaType(SurgeAreaTypeEnum.REGION.getValue())
            .regionId(1L)
            .regionVersion("0.0.1")
            .modelId(1L)
            .modelName("v4")
            .build();

    cacheService.setValue(fareBreakdownKey, fareBreakdownDetailEntity);

    final String multiFareCacheKey = CommonUtils.generateMultiFareCacheKey(FARE_ID);
    MultiFareResponse multiFareResponse =
        MultiFareResponse.builder()
            .fareId(FARE_ID)
            .pickupAddressRef(PICKUP_ADDRESS_REF)
            .destAddressRef(DEST_ADDRESS_REF)
            .distance(10000L)
            .estimatedTripTime(1200L)
            .fareCalcTime(OffsetDateTime.now())
            .flatFareVOParts(
                List.of(
                    FlatFareVOPart.builder()
                        .vehTypeId(VEHICLE_TYPE_ID)
                        .totalFare(BigDecimal.valueOf(12))
                        .pdtId("NORMAL")
                        .build()))
            .build();

    cacheService.setValue(multiFareCacheKey, multiFareResponse);
  }

  @Test
  void testStoreFareBreakdown_mlCreateBookingRequestAggStats_success() throws Exception {
    StoreFareBreakdownInboundRequest request = new StoreFareBreakdownInboundRequest();
    request.setFareId(FARE_ID);
    request.setVehicleTypeId(VEHICLE_TYPE_ID);
    request.setBookingId(BOOKING_ID);

    Response<StoreFareBreakdownInboundResponse> response =
        dynamicPricingServiceApi.storeFareBreakdown(request).execute();

    assertTrue(response.isSuccessful());
    assertNotNull(response.body());
    assertNotNull(response.body().getData());
    assertTrue(response.body().getData().getSuccess());

    // Because the mlCreateBookingRequestAggStats save is async, so here need to wait a moment
    TimeUnit.MILLISECONDS.sleep(500L);

    Optional<MlCreateBookingRequestAggStatsJPA> bookingOptional =
        mlCreateBookingRequestAggStatsJPARepository.findByBookingId(BOOKING_ID);

    assertFalse(bookingOptional.isEmpty());
    assertEquals(BOOKING_ID, bookingOptional.get().getBookingId());
    assertEquals(FARE_ID, bookingOptional.get().getFareId());
    assertEquals("0.0.1", bookingOptional.get().getRegionVersion());
    assertEquals(1L, bookingOptional.get().getPickupRegionId());
    assertEquals(1L, bookingOptional.get().getModelId());
    assertEquals(0, BigDecimal.valueOf(10).compareTo(bookingOptional.get().getEstimatedFareLF()));
    assertEquals(0, BigDecimal.valueOf(20).compareTo(bookingOptional.get().getEstimatedFareRT()));
    assertEquals(10.0, bookingOptional.get().getMeterPlatformFeeLower());
    assertEquals(20.0, bookingOptional.get().getMeterPlatformFeeUpper());
    assertEquals(5.0, bookingOptional.get().getFlatPlatformFee());
    assertEquals(SurgeAreaTypeEnum.REGION.getValue(), bookingOptional.get().getAreaType());
  }

  @Test
  void testStoreFareBreakdown_notFoundInCache() throws Exception {
    final String nonExistentFareId = "non-existent-fare-id-12345";
    StoreFareBreakdownInboundRequest request = new StoreFareBreakdownInboundRequest();
    request.setFareId(nonExistentFareId);
    request.setVehicleTypeId(VEHICLE_TYPE_ID);
    request.setBookingId(BOOKING_ID + "-not-found");

    Response<StoreFareBreakdownInboundResponse> response =
        dynamicPricingServiceApi.storeFareBreakdown(request).execute();

    assertFalse(response.isSuccessful());
    assertEquals(404, response.code()); // Not Found - GET_FARE_BREAK_DOWN_ERROR
  }

  @Test
  void testStoreFareBreakdown_duplicateBookingId() throws Exception {
    StoreFareBreakdownInboundRequest request = new StoreFareBreakdownInboundRequest();
    request.setFareId(FARE_ID);
    request.setVehicleTypeId(VEHICLE_TYPE_ID);
    request.setBookingId(BOOKING_ID);

    Response<StoreFareBreakdownInboundResponse> firstResponse =
        dynamicPricingServiceApi.storeFareBreakdown(request).execute();
    assertTrue(firstResponse.isSuccessful());
    assertNotNull(firstResponse.body());
    assertNotNull(firstResponse.body().getData());
    assertTrue(firstResponse.body().getData().getSuccess());

    Response<StoreFareBreakdownInboundResponse> secondResponse =
        dynamicPricingServiceApi.storeFareBreakdown(request).execute();

    assertFalse(secondResponse.isSuccessful());
    assertEquals(400, secondResponse.code()); // Bad Request - FARE_BREAKDOWN_EXISTED
  }
}

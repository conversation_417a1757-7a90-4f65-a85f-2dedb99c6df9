package integration.tests.surgecomputation.staticregionbasedconfiguration;

import static org.junit.jupiter.api.Assertions.*;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.*;
import integration.IntegrationTestBase;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.jdbc.Sql;
import retrofit2.Response;

@Sql(scripts = "/scripts/Cleanup.sql", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
public class UpdateStaticRegionBasedConfigurationApiIT extends IntegrationTestBase {

  private static final String TEST_USER_ID = "test-user";
  public static final String REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1 = "unmet_rate_1";
  private Long configId;
  private Long modelId;
  private OffsetDateTime firstConfigEffectiveFrom;
  private OffsetDateTime firstConfigEffectiveTo;

  @BeforeEach
  public void setupTestData() throws IOException {
    // Create a surge computation model for testing
    modelId = createSurgeComputationModel();
    // Create a configuration to update
    configId = createRegionBasedConfiguration();
  }

  @Test
  @DisplayName("Should update a existing static region-based configuration successfully")
  public void testUpdateStaticRegionBasedConfiguration_Success() throws IOException {
    // Arrange
    StaticRegionBasedConfigurationRequest updateRequest =
        new StaticRegionBasedConfigurationRequest();
    updateRequest.setName(REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1);
    updateRequest.setVersion("1.0"); // Same as the first config
    updateRequest.setEffectiveFrom(firstConfigEffectiveFrom); // Same as the first config
    updateRequest.setEffectiveTo(firstConfigEffectiveTo); // Same as the first config
    updateRequest.setDescription("Updated configuration for region-based surge pricing");
    updateRequest.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(2L, "1.8")));

    // Act
    Response<StaticRegionBasedConfigurationResponse> response =
        dynamicPricingServiceApi
            .updateStaticRegionBasedConfiguration(modelId, TEST_USER_ID, List.of(updateRequest))
            .execute();

    // Assert
    assertTrue(response.isSuccessful());
    assertEquals(204, response.code());

    // Verify the updated configuration
    Response<StaticRegionBasedConfigurationResponse> getResponse =
        dynamicPricingServiceApi.getStaticRegionBasedConfigurationById(configId).execute();
    assertTrue(getResponse.isSuccessful());

    StaticRegionBasedConfigurationResponse responseBody = getResponse.body();
    assertNotNull(responseBody);
    assertNotNull(responseBody.getData());
    assertEquals(configId, responseBody.getData().getId());
    assertEquals(REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1, responseBody.getData().getName());
    assertEquals("1.0", responseBody.getData().getVersion());
    assertNotNull(responseBody.getData().getEffectiveFrom());
    assertNotNull(responseBody.getData().getEffectiveTo());
    assertEquals(
        "Updated configuration for region-based surge pricing",
        responseBody.getData().getDescription());
    assertEquals(1, responseBody.getData().getRegionValues().size());
    assertEquals(2L, responseBody.getData().getRegionValues().get(0).getRegionId());
    assertEquals("1.8", responseBody.getData().getRegionValues().get(0).getValue());

    assertEquals(TEST_USER_ID, responseBody.getData().getCreatedBy());
    assertEquals(TEST_USER_ID, responseBody.getData().getUpdatedBy());
    assertNotNull(responseBody.getData().getCreatedDate());
    assertNotNull(responseBody.getData().getUpdatedDate());
  }

  @Test
  @DisplayName("Should return 400 Bad Request when the versions are different")
  public void testUpdateStaticRegionBasedConfiguration_DifferentVersion() throws IOException {
    StaticRegionBasedConfigurationRequest updateRequest =
        new StaticRegionBasedConfigurationRequest();
    updateRequest.setName(REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1);
    updateRequest.setVersion("1.0"); // Same as the first config
    updateRequest.setEffectiveFrom(firstConfigEffectiveFrom); // Same as the first config
    updateRequest.setEffectiveTo(firstConfigEffectiveTo); // Same as the first config
    updateRequest.setDescription("Updated configuration for region-based surge pricing");
    updateRequest.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(2L, "1.8")));
    StaticRegionBasedConfigurationRequest secondUpdateRequest =
        new StaticRegionBasedConfigurationRequest();
    secondUpdateRequest.setName(REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1);
    secondUpdateRequest.setVersion("2.0"); // Different version
    secondUpdateRequest.setEffectiveFrom(firstConfigEffectiveFrom); // Same as the first config
    secondUpdateRequest.setEffectiveTo(firstConfigEffectiveTo); // Same as the first config
    secondUpdateRequest.setDescription("Updated configuration for region-based surge pricing");
    secondUpdateRequest.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(2L, "1.8")));

    // Act
    Response<StaticRegionBasedConfigurationResponse> response =
        dynamicPricingServiceApi
            .updateStaticRegionBasedConfiguration(
                modelId, TEST_USER_ID, List.of(updateRequest, secondUpdateRequest))
            .execute();

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(400, response.code());
  }

  @Test
  @DisplayName("Should return 400 Bad Request when the effectiveFrom are different")
  public void testUpdateStaticRegionBasedConfiguration_DifferentEffectiveFrom() throws IOException {
    StaticRegionBasedConfigurationRequest updateRequest =
        new StaticRegionBasedConfigurationRequest();
    updateRequest.setName(REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1);
    updateRequest.setVersion("1.0"); // Same as the first config
    updateRequest.setEffectiveFrom(firstConfigEffectiveFrom); // Same as the first config
    updateRequest.setEffectiveTo(firstConfigEffectiveTo); // Same as the first config
    updateRequest.setDescription("Updated configuration for region-based surge pricing");
    updateRequest.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(2L, "1.8")));
    StaticRegionBasedConfigurationRequest secondUpdateRequest =
        new StaticRegionBasedConfigurationRequest();
    secondUpdateRequest.setName(REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1);
    secondUpdateRequest.setVersion("1.0"); // Same as the first config
    secondUpdateRequest.setEffectiveFrom(firstConfigEffectiveFrom.plusDays(1)); // Different
    secondUpdateRequest.setEffectiveTo(firstConfigEffectiveTo); // Same as the first config
    secondUpdateRequest.setDescription("Updated configuration for region-based surge pricing");
    secondUpdateRequest.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(2L, "1.8")));

    // Act
    Response<StaticRegionBasedConfigurationResponse> response =
        dynamicPricingServiceApi
            .updateStaticRegionBasedConfiguration(
                modelId, TEST_USER_ID, List.of(updateRequest, secondUpdateRequest))
            .execute();

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(400, response.code());
  }

  @Test
  @DisplayName("Should return 400 Bad Request when the effectiveTo are different")
  public void testUpdateStaticRegionBasedConfiguration_DifferentEffectiveTo() throws IOException {
    StaticRegionBasedConfigurationRequest updateRequest =
        new StaticRegionBasedConfigurationRequest();
    updateRequest.setName(REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1);
    updateRequest.setVersion("1.0"); // Same as the first config
    updateRequest.setEffectiveFrom(firstConfigEffectiveFrom); // Same as the first config
    updateRequest.setEffectiveTo(firstConfigEffectiveTo); // Same as the first config
    updateRequest.setDescription("Updated configuration for region-based surge pricing");
    updateRequest.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(2L, "1.8")));
    StaticRegionBasedConfigurationRequest secondUpdateRequest =
        new StaticRegionBasedConfigurationRequest();
    secondUpdateRequest.setName(REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1);
    secondUpdateRequest.setVersion("1.0"); // Same as the first config
    secondUpdateRequest.setEffectiveFrom(firstConfigEffectiveFrom); // Same as the first config
    secondUpdateRequest.setEffectiveTo(firstConfigEffectiveTo.plusDays(1)); // Different
    secondUpdateRequest.setDescription("Updated configuration for region-based surge pricing");
    secondUpdateRequest.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(2L, "1.8")));

    // Act
    Response<StaticRegionBasedConfigurationResponse> response =
        dynamicPricingServiceApi
            .updateStaticRegionBasedConfiguration(
                modelId, TEST_USER_ID, List.of(updateRequest, secondUpdateRequest))
            .execute();

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(400, response.code());
  }

  @Test
  @DisplayName(
      "Should update a existing static region-based configuration successfully with different effectiveFrom")
  public void testUpdateStaticRegionBasedConfiguration_Success_withDifferentEffectiveFrom()
      throws IOException {
    OffsetDateTime effectiveFrom = firstConfigEffectiveFrom.plusDays(31);
    OffsetDateTime effectiveTo = firstConfigEffectiveFrom.plusDays(40);
    // Arrange
    StaticRegionBasedConfigurationRequest updateRequest =
        new StaticRegionBasedConfigurationRequest();
    updateRequest.setName(REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1); // Same as the first config
    updateRequest.setVersion("1.1"); // Different from the first config version
    updateRequest.setEffectiveFrom(effectiveFrom); // Different from the first config
    updateRequest.setEffectiveTo(effectiveTo); // Different from the first config
    updateRequest.setDescription(
        "Updated configuration for region-based surge pricing with different effectiveFrom");
    updateRequest.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(2L, "1.8")));

    // Act
    Response<StaticRegionBasedConfigurationResponse> response =
        dynamicPricingServiceApi
            .updateStaticRegionBasedConfiguration(modelId, TEST_USER_ID, List.of(updateRequest))
            .execute();

    // Assert
    assertTrue(response.isSuccessful());
    assertEquals(204, response.code());

    // Verify the updated configuration, will create new configuration
    Response<StaticRegionBasedConfigurationListResponse> getByModelResponse =
        dynamicPricingServiceApi.getStaticRegionBasedConfigurations(modelId, "1.1").execute();

    assertTrue(getByModelResponse.isSuccessful());
    StaticRegionBasedConfigurationListResponse getByModelResponseBody = getByModelResponse.body();
    assertNotNull(getByModelResponseBody);
    assertNotNull(getByModelResponseBody.getData());
    assertEquals(1, getByModelResponseBody.getData().size());
    StaticRegionBasedConfiguration staticRegionBasedConfiguration =
        getByModelResponseBody.getData().get(0);
    assertNotEquals(configId, staticRegionBasedConfiguration.getId());
    assertEquals(
        REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1, staticRegionBasedConfiguration.getName());
    assertEquals("1.1", staticRegionBasedConfiguration.getVersion());
    // because the system use UTC zone, so convert to UTC here
    assertEquals(
        effectiveFrom.atZoneSameInstant(ZoneOffset.UTC).toOffsetDateTime(),
        staticRegionBasedConfiguration.getEffectiveFrom());
    assertEquals(
        effectiveTo.atZoneSameInstant(ZoneOffset.UTC).toOffsetDateTime(),
        staticRegionBasedConfiguration.getEffectiveTo());
    assertEquals(
        "Updated configuration for region-based surge pricing with different effectiveFrom",
        staticRegionBasedConfiguration.getDescription());
    assertEquals(1, staticRegionBasedConfiguration.getRegionValues().size());
    assertEquals(2L, staticRegionBasedConfiguration.getRegionValues().get(0).getRegionId());
    assertEquals("1.8", staticRegionBasedConfiguration.getRegionValues().get(0).getValue());
    assertEquals(TEST_USER_ID, staticRegionBasedConfiguration.getCreatedBy());
    assertEquals(TEST_USER_ID, staticRegionBasedConfiguration.getUpdatedBy());
    assertNotNull(staticRegionBasedConfiguration.getCreatedDate());
    assertNotNull(staticRegionBasedConfiguration.getUpdatedDate());

    // Verify the old configuration not change
    Response<StaticRegionBasedConfigurationResponse> getResponse =
        dynamicPricingServiceApi.getStaticRegionBasedConfigurationById(configId).execute();
    assertTrue(getResponse.isSuccessful());

    StaticRegionBasedConfigurationResponse responseBody = getResponse.body();
    assertNotNull(responseBody);
    assertNotNull(responseBody.getData());
    assertEquals(configId, responseBody.getData().getId());
    assertEquals(REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1, responseBody.getData().getName());
    assertEquals("1.0", responseBody.getData().getVersion());
    // because the system use UTC zone, so convert to UTC here
    assertEquals(
        firstConfigEffectiveFrom.atZoneSameInstant(ZoneOffset.UTC).toOffsetDateTime(),
        responseBody.getData().getEffectiveFrom());
    assertEquals(
        firstConfigEffectiveTo.atZoneSameInstant(ZoneOffset.UTC).toOffsetDateTime(),
        responseBody.getData().getEffectiveTo());
    assertEquals(
        "Initial configuration for region-based surge pricing",
        responseBody.getData().getDescription());
    assertEquals(1, responseBody.getData().getRegionValues().size());
    assertEquals(1L, responseBody.getData().getRegionValues().get(0).getRegionId());
    assertEquals("1.2", responseBody.getData().getRegionValues().get(0).getValue());

    assertEquals(TEST_USER_ID, responseBody.getData().getCreatedBy());
    assertEquals(TEST_USER_ID, responseBody.getData().getUpdatedBy());
    assertNotNull(responseBody.getData().getCreatedDate());
    assertNotNull(responseBody.getData().getUpdatedDate());
  }

  @Test
  @DisplayName("Should return 404 Not Found when updating with non-existent model ID")
  public void testUpdateStaticRegionBasedConfiguration_NotFound() throws IOException {
    // Arrange
    Long nonExistentModelId = 9999L;
    StaticRegionBasedConfigurationRequest updateRequest =
        new StaticRegionBasedConfigurationRequest();
    updateRequest.setName("Updated Region Config");
    updateRequest.setVersion("1.1");
    updateRequest.setEffectiveFrom(OffsetDateTime.now());
    updateRequest.setDescription("Updated configuration for region-based surge pricing");
    updateRequest.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(2L, "1.8")));

    // Act
    Response<StaticRegionBasedConfigurationResponse> response =
        dynamicPricingServiceApi
            .updateStaticRegionBasedConfiguration(
                nonExistentModelId, TEST_USER_ID, List.of(updateRequest))
            .execute();

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(404, response.code());
  }

  @Test
  @DisplayName("Should return 400 Bad Request when name is missing")
  public void testUpdateStaticRegionBasedConfiguration_MissingName() throws IOException {
    // Arrange
    StaticRegionBasedConfigurationRequest updateRequest =
        new StaticRegionBasedConfigurationRequest();
    updateRequest.setVersion("1.1");
    updateRequest.setEffectiveFrom(OffsetDateTime.now());
    updateRequest.setDescription("Updated configuration for region-based surge pricing");
    updateRequest.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(2L, "1.8")));

    // Act
    Response<StaticRegionBasedConfigurationResponse> response =
        dynamicPricingServiceApi
            .updateStaticRegionBasedConfiguration(configId, TEST_USER_ID, List.of(updateRequest))
            .execute();

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(400, response.code());
  }

  @Test
  @DisplayName("Should return 400 Bad Request when version is missing")
  public void testUpdateStaticRegionBasedConfiguration_MissingVersion() throws IOException {
    // Arrange
    StaticRegionBasedConfigurationRequest updateRequest =
        new StaticRegionBasedConfigurationRequest();
    updateRequest.setName("Updated Region Config");
    updateRequest.setEffectiveFrom(OffsetDateTime.now());
    updateRequest.setDescription("Updated configuration for region-based surge pricing");
    updateRequest.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(2L, "1.8")));

    // Act
    Response<StaticRegionBasedConfigurationResponse> response =
        dynamicPricingServiceApi
            .updateStaticRegionBasedConfiguration(configId, TEST_USER_ID, List.of(updateRequest))
            .execute();

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(400, response.code());
  }

  @Test
  @DisplayName("Should return 400 Bad Request when effective_from is missing")
  public void testUpdateStaticRegionBasedConfiguration_MissingEffectiveFrom() throws IOException {
    // Arrange
    StaticRegionBasedConfigurationRequest updateRequest =
        new StaticRegionBasedConfigurationRequest();
    updateRequest.setName("Updated Region Config");
    updateRequest.setVersion("1.1");
    updateRequest.setDescription("Updated configuration for region-based surge pricing");
    updateRequest.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(2L, "1.8")));

    // Act
    Response<StaticRegionBasedConfigurationResponse> response =
        dynamicPricingServiceApi
            .updateStaticRegionBasedConfiguration(configId, TEST_USER_ID, List.of(updateRequest))
            .execute();

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(400, response.code());
  }

  @Test
  @DisplayName("Should return 400 Bad Request when region_values is missing")
  public void testUpdateStaticRegionBasedConfiguration_MissingRegionValues() throws IOException {
    // Arrange
    StaticRegionBasedConfigurationRequest updateRequest =
        new StaticRegionBasedConfigurationRequest();
    updateRequest.setName("Updated Region Config");
    updateRequest.setVersion("1.1");
    updateRequest.setEffectiveFrom(OffsetDateTime.now());
    updateRequest.setDescription("Updated configuration for region-based surge pricing");

    // Act
    Response<StaticRegionBasedConfigurationResponse> response =
        dynamicPricingServiceApi
            .updateStaticRegionBasedConfiguration(configId, TEST_USER_ID, List.of(updateRequest))
            .execute();

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(400, response.code());
  }

  @Test
  @DisplayName("Should return 400 Bad Request when X-User-Id header is missing")
  public void testUpdateStaticRegionBasedConfiguration_MissingUserIdHeader() throws IOException {
    // Arrange
    StaticRegionBasedConfigurationRequest updateRequest =
        new StaticRegionBasedConfigurationRequest();
    updateRequest.setName("Updated Region Config");
    updateRequest.setVersion("1.1");
    updateRequest.setEffectiveFrom(OffsetDateTime.now());
    updateRequest.setDescription("Updated configuration for region-based surge pricing");
    updateRequest.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(2L, "1.8")));

    // Act
    Response<StaticRegionBasedConfigurationResponse> response =
        dynamicPricingServiceApi
            .updateStaticRegionBasedConfiguration(modelId, null, List.of(updateRequest))
            .execute();

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(400, response.code());
  }

  @Test
  @DisplayName("Should reject update with overlapping effective date ranges")
  public void testUpdateStaticRegionBasedConfiguration_OverlappingDateRanges() throws IOException {
    // Arrange - Create a second configuration with non-overlapping date range
    OffsetDateTime now = OffsetDateTime.now();
    OffsetDateTime later = now.plusDays(30);
    OffsetDateTime evenLater = later.plusDays(1); // One day after the first config ends
    OffsetDateTime furtherLater = evenLater.plusDays(30);

    // First configuration is already created in setupTestData() with current time

    // Create a second configuration with non-overlapping date range
    StaticRegionBasedConfigurationRequest createRequest =
        new StaticRegionBasedConfigurationRequest();
    createRequest.setName(
        REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1); // Same name as first config
    createRequest.setVersion("2.0"); // Different version
    createRequest.setEffectiveFrom(evenLater); // Starts after first config ends
    createRequest.setEffectiveTo(furtherLater);
    createRequest.setDescription("Second configuration with non-overlapping date range");
    createRequest.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(1L, "2.0")));

    Response<StaticRegionBasedConfigurationBatchCreateResponse> createResponse =
        dynamicPricingServiceApi
            .batchCreateStaticRegionBasedConfiguration(TEST_USER_ID, List.of(createRequest))
            .execute();

    assertTrue(createResponse.isSuccessful());

    // Now try to update the second configuration to overlap with the first one
    StaticRegionBasedConfigurationRequest updateRequest =
        new StaticRegionBasedConfigurationRequest();
    updateRequest.setName(REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1); // Same name
    updateRequest.setVersion("2.0"); // Same version as second config
    updateRequest.setEffectiveFrom(now.plusDays(15)); // Now overlaps with first config
    updateRequest.setEffectiveTo(furtherLater);
    updateRequest.setDescription("Updated second configuration to overlap with first");
    createRequest.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(1L, "2.5")));

    // Act - Try to update the second configuration to overlap with the first
    Response<StaticRegionBasedConfigurationResponse> updateResponse =
        dynamicPricingServiceApi
            .updateStaticRegionBasedConfiguration(modelId, TEST_USER_ID, List.of(updateRequest))
            .execute();

    // Assert - Update should be rejected
    assertFalse(updateResponse.isSuccessful());
    assertEquals(
        400, updateResponse.code()); // Expecting a 400 Bad Request due to constraint violation
  }

  private Long createRegionBasedConfiguration() throws IOException {
    // Use a fixed timestamp to avoid timing issues between create and update
    firstConfigEffectiveFrom =
        OffsetDateTime.now().withNano(0); // Remove nanoseconds for consistency
    firstConfigEffectiveTo = firstConfigEffectiveFrom.plusDays(30);

    StaticRegionBasedConfigurationRequest createRequest =
        new StaticRegionBasedConfigurationRequest();
    createRequest.setName(REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1);
    createRequest.setVersion("1.0");
    createRequest.setEffectiveFrom(firstConfigEffectiveFrom);
    createRequest.setEffectiveTo(
        firstConfigEffectiveFrom.plusDays(30)); // Set an effective end date
    createRequest.setDescription("Initial configuration for region-based surge pricing");
    createRequest.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(1L, "1.2")));

    Response<StaticRegionBasedConfigurationBatchCreateResponse> createResponse =
        dynamicPricingServiceApi
            .batchCreateStaticRegionBasedConfiguration(TEST_USER_ID, List.of(createRequest))
            .execute();

    assertTrue(createResponse.isSuccessful());
    return createResponse.body().getData().get(0).getId();
  }

  private Long createSurgeComputationModel() throws IOException {
    SurgeComputationModelRequest createRequest = new SurgeComputationModelRequest();
    createRequest.setModelName("Test Surge Model");
    createRequest.setDescription("Test description for surge model");
    createRequest.setEndpointUrl("http://test-endpoint.com/api/surge");
    createRequest.setRequestFieldsMappings(
        List.of(
            new SurgeComputationModelRequestRequestFieldsMappingsInner(
                SurgeComputationModelRequestRequestFieldsMappingsInner.MappingTypeEnum
                    .STATIC_REGION_BASED_CONFIGURATION,
                REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1,
                REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1)));

    Response<SurgeComputationModelResponse> createResponse =
        dynamicPricingServiceApi.createSurgeComputationModel(createRequest, TEST_USER_ID).execute();
    assertTrue(createResponse.isSuccessful());
    return createResponse.body().getData().getId();
  }
}

package integration.tests.surgecomputation.staticregionbasedconfiguration;

import static org.junit.jupiter.api.Assertions.*;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.*;
import integration.IntegrationTestBase;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.jdbc.Sql;
import retrofit2.Response;

@Sql(scripts = "/scripts/Cleanup.sql", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
public class GetStaticRegionBasedConfigurationApiIT extends IntegrationTestBase {

  private static final String TEST_USER_ID = "test-user";
  private static final String VERSION = "1.0";
  private static final String REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1 = "unmet_rate_1";
  private static final String REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_2 = "unmet_rate_2";
  private static final String REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_3 = "unmet_rate_3";
  private Long configId1;
  private Long configId2;
  private Long modelId;

  @BeforeEach
  public void setupTestData() throws IOException {
    // Create surge computation model
    modelId = createSurgeComputationModel();
    // Create first configuration
    configId1 = createRegionBasedConfiguration(REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1, "1.2");
    // Create second configuration
    configId2 = createRegionBasedConfiguration(REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_2, "1.5");
  }

  @Test
  @DisplayName("Should get a static region-based configuration by ID successfully")
  public void testGetStaticRegionBasedConfigurationById_Success() throws IOException {
    // Act
    Response<StaticRegionBasedConfigurationResponse> response =
        dynamicPricingServiceApi.getStaticRegionBasedConfigurationById(configId1).execute();

    // Assert
    assertTrue(response.isSuccessful());
    assertEquals(200, response.code());

    StaticRegionBasedConfigurationResponse responseBody = response.body();
    assertNotNull(responseBody);
    assertNotNull(responseBody.getData());
    assertEquals(configId1, responseBody.getData().getId());
    assertEquals(REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1, responseBody.getData().getName());
    assertEquals(VERSION, responseBody.getData().getVersion());
    assertNotNull(responseBody.getData().getEffectiveFrom());
    assertEquals(
        "configuration for region-based surge pricing", responseBody.getData().getDescription());
    assertEquals(1, responseBody.getData().getRegionValues().size());
    assertEquals(1L, responseBody.getData().getRegionValues().get(0).getRegionId());
    assertEquals("1.2", responseBody.getData().getRegionValues().get(0).getValue());

    assertEquals(TEST_USER_ID, responseBody.getData().getCreatedBy());
    assertEquals(TEST_USER_ID, responseBody.getData().getUpdatedBy());
    assertNotNull(responseBody.getData().getCreatedDate());
    assertNotNull(responseBody.getData().getUpdatedDate());
    assertNotNull(responseBody.getTimestamp());
    assertNotNull(responseBody.getTraceId());
  }

  @Test
  @DisplayName("Should return 404 Not Found when getting a non-existent configuration")
  public void testGetStaticRegionBasedConfigurationById_NotFound() throws IOException {
    // Arrange
    Long nonExistentId = 9999L;

    // Act
    Response<StaticRegionBasedConfigurationResponse> response =
        dynamicPricingServiceApi.getStaticRegionBasedConfigurationById(nonExistentId).execute();

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(404, response.code());
  }

  @Test
  @DisplayName("Should get static region-based configurations by version successfully")
  public void testGetStaticRegionBasedConfigurations_Success() throws IOException {
    // Create third configuration, with the same version, but not be used by the model
    createRegionBasedConfiguration(REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_3, "1.8");

    // Act
    Response<StaticRegionBasedConfigurationListResponse> response =
        dynamicPricingServiceApi.getStaticRegionBasedConfigurations(modelId, VERSION).execute();

    // Assert
    assertTrue(response.isSuccessful());
    assertEquals(200, response.code());

    StaticRegionBasedConfigurationListResponse responseBody = response.body();
    assertNotNull(responseBody);
    assertNotNull(responseBody.getData());

    List<StaticRegionBasedConfiguration> configurations = responseBody.getData();
    assertEquals(
        2, configurations.size()); // Should only return the two we created, without the third one

    // Verify our created configurations are in the list
    boolean foundConfig1 = false;
    boolean foundConfig2 = false;

    for (StaticRegionBasedConfiguration config : configurations) {
      if (config.getId().equals(configId1)) {
        foundConfig1 = true;
        assertEquals(REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1, config.getName());
        assertEquals(1, config.getRegionValues().size());
        assertEquals(1L, config.getRegionValues().get(0).getRegionId());
        assertEquals("1.2", config.getRegionValues().get(0).getValue());
      } else if (config.getId().equals(configId2)) {
        foundConfig2 = true;
        assertEquals(REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_2, config.getName());
        assertEquals(1, config.getRegionValues().size());
        assertEquals(1L, config.getRegionValues().get(0).getRegionId());
        assertEquals("1.5", config.getRegionValues().get(0).getValue());
      }
    }

    assertTrue(foundConfig1, "First configuration not found in the list");
    assertTrue(foundConfig2, "Second configuration not found in the list");

    assertNotNull(responseBody.getTimestamp());
    assertNotNull(responseBody.getTraceId());
  }

  @Test
  @DisplayName("Should return empty list when get static region-based configurations by version")
  public void testGetStaticRegionBasedConfigurations_Empty() throws IOException {
    // Act
    Response<StaticRegionBasedConfigurationListResponse> response =
        dynamicPricingServiceApi.getStaticRegionBasedConfigurations(modelId, "99.0").execute();

    // Assert
    assertTrue(response.isSuccessful());
    assertEquals(200, response.code());

    StaticRegionBasedConfigurationListResponse responseBody = response.body();
    assertNotNull(responseBody);
    assertNotNull(responseBody.getData());

    List<StaticRegionBasedConfiguration> configurations = responseBody.getData();
    assertTrue(configurations.isEmpty()); // At least the two we created
  }

  private Long createSurgeComputationModel() throws IOException {
    SurgeComputationModelRequest createRequest = new SurgeComputationModelRequest();
    createRequest.setModelName("Test Surge Model");
    createRequest.setDescription("Test description for surge model");
    createRequest.setEndpointUrl("http://test-endpoint.com/api/surge");
    createRequest.setRequestFieldsMappings(
        List.of(
            new SurgeComputationModelRequestRequestFieldsMappingsInner(
                SurgeComputationModelRequestRequestFieldsMappingsInner.MappingTypeEnum
                    .STATIC_TIME_BASED_CONFIGURATION,
                "dayOfWeek",
                "peak_hour_surge"),
            new SurgeComputationModelRequestRequestFieldsMappingsInner(
                SurgeComputationModelRequestRequestFieldsMappingsInner.MappingTypeEnum
                    .STATIC_REGION_BASED_CONFIGURATION,
                REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1,
                REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1),
            new SurgeComputationModelRequestRequestFieldsMappingsInner(
                SurgeComputationModelRequestRequestFieldsMappingsInner.MappingTypeEnum
                    .STATIC_REGION_BASED_CONFIGURATION,
                REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_2,
                REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_2)));

    Response<SurgeComputationModelResponse> createResponse =
        dynamicPricingServiceApi.createSurgeComputationModel(createRequest, TEST_USER_ID).execute();
    assertTrue(createResponse.isSuccessful());
    assertEquals(200, createResponse.code());

    return createResponse.body().getData().getId();
  }

  private Long createRegionBasedConfiguration(final String name, final String value)
      throws IOException {
    StaticRegionBasedConfigurationRequest createRequest1 =
        new StaticRegionBasedConfigurationRequest();
    createRequest1.setName(name);
    createRequest1.setVersion(VERSION);
    createRequest1.setEffectiveFrom(OffsetDateTime.now());
    createRequest1.setDescription("configuration for region-based surge pricing");
    createRequest1.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(1L, value)));

    Response<StaticRegionBasedConfigurationBatchCreateResponse> createResponse1 =
        dynamicPricingServiceApi
            .batchCreateStaticRegionBasedConfiguration(TEST_USER_ID, List.of(createRequest1))
            .execute();

    assertTrue(createResponse1.isSuccessful());
    return createResponse1.body().getData().get(0).getId();
  }
}

package integration.tests.surgecomputation.staticregionbasedconfiguration;

import static org.junit.jupiter.api.Assertions.*;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.StaticRegionBasedConfigurationJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.surgecomputation.StaticRegionBasedConfigurationJPARepository;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.*;
import integration.IntegrationTestBase;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;
import retrofit2.Response;

@RequiredArgsConstructor(onConstructor_ = {@Autowired})
@Sql(scripts = "/scripts/Cleanup.sql", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
public class CreateStaticRegionBasedConfigurationApiIT extends IntegrationTestBase {

  private static final String TEST_USER_ID = "test-user";

  private final StaticRegionBasedConfigurationJPARepository staticConfigRepository;

  @Test
  @DisplayName("Should create a static region-based configuration successfully")
  public void testCreateStaticRegionBasedConfiguration_Success() throws IOException {
    // Arrange
    StaticRegionBasedConfigurationRequest request = new StaticRegionBasedConfigurationRequest();
    request.setName("Test Region Config");
    request.setVersion("1.0");
    request.setEffectiveFrom(OffsetDateTime.now());
    request.setDescription("Test configuration for region-based surge pricing");
    request.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(1L, "1.5")));

    // Act
    Response<StaticRegionBasedConfigurationBatchCreateResponse> response =
        dynamicPricingServiceApi
            .batchCreateStaticRegionBasedConfiguration(TEST_USER_ID, List.of(request))
            .execute();

    // Assert
    assertTrue(response.isSuccessful());
    assertEquals(200, response.code());
    assertNotNull(response.body());
    assertNotNull(response.body().getData());
    assertEquals(1, response.body().getData().size());

    StaticRegionBasedConfiguration responseBody = response.body().getData().get(0);
    assertNotNull(responseBody.getId());
    assertEquals("Test Region Config", responseBody.getName());
    assertEquals("1.0", responseBody.getVersion());
    assertNotNull(responseBody.getEffectiveFrom());
    assertEquals(
        "Test configuration for region-based surge pricing", responseBody.getDescription());
    assertEquals(1, responseBody.getRegionValues().size());
    assertEquals(1L, responseBody.getRegionValues().get(0).getRegionId());
    assertEquals("1.5", responseBody.getRegionValues().get(0).getValue());

    assertEquals(TEST_USER_ID, responseBody.getCreatedBy());
    assertEquals(TEST_USER_ID, responseBody.getUpdatedBy());
    assertNotNull(responseBody.getCreatedDate());
    assertNotNull(responseBody.getUpdatedDate());
    assertNotNull(response.body().getTimestamp());
    assertNotNull(response.body().getTraceId());
  }

  @Test
  @DisplayName("Should return 400 Bad Request when the versions are different")
  public void testCreateStaticRegionBasedConfiguration_DifferentVersion() throws IOException {
    OffsetDateTime effectiveFrom = OffsetDateTime.now();
    // Arrange
    StaticRegionBasedConfigurationRequest request = new StaticRegionBasedConfigurationRequest();
    request.setName("Test Region Config");
    request.setVersion("1.0");
    request.setEffectiveFrom(effectiveFrom);
    request.setDescription("Test configuration for region-based surge pricing");
    request.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(1L, "1.5")));
    StaticRegionBasedConfigurationRequest secondRequest =
        new StaticRegionBasedConfigurationRequest();
    secondRequest.setName("Test Second Region Config");
    secondRequest.setVersion("2.0");
    secondRequest.setEffectiveFrom(effectiveFrom);
    secondRequest.setDescription("Test configuration for region-based surge pricing");
    secondRequest.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(1L, "5.5")));

    // Act
    Response<StaticRegionBasedConfigurationBatchCreateResponse> response =
        dynamicPricingServiceApi
            .batchCreateStaticRegionBasedConfiguration(
                TEST_USER_ID, List.of(request, secondRequest))
            .execute();

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(400, response.code());
  }

  @Test
  @DisplayName("Should return 400 Bad Request when the effectiveFrom are different")
  public void testCreateStaticRegionBasedConfiguration_DifferentEffectiveFrom() throws IOException {
    OffsetDateTime effectiveFrom = OffsetDateTime.now();
    // Arrange
    StaticRegionBasedConfigurationRequest request = new StaticRegionBasedConfigurationRequest();
    request.setName("Test Region Config");
    request.setVersion("1.0");
    request.setEffectiveFrom(effectiveFrom);
    request.setDescription("Test configuration for region-based surge pricing");
    request.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(1L, "1.5")));
    StaticRegionBasedConfigurationRequest secondRequest =
        new StaticRegionBasedConfigurationRequest();
    secondRequest.setName("Test Second Region Config");
    secondRequest.setVersion("1.0");
    secondRequest.setEffectiveFrom(effectiveFrom.plusDays(1));
    secondRequest.setDescription("Test configuration for region-based surge pricing");
    secondRequest.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(1L, "5.5")));

    // Act
    Response<StaticRegionBasedConfigurationBatchCreateResponse> response =
        dynamicPricingServiceApi
            .batchCreateStaticRegionBasedConfiguration(
                TEST_USER_ID, List.of(request, secondRequest))
            .execute();

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(400, response.code());
  }

  @Test
  @DisplayName("Should return 400 Bad Request when the effectiveTo are different")
  public void testCreateStaticRegionBasedConfiguration_DifferentEffectiveTo() throws IOException {
    OffsetDateTime effectiveFrom = OffsetDateTime.now();
    // Arrange
    StaticRegionBasedConfigurationRequest request = new StaticRegionBasedConfigurationRequest();
    request.setName("Test Region Config");
    request.setVersion("1.0");
    request.setEffectiveFrom(effectiveFrom);
    request.setEffectiveTo(effectiveFrom.plusDays(1));
    request.setDescription("Test configuration for region-based surge pricing");
    request.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(1L, "1.5")));
    StaticRegionBasedConfigurationRequest secondRequest =
        new StaticRegionBasedConfigurationRequest();
    secondRequest.setName("Test Second Region Config");
    secondRequest.setVersion("1.0");
    secondRequest.setEffectiveFrom(effectiveFrom);
    secondRequest.setDescription("Test configuration for region-based surge pricing");
    secondRequest.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(1L, "5.5")));

    // Act
    Response<StaticRegionBasedConfigurationBatchCreateResponse> response =
        dynamicPricingServiceApi
            .batchCreateStaticRegionBasedConfiguration(
                TEST_USER_ID, List.of(request, secondRequest))
            .execute();

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(400, response.code());
  }

  @Test
  @DisplayName("Should return 400 Bad Request when name is missing")
  public void testCreateStaticRegionBasedConfiguration_MissingName() throws IOException {
    // Arrange
    StaticRegionBasedConfigurationRequest request = new StaticRegionBasedConfigurationRequest();
    request.setVersion("1.0");
    request.setEffectiveFrom(OffsetDateTime.now());
    request.setDescription("Test configuration");
    request.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(1L, "1.5")));

    // Act
    Response<StaticRegionBasedConfigurationBatchCreateResponse> response =
        dynamicPricingServiceApi
            .batchCreateStaticRegionBasedConfiguration(TEST_USER_ID, List.of(request))
            .execute();

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(400, response.code());
  }

  @Test
  @DisplayName("Should return 400 Bad Request when version is missing")
  public void testCreateStaticRegionBasedConfiguration_MissingVersion() throws IOException {
    // Arrange
    StaticRegionBasedConfigurationRequest request = new StaticRegionBasedConfigurationRequest();
    request.setName("Test Region Config");
    request.setEffectiveFrom(OffsetDateTime.now());
    request.setDescription("Test configuration");
    request.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(1L, "1.5")));

    // Act
    Response<StaticRegionBasedConfigurationBatchCreateResponse> response =
        dynamicPricingServiceApi
            .batchCreateStaticRegionBasedConfiguration(TEST_USER_ID, List.of(request))
            .execute();

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(400, response.code());
  }

  @Test
  @DisplayName("Should return 400 Bad Request when effective_from is missing")
  public void testCreateStaticRegionBasedConfiguration_MissingEffectiveFrom() throws IOException {
    // Arrange
    StaticRegionBasedConfigurationRequest request = new StaticRegionBasedConfigurationRequest();
    request.setName("Test Region Config");
    request.setVersion("1.0");
    request.setDescription("Test configuration");
    request.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(1L, "1.5")));

    // Act
    Response<StaticRegionBasedConfigurationBatchCreateResponse> response =
        dynamicPricingServiceApi
            .batchCreateStaticRegionBasedConfiguration(TEST_USER_ID, List.of(request))
            .execute();

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(400, response.code());
  }

  @Test
  @DisplayName("Should return 400 Bad Request when region_values is missing")
  public void testCreateStaticRegionBasedConfiguration_MissingRegionValues() throws IOException {
    // Arrange
    StaticRegionBasedConfigurationRequest request = new StaticRegionBasedConfigurationRequest();
    request.setName("Test Region Config");
    request.setVersion("1.0");
    request.setEffectiveFrom(OffsetDateTime.now());
    request.setDescription("Test configuration");

    // Act
    Response<StaticRegionBasedConfigurationBatchCreateResponse> response =
        dynamicPricingServiceApi
            .batchCreateStaticRegionBasedConfiguration(TEST_USER_ID, List.of(request))
            .execute();

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(400, response.code());
  }

  @Test
  @DisplayName("Should return 400 Bad Request when X-User-Id header is missing")
  public void testCreateStaticRegionBasedConfiguration_MissingUserIdHeader() throws IOException {
    // Arrange
    StaticRegionBasedConfigurationRequest request = new StaticRegionBasedConfigurationRequest();
    request.setName("Test Region Config");
    request.setVersion("1.0");
    request.setEffectiveFrom(OffsetDateTime.now());
    request.setDescription("Test configuration");
    request.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(1L, "1.5")));

    // Act
    Response<StaticRegionBasedConfigurationBatchCreateResponse> response =
        dynamicPricingServiceApi
            .batchCreateStaticRegionBasedConfiguration(null, List.of(request))
            .execute();

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(400, response.code());
  }

  @Test
  @DisplayName("Should reject configuration with overlapping effective date ranges")
  public void testCreateStaticRegionBasedConfiguration_OverlappingDateRanges() throws IOException {
    // Arrange - Create first configuration
    OffsetDateTime now = OffsetDateTime.now();
    OffsetDateTime later = now.plusDays(30);

    StaticRegionBasedConfigurationRequest request1 = new StaticRegionBasedConfigurationRequest();
    request1.setName("Overlap Test Config");
    request1.setVersion("1.0");
    request1.setEffectiveFrom(now);
    request1.setEffectiveTo(later);
    request1.setDescription("First configuration");
    request1.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(1L, "1.5")));

    // Act - Create first configuration
    Response<StaticRegionBasedConfigurationBatchCreateResponse> response1 =
        dynamicPricingServiceApi
            .batchCreateStaticRegionBasedConfiguration(TEST_USER_ID, List.of(request1))
            .execute();

    // Assert - First configuration should be created successfully
    assertTrue(response1.isSuccessful());
    assertEquals(200, response1.code());

    // Arrange - Create second configuration with overlapping date range
    StaticRegionBasedConfigurationRequest request2 = new StaticRegionBasedConfigurationRequest();
    request2.setName("Overlap Test Config"); // Same name
    request2.setVersion("2.0"); // Different version
    request2.setEffectiveFrom(now.plusDays(15)); // Overlaps with first configuration
    request2.setEffectiveTo(later.plusDays(30));
    request2.setDescription("Second configuration with overlapping date range");
    request2.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(1L, "2.0")));

    // Act - Try to create second configuration with overlapping date range
    Response<StaticRegionBasedConfigurationBatchCreateResponse> response2 =
        dynamicPricingServiceApi
            .batchCreateStaticRegionBasedConfiguration(TEST_USER_ID, List.of(request2))
            .execute();

    // Assert - Second configuration should be rejected
    assertFalse(response2.isSuccessful());
    assertEquals(400, response2.code()); // Expecting a 400 Bad Request due to constraint violation
  }

  @Test
  @DisplayName("Should return 400 Bad Request with batch create")
  public void testCreateStaticRegionBasedConfiguration_DifferentVersion_Batch() throws IOException {
    // Arrange - Create first configuration
    OffsetDateTime now = OffsetDateTime.now();
    OffsetDateTime later = now.plusDays(30);

    StaticRegionBasedConfigurationRequest request1 = new StaticRegionBasedConfigurationRequest();
    request1.setName("Overlap Test Config");
    request1.setVersion("1.0");
    request1.setEffectiveFrom(now);
    request1.setEffectiveTo(later);
    request1.setDescription("First configuration");
    request1.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(1L, "1.5")));

    // Arrange - Create second configuration with different version
    StaticRegionBasedConfigurationRequest request2 = new StaticRegionBasedConfigurationRequest();
    request2.setName("Overlap Test Config 2");
    request2.setVersion("3.0"); // Different version
    request2.setEffectiveFrom(now);
    request2.setEffectiveTo(later);
    request2.setDescription("Second configuration with overlapping date range");
    request2.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(1L, "2.0")));

    // Act - Try to create second configuration with overlapping date range
    Response<StaticRegionBasedConfigurationBatchCreateResponse> response =
        dynamicPricingServiceApi
            .batchCreateStaticRegionBasedConfiguration(TEST_USER_ID, List.of(request1, request2))
            .execute();

    // Assert - Second configuration should be rejected
    assertFalse(response.isSuccessful());
    assertEquals(400, response.code()); // Expecting a 400 Bad Request due to constraint violation
  }

  @Test
  public void shouldSuccessWithOverlappingPeriods_whenPreviousVersionEffectiveToIsNull()
      throws IOException {
    // Arrange - Create first configuration
    String name1 = "Overlap Test Config1";
    String name2 = "Overlap Test Config2";

    OffsetDateTime now = OffsetDateTime.now();
    OffsetDateTime overlappingFrom = now.plusDays(10);

    create2ConfigurationsWithEffectiveToIsNull(name1, name2, now, "1.0");

    // Arrange - Create configurations with overlapping date range
    create2ConfigurationsWithEffectiveToIsNull(name1, name2, overlappingFrom, "2.0");

    List<StaticRegionBasedConfigurationJPA> configurations =
        staticConfigRepository.findAllByVersionAndNameIn("1.0", List.of(name1, name2));

    assertEquals(2, configurations.size());

    for (final StaticRegionBasedConfigurationJPA configuration : configurations) {
      assertNotNull(configuration.getEffectiveTo());
      OffsetDateTime expected = overlappingFrom.minusSeconds(1);
      OffsetDateTime actual =
          configuration.getEffectiveTo().atZone(ZoneOffset.systemDefault()).toOffsetDateTime();

      // Handle timestamp precision differences between Java and PostgreSQL
      // Use a tolerance-based comparison instead of exact equality
      long expectedEpochMicros =
          expected.toInstant().toEpochMilli() * 1000 + expected.toInstant().getNano() / 1000;
      long actualEpochMicros =
          actual.toInstant().toEpochMilli() * 1000 + actual.toInstant().getNano() / 1000;

      assertTrue(
          Math.abs(expectedEpochMicros - actualEpochMicros) <= 1,
          String.format(
              "Timestamp difference too large. Expected: %s, Actual: %s", expected, actual));
    }
  }

  private void create2ConfigurationsWithEffectiveToIsNull(
      final String name1, final String name2, final OffsetDateTime now, final String version)
      throws IOException {
    StaticRegionBasedConfigurationRequest request1 = new StaticRegionBasedConfigurationRequest();
    request1.setName(name1);
    request1.setVersion(version);
    request1.setEffectiveFrom(now);
    request1.setDescription("First configuration");
    request1.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(1L, "1.5")));

    StaticRegionBasedConfigurationRequest request2 = new StaticRegionBasedConfigurationRequest();
    request2.setName(name2);
    request2.setVersion(version);
    request2.setEffectiveFrom(now);
    request2.setDescription("Second configuration");
    request2.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(1L, "2.0")));

    Response<StaticRegionBasedConfigurationBatchCreateResponse> response =
        dynamicPricingServiceApi
            .batchCreateStaticRegionBasedConfiguration(TEST_USER_ID, List.of(request1, request2))
            .execute();

    assertTrue(response.isSuccessful());
  }
}

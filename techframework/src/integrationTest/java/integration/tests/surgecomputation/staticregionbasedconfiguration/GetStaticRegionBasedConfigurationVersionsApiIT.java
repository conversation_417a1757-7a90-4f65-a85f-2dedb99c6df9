package integration.tests.surgecomputation.staticregionbasedconfiguration;

import static org.junit.jupiter.api.Assertions.*;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.*;
import integration.IntegrationTestBase;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.jdbc.Sql;
import retrofit2.Response;

@Sql(scripts = "/scripts/Cleanup.sql", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
public class GetStaticRegionBasedConfigurationVersionsApiIT extends IntegrationTestBase {

  private static final String TEST_USER_ID = "test-user";
  private static final String REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1 = "unmet_rate_1";
  private static final String REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_2 = "unmet_rate_2";

  @Test
  public void testGetStaticRegionBasedConfigurationVersions_Success() throws IOException {
    OffsetDateTime now = OffsetDateTime.now();
    OffsetDateTime effectiveFrom = now.minusDays(20);
    OffsetDateTime effectiveTo = now.minusDays(10);
    OffsetDateTime secondEffectiveFrom = now.minusDays(1);

    // Create surge computation model
    Long modelId = createSurgeComputationModel();

    // Arrange
    StaticRegionBasedConfigurationRequest request = new StaticRegionBasedConfigurationRequest();
    request.setName(REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1);
    request.setVersion("1.0");
    request.setEffectiveFrom(effectiveFrom);
    request.setEffectiveTo(effectiveTo);
    request.setDescription("Test configuration for region-based surge pricing");
    request.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(1L, "1.5")));

    StaticRegionBasedConfigurationRequest secondRequest =
        new StaticRegionBasedConfigurationRequest();
    secondRequest.setName(REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_2);
    secondRequest.setVersion("2.0");
    secondRequest.setEffectiveFrom(secondEffectiveFrom);
    secondRequest.setDescription("Test configuration for region-based surge pricing");
    secondRequest.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(1L, "5.5")));

    // Act
    Response<StaticRegionBasedConfigurationBatchCreateResponse> response =
        dynamicPricingServiceApi
            .batchCreateStaticRegionBasedConfiguration(TEST_USER_ID, List.of(request))
            .execute();
    Response<StaticRegionBasedConfigurationBatchCreateResponse> secondResponse =
        dynamicPricingServiceApi
            .batchCreateStaticRegionBasedConfiguration(TEST_USER_ID, List.of(secondRequest))
            .execute();

    assertTrue(response.isSuccessful());
    assertTrue(secondResponse.isSuccessful());

    // Get all versions
    Response<StaticBasedConfigurationVersionListResponse> versionResponse =
        dynamicPricingServiceApi.getStaticRegionBasedConfigurationVersions(modelId).execute();

    assertTrue(versionResponse.isSuccessful());
    assertEquals(200, versionResponse.code());

    StaticBasedConfigurationVersionListResponse versionResponseBody = versionResponse.body();
    assertNotNull(versionResponseBody);
    assertNotNull(versionResponseBody.getData());
    assertEquals(2, versionResponseBody.getData().size());
    assertEquals("2.0", versionResponseBody.getData().get(0).getVersion());
    assertEquals(true, versionResponseBody.getData().get(0).getIsInUse());
  }

  @Test
  public void testGetStaticRegionBasedConfigurationVersions_Success_withNoEffectiveVersion()
      throws IOException {
    OffsetDateTime now = OffsetDateTime.now();
    OffsetDateTime effectiveFrom = now.minusDays(20);
    OffsetDateTime effectiveTo = now.minusDays(10);
    OffsetDateTime secondEffectiveFrom = now.plusDays(1);

    // Create surge computation model
    Long modelId = createSurgeComputationModel();

    // Arrange
    StaticRegionBasedConfigurationRequest request = new StaticRegionBasedConfigurationRequest();
    request.setName(REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1);
    request.setVersion("1.0");
    request.setEffectiveFrom(effectiveFrom);
    request.setEffectiveTo(effectiveTo);
    request.setDescription("Test configuration for region-based surge pricing");
    request.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(1L, "1.5")));

    StaticRegionBasedConfigurationRequest secondRequest =
        new StaticRegionBasedConfigurationRequest();
    secondRequest.setName(REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_2);
    secondRequest.setVersion("2.0");
    secondRequest.setEffectiveFrom(secondEffectiveFrom);
    secondRequest.setDescription("Test configuration for region-based surge pricing");
    secondRequest.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(1L, "5.5")));

    // Act
    Response<StaticRegionBasedConfigurationBatchCreateResponse> response =
        dynamicPricingServiceApi
            .batchCreateStaticRegionBasedConfiguration(TEST_USER_ID, List.of(request))
            .execute();
    Response<StaticRegionBasedConfigurationBatchCreateResponse> secondResponse =
        dynamicPricingServiceApi
            .batchCreateStaticRegionBasedConfiguration(TEST_USER_ID, List.of(secondRequest))
            .execute();

    assertTrue(response.isSuccessful());
    assertTrue(secondResponse.isSuccessful());

    // Get all versions
    Response<StaticBasedConfigurationVersionListResponse> versionResponse =
        dynamicPricingServiceApi.getStaticRegionBasedConfigurationVersions(modelId).execute();

    assertTrue(versionResponse.isSuccessful());
    assertEquals(200, versionResponse.code());

    StaticBasedConfigurationVersionListResponse versionResponseBody = versionResponse.body();
    assertNotNull(versionResponseBody);
    assertNotNull(versionResponseBody.getData());
    assertEquals(2, versionResponseBody.getData().size());
    assertEquals("2.0", versionResponseBody.getData().get(0).getVersion());
    assertEquals(false, versionResponseBody.getData().get(0).getIsInUse());
    assertEquals(true, versionResponseBody.getData().get(1).getIsInUse());
  }

  @Test
  public void testGetStaticRegionBasedConfigurationVersions_NotFound() throws IOException {
    OffsetDateTime now = OffsetDateTime.now();
    OffsetDateTime effectiveFrom = now.minusDays(20);
    OffsetDateTime effectiveTo = now.minusDays(10);
    OffsetDateTime secondEffectiveFrom = now.minusDays(1);

    // Arrange
    StaticRegionBasedConfigurationRequest request = new StaticRegionBasedConfigurationRequest();
    request.setName(REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1);
    request.setVersion("1.0");
    request.setEffectiveFrom(effectiveFrom);
    request.setEffectiveTo(effectiveTo);
    request.setDescription("Test configuration for region-based surge pricing");
    request.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(1L, "1.5")));

    StaticRegionBasedConfigurationRequest secondRequest =
        new StaticRegionBasedConfigurationRequest();
    secondRequest.setName(REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_2);
    secondRequest.setVersion("2.0");
    secondRequest.setEffectiveFrom(secondEffectiveFrom);
    secondRequest.setDescription("Test configuration for region-based surge pricing");
    secondRequest.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(1L, "5.5")));

    // Act
    Response<StaticRegionBasedConfigurationBatchCreateResponse> response =
        dynamicPricingServiceApi
            .batchCreateStaticRegionBasedConfiguration(TEST_USER_ID, List.of(request))
            .execute();
    Response<StaticRegionBasedConfigurationBatchCreateResponse> secondResponse =
        dynamicPricingServiceApi
            .batchCreateStaticRegionBasedConfiguration(TEST_USER_ID, List.of(secondRequest))
            .execute();

    assertTrue(response.isSuccessful());
    assertTrue(secondResponse.isSuccessful());

    // Get all versions
    Response<StaticBasedConfigurationVersionListResponse> versionResponse =
        dynamicPricingServiceApi.getStaticRegionBasedConfigurationVersions(999L).execute();

    assertFalse(versionResponse.isSuccessful());
    assertEquals(404, versionResponse.code());
  }

  private Long createSurgeComputationModel() throws IOException {
    SurgeComputationModelRequest createRequest = new SurgeComputationModelRequest();
    createRequest.setModelName("Test Surge Model");
    createRequest.setDescription("Test description for surge model");
    createRequest.setEndpointUrl("http://test-endpoint.com/api/surge");
    createRequest.setRequestFieldsMappings(
        List.of(
            new SurgeComputationModelRequestRequestFieldsMappingsInner(
                SurgeComputationModelRequestRequestFieldsMappingsInner.MappingTypeEnum
                    .STATIC_TIME_BASED_CONFIGURATION,
                "dayOfWeek",
                "peak_hour_surge"),
            new SurgeComputationModelRequestRequestFieldsMappingsInner(
                SurgeComputationModelRequestRequestFieldsMappingsInner.MappingTypeEnum
                    .STATIC_REGION_BASED_CONFIGURATION,
                REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1,
                REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1),
            new SurgeComputationModelRequestRequestFieldsMappingsInner(
                SurgeComputationModelRequestRequestFieldsMappingsInner.MappingTypeEnum
                    .STATIC_REGION_BASED_CONFIGURATION,
                REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_2,
                REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_2)));

    Response<SurgeComputationModelResponse> createResponse =
        dynamicPricingServiceApi.createSurgeComputationModel(createRequest, TEST_USER_ID).execute();
    assertTrue(createResponse.isSuccessful());
    assertEquals(200, createResponse.code());

    return createResponse.body().getData().getId();
  }
}

package integration.tests.surgecomputation.staticregionbasedconfiguration;

import static org.junit.jupiter.api.Assertions.*;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.*;
import integration.IntegrationTestBase;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;
import retrofit2.Response;

@RequiredArgsConstructor(onConstructor_ = {@Autowired})
@Sql(scripts = "/scripts/Cleanup.sql", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
public class StaticRegionBasedConfigurationEffectiveCheckApiIT extends IntegrationTestBase {

  private static final String TEST_USER_ID = "test-user";
  private static final String VERSION = "1.0";
  private static final String REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1 = "unmet_rate_1";
  private static final String REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_2 = "unmet_rate_2";

  @Test
  public void shouldNotWarning_whenHasEffectiveConfig_withEffectiveIsNull() throws IOException {
    Long modelId = createModel();
    createStaticRegionBasedConfiguration(OffsetDateTime.now(), null);

    Response<StaticBasedConfigurationEffectiveCheckResponse> checkResponse =
        dynamicPricingServiceApi.staticRegionBasedConfigurationEffectiveCheck(modelId).execute();

    assertTrue(checkResponse.isSuccessful());
    assertNotNull(checkResponse.body());
    assertFalse(checkResponse.body().getIsWarning());
    assertTrue(checkResponse.body().getIsEffective());
    assertEquals(VERSION, checkResponse.body().getVersion());
    assertNotNull(checkResponse.body().getEffectiveFrom());
    assertNull(checkResponse.body().getEffectiveTo());
  }

  @Test
  public void shouldNotWarning_whenHasEffectiveConfig_withEffectiveIsNotNull() throws IOException {
    Long modelId = createModel();
    OffsetDateTime now = OffsetDateTime.now();
    createStaticRegionBasedConfiguration(now, now.plusDays(10));

    Response<StaticBasedConfigurationEffectiveCheckResponse> checkResponse =
        dynamicPricingServiceApi.staticRegionBasedConfigurationEffectiveCheck(modelId).execute();

    assertTrue(checkResponse.isSuccessful());
    assertNotNull(checkResponse.body());
    assertFalse(checkResponse.body().getIsWarning());
    assertTrue(checkResponse.body().getIsEffective());
    assertEquals(VERSION, checkResponse.body().getVersion());
    assertNotNull(checkResponse.body().getEffectiveFrom());
    assertNotNull(checkResponse.body().getEffectiveTo());
  }

  @Test
  public void shouldWarning_whenNoEffectiveConfig_withOutOfDate() throws IOException {
    Long modelId = createModel();
    OffsetDateTime now = OffsetDateTime.now();
    createStaticRegionBasedConfiguration(now.minusDays(1), now.minusSeconds(10));

    Response<StaticBasedConfigurationEffectiveCheckResponse> checkResponse =
        dynamicPricingServiceApi.staticRegionBasedConfigurationEffectiveCheck(modelId).execute();

    assertTrue(checkResponse.isSuccessful());
    assertNotNull(checkResponse.body());
    assertTrue(checkResponse.body().getIsWarning());
    assertFalse(checkResponse.body().getIsEffective());
    assertEquals(VERSION, checkResponse.body().getVersion());
    assertNotNull(checkResponse.body().getEffectiveFrom());
    assertNotNull(checkResponse.body().getEffectiveTo());
  }

  @Test
  public void shouldWarning_whenNoEffectiveConfig_withCloseToExpiration() throws IOException {
    Long modelId = createModel();
    OffsetDateTime now = OffsetDateTime.now();
    createStaticRegionBasedConfiguration(now.minusDays(1), now.plusDays(1));

    Response<StaticBasedConfigurationEffectiveCheckResponse> checkResponse =
        dynamicPricingServiceApi.staticRegionBasedConfigurationEffectiveCheck(modelId).execute();

    assertTrue(checkResponse.isSuccessful());
    assertNotNull(checkResponse.body());
    assertTrue(checkResponse.body().getIsWarning());
    assertTrue(checkResponse.body().getIsEffective());
    assertEquals(VERSION, checkResponse.body().getVersion());
    assertNotNull(checkResponse.body().getEffectiveFrom());
    assertNotNull(checkResponse.body().getEffectiveTo());
  }

  @Test
  public void shouldWarning_whenNoEffectiveConfig_withNoAnyConfig() throws IOException {
    Long modelId = createModel();
    Response<StaticBasedConfigurationEffectiveCheckResponse> checkResponse =
        dynamicPricingServiceApi.staticRegionBasedConfigurationEffectiveCheck(modelId).execute();

    assertTrue(checkResponse.isSuccessful());
    assertNotNull(checkResponse.body());
    assertFalse(checkResponse.body().getIsWarning());
    assertFalse(checkResponse.body().getIsEffective());
    assertNull(checkResponse.body().getVersion());
    assertNull(checkResponse.body().getEffectiveFrom());
    assertNull(checkResponse.body().getEffectiveTo());
  }

  private Long createModel() throws IOException {
    SurgeComputationModelRequest request = new SurgeComputationModelRequest();
    request.setModelName("Test Surge Model");
    request.setDescription("Test description for surge model");
    request.setEndpointUrl("http://test-endpoint.com/api/surge");
    request.setRequestFieldsMappings(
        List.of(
            new SurgeComputationModelRequestRequestFieldsMappingsInner(
                SurgeComputationModelRequestRequestFieldsMappingsInner.MappingTypeEnum
                    .STATIC_REGION_BASED_CONFIGURATION,
                REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1,
                REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1),
            new SurgeComputationModelRequestRequestFieldsMappingsInner(
                SurgeComputationModelRequestRequestFieldsMappingsInner.MappingTypeEnum
                    .STATIC_REGION_BASED_CONFIGURATION,
                REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_2,
                REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_2)));

    // Act
    Response<SurgeComputationModelResponse> response =
        dynamicPricingServiceApi.createSurgeComputationModel(request, TEST_USER_ID).execute();

    // Assert
    assertTrue(response.isSuccessful());
    assertEquals(200, response.code());
    assertNotNull(response.body());

    return response.body().getData().getId();
  }

  private void createStaticRegionBasedConfiguration(
      final OffsetDateTime effectiveFrom, final OffsetDateTime effectiveTo) throws IOException {

    StaticRegionBasedConfigurationRequest request = new StaticRegionBasedConfigurationRequest();
    request.setName(REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1);
    request.setVersion(VERSION);
    request.setEffectiveFrom(effectiveFrom);
    request.setEffectiveTo(effectiveTo);
    request.setDescription("Test configuration for region-based surge pricing");
    request.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(1L, "1.5")));

    StaticRegionBasedConfigurationRequest request2 = new StaticRegionBasedConfigurationRequest();
    request2.setName(REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_2);
    request2.setVersion(VERSION);
    request2.setEffectiveFrom(effectiveFrom);
    request2.setEffectiveTo(effectiveTo);
    request2.setDescription("Test configuration for region-based surge pricing");
    request2.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(1L, "5.5")));

    // Act
    Response<StaticRegionBasedConfigurationBatchCreateResponse> response =
        dynamicPricingServiceApi
            .batchCreateStaticRegionBasedConfiguration(TEST_USER_ID, List.of(request, request2))
            .execute();

    assertTrue(response.isSuccessful());
  }
}

package integration.tests.surgecomputation.machinelearningdataaggregation;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.impl.MlGetFareRequestAggStatsServiceImpl;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.RequestCountConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.GetFareCountEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.SurgeAreaTypeEnum;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.adapter.RequestCounterServiceAdapter;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.MlGetFareRequestAggStatsJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.surgecomputation.MlGetFareRequestAggStatsJPARepository;
import integration.IntegrationTestBase;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@RequiredArgsConstructor(onConstructor_ = {@Autowired})
@Sql(scripts = "/scripts/Cleanup.sql", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
public class AggregateGetFareCountEveryMinuteIT extends IntegrationTestBase {

  private final RequestCounterServiceAdapter requestCounterService;
  private final MlGetFareRequestAggStatsJPARepository mlGetFareRequestAggStatsJPARepository;
  private final MlGetFareRequestAggStatsServiceImpl mlGetFareRequestAggStatsService;

  @Test
  void shouldAggregateGetFareCountEveryMinuteSuccess() throws Exception {
    final Instant now = Instant.now();
    final Instant endTime = now.truncatedTo(ChronoUnit.MINUTES).minus(1, ChronoUnit.MINUTES);
    final Instant startTime = endTime.minus(1, ChronoUnit.MINUTES);

    // region 1 and model 1
    recordGetFareCount(1L, 1L, startTime.plusSeconds(1L));
    recordGetFareCount(1L, 1L, startTime.plusSeconds(2L));
    // region 2 and model 1
    recordGetFareCount(2L, 1L, startTime.plusSeconds(3L));
    recordGetFareCount(2L, 1L, startTime.plusSeconds(4L));
    recordGetFareCount(2L, 1L, startTime.plusSeconds(5L));

    // zone based
    recordGetFareCount(1L, null, startTime.plusSeconds(6L));
    recordGetFareCount(1L, null, startTime.plusSeconds(7L));

    // Here manually trigger sync to Redis, instead of scheduled task.
    requestCounterService.syncLocalDataToRedis();

    mlGetFareRequestAggStatsService.aggregateGetFareCountEveryMinute(startTime, endTime);

    List<MlGetFareRequestAggStatsJPA> list = mlGetFareRequestAggStatsJPARepository.findAll();
    assertEquals(3, list.size());
    for (MlGetFareRequestAggStatsJPA entity : list) {
      if (SurgeAreaTypeEnum.REGION.getValue().equals(entity.getAreaType())
          && entity.getPickupRegionId() == 1L) {
        assertEquals(2, entity.getGetFareCount());
      } else if (SurgeAreaTypeEnum.REGION.getValue().equals(entity.getAreaType())
          && entity.getPickupRegionId() == 2L) {
        assertEquals(3, entity.getGetFareCount());
      } else if (SurgeAreaTypeEnum.ZONE.getValue().equals(entity.getAreaType())
          && entity.getPickupRegionId() == 1L) {
        assertEquals(2, entity.getGetFareCount());
        assertNull(entity.getModelId());
      }
    }
  }

  private void recordGetFareCount(
      final Long regionId, final Long modelId, final Instant requestTime) {
    SurgeAreaTypeEnum region;
    if (modelId == null) {
      region = SurgeAreaTypeEnum.ZONE;
    } else {
      region = SurgeAreaTypeEnum.REGION;
    }
    GetFareCountEntity getFareCountEntity =
        new GetFareCountEntity(region, regionId, "0.0.1", modelId, requestTime);

    requestCounterService.recordEndpointRequest(
        RequestCountConstant.MULTI_FARE, getFareCountEntity);
  }
}

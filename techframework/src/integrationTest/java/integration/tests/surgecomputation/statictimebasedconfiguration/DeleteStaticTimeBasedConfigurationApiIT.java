package integration.tests.surgecomputation.statictimebasedconfiguration;

import static org.junit.jupiter.api.Assertions.*;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.StaticTimeBasedConfigurationCreateResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.StaticTimeBasedConfigurationRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.StaticTimeBasedConfigurationRequestAppliedHoursInner;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.StaticTimeBasedConfigurationResponse;
import integration.IntegrationTestBase;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.jdbc.Sql;
import retrofit2.Response;

@Sql(scripts = "/scripts/Cleanup.sql", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
public class DeleteStaticTimeBasedConfigurationApiIT extends IntegrationTestBase {

  private static final String TEST_USER_ID = "test-user";
  private Long configurationId;

  @BeforeEach
  public void setupTest() throws IOException {
    // Create a configuration to delete in the tests
    StaticTimeBasedConfigurationRequest configRequest = new StaticTimeBasedConfigurationRequest();
    configRequest.setName("Config to Delete");
    configRequest.setVersion("1.0");
    configRequest.setEffectiveFrom(OffsetDateTime.now());
    configRequest.setDescription("Configuration to be deleted");
    configRequest.setTimeZoneOffset("+08:00");

    // Create applied hours
    List<StaticTimeBasedConfigurationRequestAppliedHoursInner> appliedHours =
        List.of(
            new StaticTimeBasedConfigurationRequestAppliedHoursInner(
                StaticTimeBasedConfigurationRequestAppliedHoursInner.DayOfWeekEnum.MON, 8, "1.5"));

    configRequest.setAppliedHours(appliedHours);

    Response<StaticTimeBasedConfigurationCreateResponse> configResponse =
        dynamicPricingServiceApi
            .batchCreateStaticTimeBasedConfiguration(List.of(configRequest), TEST_USER_ID)
            .execute();

    assertTrue(configResponse.isSuccessful());
    assertNotNull(configResponse.body());
    configurationId = configResponse.body().getData().get(0).getId();
  }

  @Test
  @DisplayName("Should delete a static time-based configuration successfully")
  public void testDeleteStaticTimeBasedConfiguration_Success() throws IOException {
    // Act
    Response<Void> response =
        dynamicPricingServiceApi
            .deleteStaticTimeBasedConfiguration(configurationId, TEST_USER_ID)
            .execute();

    // Assert
    assertTrue(response.isSuccessful());
    assertEquals(204, response.code());

    // Verify the configuration is deleted by trying to get it
    Response<StaticTimeBasedConfigurationResponse> getResponse =
        dynamicPricingServiceApi.getStaticTimeBasedConfigurationById(configurationId).execute();

    assertFalse(getResponse.isSuccessful());
    assertEquals(404, getResponse.code());
  }

  @Test
  @DisplayName("Should return 404 Not Found when deleting a non-existent configuration")
  public void testDeleteStaticTimeBasedConfiguration_NotFound() throws IOException {
    // Arrange
    Long nonExistentId = 999999L;

    // Act
    Response<Void> response =
        dynamicPricingServiceApi
            .deleteStaticTimeBasedConfiguration(nonExistentId, TEST_USER_ID)
            .execute();

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(404, response.code());
  }

  @Test
  @DisplayName("Should return 400 Bad Request when X-User-Id header is missing")
  public void testDeleteStaticTimeBasedConfiguration_MissingUserIdHeader() throws IOException {
    // Act
    Response<Void> response =
        dynamicPricingServiceApi
            .deleteStaticTimeBasedConfiguration(configurationId, null)
            .execute();

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(400, response.code());
  }

  @Test
  @DisplayName("Should not be able to delete a configuration twice")
  public void testDeleteStaticTimeBasedConfiguration_DeleteTwice() throws IOException {
    // First delete
    Response<Void> firstResponse =
        dynamicPricingServiceApi
            .deleteStaticTimeBasedConfiguration(configurationId, TEST_USER_ID)
            .execute();

    assertTrue(firstResponse.isSuccessful());
    assertEquals(204, firstResponse.code());

    // Second delete attempt
    Response<Void> secondResponse =
        dynamicPricingServiceApi
            .deleteStaticTimeBasedConfiguration(configurationId, TEST_USER_ID)
            .execute();

    assertFalse(secondResponse.isSuccessful());
    assertEquals(404, secondResponse.code());
  }
}

package integration.tests.surgecomputation.statictimebasedconfiguration;

import static org.junit.jupiter.api.Assertions.*;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.StaticTimeBasedConfigurationJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.surgecomputation.StaticTimeBasedConfigurationJPARepository;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.CurrentStaticTimeBasedConfigurationValue;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.CurrentStaticTimeBasedConfigurationValueResponse;
import integration.IntegrationTestBase;
import java.io.IOException;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.List;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;
import retrofit2.Response;

/**
 * Integration tests for the Get Current Static Time Based Configuration Value API. Tests the
 * /v1.0/surge-computation/static-time-based-configurations/current-value endpoint.
 */
@Sql(scripts = "/scripts/Cleanup.sql", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
public class GetCurrentStaticTimeBasedConfigurationValueApiIT extends IntegrationTestBase {

  @Autowired private StaticTimeBasedConfigurationJPARepository staticConfigRepository;

  private static final String VERSION = "1.0";
  private static final String SINGAPORE_TIMEZONE = "+08:00";

  @Test
  @DisplayName("Should get current static time-based configuration values successfully")
  public void testGetCurrentStaticTimeBasedConfigurationValue_Success() throws IOException {
    // Arrange - Create test configurations that are currently effective
    OffsetDateTime now = OffsetDateTime.now(ZoneOffset.UTC);
    OffsetDateTime effectiveFrom = now.minusDays(1); // Started yesterday
    OffsetDateTime effectiveTo = now.plusDays(1); // Ends tomorrow

    createTestConfiguration("surge_multiplier", effectiveFrom, effectiveTo);
    createTestConfiguration("base_fare_adjustment", effectiveFrom, effectiveTo);

    // Act
    Response<CurrentStaticTimeBasedConfigurationValueResponse> response =
        dynamicPricingServiceApi.getCurrentStaticTimeBasedConfigurationValue().execute();

    // Assert
    assertTrue(response.isSuccessful());
    assertEquals(200, response.code());

    CurrentStaticTimeBasedConfigurationValueResponse responseBody = response.body();
    assertNotNull(responseBody);
    assertNotNull(responseBody.getData());

    List<CurrentStaticTimeBasedConfigurationValue> values = responseBody.getData();
    assertEquals(2, values.size());

    // Verify the configuration values are returned
    boolean foundSurgeMultiplier = false;
    boolean foundBaseFareAdjustment = false;

    for (CurrentStaticTimeBasedConfigurationValue value : values) {
      if ("surge_multiplier".equals(value.getName())) {
        foundSurgeMultiplier = true;
        assertNotNull(value.getValue());
      } else if ("base_fare_adjustment".equals(value.getName())) {
        foundBaseFareAdjustment = true;
        assertNotNull(value.getValue());
      }
    }

    assertTrue(foundSurgeMultiplier, "Should find surge_multiplier configuration");
    assertTrue(foundBaseFareAdjustment, "Should find base_fare_adjustment configuration");
  }

  @Test
  @DisplayName("Should return empty list when no effective configurations exist")
  public void testGetCurrentStaticTimeBasedConfigurationValue_NoEffectiveConfigs()
      throws IOException {
    // Arrange - Create configurations that are not currently effective
    OffsetDateTime now = OffsetDateTime.now(ZoneOffset.UTC);
    OffsetDateTime effectiveFrom = now.plusDays(1); // Starts tomorrow
    OffsetDateTime effectiveTo = now.plusDays(2); // Ends day after tomorrow

    createTestConfiguration("future_config", effectiveFrom, effectiveTo);

    // Act
    Response<CurrentStaticTimeBasedConfigurationValueResponse> response =
        dynamicPricingServiceApi.getCurrentStaticTimeBasedConfigurationValue().execute();

    // Assert
    assertTrue(response.isSuccessful());
    assertEquals(200, response.code());

    CurrentStaticTimeBasedConfigurationValueResponse responseBody = response.body();
    assertNotNull(responseBody);
    assertNotNull(responseBody.getData());

    List<CurrentStaticTimeBasedConfigurationValue> values = responseBody.getData();
    assertTrue(values.isEmpty(), "Should return empty list when no effective configurations exist");
  }

  @Test
  @DisplayName("Should get current values based on time and day of week")
  public void testGetCurrentStaticTimeBasedConfigurationValue_TimeBasedValues() throws IOException {
    // Arrange - Create configuration with specific time-based values
    OffsetDateTime now = OffsetDateTime.now(ZoneOffset.UTC);
    OffsetDateTime effectiveFrom = now.minusDays(1);
    OffsetDateTime effectiveTo = now.plusDays(1);

    createDetailedTestConfiguration("time_based_config", effectiveFrom, effectiveTo);

    // Act
    Response<CurrentStaticTimeBasedConfigurationValueResponse> response =
        dynamicPricingServiceApi.getCurrentStaticTimeBasedConfigurationValue().execute();

    // Assert
    assertTrue(response.isSuccessful());
    assertEquals(200, response.code());

    CurrentStaticTimeBasedConfigurationValueResponse responseBody = response.body();
    assertNotNull(responseBody);
    assertNotNull(responseBody.getData());

    List<CurrentStaticTimeBasedConfigurationValue> values = responseBody.getData();
    assertEquals(1, values.size());

    CurrentStaticTimeBasedConfigurationValue value = values.get(0);
    assertEquals("time_based_config", value.getName());
    assertNotNull(value.getValue());
    // The actual value depends on current time and day of week
  }

  @Test
  @DisplayName("Should handle expired configurations by using most recent")
  public void testGetCurrentStaticTimeBasedConfigurationValue_ExpiredConfigs() throws IOException {
    // Arrange - Create expired configurations
    OffsetDateTime now = OffsetDateTime.now(ZoneOffset.UTC);
    OffsetDateTime effectiveFrom = now.minusDays(10); // Started 10 days ago
    OffsetDateTime effectiveTo = now.minusDays(1); // Ended yesterday

    createTestConfiguration("expired_config", effectiveFrom, effectiveTo);

    // Act
    Response<CurrentStaticTimeBasedConfigurationValueResponse> response =
        dynamicPricingServiceApi.getCurrentStaticTimeBasedConfigurationValue().execute();

    // Assert
    assertTrue(response.isSuccessful());
    assertEquals(200, response.code());

    CurrentStaticTimeBasedConfigurationValueResponse responseBody = response.body();
    assertNotNull(responseBody);
    assertNotNull(responseBody.getData());

    // According to the implementation, it should fall back to the most recent configuration
    List<CurrentStaticTimeBasedConfigurationValue> values = responseBody.getData();
    assertEquals(1, values.size());
    assertEquals("expired_config", values.get(0).getName());
  }

  /** Creates a basic test configuration with simple applied hours */
  private void createTestConfiguration(
      String name, OffsetDateTime effectiveFrom, OffsetDateTime effectiveTo) {
    StaticTimeBasedConfigurationJPA config =
        StaticTimeBasedConfigurationJPA.builder()
            .name(name)
            .version(VERSION)
            .effectiveFrom(effectiveFrom.toInstant())
            .effectiveTo(effectiveTo.toInstant())
            .description("Test configuration for " + name)
            .timeZoneOffset(SINGAPORE_TIMEZONE)
            .createdBy("test-user")
            .createdDate(Instant.now())
            .build();

    // Add basic applied hours for all days and hours
    for (String dayOfWeek :
        List.of("MON", "TUE", "WED", "THU", "FRI", "SAT", "SUN", "PUBLIC_HOLIDAY")) {
      for (int hour = 0; hour < 24; hour++) {
        String value = getTestValueForHour(hour);
        config
            .getAppliedHours()
            .add(new StaticTimeBasedConfigurationJPA.AppliedHour(dayOfWeek, hour, value));
      }
    }

    staticConfigRepository.save(config);
  }

  /** Creates a detailed test configuration with varied values based on time */
  private void createDetailedTestConfiguration(
      String name, OffsetDateTime effectiveFrom, OffsetDateTime effectiveTo) {
    StaticTimeBasedConfigurationJPA config =
        StaticTimeBasedConfigurationJPA.builder()
            .name(name)
            .version(VERSION)
            .effectiveFrom(effectiveFrom.toInstant())
            .effectiveTo(effectiveTo.toInstant())
            .description("Detailed test configuration for " + name)
            .timeZoneOffset(SINGAPORE_TIMEZONE)
            .createdBy("test-user")
            .createdDate(Instant.now())
            .build();

    // Add varied applied hours
    for (String dayOfWeek : List.of("MON", "TUE", "WED", "THU", "FRI")) {
      // Weekday values
      for (int hour = 0; hour < 24; hour++) {
        String value = getWeekdayValueForHour(hour);
        config
            .getAppliedHours()
            .add(new StaticTimeBasedConfigurationJPA.AppliedHour(dayOfWeek, hour, value));
      }
    }

    for (String dayOfWeek : List.of("SAT", "SUN")) {
      // Weekend values
      for (int hour = 0; hour < 24; hour++) {
        String value = getWeekendValueForHour(hour);
        config
            .getAppliedHours()
            .add(new StaticTimeBasedConfigurationJPA.AppliedHour(dayOfWeek, hour, value));
      }
    }

    // Holiday values
    for (int hour = 0; hour < 24; hour++) {
      String value = getHolidayValueForHour(hour);
      config
          .getAppliedHours()
          .add(new StaticTimeBasedConfigurationJPA.AppliedHour("PUBLIC_HOLIDAY", hour, value));
    }

    staticConfigRepository.save(config);
  }

  private String getTestValueForHour(int hour) {
    // Simple test values based on hour
    if (hour >= 6 && hour <= 9) return "1.5"; // Morning peak
    if (hour >= 17 && hour <= 20) return "1.8"; // Evening peak
    return "1.0"; // Normal hours
  }

  private String getWeekdayValueForHour(int hour) {
    if (hour >= 7 && hour <= 9) return "2.0"; // Morning rush
    if (hour >= 17 && hour <= 19) return "2.2"; // Evening rush
    if (hour >= 12 && hour <= 14) return "1.3"; // Lunch time
    return "1.0";
  }

  private String getWeekendValueForHour(int hour) {
    if (hour >= 10 && hour <= 14) return "1.2"; // Weekend brunch
    if (hour >= 19 && hour <= 22) return "1.4"; // Weekend dinner
    return "1.0";
  }

  private String getHolidayValueForHour(int hour) {
    if (hour >= 10 && hour <= 16) return "1.6"; // Holiday daytime
    if (hour >= 19 && hour <= 23) return "1.8"; // Holiday evening
    return "1.1";
  }
}

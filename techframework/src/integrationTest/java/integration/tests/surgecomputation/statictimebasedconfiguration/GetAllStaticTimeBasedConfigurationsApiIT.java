package integration.tests.surgecomputation.statictimebasedconfiguration;

import static org.junit.jupiter.api.Assertions.*;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.*;
import integration.IntegrationTestBase;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.jdbc.Sql;
import retrofit2.Response;

@Sql(scripts = "/scripts/Cleanup.sql", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
public class GetAllStaticTimeBasedConfigurationsApiIT extends IntegrationTestBase {

  private static final String TEST_USER_ID = "test-user";
  private static final String VERSION = "1.0";
  private Long configId1;
  private Long configId2;

  @BeforeEach
  public void setupTestData() throws IOException {
    OffsetDateTime effectiveFrom = OffsetDateTime.now();
    // Create first configuration
    StaticTimeBasedConfigurationRequest createRequest1 = new StaticTimeBasedConfigurationRequest();
    createRequest1.setName("Time Config 1");
    createRequest1.setVersion(VERSION);
    createRequest1.setEffectiveFrom(effectiveFrom);
    createRequest1.setDescription("First configuration for time-based surge pricing");
    createRequest1.setTimeZoneOffset("+08:00");

    // Create applied hours for first configuration
    List<StaticTimeBasedConfigurationRequestAppliedHoursInner> appliedHours1 =
        List.of(
            new StaticTimeBasedConfigurationRequestAppliedHoursInner(
                StaticTimeBasedConfigurationRequestAppliedHoursInner.DayOfWeekEnum.MON, 8, "1.2"));
    createRequest1.setAppliedHours(appliedHours1);

    Response<StaticTimeBasedConfigurationCreateResponse> createResponse1 =
        dynamicPricingServiceApi
            .batchCreateStaticTimeBasedConfiguration(List.of(createRequest1), TEST_USER_ID)
            .execute();

    assertTrue(createResponse1.isSuccessful());
    assertNotNull(createResponse1.body());
    configId1 = createResponse1.body().getData().get(0).getId();

    // Create second configuration
    StaticTimeBasedConfigurationRequest createRequest2 = new StaticTimeBasedConfigurationRequest();
    createRequest2.setName("Time Config 2");
    createRequest2.setVersion(VERSION);
    createRequest2.setEffectiveFrom(effectiveFrom);
    createRequest2.setDescription("Second configuration for time-based surge pricing");
    createRequest2.setTimeZoneOffset("+08:00");

    // Create applied hours for second configuration
    List<StaticTimeBasedConfigurationRequestAppliedHoursInner> appliedHours2 =
        List.of(
            new StaticTimeBasedConfigurationRequestAppliedHoursInner(
                StaticTimeBasedConfigurationRequestAppliedHoursInner.DayOfWeekEnum.TUE, 9, "1.5"));
    createRequest2.setAppliedHours(appliedHours2);

    Response<StaticTimeBasedConfigurationCreateResponse> createResponse2 =
        dynamicPricingServiceApi
            .batchCreateStaticTimeBasedConfiguration(List.of(createRequest2), TEST_USER_ID)
            .execute();

    assertTrue(createResponse2.isSuccessful());
    assertNotNull(createResponse2.body());
    configId2 = createResponse2.body().getData().get(0).getId();
  }

  @Test
  @DisplayName("Should get static time-based configurations by version successfully")
  public void testGetAllStaticTimeBasedConfigurations_Success() throws IOException {
    // Act
    Response<StaticTimeBasedConfigurationListResponse> response =
        dynamicPricingServiceApi.getStaticTimeBasedConfigurations(VERSION).execute();

    // Assert
    assertTrue(response.isSuccessful());
    assertEquals(200, response.code());

    StaticTimeBasedConfigurationListResponse responseBody = response.body();
    assertNotNull(responseBody);
    assertNotNull(responseBody.getData());

    // We should have at least the two configurations we created
    assertTrue(responseBody.getData().size() >= 2);

    // Find our created configurations in the response
    boolean foundConfig1 = false;
    boolean foundConfig2 = false;

    for (StaticTimeBasedConfiguration config : responseBody.getData()) {
      if (config.getId().equals(configId1)) {
        foundConfig1 = true;
        assertEquals("Time Config 1", config.getName());
        assertEquals(VERSION, config.getVersion());
        assertEquals("First configuration for time-based surge pricing", config.getDescription());

        // Verify applied hours
        assertNotNull(config.getAppliedHours());
        assertEquals(1, config.getAppliedHours().size());
        assertEquals("MON", config.getAppliedHours().get(0).getDayOfWeek().toString());
        assertEquals(8, config.getAppliedHours().get(0).getHourOfDay());
        assertEquals("1.2", config.getAppliedHours().get(0).getValue());
        assertEquals("+08:00", config.getTimeZoneOffset());
      } else if (config.getId().equals(configId2)) {
        foundConfig2 = true;
        assertEquals("Time Config 2", config.getName());
        assertEquals(VERSION, config.getVersion());
        assertEquals("Second configuration for time-based surge pricing", config.getDescription());

        // Verify applied hours
        assertNotNull(config.getAppliedHours());
        assertEquals(1, config.getAppliedHours().size());
        assertEquals("TUE", config.getAppliedHours().get(0).getDayOfWeek().toString());
        assertEquals(9, config.getAppliedHours().get(0).getHourOfDay());
        assertEquals("1.5", config.getAppliedHours().get(0).getValue());
        assertEquals("+08:00", config.getTimeZoneOffset());
      }
    }

    assertTrue(foundConfig1, "Configuration 1 should be found in the response");
    assertTrue(foundConfig2, "Configuration 2 should be found in the response");

    assertNotNull(responseBody.getTimestamp());
    assertNotNull(responseBody.getTraceId());
  }

  @Test
  @DisplayName("Should return empty list when get static region-based configurations by version")
  public void testGetStaticTimeBasedConfigurations_Empty() throws IOException {
    // Act
    Response<StaticTimeBasedConfigurationListResponse> response =
        dynamicPricingServiceApi.getStaticTimeBasedConfigurations("99.0").execute();

    // Assert
    assertTrue(response.isSuccessful());
    assertEquals(200, response.code());

    StaticTimeBasedConfigurationListResponse responseBody = response.body();
    assertNotNull(responseBody);
    assertNotNull(responseBody.getData());

    List<StaticTimeBasedConfiguration> configurations = responseBody.getData();
    assertTrue(configurations.isEmpty()); // At least the two we created
  }
}

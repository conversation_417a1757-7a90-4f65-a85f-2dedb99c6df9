package integration.tests.surgecomputation.modelmanagement;

import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.SURGE_COMPUTATION_MODEL_DELETE_ERROR;
import static org.junit.jupiter.api.Assertions.*;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.surgecomputation.RegionModelDistributionRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.ModelPercentage;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.RegionModelDistributionEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.*;
import integration.IntegrationTestBase;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;
import retrofit2.Response;

@RequiredArgsConstructor(onConstructor_ = {@Autowired})
@Sql(scripts = "/scripts/Cleanup.sql", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
public class DeleteSurgeComputationModelApiIT extends IntegrationTestBase {

  private static final String TEST_USER_ID = "test-user";
  private static final String DELETE_USER_ID = "delete-user";
  private static final String REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1 = "unmet_rate_1";

  private final RegionModelDistributionRepository regionModelDistributionRepository;

  @Test
  @DisplayName(
      "Should delete a surge computation model with related region-based configurations successfully")
  public void testDeleteSurgeComputationModel_withRelatedRegionBasedConfigurations_Success()
      throws IOException {
    // Arrange - First create a model to delete
    Long modelId =
        createSurgeComputationModel(
            "Test Surge Model", REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1);

    // Create a static region-based configuration
    Long regionBasedConfigurationId =
        createStaticRegionBasedConfiguration(
            REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1); // Same name as the request field mapping

    // Act - Delete the model
    Response<Void> deleteResponse =
        dynamicPricingServiceApi.deleteSurgeComputationModel(modelId, DELETE_USER_ID).execute();

    // Assert
    assertTrue(deleteResponse.isSuccessful());
    assertEquals(204, deleteResponse.code());

    // Verify the model is deleted by trying to get it
    Response<SurgeComputationModelResponse> getResponse =
        dynamicPricingServiceApi.getSurgeComputationModelById(modelId).execute();

    assertFalse(getResponse.isSuccessful());
    assertEquals(404, getResponse.code());

    // Verify the static region-based configuration is deleted by trying to get it
    Response<StaticRegionBasedConfigurationResponse> getRegionBasedConfigResponse =
        dynamicPricingServiceApi
            .getStaticRegionBasedConfigurationById(regionBasedConfigurationId)
            .execute();

    assertFalse(getRegionBasedConfigResponse.isSuccessful());
    assertEquals(404, getRegionBasedConfigResponse.code());
  }

  @Test
  @DisplayName(
      "Should delete a surge computation model with the related region-based configuration is used by other models successfully")
  public void
      testDeleteSurgeComputationModel_withTheRelatedRegionBasedConfigurationIsUsedByOtherModels_Success()
          throws IOException {
    // Arrange - First create a model to delete
    Long modelId =
        createSurgeComputationModel(
            "Test Surge Model", REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1);

    // create another model with the same region-based configuration
    createSurgeComputationModel(
        "Test Surge Model222", REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1);

    // Create a static region-based configuration
    Long regionBasedConfigurationId =
        createStaticRegionBasedConfiguration(
            REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1); // Same name as the request field mapping

    // Act - Delete the model
    Response<Void> deleteResponse =
        dynamicPricingServiceApi.deleteSurgeComputationModel(modelId, DELETE_USER_ID).execute();

    // Assert
    assertTrue(deleteResponse.isSuccessful());
    assertEquals(204, deleteResponse.code());

    // Verify the model is deleted by trying to get it
    Response<SurgeComputationModelResponse> getResponse =
        dynamicPricingServiceApi.getSurgeComputationModelById(modelId).execute();

    assertFalse(getResponse.isSuccessful());
    assertEquals(404, getResponse.code());

    // Verify the static region-based configuration is not deleted, because it is used by other
    // models
    Response<StaticRegionBasedConfigurationResponse> getRegionBasedConfigResponse =
        dynamicPricingServiceApi
            .getStaticRegionBasedConfigurationById(regionBasedConfigurationId)
            .execute();

    assertTrue(getRegionBasedConfigResponse.isSuccessful());
    assertNotNull(getRegionBasedConfigResponse.body());
    assertNotNull(getRegionBasedConfigResponse.body().getData());
    assertEquals(
        REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1,
        getRegionBasedConfigResponse.body().getData().getName());
  }

  @Test
  @DisplayName("Should return 404 Not Found when deleting non-existent model")
  public void testDeleteSurgeComputationModel_NotFound() throws IOException {
    // Arrange
    Long nonExistentId = 999999L;

    // Act
    Response<Void> response =
        dynamicPricingServiceApi.deleteSurgeComputationModel(nonExistentId, TEST_USER_ID).execute();

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(404, response.code());
  }

  @Test
  @DisplayName("Should return 400 Bad Request when X-User-Id header is missing")
  public void testDeleteSurgeComputationModel_MissingUserIdHeader() throws IOException {
    // Arrange - First create a model to delete
    SurgeComputationModelRequest createRequest = new SurgeComputationModelRequest();
    createRequest.setModelName("Test Surge Model");
    createRequest.setDescription("Test description for surge model");
    createRequest.setEndpointUrl("http://test-endpoint.com/api/surge");

    Response<SurgeComputationModelResponse> createResponse =
        dynamicPricingServiceApi.createSurgeComputationModel(createRequest, TEST_USER_ID).execute();

    assertTrue(createResponse.isSuccessful());
    Long modelId = createResponse.body().getData().getId();

    // Act - Try to delete without user ID header
    Response<Void> response =
        dynamicPricingServiceApi.deleteSurgeComputationModel(modelId, null).execute();

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(400, response.code());

    // Verify the model still exists
    Response<SurgeComputationModelResponse> getResponse =
        dynamicPricingServiceApi.getSurgeComputationModelById(modelId).execute();

    assertTrue(getResponse.isSuccessful());
    assertEquals(200, getResponse.code());
  }

  @Test
  @DisplayName("Should return 400 Bad Request when model is used by distribution")
  public void testDeleteSurgeComputationModel_BadRequest_WhenModelIsUsed() throws IOException {
    Long modelId =
        createSurgeComputationModel(
            "Test Surge Model", REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1);

    createDistribution(modelId);

    // Act - Try to delete without user ID header
    Response<Void> response =
        dynamicPricingServiceApi.deleteSurgeComputationModel(modelId, TEST_USER_ID).execute();

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(400, response.code());
    assertTrue(
        response.errorBody().string().contains(SURGE_COMPUTATION_MODEL_DELETE_ERROR.getMessage()));

    // Verify the model still exists
    Response<SurgeComputationModelResponse> getResponse =
        dynamicPricingServiceApi.getSurgeComputationModelById(modelId).execute();

    assertTrue(getResponse.isSuccessful());
    assertEquals(200, getResponse.code());
  }

  private void createDistribution(final Long modelId) {
    Instant now = Instant.now();

    ModelPercentage modelPercentage = new ModelPercentage();
    modelPercentage.setModelId(modelId);
    modelPercentage.setPercentage(BigDecimal.valueOf(50));
    ModelPercentage modelPercentage2 = new ModelPercentage();
    modelPercentage2.setModelId(modelId);
    modelPercentage2.setPercentage(BigDecimal.valueOf(50));

    RegionModelDistributionEntity entity = new RegionModelDistributionEntity();
    entity.setRegionId(1L);
    entity.setModels(List.of(modelPercentage, modelPercentage2));
    entity.setEffectiveFrom(now.minusSeconds(50));
    entity.setEffectiveTo(now.plusSeconds(50));

    regionModelDistributionRepository.save(entity);
  }

  private Long createStaticRegionBasedConfiguration(final String name) throws IOException {
    StaticRegionBasedConfigurationRequest request = new StaticRegionBasedConfigurationRequest();
    request.setName(name);
    request.setVersion("1.0");
    request.setEffectiveFrom(OffsetDateTime.now());
    request.setDescription("Test configuration for region-based surge pricing");
    request.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(1L, "1.5")));
    Response<StaticRegionBasedConfigurationBatchCreateResponse> regionBasedResponse =
        dynamicPricingServiceApi
            .batchCreateStaticRegionBasedConfiguration(TEST_USER_ID, List.of(request))
            .execute();
    assertTrue(regionBasedResponse.isSuccessful());
    return regionBasedResponse.body().getData().get(0).getId();
  }

  private Long createSurgeComputationModel(
      final String modelName, final String regionBasedConfigurationName) throws IOException {
    SurgeComputationModelRequest createRequest = new SurgeComputationModelRequest();
    createRequest.setModelName(modelName);
    createRequest.setDescription("Test description for surge model");
    createRequest.setEndpointUrl("http://test-endpoint.com/api/surge");
    createRequest.setRequestFieldsMappings(
        List.of(
            new SurgeComputationModelRequestRequestFieldsMappingsInner(
                SurgeComputationModelRequestRequestFieldsMappingsInner.MappingTypeEnum
                    .STATIC_TIME_BASED_CONFIGURATION,
                "dayOfWeek",
                "peak_hour_surge"),
            new SurgeComputationModelRequestRequestFieldsMappingsInner(
                SurgeComputationModelRequestRequestFieldsMappingsInner.MappingTypeEnum
                    .STATIC_REGION_BASED_CONFIGURATION,
                REGION_BASED_CONFIGURATION_NAME_UNMET_RATE_1,
                regionBasedConfigurationName)));

    Response<SurgeComputationModelResponse> createResponse =
        dynamicPricingServiceApi.createSurgeComputationModel(createRequest, TEST_USER_ID).execute();
    assertTrue(createResponse.isSuccessful());
    assertEquals(200, createResponse.code());

    return createResponse.body().getData().getId();
  }
}

package integration.tests.surgecomputation.modelmanagement;

import static org.junit.jupiter.api.Assertions.*;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.SurgeComputationModelRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.SurgeComputationModelResponse;
import integration.IntegrationTestBase;
import java.io.IOException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.jdbc.Sql;
import retrofit2.Response;

@Sql(scripts = "/scripts/Cleanup.sql", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
public class GetSurgeComputationModelApiIT extends IntegrationTestBase {

  private static final String TEST_USER_ID = "test-user";

  @Test
  @DisplayName("Should get a surge computation model with request field mappings successfully")
  public void testGetSurgeComputationModel_WithRequestFieldMappings_Success() throws IOException {
    // Arrange - First create a model with request field mappings
    SurgeComputationModelRequest createRequest = new SurgeComputationModelRequest();
    createRequest.setModelName("Test Surge Model With Mappings");
    createRequest.setDescription("Test description for surge model with request field mappings");
    createRequest.setEndpointUrl("http://test-endpoint.com/api/surge");

    // Note: We're skipping setting requestFieldsMappings in this test
    // because we don't have access to the generated class
    // and there's a type mismatch between our objects and the generated class

    Response<SurgeComputationModelResponse> createResponse =
        dynamicPricingServiceApi.createSurgeComputationModel(createRequest, TEST_USER_ID).execute();

    assertTrue(createResponse.isSuccessful());
    assertEquals(200, createResponse.code());

    Long modelId = createResponse.body().getData().getId();

    // Act - Get the model
    Response<SurgeComputationModelResponse> getResponse =
        dynamicPricingServiceApi.getSurgeComputationModelById(modelId).execute();

    // Assert
    assertTrue(getResponse.isSuccessful());
    assertEquals(200, getResponse.code());

    SurgeComputationModelResponse responseBody = getResponse.body();
    assertNotNull(responseBody);
    assertNotNull(responseBody.getData());
    assertEquals(modelId, responseBody.getData().getId());
    assertEquals("Test Surge Model With Mappings", responseBody.getData().getModelName());
    assertEquals(
        "Test description for surge model with request field mappings",
        responseBody.getData().getDescription());
    assertEquals("http://test-endpoint.com/api/surge", responseBody.getData().getEndpointUrl());

    // Note: We're skipping verification of requestFieldsMappings in this test
    // because we didn't set it in the request

    assertEquals(TEST_USER_ID, responseBody.getData().getCreatedBy());
    assertEquals(TEST_USER_ID, responseBody.getData().getUpdatedBy());
    assertNotNull(responseBody.getData().getCreatedDate());
    assertNotNull(responseBody.getData().getUpdatedDate());
    assertNotNull(responseBody.getTimestamp());
    assertNotNull(responseBody.getTraceId());
  }

  @Test
  @DisplayName("Should return 404 Not Found when getting non-existent model")
  public void testGetSurgeComputationModel_NotFound() throws IOException {
    // Arrange
    Long nonExistentId = 999999L;

    // Act
    Response<SurgeComputationModelResponse> response =
        dynamicPricingServiceApi.getSurgeComputationModelById(nonExistentId).execute();

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(404, response.code());
  }
}

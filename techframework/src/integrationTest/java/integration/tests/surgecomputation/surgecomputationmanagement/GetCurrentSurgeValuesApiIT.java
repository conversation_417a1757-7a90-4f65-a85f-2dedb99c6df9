package integration.tests.surgecomputation.surgecomputationmanagement;

import static org.junit.jupiter.api.Assertions.*;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.CurrentSurgeValuesResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.ModelSurgeData;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.SurgeComputationModelRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.SurgeComputationModelRequestRequestFieldsMappingsInner;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.SurgeComputationModelResponse;
import integration.IntegrationTestBase;
import java.io.IOException;
import java.util.List;
import org.junit.jupiter.api.Test;
import retrofit2.Response;

/**
 * Integration test for the getCurrentSurgeValues endpoint. Tests the monitoring endpoint that
 * retrieves current surge values from cache.
 */
public class GetCurrentSurgeValuesApiIT extends IntegrationTestBase {

  private static final String TEST_MODEL_NAME = "Test Monitoring Model";
  private static final String TEST_USER_ID = "test-user-123";

  @Test
  void testGetCurrentSurgeValues_withNoModels_shouldReturnEmptyList() throws IOException {
    // Act
    Response<CurrentSurgeValuesResponse> response =
        dynamicPricingServiceApi.getCurrentSurgeValues().execute();

    // Assert
    assertTrue(response.isSuccessful());
    assertNotNull(response.body());
    assertNotNull(response.body().getData());
    assertTrue(response.body().getData().isEmpty());
    assertNotNull(response.body().getTimestamp());
    assertNotNull(response.body().getTraceId());
  }

  @Test
  void testGetCurrentSurgeValues_withModelsButNoCache_shouldReturnModelsWithEmptyRegions()
      throws IOException {
    // Arrange
    Long modelId = createTestModel();

    try {
      // Act
      Response<CurrentSurgeValuesResponse> response =
          dynamicPricingServiceApi.getCurrentSurgeValues().execute();

      // Assert
      assertTrue(response.isSuccessful());
      assertNotNull(response.body());
      assertNotNull(response.body().getData());
      assertEquals(1, response.body().getData().size());

      ModelSurgeData modelData = response.body().getData().get(0);
      assertEquals(modelId, modelData.getModelId());
      assertEquals(TEST_MODEL_NAME, modelData.getModelName());
      // CacheStatus field was removed from the response for simplicity
      assertNotNull(modelData.getRegions());
      assertTrue(modelData.getRegions().isEmpty());
      assertNotNull(response.body().getTimestamp());
      assertNotNull(response.body().getTraceId());

    } finally {
      // Cleanup
      if (modelId != null) {
        dynamicPricingServiceApi.deleteSurgeComputationModel(modelId, TEST_USER_ID).execute();
      }
    }
  }

  @Test
  void testGetCurrentSurgeValues_performanceTest() throws IOException {
    // Arrange
    Long modelId = createTestModel();

    try {
      // Act - Call the endpoint multiple times to test performance
      long startTime = System.currentTimeMillis();

      for (int i = 0; i < 5; i++) {
        Response<CurrentSurgeValuesResponse> response =
            dynamicPricingServiceApi.getCurrentSurgeValues().execute();
        assertTrue(response.isSuccessful());
      }

      long endTime = System.currentTimeMillis();
      long totalTime = endTime - startTime;

      // Assert - Should be fast since it's cache-based
      assertTrue(
          totalTime < 5000,
          "Endpoint should be fast for monitoring calls, took: " + totalTime + "ms");

    } finally {
      // Cleanup
      if (modelId != null) {
        dynamicPricingServiceApi.deleteSurgeComputationModel(modelId, TEST_USER_ID).execute();
      }
    }
  }

  /** Creates a test surge computation model for testing purposes. */
  private Long createTestModel() throws IOException {
    SurgeComputationModelRequest request = new SurgeComputationModelRequest();
    request.setModelName(TEST_MODEL_NAME);
    request.setDescription("Test model for monitoring endpoint integration test");
    request.setEndpointUrl("http://test-endpoint.com/api/surge");
    request.setRequestFieldsMappings(
        List.of(
            new SurgeComputationModelRequestRequestFieldsMappingsInner(
                SurgeComputationModelRequestRequestFieldsMappingsInner.MappingTypeEnum
                    .LIVE_STANDARD_INPUT,
                "demand",
                "ComfortRideDemand")));

    Response<SurgeComputationModelResponse> response =
        dynamicPricingServiceApi.createSurgeComputationModel(request, TEST_USER_ID).execute();

    assertTrue(response.isSuccessful());
    assertNotNull(response.body());
    assertNotNull(response.body().getData());

    return response.body().getData().getId();
  }
}

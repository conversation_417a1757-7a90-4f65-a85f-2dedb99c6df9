package integration.tests.surgecomputation.surgecomputationmanagement;

import static org.junit.jupiter.api.Assertions.*;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.SurgeFactorCalculationService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.surgecomputation.ModelRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.surgecomputation.SurgeComputationModelApiLogRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.surgecomputation.SurgeComputationModelSurgeRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.ModelEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.SurgeComputationModelApiLogEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.SurgeComputationModelSurgeEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.*;
import integration.IntegrationTestBase;
import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;
import retrofit2.Response;

@RequiredArgsConstructor(onConstructor_ = {@Autowired})
@Sql(scripts = "/scripts/Cleanup.sql", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
public class CalculateSurgeFactorIT extends IntegrationTestBase {

  private static final String TEST_USER_ID = "test-user";
  private static final String MODEL_NAME = "test_v4_model";
  private static final String VERSION = "1.0";
  private static final long REGION_ID = 1L;

  private static final String TIME_BASED_SURGE_HIGH = "surge_high";
  private static final String TIME_BASED_SURGE_LOW = "surge_low";

  private static final String REGION_BASED_UNMET_RATE_1 = "unmet_rate_1";
  private static final String REGION_BASED_UNMET_RATE_2 = "unmet_rate_2";

  private static final String LIVE_STANDARD_INPUT_COMFORTRIDE_DEMAND = "ComfortRideDemand";
  private static final String LIVE_STANDARD_INPUT_METER_UNMET_DEMAND = "MeterUnmetDemand";

  private final SurgeFactorCalculationService surgeFactorCalculationService;
  private final SurgeComputationModelApiLogRepository surgeComputationModelApiLogRepository;
  private final SurgeComputationModelSurgeRepository surgeComputationModelSurgeRepository;
  private final ModelRepository modelRepository;

  /**
   * Don't change this url, if you want to change it, please make sure the new url is defined under
   * wiremock/mappings.
   */
  private static final String SURGE_COMPUTATION_MODEL_V4_URL = "/test/calculate_surge";

  @Test
  @DisplayName("Should successfully calculate surge factor with valid data")
  void testCalculateSurgeFactor_success() throws Exception {
    // Arrange
    Long modelId = setupModelData();
    setupStaticTimeBasedConfigurations();
    setupStaticRegionBasedConfigurations();

    // Act
    Response<Void> response = dynamicPricingServiceApi.calculateSurgeFactor().execute();

    assertTrue(response.isSuccessful());

    Map<Long, BigDecimal> h3RegionSurgeMap =
        surgeFactorCalculationService.getH3RegionSurgeMap(MODEL_NAME);
    assertEquals(3, h3RegionSurgeMap.size());
    assertEquals(BigDecimal.valueOf(6), h3RegionSurgeMap.get(REGION_ID));

    List<SurgeComputationModelApiLogEntity> apiLogs =
        surgeComputationModelApiLogRepository.findByModelId(modelId);
    assertEquals(1, apiLogs.size());
    assertEquals(200, apiLogs.get(0).getStatusCode());
    assertEquals(SURGE_COMPUTATION_MODEL_V4_URL, apiLogs.get(0).getEndpointUrl());

    List<SurgeComputationModelSurgeEntity> h3RegionSurgesFromDB =
        surgeComputationModelSurgeRepository.findByModelId(modelId);
    assertEquals(3, h3RegionSurgesFromDB.size());
    for (final SurgeComputationModelSurgeEntity surge : h3RegionSurgesFromDB) {
      assertNotNull(surge.getId());
      assertNotNull(surge.getLastUpdDt());
      if (surge.getRegionId() == REGION_ID) {
        assertEquals(BigDecimal.valueOf(6), surge.getSurge());
      } else if (surge.getRegionId() == 2L) {
        assertEquals(BigDecimal.valueOf(10), surge.getSurge());
      } else if (surge.getRegionId() == 3L) {
        assertEquals(BigDecimal.valueOf(50), surge.getSurge());
      }
    }
  }

  @Test
  @DisplayName("Should handle case when no active models exist")
  void testCalculateSurgeFactor_noActiveModels() throws Exception {
    // Arrange - Don't create any models
    setupStaticTimeBasedConfigurations();
    setupStaticRegionBasedConfigurations();

    // Act
    Response<Void> response = dynamicPricingServiceApi.calculateSurgeFactor().execute();

    // Assert - Should still return 204 but no processing should occur
    assertFalse(response.isSuccessful());
    assertEquals(500, response.code());

    // Verify no API logs were created since no models exist
    // We can't easily check all logs without a model ID, so we'll check that no models exist
    List<ModelEntity> models = modelRepository.findAll();
    assertTrue(models.isEmpty());

    // Since no models exist, no surge data should be created either
    // We'll verify this by checking that no models were processed
  }

  @Test
  @DisplayName("Should handle case when model exists but no time-based configurations")
  void testCalculateSurgeFactor_noTimeBasedConfigurations() throws Exception {
    // Arrange
    Long modelId = setupModelData();
    // Don't setup time-based configurations
    setupStaticRegionBasedConfigurations();

    // Act
    Response<Void> response = dynamicPricingServiceApi.calculateSurgeFactor().execute();

    // Assert - Should still return 204 with default config
    assertTrue(response.isSuccessful());
    assertEquals(204, response.code());

    // Check if any API logs were created (may have error status)
    List<SurgeComputationModelApiLogEntity> apiLogs =
        surgeComputationModelApiLogRepository.findByModelId(modelId);
    assertFalse(apiLogs.isEmpty());
    assertEquals(200, apiLogs.get(0).getStatusCode());
  }

  @Test
  @DisplayName("Should handle case when model exists but no region-based configurations")
  void testCalculateSurgeFactor_noRegionBasedConfigurations() throws Exception {
    // Arrange
    Long modelId = setupModelData();
    setupStaticTimeBasedConfigurations();
    // Don't setup region-based configurations

    // Act
    Response<Void> response = dynamicPricingServiceApi.calculateSurgeFactor().execute();

    // Assert - Should still return 204 but processing may fail
    assertTrue(response.isSuccessful());
    assertEquals(204, response.code());

    // Check if any API logs were created (may have error status)
    List<SurgeComputationModelApiLogEntity> apiLogs =
        surgeComputationModelApiLogRepository.findByModelId(modelId);
    assertFalse(apiLogs.isEmpty());
    assertEquals(200, apiLogs.get(0).getStatusCode());
  }

  @Test
  @DisplayName("Should handle external service error response")
  void testCalculateSurgeFactor_externalServiceError() throws Exception {
    // Arrange
    Long modelId = setupModelDataWithErrorEndpoint();
    setupStaticTimeBasedConfigurations();
    setupStaticRegionBasedConfigurations();

    // Act
    Response<Void> response = dynamicPricingServiceApi.calculateSurgeFactor().execute();

    // Assert - Should still return 204 even if external service fails
    assertFalse(response.isSuccessful());
    assertEquals(500, response.code());

    // Verify API log was created with error status
    List<SurgeComputationModelApiLogEntity> apiLogs =
        surgeComputationModelApiLogRepository.findByModelId(modelId);
    assertEquals(1, apiLogs.size());
    assertEquals(500, apiLogs.get(0).getStatusCode());
    assertEquals("/test/calculate_surge_error", apiLogs.get(0).getEndpointUrl());

    // Verify no surge data was created due to error
    List<SurgeComputationModelSurgeEntity> surgeData =
        surgeComputationModelSurgeRepository.findByModelId(modelId);
    assertTrue(surgeData.isEmpty());
  }

  @Test
  @DisplayName("Should handle multiple models with mixed success and failure")
  void testCalculateSurgeFactor_multipleModels() throws Exception {
    // Arrange
    Long successModelId = setupModelData();
    Long errorModelId = setupModelDataWithErrorEndpoint();
    setupStaticTimeBasedConfigurations();
    setupStaticRegionBasedConfigurations();

    // Act
    Response<Void> response = dynamicPricingServiceApi.calculateSurgeFactor().execute();

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(500, response.code());

    // Verify success model has successful API log and surge data
    List<SurgeComputationModelApiLogEntity> successApiLogs =
        surgeComputationModelApiLogRepository.findByModelId(successModelId);
    assertEquals(1, successApiLogs.size());
    assertEquals(200, successApiLogs.get(0).getStatusCode());

    List<SurgeComputationModelSurgeEntity> successSurgeData =
        surgeComputationModelSurgeRepository.findByModelId(successModelId);
    assertEquals(3, successSurgeData.size());

    // Verify error model has error API log and no surge data
    List<SurgeComputationModelApiLogEntity> errorApiLogs =
        surgeComputationModelApiLogRepository.findByModelId(errorModelId);
    assertEquals(1, errorApiLogs.size());
    assertEquals(500, errorApiLogs.get(0).getStatusCode());

    List<SurgeComputationModelSurgeEntity> errorSurgeData =
        surgeComputationModelSurgeRepository.findByModelId(errorModelId);
    assertTrue(errorSurgeData.isEmpty());
  }

  @Test
  @DisplayName("Should handle concurrent requests gracefully")
  void testCalculateSurgeFactor_concurrentRequests() throws Exception {
    // Arrange
    Long modelId = setupModelData();
    setupStaticTimeBasedConfigurations();
    setupStaticRegionBasedConfigurations();

    // Act - Send multiple concurrent requests
    Response<Void> response1 = dynamicPricingServiceApi.calculateSurgeFactor().execute();
    Response<Void> response2 = dynamicPricingServiceApi.calculateSurgeFactor().execute();
    Response<Void> response3 = dynamicPricingServiceApi.calculateSurgeFactor().execute();

    // Assert - All requests should succeed
    assertTrue(response1.isSuccessful());
    assertTrue(response2.isSuccessful());
    assertTrue(response3.isSuccessful());
    assertEquals(204, response1.code());
    assertEquals(204, response2.code());
    assertEquals(204, response3.code());

    // Verify that surge data was created (may have multiple entries due to concurrent processing)
    List<SurgeComputationModelApiLogEntity> apiLogs =
        surgeComputationModelApiLogRepository.findByModelId(modelId);
    assertFalse(apiLogs.isEmpty(), "At least one API log should be created");

    // Verify surge data exists
    List<SurgeComputationModelSurgeEntity> surgeData =
        surgeComputationModelSurgeRepository.findByModelId(modelId);
    assertEquals(3, surgeData.size(), "Should have surge data for 3 regions");
  }

  @Test
  @DisplayName("Should handle model with invalid mapping configuration")
  void testCalculateSurgeFactor_invalidMappingConfiguration() throws Exception {
    // Arrange
    setupModelDataWithInvalidMapping();
    setupStaticTimeBasedConfigurations();
    setupStaticRegionBasedConfigurations();

    // Act
    Response<Void> response = dynamicPricingServiceApi.calculateSurgeFactor().execute();

    // Assert - Should still return 204 but processing may fail
    assertTrue(response.isSuccessful());
    assertEquals(204, response.code());
  }

  @Test
  @DisplayName("Should verify cache behavior after successful calculation")
  void testCalculateSurgeFactor_cacheVerification() throws Exception {
    // Arrange
    Long modelId = setupModelData();
    setupStaticTimeBasedConfigurations();
    setupStaticRegionBasedConfigurations();

    // Act - First calculation
    Response<Void> response = dynamicPricingServiceApi.calculateSurgeFactor().execute();
    assertTrue(response.isSuccessful());

    // Verify cache is populated by checking service method
    Map<Long, BigDecimal> h3RegionSurgeMap1 =
        surgeFactorCalculationService.getH3RegionSurgeMap(MODEL_NAME);
    assertEquals(3, h3RegionSurgeMap1.size());
    assertEquals(BigDecimal.valueOf(6), h3RegionSurgeMap1.get(REGION_ID));

    // Act - Second calculation (should update cache)
    Response<Void> response2 = dynamicPricingServiceApi.calculateSurgeFactor().execute();
    assertTrue(response2.isSuccessful());

    // Verify cache is still populated with correct data
    Map<Long, BigDecimal> h3RegionSurgeMap2 =
        surgeFactorCalculationService.getH3RegionSurgeMap(MODEL_NAME);
    assertEquals(3, h3RegionSurgeMap2.size());
    assertEquals(BigDecimal.valueOf(6), h3RegionSurgeMap2.get(REGION_ID));

    // Verify database has the latest data
    List<SurgeComputationModelSurgeEntity> surgeData =
        surgeComputationModelSurgeRepository.findByModelId(modelId);
    assertEquals(3, surgeData.size());
  }

  @Test
  @DisplayName("Should handle empty response from external service")
  void testCalculateSurgeFactor_emptyExternalResponse() throws Exception {
    // Arrange
    Long modelId = setupModelDataWithEmptyResponse();
    setupStaticTimeBasedConfigurations();
    setupStaticRegionBasedConfigurations();

    // Act
    Response<Void> response = dynamicPricingServiceApi.calculateSurgeFactor().execute();

    // Assert - Should still return 204
    assertTrue(response.isSuccessful());
    assertEquals(204, response.code());

    // Verify API log was created
    List<SurgeComputationModelApiLogEntity> apiLogs =
        surgeComputationModelApiLogRepository.findByModelId(modelId);
    assertEquals(1, apiLogs.size());
    assertEquals(200, apiLogs.get(0).getStatusCode());

    // Verify no surge data was created due to empty response
    List<SurgeComputationModelSurgeEntity> surgeData =
        surgeComputationModelSurgeRepository.findByModelId(modelId);
    assertTrue(surgeData.isEmpty());
  }

  @Test
  @DisplayName("Should handle rapid successive requests without resource leaks")
  void testCalculateSurgeFactor_rapidSuccessiveRequests() throws Exception {
    // Arrange
    Long modelId = setupModelData();
    setupStaticTimeBasedConfigurations();
    setupStaticRegionBasedConfigurations();

    // Act - Send rapid successive requests
    for (int i = 0; i < 5; i++) {
      Response<Void> response = dynamicPricingServiceApi.calculateSurgeFactor().execute();
      assertTrue(response.isSuccessful());
      assertEquals(204, response.code());
      Thread.sleep(500); // Small delay between requests
    }

    // Assert - Verify that the system handled all requests properly
    List<SurgeComputationModelApiLogEntity> apiLogs =
        surgeComputationModelApiLogRepository.findByModelId(modelId);
    assertFalse(apiLogs.isEmpty(), "At least one API log should be created");

    // Verify final surge data is consistent
    List<SurgeComputationModelSurgeEntity> surgeData =
        surgeComputationModelSurgeRepository.findByModelId(modelId);
    assertEquals(3, surgeData.size(), "Should have surge data for 3 regions");

    // Verify cache is still accessible
    Map<Long, BigDecimal> h3RegionSurgeMap =
        surgeFactorCalculationService.getH3RegionSurgeMap(MODEL_NAME);
    assertEquals(3, h3RegionSurgeMap.size());
  }

  @Test
  @DisplayName("Should verify API logs contain proper request and response data")
  void testCalculateSurgeFactor_apiLogVerification() throws Exception {
    // Arrange
    Long modelId = setupModelData();
    setupStaticTimeBasedConfigurations();
    setupStaticRegionBasedConfigurations();

    // Act
    Response<Void> response = dynamicPricingServiceApi.calculateSurgeFactor().execute();
    assertTrue(response.isSuccessful());

    // Assert - Verify API log details
    List<SurgeComputationModelApiLogEntity> apiLogs =
        surgeComputationModelApiLogRepository.findByModelId(modelId);
    assertEquals(1, apiLogs.size());

    SurgeComputationModelApiLogEntity apiLog = apiLogs.get(0);
    assertEquals(200, apiLog.getStatusCode());
    assertEquals(SURGE_COMPUTATION_MODEL_V4_URL, apiLog.getEndpointUrl());
    assertNotNull(apiLog.getRequestParams());
    assertNotNull(apiLog.getResponseBody());
    assertNotNull(apiLog.getCreateTimestamp());
    assertEquals(modelId, apiLog.getModelId());

    // Verify request params contains expected fields (stored as JSON object)
    Object requestParams = apiLog.getRequestParams();
    assertNotNull(requestParams);
    String requestParamsStr = requestParams.toString();
    assertTrue(requestParamsStr.contains("v4_t_surge_high") || requestParamsStr.contains("surge"));

    // Verify response body contains expected structure (stored as JSON object)
    Object responseBody = apiLog.getResponseBody();
    assertNotNull(responseBody);
    String responseBodyStr = responseBody.toString();
    assertTrue(responseBodyStr.contains("request_id") || responseBodyStr.contains("results"));
    assertTrue(responseBodyStr.contains("h3_region_id") || responseBodyStr.contains("surge"));
  }

  private void setupStaticRegionBasedConfigurations() throws Exception {
    OffsetDateTime now = OffsetDateTime.now();

    StaticRegionBasedConfigurationRequest request = new StaticRegionBasedConfigurationRequest();
    request.setName(REGION_BASED_UNMET_RATE_1);
    request.setVersion(VERSION);
    request.setEffectiveFrom(now.minusDays(1));
    request.setDescription(REGION_BASED_UNMET_RATE_1);
    request.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(REGION_ID, "0.4")));

    StaticRegionBasedConfigurationRequest request2 = new StaticRegionBasedConfigurationRequest();
    request2.setName(REGION_BASED_UNMET_RATE_2);
    request2.setVersion(VERSION);
    request2.setEffectiveFrom(now.minusDays(1));
    request2.setDescription(REGION_BASED_UNMET_RATE_2);
    request2.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(REGION_ID, "0.75")));

    // Act
    Response<StaticRegionBasedConfigurationBatchCreateResponse> response =
        dynamicPricingServiceApi
            .batchCreateStaticRegionBasedConfiguration(TEST_USER_ID, List.of(request, request2))
            .execute();

    // Assert
    assertTrue(response.isSuccessful());
  }

  private void setupStaticTimeBasedConfigurations() throws Exception {
    OffsetDateTime now = OffsetDateTime.now(ZoneId.of("Asia/Singapore"));
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("EEE", Locale.ENGLISH);
    String dayOfWeek = now.format(formatter).toUpperCase();
    OffsetDateTime effectiveFrom = now.minusDays(1);

    StaticTimeBasedConfigurationRequest request = new StaticTimeBasedConfigurationRequest();
    request.setName(TIME_BASED_SURGE_HIGH);
    request.setVersion(VERSION);
    request.setEffectiveFrom(effectiveFrom);
    request.setDescription(TIME_BASED_SURGE_HIGH);
    request.setTimeZoneOffset("+08:00");

    // Create applied hours
    List<StaticTimeBasedConfigurationRequestAppliedHoursInner> appliedHours =
        List.of(
            new StaticTimeBasedConfigurationRequestAppliedHoursInner(
                StaticTimeBasedConfigurationRequestAppliedHoursInner.DayOfWeekEnum.fromValue(
                    dayOfWeek),
                now.getHour(),
                "8.5"));

    request.setAppliedHours(appliedHours);

    StaticTimeBasedConfigurationRequest request2 = new StaticTimeBasedConfigurationRequest();
    request2.setName(TIME_BASED_SURGE_LOW);
    request2.setVersion(VERSION);
    request2.setEffectiveFrom(effectiveFrom);
    request2.setDescription(TIME_BASED_SURGE_LOW);
    request2.setTimeZoneOffset("+08:00");

    // Create applied hours
    List<StaticTimeBasedConfigurationRequestAppliedHoursInner> appliedHours2 =
        List.of(
            new StaticTimeBasedConfigurationRequestAppliedHoursInner(
                StaticTimeBasedConfigurationRequestAppliedHoursInner.DayOfWeekEnum.fromValue(
                    dayOfWeek),
                now.getHour(),
                "1.5"));

    request2.setAppliedHours(appliedHours2);

    // Act
    Response<StaticTimeBasedConfigurationCreateResponse> response =
        dynamicPricingServiceApi
            .batchCreateStaticTimeBasedConfiguration(List.of(request, request2), TEST_USER_ID)
            .execute();

    // Assert
    assertTrue(response.isSuccessful());
  }

  private Long setupModelData() throws Exception {
    List<SurgeComputationModelRequestRequestFieldsMappingsInner> mappings =
        List.of(
            new SurgeComputationModelRequestRequestFieldsMappingsInner(
                SurgeComputationModelRequestRequestFieldsMappingsInner.MappingTypeEnum
                    .STATIC_TIME_BASED_CONFIGURATION,
                "v4_t_surge_high",
                TIME_BASED_SURGE_HIGH),
            new SurgeComputationModelRequestRequestFieldsMappingsInner(
                SurgeComputationModelRequestRequestFieldsMappingsInner.MappingTypeEnum
                    .STATIC_TIME_BASED_CONFIGURATION,
                "v4_t_surge_low",
                TIME_BASED_SURGE_LOW),
            new SurgeComputationModelRequestRequestFieldsMappingsInner(
                SurgeComputationModelRequestRequestFieldsMappingsInner.MappingTypeEnum
                    .STATIC_REGION_BASED_CONFIGURATION,
                "v4_c_unmet_rate_1",
                REGION_BASED_UNMET_RATE_1),
            new SurgeComputationModelRequestRequestFieldsMappingsInner(
                SurgeComputationModelRequestRequestFieldsMappingsInner.MappingTypeEnum
                    .STATIC_REGION_BASED_CONFIGURATION,
                "v4_c_unmet_rate_2",
                REGION_BASED_UNMET_RATE_2),
            new SurgeComputationModelRequestRequestFieldsMappingsInner(
                SurgeComputationModelRequestRequestFieldsMappingsInner.MappingTypeEnum
                    .LIVE_STANDARD_INPUT,
                "comfortride_demand",
                LIVE_STANDARD_INPUT_COMFORTRIDE_DEMAND),
            new SurgeComputationModelRequestRequestFieldsMappingsInner(
                SurgeComputationModelRequestRequestFieldsMappingsInner.MappingTypeEnum
                    .LIVE_STANDARD_INPUT,
                "meter_unmet_demand",
                LIVE_STANDARD_INPUT_METER_UNMET_DEMAND));

    SurgeComputationModelRequest request = new SurgeComputationModelRequest();
    request.setModelName(MODEL_NAME);
    request.setDescription("Test description for surge model");
    request.setEndpointUrl(SURGE_COMPUTATION_MODEL_V4_URL);
    request.setRequestFieldsMappings(mappings);

    // Act
    Response<SurgeComputationModelResponse> response =
        dynamicPricingServiceApi.createSurgeComputationModel(request, TEST_USER_ID).execute();

    // Assert
    assertTrue(response.isSuccessful());
    Assertions.assertNotNull(response.body());

    return response.body().getData().getId();
  }

  /** Creates a model that points to an endpoint that returns error responses */
  private Long setupModelDataWithErrorEndpoint() throws Exception {
    List<SurgeComputationModelRequestRequestFieldsMappingsInner> mappings =
        List.of(
            new SurgeComputationModelRequestRequestFieldsMappingsInner(
                SurgeComputationModelRequestRequestFieldsMappingsInner.MappingTypeEnum
                    .STATIC_TIME_BASED_CONFIGURATION,
                "v4_t_surge_high",
                TIME_BASED_SURGE_HIGH),
            new SurgeComputationModelRequestRequestFieldsMappingsInner(
                SurgeComputationModelRequestRequestFieldsMappingsInner.MappingTypeEnum
                    .STATIC_TIME_BASED_CONFIGURATION,
                "v4_t_surge_low",
                TIME_BASED_SURGE_LOW),
            new SurgeComputationModelRequestRequestFieldsMappingsInner(
                SurgeComputationModelRequestRequestFieldsMappingsInner.MappingTypeEnum
                    .STATIC_REGION_BASED_CONFIGURATION,
                "v4_c_unmet_rate_1",
                REGION_BASED_UNMET_RATE_1),
            new SurgeComputationModelRequestRequestFieldsMappingsInner(
                SurgeComputationModelRequestRequestFieldsMappingsInner.MappingTypeEnum
                    .STATIC_REGION_BASED_CONFIGURATION,
                "v4_c_unmet_rate_2",
                REGION_BASED_UNMET_RATE_2),
            new SurgeComputationModelRequestRequestFieldsMappingsInner(
                SurgeComputationModelRequestRequestFieldsMappingsInner.MappingTypeEnum
                    .LIVE_STANDARD_INPUT,
                "comfortride_demand",
                LIVE_STANDARD_INPUT_COMFORTRIDE_DEMAND),
            new SurgeComputationModelRequestRequestFieldsMappingsInner(
                SurgeComputationModelRequestRequestFieldsMappingsInner.MappingTypeEnum
                    .LIVE_STANDARD_INPUT,
                "meter_unmet_demand",
                LIVE_STANDARD_INPUT_METER_UNMET_DEMAND));

    SurgeComputationModelRequest request = new SurgeComputationModelRequest();
    request.setModelName("test_error_model");
    request.setDescription("Test model that returns errors");
    request.setEndpointUrl("/test/calculate_surge_error");
    request.setRequestFieldsMappings(mappings);

    // Act
    Response<SurgeComputationModelResponse> response =
        dynamicPricingServiceApi.createSurgeComputationModel(request, TEST_USER_ID).execute();

    // Assert
    assertTrue(response.isSuccessful());
    Assertions.assertNotNull(response.body());

    return response.body().getData().getId();
  }

  /** Creates a model with invalid mapping configuration (references non-existent configurations) */
  private void setupModelDataWithInvalidMapping() throws Exception {
    List<SurgeComputationModelRequestRequestFieldsMappingsInner> mappings =
        List.of(
            new SurgeComputationModelRequestRequestFieldsMappingsInner(
                SurgeComputationModelRequestRequestFieldsMappingsInner.MappingTypeEnum
                    .STATIC_TIME_BASED_CONFIGURATION,
                "v4_t_surge_high",
                "non_existent_time_config"), // Invalid configuration name
            new SurgeComputationModelRequestRequestFieldsMappingsInner(
                SurgeComputationModelRequestRequestFieldsMappingsInner.MappingTypeEnum
                    .STATIC_REGION_BASED_CONFIGURATION,
                "v4_c_unmet_rate_1",
                "non_existent_region_config"), // Invalid configuration name
            new SurgeComputationModelRequestRequestFieldsMappingsInner(
                SurgeComputationModelRequestRequestFieldsMappingsInner.MappingTypeEnum
                    .LIVE_STANDARD_INPUT,
                "comfortride_demand",
                "NonExistentStandardInput")); // Invalid standard input name

    SurgeComputationModelRequest request = new SurgeComputationModelRequest();
    request.setModelName("test_invalid_mapping_model");
    request.setDescription("Test model with invalid mapping configuration");
    request.setEndpointUrl(SURGE_COMPUTATION_MODEL_V4_URL);
    request.setRequestFieldsMappings(mappings);

    // Act
    Response<SurgeComputationModelResponse> response =
        dynamicPricingServiceApi.createSurgeComputationModel(request, TEST_USER_ID).execute();

    // Assert
    assertTrue(response.isSuccessful());
    Assertions.assertNotNull(response.body());
  }

  /** Creates a model that points to an endpoint that returns empty results */
  private Long setupModelDataWithEmptyResponse() throws Exception {
    List<SurgeComputationModelRequestRequestFieldsMappingsInner> mappings =
        List.of(
            new SurgeComputationModelRequestRequestFieldsMappingsInner(
                SurgeComputationModelRequestRequestFieldsMappingsInner.MappingTypeEnum
                    .STATIC_TIME_BASED_CONFIGURATION,
                "v4_t_surge_high",
                TIME_BASED_SURGE_HIGH),
            new SurgeComputationModelRequestRequestFieldsMappingsInner(
                SurgeComputationModelRequestRequestFieldsMappingsInner.MappingTypeEnum
                    .LIVE_STANDARD_INPUT,
                "comfortride_demand",
                LIVE_STANDARD_INPUT_COMFORTRIDE_DEMAND));

    SurgeComputationModelRequest request = new SurgeComputationModelRequest();
    request.setModelName("test_empty_response_model");
    request.setDescription("Test model that returns empty results");
    request.setEndpointUrl("/test/calculate_surge_empty");
    request.setRequestFieldsMappings(mappings);

    // Act
    Response<SurgeComputationModelResponse> response =
        dynamicPricingServiceApi.createSurgeComputationModel(request, TEST_USER_ID).execute();

    // Assert
    assertTrue(response.isSuccessful());
    Assertions.assertNotNull(response.body());

    return response.body().getData().getId();
  }
}

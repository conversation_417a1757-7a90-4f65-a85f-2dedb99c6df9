package integration.tests.surgecomputation.regionmodeldistribution;

import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.NOT_FOUND_REGION_MODEL_DISTRIBUTION;
import static org.junit.jupiter.api.Assertions.*;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.*;
import integration.IntegrationTestBase;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.jdbc.Sql;
import retrofit2.Response;

@Sql(scripts = "/scripts/Cleanup.sql", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
public class DeleteRegionModelDistributionApiIT extends IntegrationTestBase {

  private static final Long TEST_REGION_ID = 1L;
  private static final String USER_ID = "test";

  @Test
  public void testDeleteRegionModelDistribution_Success() throws IOException {
    // Create a region model distribution for testing deletion
    setupDistribution();

    // Get id
    Response<GetRegionModelDistributionResponse> getResponse =
        dynamicPricingServiceApi.getRegionModelDistribution(TEST_REGION_ID).execute();
    assertTrue(getResponse.isSuccessful());
    assertNotNull(getResponse.body());
    Long id = getResponse.body().getModelDistributionVersions().get(0).getId();

    // Execute
    Response<Void> response =
        dynamicPricingServiceApi.deleteRegionModelDistribution(id, USER_ID).execute();

    // Assert
    assertTrue(response.isSuccessful());
    assertEquals(204, response.code());

    // Verify region model distribution has been deleted
    getResponse = dynamicPricingServiceApi.getRegionModelDistribution(TEST_REGION_ID).execute();

    // Should return empty list instead of 404, as this is a query by region ID
    assertTrue(getResponse.isSuccessful());
    assertNotNull(getResponse.body());
    assertEquals(TEST_REGION_ID, getResponse.body().getRegionId());
    assertEquals(1, getResponse.body().getModelDistributionVersions().size());
  }

  @Test
  public void testDeleteRegionModelDistribution_NonExistent() throws IOException {
    // Arrange
    Long nonExistentId = 99L;

    // Execute
    Response<Void> response =
        dynamicPricingServiceApi.deleteRegionModelDistribution(nonExistentId, USER_ID).execute();

    // Assert - should return success as deleting non-existent resource is typically considered
    // idempotent
    assertFalse(response.isSuccessful());
    assertEquals(404, response.code());
    Assertions.assertNotNull(response.errorBody());
    Assertions.assertTrue(
        response
            .errorBody()
            .string()
            .contains(
                MessageFormat.format(
                    NOT_FOUND_REGION_MODEL_DISTRIBUTION.getMessage(), nonExistentId)));
  }

  private void setupDistribution() throws IOException {
    List<Long> modelIds = setupModels();

    OffsetDateTime now = OffsetDateTime.now();
    CreateOrUpdateRegionModelDistributionRequest distribution =
        new CreateOrUpdateRegionModelDistributionRequest();
    distribution.setRegionId(TEST_REGION_ID);
    distribution.setEffectiveFrom(now.minusDays(1));
    distribution.setEffectiveTo(now.plusDays(1));

    List<RegionModelDistributionModelsInner> modelPercentages =
        List.of(
            new RegionModelDistributionModelsInner()
                .modelId(modelIds.get(0))
                .percentage(BigDecimal.TEN),
            new RegionModelDistributionModelsInner()
                .modelId(modelIds.get(1))
                .percentage(BigDecimal.valueOf(50)),
            new RegionModelDistributionModelsInner()
                .modelId(modelIds.get(2))
                .percentage(BigDecimal.valueOf(40)));

    distribution.setModels(modelPercentages);

    // Execute
    Response<Void> response1 =
        dynamicPricingServiceApi
            .createOrUpdateRegionModelDistribution(distribution, USER_ID)
            .execute();

    // Assert
    assertTrue(response1.isSuccessful());
  }

  private List<Long> setupModels() throws IOException {
    List<Long> modelIds = new ArrayList<>();

    // Create three test models
    String[][] modelData = {
      {
        "Test Surge Model1",
        "Test description for surge model1",
        "http://test-endpoint.com/api/surge1"
      },
      {
        "Test Surge Model2",
        "Test description for surge model2",
        "http://test-endpoint.com/api/surge2"
      },
      {
        "Test Surge Model3",
        "Test description for surge model3",
        "http://test-endpoint.com/api/surge3"
      }
    };

    for (String[] data : modelData) {
      SurgeComputationModelRequest request = new SurgeComputationModelRequest();
      request.setModelName(data[0]);
      request.setDescription(data[1]);
      request.setEndpointUrl(data[2]);

      Response<SurgeComputationModelResponse> response =
          dynamicPricingServiceApi.createSurgeComputationModel(request, USER_ID).execute();

      assertTrue(response.isSuccessful());
      assertNotNull(response.body());
      assertNotNull(response.body().getData());
      modelIds.add(response.body().getData().getId());
    }

    return modelIds;
  }
}

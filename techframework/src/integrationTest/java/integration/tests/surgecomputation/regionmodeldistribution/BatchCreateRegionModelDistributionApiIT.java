package integration.tests.surgecomputation.regionmodeldistribution;

import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.REGION_MODEL_DISTRIBUTION_INVALID_REGION_IDS;
import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.REGION_MODEL_DISTRIBUTION_MISSING_REGION_IDS;
import static org.junit.jupiter.api.Assertions.*;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.*;
import integration.IntegrationTestBase;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.jdbc.Sql;
import retrofit2.Response;

@Sql(scripts = "/scripts/Cleanup.sql", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
public class BatchCreateRegionModelDistributionApiIT extends IntegrationTestBase {

  private static final String USER_ID = "test";

  @Test
  public void testBatchCreateRegionModelDistribution_Success() throws IOException {
    // Arrange
    List<Long> modelIds = setupModels();
    OffsetDateTime now = OffsetDateTime.now();

    List<RegionModelDistribution> request = createBatchRequest(modelIds, now);

    // Execute
    Response<Void> response = executeBatchCreateRequest(request);

    // Assert
    assertSuccessfulResponse(response);

    // Verify all regions were created
    for (Long regionId : Arrays.asList(1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L, 9L, 10L)) {
      verifyRegionModelDistribution(regionId, modelIds);
    }
  }

  @Test
  public void testBatchCreateRegionModelDistribution_InvalidPercentageSum() throws IOException {
    // Arrange
    List<Long> modelIds = setupModels();
    OffsetDateTime now = OffsetDateTime.now();

    // Create request with invalid percentage sum (not 100)
    List<RegionModelDistribution> distributions = new ArrayList<>();

    RegionModelDistribution distribution1 =
        createRegionModelDistribution(1L, now.minusDays(1), now.plusDays(1));
    distribution1.setModels(
        Arrays.asList(
            createModelPercentage(modelIds.get(0), BigDecimal.valueOf(60)),
            createModelPercentage(modelIds.get(1), BigDecimal.valueOf(30)),
            createModelPercentage(modelIds.get(2), BigDecimal.valueOf(5)) // Sum = 95, not 100
            ));
    distributions.add(distribution1);

    // Execute
    Response<Void> response = executeBatchCreateRequest(distributions);

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(400, response.code());
    assertTrue(response.errorBody().string().contains("The sum of all percentages must be 100"));
  }

  @Test
  public void testBatchCreateRegionModelDistribution_InvalidModelId() throws IOException {
    // Arrange
    List<Long> modelIds = setupModels();
    OffsetDateTime now = OffsetDateTime.now();

    // Create request with invalid model ID
    List<RegionModelDistribution> distributions = new ArrayList<>();

    RegionModelDistribution distribution1 =
        createRegionModelDistribution(1L, now.minusDays(1), now.plusDays(1));
    distribution1.setModels(
        Arrays.asList(
            createModelPercentage(modelIds.get(0), BigDecimal.valueOf(50)),
            createModelPercentage(modelIds.get(1), BigDecimal.valueOf(30)),
            createModelPercentage(9999L, BigDecimal.valueOf(20)) // Invalid model ID
            ));
    distributions.add(distribution1);

    // Execute
    Response<Void> response = executeBatchCreateRequest(distributions);

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(400, response.code());
    assertTrue(response.errorBody().string().contains("Invalid model ids"));
  }

  @Test
  public void testBatchCreateRegionModelDistribution_MissingModelId() throws IOException {
    // Arrange
    List<Long> modelIds = setupModels();
    OffsetDateTime now = OffsetDateTime.now();

    // Create request with missing model ID (only 2 models instead of 3)
    List<RegionModelDistribution> distributions = new ArrayList<>();

    RegionModelDistribution distribution1 =
        createRegionModelDistribution(1L, now.minusDays(1), now.plusDays(1));
    distribution1.setModels(
        Arrays.asList(
            createModelPercentage(modelIds.get(0), BigDecimal.valueOf(50)),
            createModelPercentage(modelIds.get(1), BigDecimal.valueOf(50))
            // Missing modelIds.get(2)
            ));
    distributions.add(distribution1);

    // Execute
    Response<Void> response = executeBatchCreateRequest(distributions);

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(400, response.code());
    assertTrue(response.errorBody().string().contains("Missing model ids"));
  }

  @Test
  public void testBatchCreateRegionModelDistribution_InvalidRegionId() throws IOException {
    // Arrange
    List<Long> modelIds = setupModels();
    OffsetDateTime now = OffsetDateTime.now();

    // Create request with invalid region ID
    List<RegionModelDistribution> distributions = new ArrayList<>();

    RegionModelDistribution distribution1 =
        createRegionModelDistribution(
            9999L, now.minusDays(1), now.plusDays(1)); // Invalid region ID
    distribution1.setModels(
        Arrays.asList(
            createModelPercentage(modelIds.get(0), BigDecimal.valueOf(40)),
            createModelPercentage(modelIds.get(1), BigDecimal.valueOf(30)),
            createModelPercentage(modelIds.get(2), BigDecimal.valueOf(30))));
    distributions.add(distribution1);

    // Execute
    Response<Void> response = executeBatchCreateRequest(distributions);

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(400, response.code());
    assertTrue(
        response
            .errorBody()
            .string()
            .contains(
                MessageFormat.format(
                    REGION_MODEL_DISTRIBUTION_INVALID_REGION_IDS.getMessage(), List.of(9999))));
  }

  @Test
  public void testBatchCreateRegionModelDistribution_MissingRegionId() throws IOException {
    // Arrange
    List<Long> modelIds = setupModels();
    OffsetDateTime now = OffsetDateTime.now();

    // Create request with missing region ID
    List<RegionModelDistribution> distributions = new ArrayList<>();

    RegionModelDistribution distribution1 =
        createRegionModelDistribution(1L, now.minusDays(1), now.plusDays(1));
    distribution1.setModels(
        Arrays.asList(
            createModelPercentage(modelIds.get(0), BigDecimal.valueOf(40)),
            createModelPercentage(modelIds.get(1), BigDecimal.valueOf(30)),
            createModelPercentage(modelIds.get(2), BigDecimal.valueOf(30))));
    distributions.add(distribution1);

    RegionModelDistribution distribution2 =
        createRegionModelDistribution(2L, now.minusDays(1), now.plusDays(1));
    distribution2.setModels(
        Arrays.asList(
            createModelPercentage(modelIds.get(0), BigDecimal.valueOf(40)),
            createModelPercentage(modelIds.get(1), BigDecimal.valueOf(30)),
            createModelPercentage(modelIds.get(2), BigDecimal.valueOf(30))));
    distributions.add(distribution2);

    // Execute
    Response<Void> response = executeBatchCreateRequest(distributions);

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(400, response.code());
    assertTrue(
        response
            .errorBody()
            .string()
            .contains(
                MessageFormat.format(
                    REGION_MODEL_DISTRIBUTION_MISSING_REGION_IDS.getMessage(),
                    List.of(3, 4, 5, 6, 7, 8, 9, 10))));
  }

  @Test
  public void testBatchCreateRegionModelDistribution_EmptyRequest() throws IOException {
    // Execute
    Response<Void> response = executeBatchCreateRequest(new ArrayList<>());

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(400, response.code());
  }

  @Test
  public void testBatchCreateRegionModelDistribution_MixedValidAndInvalid() throws IOException {
    // Arrange
    List<Long> modelIds = setupModels();
    OffsetDateTime now = OffsetDateTime.now();

    List<RegionModelDistribution> distributions = new ArrayList<>();

    // Valid distribution
    RegionModelDistribution validDistribution =
        createRegionModelDistribution(1L, now.minusDays(1), now.plusDays(1));
    validDistribution.setModels(
        Arrays.asList(
            createModelPercentage(modelIds.get(0), BigDecimal.valueOf(40)),
            createModelPercentage(modelIds.get(1), BigDecimal.valueOf(30)),
            createModelPercentage(modelIds.get(2), BigDecimal.valueOf(30))));
    distributions.add(validDistribution);

    // Invalid distribution (wrong percentage sum)
    RegionModelDistribution invalidDistribution =
        createRegionModelDistribution(2L, now.minusDays(1), now.plusDays(1));
    invalidDistribution.setModels(
        Arrays.asList(
            createModelPercentage(modelIds.get(0), BigDecimal.valueOf(40)),
            createModelPercentage(modelIds.get(1), BigDecimal.valueOf(30)),
            createModelPercentage(modelIds.get(2), BigDecimal.valueOf(20)) // Sum = 90, not 100
            ));
    distributions.add(invalidDistribution);

    // Execute
    Response<Void> response = executeBatchCreateRequest(distributions);

    // Assert - Should fail because of validation error
    assertFalse(response.isSuccessful());
    assertEquals(400, response.code());

    // Verify no data was created (transaction should be rolled back)
    Response<GetRegionModelDistributionResponse> getResponse1 =
        dynamicPricingServiceApi.getRegionModelDistribution(1L).execute();
    assertTrue(getResponse1.isSuccessful());
    // Should have default distribution with zero percentages, not our data
    assertEquals(
        0,
        getResponse1
            .body()
            .getModelDistributionVersions()
            .get(0)
            .getModels()
            .get(0)
            .getPercentage()
            .intValue());
  }

  private List<RegionModelDistribution> createBatchRequest(
      List<Long> modelIds, OffsetDateTime now) {
    List<RegionModelDistribution> distributions = new ArrayList<>();

    // Create distributions for regions 1 and 2
    for (Long regionId : Arrays.asList(1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L, 9L, 10L)) {
      RegionModelDistribution distribution =
          createRegionModelDistribution(regionId, now.minusDays(1), now.plusDays(1));
      distribution.setModels(
          Arrays.asList(
              createModelPercentage(modelIds.get(0), BigDecimal.valueOf(40)),
              createModelPercentage(modelIds.get(1), BigDecimal.valueOf(35)),
              createModelPercentage(modelIds.get(2), BigDecimal.valueOf(25))));
      distributions.add(distribution);
    }

    return distributions;
  }

  private RegionModelDistribution createRegionModelDistribution(
      Long regionId, OffsetDateTime effectiveFrom, OffsetDateTime effectiveTo) {
    RegionModelDistribution distribution = new RegionModelDistribution();
    distribution.setRegionId(regionId);
    distribution.setEffectiveFrom(effectiveFrom);
    distribution.setEffectiveTo(effectiveTo);
    return distribution;
  }

  private RegionModelDistributionModelsInner createModelPercentage(
      Long modelId, BigDecimal percentage) {
    RegionModelDistributionModelsInner model = new RegionModelDistributionModelsInner();
    model.setModelId(modelId);
    model.setPercentage(percentage);
    return model;
  }

  private Response<Void> executeBatchCreateRequest(List<RegionModelDistribution> request)
      throws IOException {
    return dynamicPricingServiceApi.batchCreateRegionModelDistribution(request, USER_ID).execute();
  }

  private void assertSuccessfulResponse(Response<Void> response) {
    assertTrue(response.isSuccessful());
    assertEquals(204, response.code());
  }

  private void verifyRegionModelDistribution(Long regionId, List<Long> expectedModelIds)
      throws IOException {
    Response<GetRegionModelDistributionResponse> getResponse =
        dynamicPricingServiceApi.getRegionModelDistribution(regionId).execute();

    assertTrue(getResponse.isSuccessful());
    GetRegionModelDistributionResponse responseBody = getResponse.body();
    assertNotNull(responseBody);
    assertEquals(regionId, responseBody.getRegionId());
    assertEquals(1, responseBody.getModelDistributionVersions().size());

    List<GetRegionModelDistributionResponseModelDistributionVersionsInnerModelsInner> models =
        responseBody.getModelDistributionVersions().get(0).getModels();
    assertEquals(expectedModelIds.size(), models.size());

    // Verify all expected model IDs are present
    for (Long expectedModelId : expectedModelIds) {
      assertTrue(models.stream().anyMatch(model -> expectedModelId.equals(model.getModelId())));
    }
  }

  private List<Long> setupModels() throws IOException {
    List<Long> modelIds = new ArrayList<>();

    // Create three test models
    String[][] modelData = {
      {
        "Test Surge Model1",
        "Test description for surge model1",
        "http://test-endpoint.com/api/surge1"
      },
      {
        "Test Surge Model2",
        "Test description for surge model2",
        "http://test-endpoint.com/api/surge2"
      },
      {
        "Test Surge Model3",
        "Test description for surge model3",
        "http://test-endpoint.com/api/surge3"
      }
    };

    for (String[] data : modelData) {
      SurgeComputationModelRequest request = new SurgeComputationModelRequest();
      request.setModelName(data[0]);
      request.setDescription(data[1]);
      request.setEndpointUrl(data[2]);

      Response<SurgeComputationModelResponse> response =
          dynamicPricingServiceApi.createSurgeComputationModel(request, USER_ID).execute();

      assertTrue(response.isSuccessful());
      assertNotNull(response.body());
      assertNotNull(response.body().getData());
      modelIds.add(response.body().getData().getId());
    }

    return modelIds;
  }
}

package integration.tests.surgecomputation.regionmodeldistribution;

import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.REGION_MODEL_DISTRIBUTION_INVALID_REGION_ID;
import static org.junit.jupiter.api.Assertions.*;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.*;
import integration.IntegrationTestBase;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.jdbc.Sql;
import retrofit2.Response;

@Sql(scripts = "/scripts/Cleanup.sql", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
public class GetRegionModelDistributionApiIT extends IntegrationTestBase {

  private static final Long TEST_REGION_ID = 1L;
  private static final String USER_ID = "test";

  @Test
  public void testGetRegionModelDistribution_Success() throws IOException {
    OffsetDateTime now = OffsetDateTime.now();
    List<Long> modelIds = setupModels();
    // Create a region model distribution for testing deletion
    setupDistribution(modelIds, now.minusDays(1), now.plusDays(1));

    // Execute
    Response<GetRegionModelDistributionResponse> response =
        dynamicPricingServiceApi.getRegionModelDistribution(TEST_REGION_ID).execute();

    // Assert
    assertTrue(response.isSuccessful());
    assertEquals(200, response.code());

    GetRegionModelDistributionResponse responseBody = response.body();
    assertNotNull(responseBody);
    assertEquals(TEST_REGION_ID, responseBody.getRegionId());
    assertEquals(1, responseBody.getModelDistributionVersions().size());
    assertEquals(3, responseBody.getModelDistributionVersions().get(0).getModels().size());
    assertEquals(true, responseBody.getModelDistributionVersions().get(0).getIsInUse());
    assertNotNull(responseBody.getModelDistributionVersions().get(0).getId());
    assertEquals(
        modelIds.get(0),
        responseBody.getModelDistributionVersions().get(0).getModels().get(0).getModelId());
    assertEquals(
        BigDecimal.TEN,
        responseBody.getModelDistributionVersions().get(0).getModels().get(0).getPercentage());
  }

  @Test
  public void testGetRegionModelDistribution_Success_withNoEffectiveVersion() throws IOException {
    OffsetDateTime now = OffsetDateTime.now();
    List<Long> modelIds = setupModels();
    // Create region model distributions for testing deletion, no effective
    setupDistribution(modelIds, now.minusDays(10), now.minusDays(1));
    setupDistribution(modelIds, now.plusDays(1), now.plusDays(10));

    // Execute
    Response<GetRegionModelDistributionResponse> response =
        dynamicPricingServiceApi.getRegionModelDistribution(TEST_REGION_ID).execute();

    // Assert
    assertTrue(response.isSuccessful());
    assertEquals(200, response.code());

    GetRegionModelDistributionResponse responseBody = response.body();
    assertNotNull(responseBody);
    assertEquals(TEST_REGION_ID, responseBody.getRegionId());
    assertEquals(2, responseBody.getModelDistributionVersions().size());
    assertEquals(false, responseBody.getModelDistributionVersions().get(0).getIsInUse());
    assertEquals(true, responseBody.getModelDistributionVersions().get(1).getIsInUse());
  }

  @Test
  public void testGetRegionModelDistribution_withNonExistent() throws IOException {
    // Arrange
    Long nonExistentRegionId = 1L;

    // Execute
    Response<GetRegionModelDistributionResponse> response =
        dynamicPricingServiceApi.getRegionModelDistribution(nonExistentRegionId).execute();

    // Assert - should return success but with empty model distribution list
    assertTrue(response.isSuccessful());
    assertNotNull(response.body());
    assertEquals(nonExistentRegionId, response.body().getRegionId());
    assertEquals(1, response.body().getModelDistributionVersions().size());
    assertEquals(0, response.body().getModelDistributionVersions().get(0).getModels().size());
  }

  @Test
  public void testGetRegionModelDistribution_BadRequest_NonExistent() throws IOException {
    // Arrange
    Long nonExistentRegionId = 9999L;

    // Execute
    Response<GetRegionModelDistributionResponse> response =
        dynamicPricingServiceApi.getRegionModelDistribution(nonExistentRegionId).execute();

    // Assert - should return success but with empty model distribution list
    assertFalse(response.isSuccessful());
    assertTrue(
        response
            .errorBody()
            .string()
            .contains(
                MessageFormat.format(
                    REGION_MODEL_DISTRIBUTION_INVALID_REGION_ID.getMessage(),
                    nonExistentRegionId)));
  }

  private void setupDistribution(
      final List<Long> modelIds,
      final OffsetDateTime effectiveFrom,
      final OffsetDateTime effectiveTo)
      throws IOException {

    CreateOrUpdateRegionModelDistributionRequest distribution =
        new CreateOrUpdateRegionModelDistributionRequest();
    distribution.setRegionId(TEST_REGION_ID);
    distribution.setEffectiveFrom(effectiveFrom);
    distribution.setEffectiveTo(effectiveTo);

    List<RegionModelDistributionModelsInner> modelPercentages =
        List.of(
            new RegionModelDistributionModelsInner()
                .modelId(modelIds.get(0))
                .percentage(BigDecimal.TEN),
            new RegionModelDistributionModelsInner()
                .modelId(modelIds.get(1))
                .percentage(BigDecimal.valueOf(50)),
            new RegionModelDistributionModelsInner()
                .modelId(modelIds.get(2))
                .percentage(BigDecimal.valueOf(40)));

    distribution.setModels(modelPercentages);

    // Execute
    Response<Void> response1 =
        dynamicPricingServiceApi
            .createOrUpdateRegionModelDistribution(distribution, USER_ID)
            .execute();

    // Assert
    assertTrue(response1.isSuccessful());
  }

  private List<Long> setupModels() throws IOException {
    List<Long> modelIds = new ArrayList<>();

    // Create three test models
    String[][] modelData = {
      {
        "Test Surge Model1",
        "Test description for surge model1",
        "http://test-endpoint.com/api/surge1"
      },
      {
        "Test Surge Model2",
        "Test description for surge model2",
        "http://test-endpoint.com/api/surge2"
      },
      {
        "Test Surge Model3",
        "Test description for surge model3",
        "http://test-endpoint.com/api/surge3"
      }
    };

    for (String[] data : modelData) {
      SurgeComputationModelRequest request = new SurgeComputationModelRequest();
      request.setModelName(data[0]);
      request.setDescription(data[1]);
      request.setEndpointUrl(data[2]);

      Response<SurgeComputationModelResponse> response =
          dynamicPricingServiceApi.createSurgeComputationModel(request, USER_ID).execute();

      assertTrue(response.isSuccessful());
      assertNotNull(response.body());
      assertNotNull(response.body().getData());
      modelIds.add(response.body().getData().getId());
    }

    return modelIds;
  }
}

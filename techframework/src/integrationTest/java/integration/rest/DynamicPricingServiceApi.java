package integration.rest;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.*;
import java.util.List;
import retrofit2.Call;
import retrofit2.http.*;

public interface DynamicPricingServiceApi {

  // Surge Computation Model endpoints
  @GET("/v1.0/surge-computation/models")
  Call<SurgeComputationModelListResponse> getAllSurgeComputationModels();

  @GET("/v1.0/surge-computation/models/{id}")
  Call<SurgeComputationModelResponse> getSurgeComputationModelById(@Path("id") Long id);

  @POST("/v1.0/surge-computation/models")
  Call<SurgeComputationModelResponse> createSurgeComputationModel(
      @Body SurgeComputationModelRequest request, @Header("X-User-Id") String userId);

  @PUT("/v1.0/surge-computation/models/{id}")
  Call<SurgeComputationModelResponse> updateSurgeComputationModel(
      @Path("id") Long id,
      @Body SurgeComputationModelRequest request,
      @Header("X-User-Id") String userId);

  @DELETE("/v1.0/surge-computation/models/{id}")
  Call<Void> deleteSurgeComputationModel(@Path("id") Long id, @Header("X-User-Id") String userId);

  // Surge Computation Time-Based Static Configuration endpoints
  @GET("/v1.0/surge-computation/static-time-based-configurations/versions")
  Call<StaticBasedConfigurationVersionListResponse> getStaticTimeBasedConfigurationVersions();

  @GET("/v1.0/surge-computation/static-time-based-configurations")
  Call<StaticTimeBasedConfigurationListResponse> getStaticTimeBasedConfigurations(
      @Query("version") String version);

  @GET("/v1.0/surge-computation/static-time-based-configurations/{id}")
  Call<StaticTimeBasedConfigurationResponse> getStaticTimeBasedConfigurationById(
      @Path("id") Long id);

  @POST("/v1.0/surge-computation/static-time-based-configurations")
  Call<StaticTimeBasedConfigurationCreateResponse> batchCreateStaticTimeBasedConfiguration(
      @Body List<StaticTimeBasedConfigurationRequest> request, @Header("X-User-Id") String userId);

  @PUT("/v1.0/surge-computation/static-time-based-configurations/{id}")
  Call<StaticTimeBasedConfigurationResponse> updateStaticTimeBasedConfiguration(
      @Path("id") Long id,
      @Body StaticTimeBasedConfigurationRequest request,
      @Header("X-User-Id") String userId);

  @DELETE("/v1.0/surge-computation/static-time-based-configurations/{id}")
  Call<Void> deleteStaticTimeBasedConfiguration(
      @Path("id") Long id, @Header("X-User-Id") String userId);

  @GET("/v1.0/surge-computation/static-time-based-configurations/effective-check")
  Call<StaticBasedConfigurationEffectiveCheckResponse> staticTimeBasedConfigurationEffectiveCheck();

  @GET("/v1.0/surge-computation/static-time-based-configurations/current-value")
  Call<CurrentStaticTimeBasedConfigurationValueResponse>
      getCurrentStaticTimeBasedConfigurationValue();

  // Static Region-Based Configuration endpoints
  @GET("/v1.0/surge-computation/{modelId}/static-region-based-configurations/versions")
  Call<StaticBasedConfigurationVersionListResponse> getStaticRegionBasedConfigurationVersions(
      @Path("modelId") Long modelId);

  @GET("/v1.0/surge-computation/{modelId}/static-region-based-configurations")
  Call<StaticRegionBasedConfigurationListResponse> getStaticRegionBasedConfigurations(
      @Path("modelId") Long modelId, @Query("version") String version);

  @GET("/v1.0/surge-computation/static-region-based-configurations/{id}")
  Call<StaticRegionBasedConfigurationResponse> getStaticRegionBasedConfigurationById(
      @Path("id") Long id);

  @POST("/v1.0/surge-computation/static-region-based-configurations")
  Call<StaticRegionBasedConfigurationBatchCreateResponse> batchCreateStaticRegionBasedConfiguration(
      @Header("X-User-Id") String userId,
      @Body List<StaticRegionBasedConfigurationRequest> request);

  @PUT("/v1.0/surge-computation/{modelId}/static-region-based-configurations")
  Call<StaticRegionBasedConfigurationResponse> updateStaticRegionBasedConfiguration(
      @Path("modelId") Long modelId,
      @Header("X-User-Id") String userId,
      @Body List<StaticRegionBasedConfigurationRequest> request);

  @GET("/v1.0/surge-computation/static-region-based-configurations/effective-check")
  Call<StaticBasedConfigurationEffectiveCheckResponse> staticRegionBasedConfigurationEffectiveCheck(
      @Query("modelId") Long modelId);

  // Region Model Distribution endpoints
  @DELETE("/v1.0/surge-computation/region-model-distribution/id/{id}")
  Call<Void> deleteRegionModelDistribution(@Path("id") Long id, @Header("X-User-Id") String userId);

  @GET("/v1.0/surge-computation/region-model-distribution/region/{regionId}")
  Call<GetRegionModelDistributionResponse> getRegionModelDistribution(
      @Path("regionId") Long regionId);

  @PUT("/v1.0/surge-computation/region-model-distribution")
  Call<Void> createOrUpdateRegionModelDistribution(
      @Body CreateOrUpdateRegionModelDistributionRequest request,
      @Header("X-User-Id") String userId);

  @POST("/v1.0/surge-computation/region-model-distribution/batch")
  Call<Void> batchCreateRegionModelDistribution(
      @Body List<RegionModelDistribution> request, @Header("X-User-Id") String userId);

  @GET("/v1.0/surge-computation/standard-inputs")
  Call<GetStandardInputsResponse> getStandardInputs();

  @POST("/v1.0/surge-computation/calculate-surge-factor")
  Call<Void> calculateSurgeFactor();

  @GET("/v1.0/surge-computation/current-surge-values")
  Call<CurrentSurgeValuesResponse> getCurrentSurgeValues();

  @POST("/v1.0/pricing/multi-fare")
  Call<GetEstimatedFareResponse> getMultiFare(@Body GetEstimatedFareInboundRequest request);

  @POST("/v1.0/pricing/store-fare-breakdown")
  Call<StoreFareBreakdownInboundResponse> storeFareBreakdown(
      @Body StoreFareBreakdownInboundRequest request);
}

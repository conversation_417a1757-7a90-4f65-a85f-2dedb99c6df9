// This proto defines Google's Book-a-Ride feed format.
//
// The purpose is to enable Book-a-Ride directions results on Google Maps (and beyond)
// that provide reasonably up-to-date wait time and fare estimates.
// The underlying assumption is that a feed is updated frequently, such as
// every two to three minutes, and that a new feed version replaces all data of
// a previous feed. Furthermore, if no feed updates are received for a certain
// timespan, then the last known feed version is considered stale, and no wait
// time or fare estimates will be presented to the user until the next feed
// update is available.

syntax = "proto3";

import "google/protobuf/timestamp.proto";

option java_package = "com.cdg.pmg.ngp.me.dynamicpricing.techframework";
option java_multiple_files = true;

// A TaxiFeed describes products that are available in certain locations,
// such as fare parameters and current local wait time estimates.
//
// A Book-a-Ride provider can deliver its data through one or more feeds. A feed
// consists of exactly one TaxiFeed message, which describes one or more
// products. Although we don't restrict feeds geographically by design, in
// practice each product is typically available only in a particular city or
// region, for example Berlin, Sydney, or the Bay Area.
// Once a newer version of a feed becomes available, all previously known data
// for that feed is discarded.

message TaxiFeed {
  // Describes the feed version.
  TaxiFeedMetaData meta_data = 1;

  // The set of products that are available in the feed.
  repeated TaxiProduct products = 2;

  // Describes named areas that can be used for various purposes, such as to
  // model flat rates between an airport and a city center.
  repeated TaxiArea areas = 3;

  // Describes all of your region-bounded taxi products.
  // This field must be empty if the products field is non-empty.
  repeated TaxiRegionBoundedProduct region_bounded_products = 4;
}

// Contains data that allows version management.

message TaxiFeedMetaData {
  // A human-readable identifier for the feed that's stable across feed
  // versions. Outdated versions of a
  // feed are replaced with the latest known version. such as us-west-9.
  // Encoded in UTF8.
  string feed_id = 1;

  message Timestamp {
    // Represents the seconds of UTC time that have passed since Unix epoch
    // 1970-01-01T00:00:00Z. Must be from 0001-01-01T00:00:00Z to
    // 9999-12-31T23:59:59Z inclusive.
    int64 seconds = 1;
  }
  // A point in time that's representative for the data in the feed.
  // Defines the order of different feed versions.
  Timestamp creation_date = 2;
}

// A product, in particular its fare, is local to an area. The area is defined
// by the union of the TaxiGeoData entries that reference the product.

message TaxiProduct {
 // Provides a unique identifier for a given product. The same product_id is
  // used within all the feeds from the same partner, and it stays stable across
  // feed versions. This doesn't need to be human readable.
  // Example: "de304e54-4711-431b-adc2-eb6b8e546013".
  string product_id = 1;

  // Provides a human-readable identifier for the product, such as "Yellow Cab,
  // New York". This is only used to monitor and debug the feed. Encoded in UTF8.
  string internal_name = 2;

  // Describes how to display the product name to the user in specific locales.
  // Maps from language to localized name.
  // The localized names must be consistent with the branding of the rideshare
  // provider. Each product in the feed must contain the localized product name
  // in English. We recommend that the localized product name in other languages
  // also be included in the feed.

  // The language is given as a BCP-47 language tag, such as "en", "de-CH", or
  // "zh-Hant-HK". For more details, see
  // http://www.w3.org/International/articles/language-tags/ or
  // http://www.rfc-editor.org/rfc/bcp/bcp47.txt.
  map<string, string> localized_names = 3;

  // Describes properties of the product that are location dependent, such as
  // the estimated wait time.
  TaxiGeoData geo_data = 4;

  // Describes applicable fare models for this product.
  TaxiFare fare = 5;

  // Defines promotions for this product, if available.
  TaxiPromotion promotion = 6;

  // Shows the currency in which fares and promotions are given.
  // This is an ISO 4217 currency code, such as USD, EUR, or CHF.
  string currency_code = 7;

  // Provides an identifier that's used to deep link into a partner app
  // to select the right product in the app. // The value
  // of this field is a unique product ID, such as
  // product_id=bb4133cd-7020-48e1-ab54-c3418a2eeb4c. If this field isn't set,
  // the value of product_id is used instead.
  // This field lets you model a product through several TaxiProduct
  // messages. This is useful in situations where the fare depends on the pickup
  // location, such as a city center versus suburbs, but the partner app expects
  // the same product ID regardless of the fare or pickup location.
  // The value of this field doesn't need to be unique among all TaxiProduct
  // messages.
  string deep_link_product_id = 8;

  // Provides the category of the product.
  ProductCategory product_category = 10;

  // Specifies the maximum passenger capacity for the product.
  SeatCountParams seat_count_params = 11;

  // Defines the icon used to display cars on the map.
  // This icon identifier is provided by Google, and it must match the regex
  // /[-_A-Za-z0-9]+/.
  string car_icon_id = 12;

  // Do not reuse old ids.
  reserved 9;
}


// A region-bounded rideshare product, which offers trips between a fixed set of
// origin regions to a fixed set of destination regions.
// Unlike TaxiProduct, which describes a range-bounded product where the
// destination must be within a fixed range of the origin, region-bounded
// products explicitly specify both origin and destination regions.
//
// All trips within a product share the same currency code, promotion information,
// and localization rules. At the same time, each trip defines its own
// pickup and dropoff regions, price (in the product's currency), and schedule.
message TaxiRegionBoundedProduct {
  // A unique identifier. The ID is associated with a partner and must be used
  // across different feed versions.
  // For example: "de304e54-4711-431b-adc2-eb6b8e546013".
  // Required.
  string product_id = 1;

  // A human-readable identifier for the product.
  // Used only to monitor and debug the feed. For example: "Casey's Chicago
  // Cabs".
  string internal_name = 2;

  // Describes how to display the product name to the user in specific locales.
  // It maps from a language to the localized name.
  // The language is given as a BCP-47 language tag, such as "en", "de-CH", or
  // "zh-Hant-HK". For details, see
  // http://www.w3.org/International/articles/language-tags/ or
  // http://www.rfc-editor.org/rfc/bcp/bcp47.txt.
  // The localized name is shown to the user and must be consistent with the
  // branding of the ride provider.
  // Optional, provider's name is used if no suitable localization is provided.
  map<string, string> localized_names = 3;

  // Provides the ISO 4217 alphabetic currency code in which fares and
  // promotions are given. For example: EUR.
  // See: https://www.iso.org/iso-4217-currency-codes.
  // Required.
  string currency_code = 4;

  // Promotion model for this product, if available.
  // Optional.
  TaxiPromotion promotion = 5;

  // All currently-available trips that match this product offering.
  // Products without any valid trips are considered invalid. Required.
  repeated TaxiRegionBoundedTrip trips = 6;
}

// Describes properties of a Book-a-Ride product that are location dependent.
// The location of a product is every geographical region associated with the
// product. You define these regions with the S2 Geometry Library.
//
// For an introduction to the S2 Geometry Library, see
// https://developers.google.com/book-a-ride/reference/s2-geometry-library.
//
// Recommended S2 cell sizes are level 12 (around 1.3 km^2) or level 13 (around
// 5.1 km^2). Cells of different levels can be used as keys of the map. In cases
// where cells overlap, the data associated with the higher-level, more
// specific cell is preferred.

message TaxiGeoData {
  // Maps a wait time, given in seconds, to an S2 cell ID.
  // Each entry contains the current estimated wait time at a pickup
  // location for the given S2 cell. This is commonly measured as the estimated
  // duration between ordering a ride and a vehicle arriving at the pickup
  // location. The value of each entry must be greater than 0.
  message WaitingTimeEntry {
    uint64 key = 1;
    int32 value = 2;
  }
  // Repeated values of WaitingTimeEntry are equivalent to the use of
  // "map<uint64, int32>".
  repeated WaitingTimeEntry waiting_times_seconds = 1;

  // Maps a fare multiplier to an S2 cell ID.
  // Each entry contains the current fare multiplier at a pickup location for
  // the given S2 cell. The fare multiplier is used as a factor in
  // the fare formula in StandardRate.
  // The value of each entry must be nonnegative. The default value is 1.0. If
  // the multiplier equals 1.0, it can be omitted.
  message FareMultiplierEntry {
    uint64 key = 1;
    double value = 2;
  }
  // Repeated values of FareMultiplierEntry are equivalent to the use of
  // "map<uint64, double>".
  repeated FareMultiplierEntry fare_multipliers = 2;

  // List of S2 cell IDs that define a restricted dropoff area. If specified,
  // the taxi product dropoffs will be restricted to this area. I.e. the
  // product won't trigger on queries having destination outside of this area.
  // If omitted, the dropoff areas are assumed to be unrestricted.
  repeated uint64 restricted_dropoff_areas = 4;

  // Do not reuse old IDs.
  reserved 3;
}

// Describes an available region-bounded trip. Each trip has its own
// fare model, as well as defined pickup and dropoff regions. The trip's
// applicable currency and promotion are determined by its
// TaxiRegionBoundedProduct. Each trip is associated with a single
// TaxiRegionBoundedProduct.
message TaxiRegionBoundedTrip {
  // An identifier that must be unique across all of a partner's feeds. No two
  // trips can use the same ID, even if they are in separate feed files. For
  // example: sg04e57-4711-567s-adc2-eb6b8e547128.
  // Required.
  string deep_link_trip_id = 1;

  // Describes the valid pickup areas for this trip. Trips without any pickup
  // areas are considered invalid.
  // Each value must be unique and refer to a valid zero-based index in
  // TaxiFeed.areas. Required.
  repeated uint32 pickup_area_index = 2;

  // Describes the valid dropoff areas for this trip. Trips without any dropoff
  // areas are considered invalid.
  // Each value must be unique and refer to a valid zero-based index in
  // TaxiFeed.areas. Required.
  repeated uint32 dropoff_area_index = 3;

  // Describes the fare models for this product. The currency is
  // determined by the TaxiRegionBoundedProduct message.
  //
  // The fare model can't contain surcharges.
  // Required.
  TaxiFare fare = 4;

  // Describes this trip's schedule.
  message TripSchedule {
    // Describes the initially scheduled start time of this trip instance.
    // This value must describe a time in the future.
    // Required.
    google.protobuf.Timestamp start_time = 1;

    // Describes the time after which this trip can no longer be booked.
    // This value must describe a time in the future, and it must be at or
    // before start_time.
    // Required.
    google.protobuf.Timestamp expiration_time = 2;
  }
  // Describes the trip's schedule. Provide this field for trips that have a
  // fixed departure time, such as when it's scheduled by the driver, rather
  // than an always-available offering, such as a daily, fixed inter-city route.
  // Optional.
  TripSchedule trip_schedule = 5;
}

// Describes parameters for the formula for a fare. These are used to calculate
// an estimated range for the price of a ride.

message TaxiFare {
  // Describes parameters of a formula used to compute a range of fare estimates,
  // from "low" to "high." Given the duration and distance of a ride,
  // the estimate is computed as follows:
  // low = max(minimum_fixed,
  //           fare_multiplier *
  //           max(minimum,
  //              base + (low_range_estimate_multiplier
  //                       * (distance_km * price_per_km
  //                          + duration_minutes * price_per_minute))))
  //       + fees;
  //
  // high = max(minimum_fixed,
  //            fare_multiplier *
  //            max(minimum,
  //                base + (high_range_estimate_multiplier
  //                        * (distance_km * price_per_km
  //                           + duration_minutes * price_per_minute))))
  //        + fees;

  message StandardRate {
    // Provides the base fare for a ride.
    double base = 1;

    // Provides the minimum fare for a ride. Comes in two flavors: one subject
    // to the fare multiplier, the other not. You must provide at least one of
    // either minimum or minimum_fixed, but both aren't required.
    double minimum = 2;        // Subject to the fare multiplier.
    double minimum_fixed = 8;  // Not subject to the fare multiplier.

    // Provides the price per kilometer driven for a ride.
    // StandardRate requires at least one of either price_per_km or
    // price_per_minute.
    double price_per_km = 3;

    // Provides the price per minute for a ride.
    // StandardRate requires at least one of either price_per_km or
    // price_per_minute.
    double price_per_minute = 4;

    message DoubleValue {
      // Provides the double value for the multiplier.
      double value = 1;
    }
    // Provides the multipliers used to compute the price range that's given to
    // the user. If used, we require that
    // 0 < low_range_estimate_multiplier < high_range_estimate_multiplier.
    DoubleValue low_range_estimate_multiplier = 5;
    DoubleValue high_range_estimate_multiplier = 6;

    // Provides any fees that are added on top of the fare and that aren't
    // subject to the fare multiplier. The fees field can't be used for tolls,
    // which are trip dependent.
    double fees = 7;
  }
  StandardRate standard_rate = 1;

  // The FlatRate message models rides that are offered for a fixed
  // amount, as long as the pickup and dropoff points for the ride fall into
  // specific areas that you define. For example, FlatRate could be used for
  // rides from an airport to a city center. A flat rate applies if the pickup
  // point of the ride is within the pickup area and the dropoff point is within
  // the dropoff area. In such a case, the StandardRate doesn't apply and is
  // overridden by FlatRate.

  message FlatRate {
    // Describes the valid pickup or dropoff points for this flat rate.
    // Each pair of (pickup, dropoff) values must be unique and refer to a valid
    // zero-based index in TaxiFeed.areas.
    int32 pickup_area_index = 1;
    int32 dropoff_area_index = 2;

    // The total fare for a ride. In this case, the fare estimate isn't a range
    // but a single amount.
    double amount = 3;
  }
  repeated FlatRate flat_rates = 2;

  // Defines a fare, similar to StandardRate. However, the price per kilometer
  // and price per minute of the ride can vary over distance or time, so
  // piecewise linear functions are used to calculate the rate. Given the
  // duration and distance of a ride, the estimate is computed as follows:
  // low range = max(minimum_fixed, fare_multiplier *
  //                 max(mimimum, base + low_range_estimate_multiplier
  //                     * (distance_km * price_per_km_function +
  //                        duration_minutes * price_per_minute_function)))
  //             + fees;
  //
  // high range = max(minimum_fixed, fare_multiplier *
  //                  max(minimum, base + (high_range_estimate_multiplier
  //                      * (distance_km * price_per_km_function +
  //                         duration_minutes * price_per_minute_function)))
  //              + fees;

  message PiecewiseLinearRate {
    // Describes the underlying rate for the ride. All of the fields from
    // StandardRate apply except for price_per_km and price_per_minute, which
    // are both ignored.
    StandardRate standard_rate = 1;

    // Models a piecewise linear function that maps between the number of
    // kilometers or minutes driven and the fare for the trip. It uses the
    // currency defined by TaxiProduct.currency_code.

    message PiecewiseLinearFunction {
      message Segment {
        // Gives the total number of units, in kilometers or minutes, that must
        // be driven since the start of the ride before this Segment applies. If
        // another Segment follows this one, the (implicit) end of this Segment
        // is the beginning of the next one.
        double start = 1;

        // Gives the price per unit to be applied along this Segment. The units
        // can be kilometers or minutes. The total price is the sum of price
        // times units driven along a Segment, taken over all Segments.
        double price = 2;
      }
      // Defines a piecewise linear price function. The line segments are given
      // in strictly increasing order, beginning with a "start" of 0.
      // For example, the following segments (start, price), defined by the
      // Segments type, are a possible input for PiecewiseLinearFunction:
      //   (0 km, $0.60), (5 km, $0.50), (15 km, $0.30)
      // The total price is the sum of price times units driven along a Segment,
      // taken over all Segments. In this case, the price for a distance of
      // 17 km is the following:
      //   $8.60 = 5 * $0.60 + 10 * $0.50 + 2 * $0.30
      repeated Segment segments = 1;
    }

    // Maps the distance traveled, in kilometers, to a price.
    PiecewiseLinearFunction price_per_km_function = 2;

    // Maps the duration of a ride, in minutes, to a price.
    PiecewiseLinearFunction price_per_minute_function = 3;
  }
  PiecewiseLinearRate piecewise_linear_rate = 3;


 // Describes a surcharge that's to be added to the fare, whether or not
  // FlatRate applies, iff either of the following are true:
  //   1) The entrance area of the surcharge is the same (by index) as the exit
  //      area, and some part of the route of the trip intersects that area.
  //   2) The entrance area is distinct from the exit area, and the route of the
  //      trip crosses the pickup area first and then the dropoff area. In such
  //      a case, the aforementioned two areas must not overlap.
  // This message is intended to model tolls, where each of the referenced areas
  // represents a toll barrier or plaza.

  message TollSurcharge {
    // A human-readable identifier that's used only to monitor and debug.
    // For example, “airport surcharge” or “inter-city surcharge”.
    // Encoded in UTF8.
    string name = 1;

    // Describes the entrance area for the surcharge. Each pair of (pickup,
    // dropoff) values must be unique and refer to a valid zero-based index in
    // TaxiFeed.areas.
    int32 entrance_area_index = 2;

    // Describes the exit area for the surcharge. Each pair of (pickup, dropoff)
    // values must be unique and refer to a valid zero-based index in
    // TaxiFeed.areas.
    int32 exit_area_index = 3;

    // The amount added to the fare of any trip for which this surcharge
    // applies. This isn't subject to the fare multipliers described in
    // StandardRate.
    double amount = 4;
  }
  // The set of surcharges that might apply to this product, based on the route
  // of the trip.
  repeated TollSurcharge toll_surcharges = 4;

  // Describes a surcharge that's to be added to the fare iff the pickup and
  // dropoff points of the ride are within a specified area.

  message EndpointsSurcharge {
    // Provides a human-readable identifier, which is used only to monitor and
    // debug. For example, “airport surcharge” or “inter-city surcharge”.
    // Encoded in UTF8.
    string name = 1;

    // Describes the pickup area for the surcharge. Each pair of (pickup,
    // dropoff) values must be unique and refer to a valid zero-based index in
    // TaxiFeed.areas.
    int32 pickup_area_index = 2;

    // Describes the dropoff area for the surcharge. Each pair of (pickup,
    // dropoff) values must be unique and refer to a valid zero-based index in
    // TaxiFeed.areas.
    int32 dropoff_area_index = 3;

    // The amount added to the fare of any trip for which this surcharge
    // applies. This isn't subject to the fare multipliers described in
    // StandardRate.
    double amount = 4;
  }
  // The set of surcharges that might apply to this product, based on the pickup
  // and dropoff points.
  repeated EndpointsSurcharge endpoints_surcharges = 5;
}

// Contains the details used to trigger promotional results, such as offers for
// free or discounted rides.

message TaxiPromotion {
  // Offers a free ride for new users up to the given amount. For example: "Free
  // first ride (up to $15)."

  message FirstRideFree {
    // Gives the amount up to which the first ride is offered for free. If the
    // ride costs more than this amount, the user gets a discount up to this
    // value. Uses the currency that's specified in TaxiProduct.currency_code.
    double coupon = 1;
    // The promo code, such as "GOOG35", that the user must apply to get the
    // discount. If empty, no code is required.
    string promo_code = 2;
  }

  // Promotional discount that offers a number of discounted rides. For example:
  // "3 rides free, up to $5 each (use code GOOGLE315)."

  message FreeRides {
    // Gives the amount up to which the rides are offered for free. If the ride
    // costs more than this amount, the user gets a discount up to this value.
    // Uses the currency that's specified in TaxiProduct.currency_code.
    double coupon = 1;
    // Gives the number of rides for which the discount is offered.
    int32 num_rides = 2;  // Required; must be at least 1.
    // The promo code, such as "GOOGLE35", that users must apply to get the
    // discount.
    string promo_code = 3;  // Required.
  }
  // Promotional discount that offers ride credits that can be applied across
  // multiple discounted rides. For example: "$20 in ride credits (use code
  // GOOGLE20)."

  message RideCredits {
    // Gives the value of the ride credits. Uses the currency that's specified
    // in TaxiProduct.currency_code.
    double coupon = 1;
    // The promo code, such as "GOOGLE35", that users must apply to get the
    // discount.
    string promo_code = 2;  // Required.
  }
  // The promotion must be exactly one of the following types.
  // WARNING: Once one field is set, it automatically clears all other members.
  // If you have set several oneof fields, only the last one you set is used.
  //oneof promotion {
  //  FirstFreeRide first_free_ride = 1;
  //  FreeRides free_rides = 2;
  //  RideCredits ride_credits = 3;
  //}
}

// Describes a named area that's formed by a collection of S2 cells. Use the
// S2 Geometry Library to define these regions. The same taxi area can be reused
// throughout your feed.

message TaxiArea {
  // Defines a human-readable identifier that's used across all feed versions.
  // For example, "JFK airport" or "Brooklyn". Used only to monitor and debug
  // the feed. Encoded in UTF8.
  string name = 1;

  // A set of S2 cell IDs, the union of which defines the area.
  repeated uint64 s2_cells = 2;
}

// Identifies the category for a single product.
// Usually used to describe the most pertinent feature of a product.
enum ProductCategory {
  // A generic product category that is the default field value. Used to group
  // together products that belong to an unspecified category, or to no category
  //  at all.
  UNKNOWN_CATEGORY = 0;

  // A single ride that can be shared between multiple bookings by several
  // users.
  CARPOOL_CATEGORY = 1;

  // A middle-tier product.
  ECONOMY_CATEGORY = 2;

  // A premium-tier product.
  PREMIUM_CATEGORY = 3;

  // A product that has an increased passenger capacity.
  HIGH_CAPACITY_CATEGORY = 4;

  // A government-licensed taxicab product.
  LICENSED_TAXI_CATEGORY = 5;

  // A motorcycle product suitable for one passenger.
  MOTORCYCLE_CATEGORY = 6;

  // An auto rickshaw, a three-wheeled vehicle suitable for up to two
  // passengers.
  AUTO_RICKSHAW_CATEGORY = 7;
}

// Specifies the maximum passenger capacity for the product.
// Note that this proto is used by TaxiFeed and UberFeed.
message SeatCountParams {

  // Specifies the seating capacity for the ride. Must be at least one.
  // For example, carpool products might be limited to two passengers, while
  // luxury products might be limited to four passengers.
  int32 max_seat_count = 1;

  // Defines whether end users must specify the number of seats they require
  // when they book a ride. If true, the required number of seats must be in the
  // range [1, max_seat_count].
  bool requires_seat_count = 2;
}
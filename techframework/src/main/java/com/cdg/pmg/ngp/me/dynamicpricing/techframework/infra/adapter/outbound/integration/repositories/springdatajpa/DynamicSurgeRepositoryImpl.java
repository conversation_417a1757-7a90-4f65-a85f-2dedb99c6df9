package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.DynamicSurgeRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.DynamicSurgesEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.DynamicSurgeJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.DynamicSurgeNgpJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.DynamicSurgeJPARepository;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.DynamicSurgeNgpJPARepository;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.mapper.DynamicSurgeMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.utils.CollectionUtils;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@Primary
@RequiredArgsConstructor
@Slf4j
public class DynamicSurgeRepositoryImpl implements DynamicSurgeRepository {
  private final DynamicSurgeJPARepository dynamicSurgeJPARepository;
  private final DynamicSurgeNgpJPARepository dynamicSurgeNgpJPARepository;
  private final DynamicSurgeMapper dynamicSurgeMapper;
  private final EntityManager entityManager;

  @Override
  public List<DynamicSurgesEntity> getDynpSurges() {
    final List<DynamicSurgeJPA> dynpSurges = dynamicSurgeJPARepository.getDynpSurges();
    log.info("Dynamic surges size: {}", dynpSurges.size());

    if (CollectionUtils.isEmpty(dynpSurges)) {
      return new ArrayList<>();
    }

    return dynpSurges.stream()
        .map(
            dynpSurge ->
                DynamicSurgesEntity.builder()
                    .zoneId(dynpSurge.getZoneId())
                    .surge(dynpSurge.getSurge())
                    .lastUpdDt(dynpSurge.getLastUpdDt())
                    .surgeLow(dynpSurge.getSurgeLow())
                    .surgeHigh(dynpSurge.getSurgeHigh())
                    .demandRecent(dynpSurge.getDemandRecent())
                    .demandPrevious(dynpSurge.getDemandPrevious())
                    .demandPredicted(dynpSurge.getDemandPredicted())
                    .supply(dynpSurge.getSupply())
                    .prevSurge(dynpSurge.getPrevSurge())
                    .batchKey(dynpSurge.getBatchKey())
                    .build())
        .toList();
  }

  @Override
  public void updateDynpSurges(List<DynamicSurgesEntity> entities) {
    final List<DynamicSurgeNgpJPA> jpaEntities =
        entities.stream()
            .map(
                entity ->
                    DynamicSurgeNgpJPA.builder()
                        .zoneId(entity.getZoneId())
                        .prevSurge(entity.getPrevSurge())
                        .surge(entity.getSurge())
                        .surgeLow(entity.getSurgeLow())
                        .surgeHigh(entity.getSurgeHigh())
                        .supply(entity.getSupply())
                        .lastUpdDt(entity.getLastUpdDt())
                        .demandPredicted(entity.getDemandPredicted())
                        .demandPrevious(entity.getDemandPrevious())
                        .demandRecent(entity.getDemandRecent())
                        .excessDemand(entity.getExcessDemand())
                        .batchKey(entity.getBatchKey())
                        .build())
            .toList();
    dynamicSurgeNgpJPARepository.saveAll(jpaEntities);
  }

  @Override
  @Transactional(readOnly = true)
  public List<DynamicSurgesEntity> getNGPDynpSurges() {
    final List<DynamicSurgeNgpJPA> dynpSurges = dynamicSurgeNgpJPARepository.getDynpSurges();
    log.info("Dynamic surges size: {}", dynpSurges.size());

    if (CollectionUtils.isEmpty(dynpSurges)) {
      return new ArrayList<>();
    }
    return dynamicSurgeMapper.toListDynamicSurgesEntities(dynpSurges);
  }

  @Transactional
  @Override
  public void removeInvalidDynSurges() {
    dynamicSurgeNgpJPARepository.removeInvalidDynSurges();
  }

  @Transactional
  @Override
  public void updateDynpSurgesV2(List<DynamicSurgesEntity> entities) {
    if (CollectionUtils.isEmpty(entities)) {
      return;
    }

    StringBuilder sb =
        new StringBuilder(
            "INSERT INTO dynp_surges_ngp(zone_id,surge,surge_low,surge_high,demand_recent,");
    sb.append(
        "demand_previous,demand_predicted,supply,prev_surge,last_upd_dt,excess_demand,batch_key,");
    sb.append(
        "demand_predicted_15,zone_price_model,unmet_15,unmet_15_pre,demand_15,excess_demand_15,created_dt,created_by) VALUES");
    for (int i = 0; i < entities.size(); i++) {
      sb.append("(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
      if (i != entities.size() - 1) {
        sb.append(",");
      }
    }
    sb.append("  ON CONFLICT (zone_id) DO UPDATE SET ");
    sb.append("surge=EXCLUDED.surge,surge_low=EXCLUDED.surge_low,");
    sb.append("surge_high=EXCLUDED.surge_high,demand_recent=EXCLUDED.demand_recent,");
    sb.append(
        "demand_previous=EXCLUDED.demand_previous,demand_predicted=EXCLUDED.demand_predicted");
    sb.append(",supply=EXCLUDED.supply,prev_surge=EXCLUDED.prev_surge,");
    sb.append("last_upd_dt=EXCLUDED.last_upd_dt,excess_demand=EXCLUDED.excess_demand,");
    sb.append("batch_key=EXCLUDED.batch_key,demand_predicted_15=EXCLUDED.demand_predicted_15,");
    sb.append("demand_15=EXCLUDED.demand_15,excess_demand_15=EXCLUDED.excess_demand_15,");
    sb.append("zone_price_model=EXCLUDED.zone_price_model,");
    sb.append("updated_dt=EXCLUDED.last_upd_dt,");
    sb.append("updated_by='system',");
    sb.append("unmet_15=EXCLUDED.unmet_15,unmet_15_pre=EXCLUDED.unmet_15_pre");

    Query query = entityManager.createNativeQuery(sb.toString());
    final int totalFields = 20;
    for (int i = 0; i < entities.size(); i++) {
      var entity = entities.get(i);
      query.setParameter(i * totalFields + 1, entity.getZoneId());
      query.setParameter(i * totalFields + 2, entity.getSurge());
      query.setParameter(i * totalFields + 3, entity.getSurgeLow());
      query.setParameter(i * totalFields + 4, entity.getSurgeHigh());
      query.setParameter(i * totalFields + 5, entity.getDemandRecent());
      query.setParameter(i * totalFields + 6, entity.getDemandPrevious());
      query.setParameter(i * totalFields + 7, entity.getDemandPredicted());
      query.setParameter(i * totalFields + 8, entity.getSupply());
      query.setParameter(i * totalFields + 9, entity.getPrevSurge());
      query.setParameter(i * totalFields + 10, entity.getLastUpdDt());
      query.setParameter(i * totalFields + 11, entity.getExcessDemand());
      query.setParameter(i * totalFields + 12, entity.getBatchKey());
      query.setParameter(i * totalFields + 13, entity.getPredictedDemand15());
      query.setParameter(i * totalFields + 14, entity.getZonePriceModel());
      query.setParameter(i * totalFields + 15, entity.getUnmet15());
      query.setParameter(i * totalFields + 16, entity.getPreviousUnmet15());
      query.setParameter(i * totalFields + 17, entity.getDemand15());
      query.setParameter(i * totalFields + 18, entity.getExcessDemand15());
      query.setParameter(i * totalFields + 19, new Timestamp(System.currentTimeMillis()));
      query.setParameter(i * totalFields + 20, "system");
    }
    query.executeUpdate();
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticBasedConfigurationEffectiveCheckEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticBasedConfigurationVersionEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticRegionBasedConfigurationEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.StaticRegionBasedConfigurationJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.projection.StaticBasedConfigurationVersionProjection;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.StaticBasedConfigurationEffectiveCheckResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.StaticBasedConfigurationVersion;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.StaticRegionBasedConfiguration;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.StaticRegionBasedConfigurationRequest;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * Maps between StaticRegionBasedConfigurationJPA entity, StaticRegionBasedConfigurationEntity
 * domain entity, and StaticRegionBasedConfiguration API model.
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface StaticRegionBasedConfigurationMapper {

  /**
   * Maps StaticRegionBasedConfigurationJPA to StaticRegionBasedConfigurationEntity.
   *
   * @param source the JPA entity
   * @return the domain entity
   */
  StaticRegionBasedConfigurationEntity mapJpaToEntity(StaticRegionBasedConfigurationJPA source);

  /**
   * Maps a list of StaticRegionBasedConfigurationJPA to a list of
   * StaticRegionBasedConfigurationEntity.
   *
   * @param source the list of JPA entities
   * @return the list of domain entities
   */
  List<StaticRegionBasedConfigurationEntity> mapJpaToEntity(
      List<StaticRegionBasedConfigurationJPA> source);

  /**
   * Maps StaticRegionBasedConfigurationEntity to StaticRegionBasedConfigurationJPA.
   *
   * @param source the domain entity
   * @return the JPA entity
   */
  StaticRegionBasedConfigurationJPA mapEntityToJpa(StaticRegionBasedConfigurationEntity source);

  /**
   * Maps a list of StaticRegionBasedConfigurationEntity to a list of
   * StaticRegionBasedConfigurationJPA.
   *
   * @param source the list of domain entities
   * @return the list of JPA entities
   */
  List<StaticRegionBasedConfigurationJPA> mapEntityToJpa(
      List<StaticRegionBasedConfigurationEntity> source);

  /**
   * Maps StaticRegionBasedConfigurationEntity to StaticRegionBasedConfiguration.
   *
   * @param source the domain entity
   * @return the API model
   */
  StaticRegionBasedConfiguration mapEntityToDto(StaticRegionBasedConfigurationEntity source);

  /**
   * Maps a list of StaticRegionBasedConfigurationEntity to a list of
   * StaticRegionBasedConfiguration.
   *
   * @param source the list of domain entities
   * @return the list of API models
   */
  List<StaticRegionBasedConfiguration> mapEntityToDto(
      List<StaticRegionBasedConfigurationEntity> source);

  /**
   * Maps StaticRegionBasedConfigurationEntity to the existing StaticRegionBasedConfigurationJPA.
   *
   * @param jpa the target JPA entity
   * @param entity the source domain entity
   */
  void mapEntityToJpa(
      @MappingTarget StaticRegionBasedConfigurationJPA jpa,
      StaticRegionBasedConfigurationEntity entity);

  /**
   * Maps StaticRegionBasedConfigurationEntity to the existing StaticRegionBasedConfigurationJPA
   * with audit fields.
   *
   * @param jpa the target JPA entity
   * @param entity the source domain entity
   */
  void mapEntityToJpaWithAuditUpdate(
      @MappingTarget StaticRegionBasedConfigurationJPA jpa,
      StaticRegionBasedConfigurationEntity entity);

  /**
   * Maps StaticRegionBasedConfigurationRequest to StaticRegionBasedConfigurationEntity.
   *
   * @param source the API request model
   * @return the domain entity
   */
  StaticRegionBasedConfigurationEntity mapRequestToEntity(
      StaticRegionBasedConfigurationRequest source);

  /**
   * Maps a list of StaticRegionBasedConfigurationRequest to a list of
   * StaticRegionBasedConfigurationEntity.
   *
   * @param source the list of API request models
   * @return the list of domain entities
   */
  List<StaticRegionBasedConfigurationEntity> mapRequestToEntity(
      List<StaticRegionBasedConfigurationRequest> source);

  /**
   * Converts Instant to OffsetDateTime.
   *
   * @param instant the Instant to convert
   * @return the equivalent OffsetDateTime
   */
  default OffsetDateTime map(Instant instant) {
    return instant != null ? instant.atOffset(ZoneOffset.UTC) : null;
  }

  /**
   * Converts OffsetDateTime to Instant.
   *
   * @param offsetDateTime the OffsetDateTime to convert
   * @return the equivalent Instant
   */
  default Instant map(OffsetDateTime offsetDateTime) {
    return offsetDateTime != null ? offsetDateTime.toInstant() : null;
  }

  List<StaticBasedConfigurationVersion> mapVersionEntityToDto(
      List<StaticBasedConfigurationVersionEntity> versions);

  List<StaticBasedConfigurationVersionEntity> mapVersionProjectionToEntity(
      List<StaticBasedConfigurationVersionProjection> allVersionsDesc);

  StaticBasedConfigurationEffectiveCheckResponse mapEffectiveCheckEntityToDto(
      StaticBasedConfigurationEffectiveCheckEntity entity);
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.NewPricingModelConfigChangeRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.ConfigKeyValueConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.NewPricingModelConfigChangeEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.mappers.NewPricingModelConfigChangeMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.ConfigKeyValueConfigJPARepository;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.NewPricingModelConfigChangeJPARepository;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class NewPricingModelConfigChangeRepositoryImpl
    implements NewPricingModelConfigChangeRepository {

  private final NewPricingModelConfigChangeJPARepository newPricingModelConfigChangeJPARepository;
  private final NewPricingModelConfigChangeMapper newPricingModelConfigChangeMapper;
  private final ConfigKeyValueConfigJPARepository configKeyValueConfigJPARepository;

  @Override
  public void saveAllPricingConfigModelChanges(
      List<NewPricingModelConfigChangeEntity> newPricingModelConfigChangeEntities) {
    newPricingModelConfigChangeJPARepository.saveAll(
        newPricingModelConfigChangeEntities.stream()
            .map(newPricingModelConfigChangeMapper::mapObject)
            .toList());
  }

  @Override
  public void saveAllPropertyChanges(List<ConfigKeyValueConfigEntity> keyValueConfigs) {
    configKeyValueConfigJPARepository.saveAll(
        keyValueConfigs.stream()
            .map(newPricingModelConfigChangeMapper::mapToKeyValueConfigJPAEntity)
            .toList());
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.annotation;

import static java.lang.annotation.ElementType.ANNOTATION_TYPE;
import static java.lang.annotation.ElementType.CONSTRUCTOR;
import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.ElementType.TYPE_USE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.validator.CustomNotEmptyValidator;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Repeatable;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import org.springframework.http.HttpStatus;

/** The interface Custom not empty. */
@Documented
@Constraint(validatedBy = CustomNotEmptyValidator.class)
@Target({METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE})
@Retention(RUNTIME)
@Repeatable(CustomNotEmpty.List.class)
public @interface CustomNotEmpty {

  /**
   * Message string.
   *
   * @return the string
   */
  String message() default "{javax.validation.constraints.NotEmpty.message}";

  /**
   * Status code int.
   *
   * @return the int
   */
  long errorCode() default 1L;

  /**
   * Http status http status.
   *
   * @return the http status
   */
  HttpStatus httpStatus() default HttpStatus.BAD_REQUEST;

  /**
   * Groups class [ ].
   *
   * @return the class [ ]
   */
  Class<?>[] groups() default {};

  /**
   * Payload class [ ].
   *
   * @return the class [ ]
   */
  Class<? extends Payload>[] payload() default {};

  /** The interface List. */
  @Target({METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE})
  @Retention(RUNTIME)
  @Documented
  @interface List {
    /**
     * Value custom not blank [ ].
     *
     * @return the custom not blank [ ]
     */
    CustomNotEmpty[] value();
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class CreateNewPricingModelConfigRequest implements Serializable {
  @Serial private static final long serialVersionUID = 6964690958117534234L;

  private String service;
  private String key;
  private String value;
  private String description;
  private String createdBy;
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.controllers.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.RegionModelDistributionService;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.RegionModelDistributionEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.api.SurgeComputationRegionModelDistributionManagementApi;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper.surgecomputation.RegionModelDistributionMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.CreateOrUpdateRegionModelDistributionRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.GetRegionModelDistributionResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.RegionModelDistribution;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class RegionModelDistributionManagementController
    implements SurgeComputationRegionModelDistributionManagementApi {

  private final RegionModelDistributionService service;
  private final RegionModelDistributionMapper mapper;

  @Override
  public ResponseEntity<Void> batchCreateRegionModelDistribution(
      final List<RegionModelDistribution> request) {
    List<RegionModelDistributionEntity> entities =
        mapper.mapToRegionModelDistributionEntities(request);
    service.batchCreateRegionModelDistribution(entities);
    return ResponseEntity.noContent().build();
  }

  @Override
  public ResponseEntity<Void> createOrUpdateRegionModelDistribution(
      final CreateOrUpdateRegionModelDistributionRequest request) {
    RegionModelDistributionEntity entity = mapper.mapToRegionModelDistributionEntity(request);
    service.createOrUpdateRegionModelDistribution(entity);
    return ResponseEntity.noContent().build();
  }

  @Override
  public ResponseEntity<GetRegionModelDistributionResponse> getRegionModelDistribution(
      final Long regionId) {
    List<RegionModelDistributionEntity> entities = service.getRegionModelDistribution(regionId);
    return ResponseEntity.ok(mapper.entitiesToResponse(entities).get(0));
  }

  @Override
  public ResponseEntity<Void> deleteRegionModelDistribution(final Long id) {
    service.deleteRegionModelDistribution(id);
    return ResponseEntity.noContent().build();
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.converters;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.StaticTimeBasedConfigurationJPA;
import jakarta.persistence.Converter;

/**
 * JPA converter for JSONB columns containing AppliedHour objects. Converts between a List of
 * AppliedHour objects and a JSON string.
 */
@Converter
public class AppliedHourJsonbConverter
    extends JsonbConverter<StaticTimeBasedConfigurationJPA.AppliedHour> {

  public AppliedHourJsonbConverter() {
    super(StaticTimeBasedConfigurationJPA.AppliedHour.class);
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.Instant;
import lombok.*;

/**
 * Entity class for surge_computation_model_surges table. Stores information about surge values
 * calculated by the surge computation model service.
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Entity
@Table(name = "surge_computation_model_surges")
public class SurgeComputationModelSurgeJPA {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  private Long id;

  @Column(name = "model_id", nullable = false)
  private Long modelId;

  @Column(name = "region_id", nullable = false)
  private Long regionId;

  @Column(name = "prev_surge")
  private BigDecimal prevSurge;

  @Column(name = "surge", nullable = false)
  private BigDecimal surge;

  @Column(name = "last_upd_dt", nullable = false)
  private Instant lastUpdDt;
}

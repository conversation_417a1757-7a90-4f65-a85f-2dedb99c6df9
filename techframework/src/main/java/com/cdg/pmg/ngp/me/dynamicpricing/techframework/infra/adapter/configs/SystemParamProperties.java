package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Setter
@Getter
public class SystemParamProperties {

  @Value("${dps.system.param.applicationRelease}")
  private int applicationRelease;

  @Value("${dps.system.param.dynamicSurge.forceUsingR1.enabled}")
  private boolean surgeForceUsingR1;
}

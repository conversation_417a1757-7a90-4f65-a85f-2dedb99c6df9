package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.DynamicSurgeNgpJPA;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

public interface DynamicSurgeNgpJPARepository extends JpaRepository<DynamicSurgeNgpJPA, String> {
  @Query("SELECT ds FROM DynamicSurgeNgpJPA as ds ")
  List<DynamicSurgeNgpJPA> getDynpSurges();

  @Modifying
  @Query(
      value =
          """
              DELETE FROM dynp_surges_ngp s
              WHERE s.zone_id NOT IN (
                      SELECT m.zone_id FROM dynp_pricing_range r
                      JOIN dynp_zone_mapping m ON r.dynp_pricing_range_id = m.dynp_pricing_range_id
                      WHERE NOW() >= m.valid_start_dt AND
                          NOW() < m.valid_end_dt
               )
              """,
      nativeQuery = true)
  void removeInvalidDynSurges();
}

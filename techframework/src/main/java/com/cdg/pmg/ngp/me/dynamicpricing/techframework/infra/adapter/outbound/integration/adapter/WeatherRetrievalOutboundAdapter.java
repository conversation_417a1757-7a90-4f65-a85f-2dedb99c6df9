package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.adapter;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos.WeatherRetrievalOutboundResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;

@FeignClient(name = "${openfeign.weatherRetrievalClient.name}")
public interface WeatherRetrievalOutboundAdapter {

  @PostMapping(value = "/v1.0/rainfall/fetch", produces = "application/json")
  ResponseEntity<WeatherRetrievalOutboundResponse> getRainfall();
}

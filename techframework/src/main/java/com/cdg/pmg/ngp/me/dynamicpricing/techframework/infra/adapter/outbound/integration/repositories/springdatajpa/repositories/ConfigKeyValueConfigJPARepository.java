package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.ConfigKeyValueConfigJPAEntity;
import org.springframework.data.jpa.repository.JpaRepository;

public interface ConfigKeyValueConfigJPARepository
    extends JpaRepository<ConfigKeyValueConfigJPAEntity, String> {}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities;

import jakarta.persistence.*;
import java.io.Serial;
import java.io.Serializable;
import lombok.*;

@Entity
@Table(name = "flatfare_adjustment_conf")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FlatFareAdjustmentConfJPA implements Serializable {

  @Serial private static final long serialVersionUID = 1493628712332623034L;

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  @Setter(AccessLevel.NONE)
  private int id;

  @Column(name = "veh_grp")
  private int vehGrp;

  @Column(name = "fixed_val")
  private Double fixedVal;

  @Column(name = "per_val")
  private Double perVal;

  @Column(name = "is_enabled")
  private Boolean isEnabled;
}

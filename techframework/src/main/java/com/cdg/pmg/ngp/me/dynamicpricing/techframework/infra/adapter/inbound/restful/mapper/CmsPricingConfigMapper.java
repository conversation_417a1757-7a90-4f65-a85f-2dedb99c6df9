package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.commands.NewPricingModelCommand;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.NewPricingModelConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.ExcludeBookingChannelRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.ExcludeBookingChannelResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.JobStatusRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.JobStatusResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.NewPricingModelConfigListResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.NewPricingModelConfigResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.NewPricingModelRequest;
import java.util.List;
import org.mapstruct.Mapper;

/** The interface Cms pricing config mapper. */
@Mapper
public interface CmsPricingConfigMapper {

  /**
   * Map List NewPricingModelConfigEntity to List NewPricingModelItemResponse
   *
   * @param newPricingModelConfigEntityList
   * @return List NewPricingModelItemResponse
   */
  default NewPricingModelConfigListResponse toNewPricingModelItemResponse(
      List<NewPricingModelConfigEntity> newPricingModelConfigEntityList) {
    NewPricingModelConfigListResponse response = new NewPricingModelConfigListResponse();
    response.setItems(
        newPricingModelConfigEntityList.stream().map(this::toNewPricingModelResponse).toList());
    return response;
  }

  /**
   * @param newPricingModelConfigEntity
   * @return
   */
  NewPricingModelConfigResponse toNewPricingModelResponse(
      NewPricingModelConfigEntity newPricingModelConfigEntity);

  /**
   * @param source
   * @return
   */
  NewPricingModelCommand toUpdateNewPricingModelCommand(NewPricingModelRequest source);

  ExcludeBookingChannelResponse toExcludeBookingChannelResponse(
      ExcludeBookingChannelRequest excludeBookingChannelRequest);

  JobStatusResponse toJobStatusReponse(JobStatusRequest jobStatusRequest);
}

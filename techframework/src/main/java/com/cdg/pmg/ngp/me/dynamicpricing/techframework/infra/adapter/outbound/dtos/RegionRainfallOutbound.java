package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

@Data
public class RegionRainfallOutbound implements Serializable {
  @Serial private static final long serialVersionUID = 9137351376780419633L;

  @JsonProperty("region_id")
  private Long regionId;

  @JsonProperty("average_intensity")
  private Double averageIntensity;
}

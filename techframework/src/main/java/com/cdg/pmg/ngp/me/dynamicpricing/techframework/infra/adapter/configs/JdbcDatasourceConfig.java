package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs;

import com.zaxxer.hikari.HikariDataSource;
import java.util.HashMap;
import java.util.Map;
import javax.sql.DataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.LazyConnectionDataSourceProxy;

/**
 * Consolidated configuration for PostgreSQL database connections.
 *
 * <p>This class configures separate reader and writer datasources and sets up a routing mechanism
 * to direct operations to the appropriate datasource based on transaction type.
 *
 * <p>Read-only operations (marked with @Transactional(readOnly = true)) will use the reader
 * database, while write operations will use the writer database.
 *
 * <p>This configuration also includes a special datasource for LocationSurchargeJPARepository to
 * avoid RAM issues. Before using JDBC, this repository used JPA and consumed up to 3GB of RAM,
 * making the service unstable.
 *
 * <p>Ticket monitoring: NGPME-8108
 */
@Configuration
public class JdbcDatasourceConfig {

  /**
   * Creates the writer datasource using properties from application.properties.
   *
   * @return the writer datasource
   */
  @Bean
  public DataSource writerDataSource(
      @Value("${spring.datasource.writer.url}") String url,
      @Value("${spring.datasource.writer.username}") String username,
      @Value("${spring.datasource.writer.password:}") String password,
      @Value("${spring.datasource.writer.driver-class-name}") String driverClassName) {
    HikariDataSource dataSource = new HikariDataSource();
    dataSource.setJdbcUrl(url);
    dataSource.setUsername(username);
    dataSource.setPassword(password);
    dataSource.setDriverClassName(driverClassName);
    return dataSource;
  }

  /**
   * Creates the reader datasource using properties from application.properties.
   *
   * @return the reader datasource
   */
  @Bean
  public DataSource readerDataSource(
      @Value("${spring.datasource.reader.url}") String url,
      @Value("${spring.datasource.reader.username}") String username,
      @Value("${spring.datasource.reader.password:}") String password,
      @Value("${spring.datasource.reader.driver-class-name}") String driverClassName) {
    HikariDataSource dataSource = new HikariDataSource();
    dataSource.setJdbcUrl(url);
    dataSource.setUsername(username);
    dataSource.setPassword(password);
    dataSource.setDriverClassName(driverClassName);
    return dataSource;
  }

  /**
   * Creates a routing datasource that switches between reader and writer datasources based on the
   * transaction's read-only flag.
   *
   * @param writerDataSource the writer datasource
   * @param readerDataSource the reader datasource
   * @return the routing datasource
   */
  @Bean
  public DataSource routingDataSource(
      @Qualifier("writerDataSource") DataSource writerDataSource,
      @Qualifier("readerDataSource") DataSource readerDataSource) {

    RoutingDataSource routingDataSource = new RoutingDataSource();

    Map<Object, Object> dataSources = new HashMap<>();
    dataSources.put(RoutingDataSource.WRITER, writerDataSource);
    dataSources.put(RoutingDataSource.READER, readerDataSource);

    routingDataSource.setTargetDataSources(dataSources);
    routingDataSource.setDefaultTargetDataSource(writerDataSource);

    return routingDataSource;
  }

  /**
   * Creates the primary datasource that will be used by the application. This wraps the routing
   * datasource with a LazyConnectionDataSourceProxy to ensure that connections are only fetched
   * when needed.
   *
   * @param routingDataSource the routing datasource
   * @return the primary datasource
   */
  @Primary
  @Bean
  public DataSource dataSource(@Qualifier("routingDataSource") DataSource routingDataSource) {
    // Wrap with LazyConnectionDataSourceProxy to ensure connection is only fetched when needed
    return new LazyConnectionDataSourceProxy(routingDataSource);
  }

  /**
   * Creates a DataSource for JDBC operations that uses the routing datasource. This ensures that
   * read operations go to the reader database and write operations go to the writer database based
   * on the transaction's read-only flag.
   *
   * @param dataSource the primary routing datasource
   * @return a DataSource for JDBC operations
   */
  @Bean
  public DataSource datasourceJDBCConfiguration(@Qualifier("dataSource") DataSource dataSource) {
    return dataSource;
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs;

import static com.cdg.pmg.ngp.me.dynamicpricing.constants.CommonConstant.SYSTEM_USER;

import java.util.Objects;
import java.util.Optional;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.AuditorAware;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

@Component
public class SpringSecurityAuditorAware implements AuditorAware<String> {

  @Override
  public @NotNull Optional<String> getCurrentAuditor() {
    // For HTTP calls
    ServletRequestAttributes requestAttributes =
        (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
    if (requestAttributes == null) {
      return Optional.of(SYSTEM_USER);
    }
    String userId = requestAttributes.getRequest().getHeader("X-User-Id");
    return Objects.nonNull(userId) ? Optional.of(userId) : Optional.of(SYSTEM_USER);
  }
}

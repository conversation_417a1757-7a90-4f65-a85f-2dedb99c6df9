package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.controllers;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.S2CellService;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.api.S2CellRestApiInboundAdapterApi;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@Slf4j
public class S2CellController implements S2CellRestApiInboundAdapterApi {
  private final S2CellService s2Service;

  @Override
  public ResponseEntity<Void> fetchCache() {
    s2Service.fetchCacheS2Cell();
    return ResponseEntity.ok().build();
  }
}

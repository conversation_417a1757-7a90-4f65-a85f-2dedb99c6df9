package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serial;
import java.time.LocalDate;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "fare_type_conf")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class FareTypeConfigJPA extends AbstractAuditingEntity<Integer> {

  @Serial private static final long serialVersionUID = -1129725040196220269L;

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "fare_type_id")
  private Integer fareTypeId;

  @Column(name = "fare_type")
  private String fareType;

  @Column(name = "day")
  private String day;

  @Column(name = "hour")
  private String hour;

  @Column(name = "default_fixed")
  private Double defaultFixed;

  @Column(name = "default_perc")
  private Double defaultPercent;

  @Column(name = "start_date")
  private LocalDate startDate;

  @Column(name = "end_date")
  private LocalDate endDate;

  @Override
  public Integer getId() {
    return fareTypeId;
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.service.impl;

import static com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.CommonUtils.getIterableFromIterator;
import static com.cdg.pmg.ngp.me.dynamicpricing.constants.BookARideConfigsConstant.ONE_THOUSAND;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.BookARideConfigsConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FareUploadConfiguration;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.TaxiFeed;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.TaxiFeedMetaData;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.service.TaxiFeedService;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.utils.TaxiFeedUtils;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/** This class implement functions from TaxiFeedService. */
@ServiceComponent
@RequiredArgsConstructor
@Slf4j
public class TaxiFeedServiceImpl implements TaxiFeedService {

  @Override
  public TaxiFeed buildTaxiFeed(
      final Map<String, String> cmsConfigData,
      final FareUploadConfiguration fareUploadConfiguration,
      final Map<String, List<String>> vehicleGroupIdAndProductIdListMap) {

    Integer productCategory =
        Integer.parseInt(
            cmsConfigData.getOrDefault(
                BookARideConfigsConstant.BOOK_RIDE_PROD_CATEGORY,
                BookARideConfigsConstant.BOOK_RIDE_PROD_CATEGORY_DEFAULT_VALUE));

    TaxiFeed.Builder taxiFeedBuilder = TaxiFeed.newBuilder();
    return taxiFeedBuilder
        .setMetaData(
            TaxiFeedMetaData.newBuilder()
                .setFeedId(cmsConfigData.get(BookARideConfigsConstant.FEED_ID))
                .setCreationDate(getCreationDate())
                .build())
        .addAllAreas(
            getIterableFromIterator(
                TaxiFeedUtils.buildTaxiAreas(fareUploadConfiguration).iterator()))
        .addAllProducts(
            getIterableFromIterator(
                TaxiFeedUtils.buildTaxiProducts(
                        fareUploadConfiguration,
                        cmsConfigData,
                        vehicleGroupIdAndProductIdListMap,
                        productCategory)
                    .iterator()))
        .build();
  }

  private TaxiFeedMetaData.Timestamp getCreationDate() {
    return TaxiFeedMetaData.Timestamp.newBuilder()
        .setSeconds(System.currentTimeMillis() / ONE_THOUSAND)
        .build();
  }
}

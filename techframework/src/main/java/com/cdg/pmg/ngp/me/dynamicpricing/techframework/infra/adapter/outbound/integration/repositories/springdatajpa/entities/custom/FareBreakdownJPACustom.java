package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.custom;

import java.math.BigDecimal;

public interface FareBreakdownJPACustom {
  String getFareId();

  String getBookingId();

  String getTripId();

  Double getFlagDownRate();

  Double getWaitTimeFare();

  Long getRoutingDistance();

  Long getEtt();

  Double getDpFinalFare();

  BigDecimal getTotalFare();

  BigDecimal getEstimatedFareLF();

  BigDecimal getEstimatedFareRT();

  Long getFlatPlatformFeeId();

  Double getFlatPlatformFee();

  Long getMeterPlatformFeeId();

  Double getMeterPlatformFeeLower();

  Double getMeterPlatformFeeUpper();

  String getUpdatedBy();

  Double getMeteredBaseFare();
}

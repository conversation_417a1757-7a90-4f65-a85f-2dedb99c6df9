package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.AbstractAuditingEntity;
import jakarta.persistence.*;
import java.io.Serial;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "surge_computation_region_model_distribution")
public class RegionModelDistributionJPA extends AbstractAuditingEntity<Long> {

  @Serial private static final long serialVersionUID = 1L;

  @Id
  @GeneratedValue(
      strategy = GenerationType.SEQUENCE,
      generator = "surgeComputationRegionModelDistributionSequenceGenerator")
  @SequenceGenerator(
      name = "surgeComputationRegionModelDistributionSequenceGenerator",
      sequenceName = "SEQ_surge_computation_region_model_distribution__id",
      allocationSize = 1)
  private Long id;

  @Column(name = "region_id", nullable = false)
  private Long regionId;

  @JdbcTypeCode(SqlTypes.JSON)
  @Column(name = "models", columnDefinition = "jsonb", nullable = false)
  private List<ModelPercentage> models;

  @Column(name = "effective_from", nullable = false)
  private Instant effectiveFrom;

  @Column(name = "effective_to", nullable = false)
  private Instant effectiveTo;

  @Data
  public static class ModelPercentage {
    private Long modelId;
    private BigDecimal percentage;
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;
import org.zalando.logbook.Logbook;
import org.zalando.logbook.core.BodyFilters;
import org.zalando.logbook.core.Conditions;
import org.zalando.logbook.core.DefaultHttpLogWriter;
import org.zalando.logbook.core.DefaultSink;
import org.zalando.logbook.core.HeaderFilters;
import org.zalando.logbook.core.QueryFilters;
import org.zalando.logbook.core.RequestFilters;
import org.zalando.logbook.core.ResponseFilters;
import org.zalando.logbook.core.SplunkHttpLogFormatter;

/** The type Logbook config. */
@Configuration
public class LogbookConfig {

  /**
   * Config log book logbook.
   *
   * @return the logbook
   */
  @Bean
  public Logbook configLogBook() {
    return Logbook.builder()
        .sink(new DefaultSink(new SplunkHttpLogFormatter(), new DefaultHttpLogWriter()))
        .condition(Conditions.exclude(Conditions.requestTo("/actuator/**")))
        .queryFilter(QueryFilters.defaultValue())
        .headerFilter(HeaderFilters.defaultValue())
        .headerFilter(
            HeaderFilters.removeHeaders(
                "traceparent", "tracestate", HttpHeaders.CONNECTION, HttpHeaders.TRANSFER_ENCODING))
        .bodyFilter(BodyFilters.defaultValue())
        .requestFilter(RequestFilters.defaultValue())
        .responseFilter(ResponseFilters.defaultValue())
        .build();
  }
}

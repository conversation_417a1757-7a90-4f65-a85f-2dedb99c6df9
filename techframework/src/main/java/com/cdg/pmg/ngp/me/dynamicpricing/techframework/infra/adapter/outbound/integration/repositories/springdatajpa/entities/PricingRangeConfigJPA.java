package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serial;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "dynp_pricing_range")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PricingRangeConfigJPA extends AbstractAuditingEntity<Integer> {
  @Serial private static final long serialVersionUID = -1353707558968685274L;

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "dynp_pricing_range_id")
  private Integer dynamicPricingRangeId;

  @Column(name = "start_price")
  private Double startPrice;

  @Column(name = "end_price")
  private Double endPrice;

  @Column(name = "step")
  private Double step;

  @Column(name = "refresh_period")
  private Integer refreshPeriod;

  @Column(name = "quote_valid_period")
  private Integer quoteValidPeriod;

  @Column(name = "day")
  private String day;

  @Column(name = "hour")
  private String hour;

  @Column(name = "step_positive")
  private Double stepPositive;

  @Column(name = "step_negative")
  private Double stepNegative;

  @Column(name = "is_enabled")
  private Integer isEnabled;

  @Override
  public Integer getId() {
    return dynamicPricingRangeId;
  }
}

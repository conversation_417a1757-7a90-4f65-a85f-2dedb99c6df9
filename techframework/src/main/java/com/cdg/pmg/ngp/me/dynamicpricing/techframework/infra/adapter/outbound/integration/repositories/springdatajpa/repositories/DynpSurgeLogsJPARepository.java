package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.DynpSurgeLogsJPA;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

public interface DynpSurgeLogsJPARepository extends JpaRepository<DynpSurgeLogsJPA, String> {

  @Modifying
  @Transactional
  @Query(
      value =
          "INSERT INTO dynp_surge_logs "
              + "(zone_id, "
              + "surge, "
              + "surge_low, "
              + "surge_high, "
              + "demand_recent, "
              + "demand_previous, "
              + "demand_predicted, "
              + "supply, "
              + "excess_demand, "
              + "prev_surge, "
              + "last_upd_dt, "
              + "batch_key) "
              + "SELECT zone_id,"
              + "surge, "
              + "surge_low, "
              + "surge_high, "
              + "demand_recent, "
              + "demand_previous, "
              + "demand_predicted, "
              + "supply, "
              + "excess_demand, "
              + "prev_surge, "
              + "last_upd_dt, "
              + "batch_key "
              + "FROM dynp_surges_ngp",
      nativeQuery = true)
  void insertDynpSurgeLogs();

  @Modifying
  @Transactional
  @Query(
      value =
          """
                  INSERT INTO dynp_surge_logs (zone_id, surge, surge_low, surge_high, demand_recent, demand_previous,
                                                demand_predicted, supply, excess_demand, prev_surge, last_upd_dt,
                                               batch_key, unmet_15, unmet_15_pre, demand_15, demand_predicted_15,
                                               zone_price_model, excess_demand_15)
                  SELECT zone_id,
                        surge,
                        surge_low,
                        surge_high,
                        demand_recent,
                        demand_previous,
                        demand_predicted,
                        supply,
                        excess_demand,
                        prev_surge,
                        last_upd_dt,
                        batch_key,
                        unmet_15,
                        unmet_15_pre,
                        demand_15,
                        demand_predicted_15,
                        zone_price_model,
                        excess_demand_15
                  FROM dynp_surges_ngp
                  ON CONFLICT (batch_key, zone_id)
                  DO UPDATE SET
                    surge = EXCLUDED.surge,
                    surge_low = EXCLUDED.surge_low,
                    surge_high = EXCLUDED.surge_high,
                    demand_recent = EXCLUDED.demand_recent,
                    demand_previous = EXCLUDED.demand_previous,
                    demand_predicted = EXCLUDED.demand_predicted,
                    supply = EXCLUDED.supply,
                    excess_demand = EXCLUDED.excess_demand,
                    prev_surge = EXCLUDED.prev_surge,
                    last_upd_dt = EXCLUDED.last_upd_dt,
                    unmet_15 = EXCLUDED.unmet_15,
                    unmet_15_pre = EXCLUDED.unmet_15_pre,
                    demand_15 = EXCLUDED.demand_15,
                    demand_predicted_15 = EXCLUDED.demand_predicted_15,
                    zone_price_model = EXCLUDED.zone_price_model,
                    excess_demand_15 = EXCLUDED.excess_demand_15;
                        """,
      nativeQuery = true)
  void insertDynpSurgeLogsV2();
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.cbdcharge.CBDAddress;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.cbdcharge.LocReloadCache;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.cbdcharge.UpdateCBDAddress;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.CBDAddressRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.LocReloadCacheRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.UpdateCBDAddressRequest;
import org.mapstruct.Mapper;

@Mapper
public interface CBDAddressMapper {

  /**
   * UpdateCBDAddressRequest to UpdateCBDAddress
   *
   * @param updateCBDAddressRequest request
   * @return UpdateCBDAddress
   */
  UpdateCBDAddress mapToCBDAddressRequest(UpdateCBDAddressRequest updateCBDAddressRequest);

  /**
   * CBD Address Request to CBD Address
   *
   * @param cbdAddressRequest request
   * @return CBD Address
   */
  CBDAddress mapToCBDAddress(CBDAddressRequest cbdAddressRequest);

  /**
   * CBDReloadCacheRequest to CBDReloadCache
   *
   * @param locReloadCacheRequest locReloadCacheRequest
   * @return CBDReloadCache
   */
  LocReloadCache toLocReloadCache(LocReloadCacheRequest locReloadCacheRequest);
}

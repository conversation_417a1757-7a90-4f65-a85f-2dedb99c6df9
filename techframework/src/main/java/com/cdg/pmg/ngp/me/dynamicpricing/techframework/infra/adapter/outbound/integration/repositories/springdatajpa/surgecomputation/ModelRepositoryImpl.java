package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.surgecomputation.ModelRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.ModelEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.SurgeValuesFilterCriteria;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper.surgecomputation.ModelMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.ModelJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.surgecomputation.ModelJPARepository;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@Primary
@RequiredArgsConstructor
@Slf4j
public class ModelRepositoryImpl implements ModelRepository {

  private final ModelJPARepository jpaRepository;
  private final ModelMapper modelMapper;

  @Override
  @Transactional
  public ModelEntity save(ModelEntity entity) {
    ModelJPA jpaEntity = modelMapper.mapEntityToJpa(entity);
    ModelJPA savedJpaEntity = jpaRepository.save(jpaEntity);
    return modelMapper.mapJpaToEntity(savedJpaEntity);
  }

  @Override
  @Transactional(readOnly = true)
  public List<ModelEntity> findAll() {
    List<ModelJPA> jpaEntities = jpaRepository.findAll();
    return modelMapper.mapJpaToEntity(jpaEntities);
  }

  @Override
  @Transactional(readOnly = true)
  public Optional<ModelEntity> findById(Long id) {
    Optional<ModelJPA> jpaEntityOpt = jpaRepository.findById(id);
    return jpaEntityOpt.map(modelMapper::mapJpaToEntity);
  }

  @Override
  @Transactional
  public void deleteById(Long id) {
    jpaRepository.deleteById(id);
  }

  @Override
  @Transactional(readOnly = true)
  public List<Long> findIdByIds(final List<Long> modelIds) {
    return jpaRepository.findIdByIds(modelIds);
  }

  @Override
  @Transactional(readOnly = true)
  public List<String> findConfigNamesUsedByOtherModels(
      final List<String> configNames, final Long modelId) {
    return jpaRepository.findConfigNamesUsedByOtherModels(configNames, modelId);
  }

  @Override
  @Transactional(readOnly = true)
  public String findModelName(final Long id) {
    return jpaRepository.findModelNameById(id);
  }

  @Override
  @Transactional(readOnly = true)
  public List<ModelEntity> findWithFilter(SurgeValuesFilterCriteria filterCriteria) {
    Pageable pageable = PageRequest.of(filterCriteria.getPage(), filterCriteria.getSize());

    Page<ModelJPA> jpaPage;
    if (filterCriteria.hasModelFilters()) {
      if (filterCriteria.getModelIds() != null && !filterCriteria.getModelIds().isEmpty()) {
        jpaPage = jpaRepository.findByIdIn(filterCriteria.getModelIds(), pageable);
      } else if (filterCriteria.getModelNames() != null
          && !filterCriteria.getModelNames().isEmpty()) {
        jpaPage = jpaRepository.findByModelNameIn(filterCriteria.getModelNames(), pageable);
      } else {
        jpaPage = jpaRepository.findAll(pageable);
      }
    } else {
      jpaPage = jpaRepository.findAll(pageable);
    }

    return jpaPage.getContent().stream()
        .map(modelMapper::mapJpaToEntity)
        .collect(Collectors.toList());
  }

  @Override
  @Transactional(readOnly = true)
  public long countWithFilter(SurgeValuesFilterCriteria filterCriteria) {
    if (filterCriteria.hasModelFilters()) {
      if (filterCriteria.getModelIds() != null && !filterCriteria.getModelIds().isEmpty()) {
        return jpaRepository.countByIdIn(filterCriteria.getModelIds());
      } else if (filterCriteria.getModelNames() != null
          && !filterCriteria.getModelNames().isEmpty()) {
        return jpaRepository.countByModelNameIn(filterCriteria.getModelNames());
      }
    }
    return jpaRepository.count();
  }

  @Override
  @Transactional(readOnly = true)
  public List<Long> findAllIds() {
    return jpaRepository.findAllIds();
  }
}

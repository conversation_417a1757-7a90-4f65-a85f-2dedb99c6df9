package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StandardInputEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.StandardInput;
import java.util.List;
import org.mapstruct.Mapper;

@Mapper
public interface StandardInputMapper {

  List<StandardInput> mapToStandardInput(List<StandardInputEntity> standardInputs);
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

@Data
public class WeatherRetrievalOutboundResponse implements Serializable {
  @Serial private static final long serialVersionUID = 1256494953772378476L;

  private String status;
  private String timestamp;
  private List<RegionRainfallOutbound> data;
}

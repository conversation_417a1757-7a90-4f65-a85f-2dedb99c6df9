package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.mappers;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.ConfigKeyValueConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.NewPricingModelConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.ConfigKeyValueConfigJPAEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.NewPricingModelConfigChangeJPAEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface NewPricingModelConfigChangeMapper {
  NewPricingModelConfigChangeJPAEntity mapObject(
      com.cdg.pmg.ngp.me.dynamicpricing.entities.NewPricingModelConfigChangeEntity
          pricingModelConfigChangeDomainEntity);

  @Mapping(source = "id", target = "configId")
  @Mapping(source = "createdBy", target = "configCreatedBy")
  @Mapping(source = "createdDt", target = "configCreatedDt")
  @Mapping(source = "updatedBy", target = "configUpdatedBy")
  @Mapping(source = "updatedDt", target = "configUpdatedDt")
  com.cdg.pmg.ngp.me.dynamicpricing.entities.NewPricingModelConfigChangeEntity
      mapToNewPricingModelConfigChangeEntity(
          NewPricingModelConfigEntity newPricingModelConfigEntity);

  ConfigKeyValueConfigJPAEntity mapToKeyValueConfigJPAEntity(
      ConfigKeyValueConfigEntity configKeyValueConfigEntity);
}

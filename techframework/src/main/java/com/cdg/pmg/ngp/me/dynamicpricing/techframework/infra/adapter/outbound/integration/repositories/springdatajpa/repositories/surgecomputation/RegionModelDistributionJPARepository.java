package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.RegionModelDistributionJPA;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * Repository interface for RegionModelDistributionJPA entity. Provides standard CRUD operations for
 * surge computation models.
 */
@Repository
public interface RegionModelDistributionJPARepository
    extends JpaRepository<RegionModelDistributionJPA, Long> {

  List<RegionModelDistributionJPA> findAllByRegionIdOrderByEffectiveFromDesc(Long regionId);

  @Query(
      value =
          """
        SELECT COUNT(*)
                FROM surge_computation_region_model_distribution
                WHERE EXISTS (
                    SELECT 1
                    FROM jsonb_array_elements(models) AS elem
                    WHERE CAST(elem ->> 'modelId' AS BIGINT) = :modelId
                )
      """,
      nativeQuery = true)
  long countByModelId(@Param("modelId") Long modelId);
}

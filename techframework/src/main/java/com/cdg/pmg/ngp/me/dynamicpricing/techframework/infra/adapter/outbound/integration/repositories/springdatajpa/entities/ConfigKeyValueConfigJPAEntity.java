package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Entity
@Table(name = "config_key_value_change")
public class ConfigKeyValueConfigJPAEntity extends AbstractAuditingEntity<String> {
  @Id
  @Column(name = "unique_payload_hash")
  private String uniquePayloadHash;

  @Column(name = "key")
  private String key;

  @Column(name = "value")
  private String value;

  @Override
  public String getId() {
    return uniquePayloadHash;
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetAdditionalChargeFeeConfigurationsListResponse implements Serializable {

  @Serial private static final long serialVersionUID = 5876743711863097734L;

  private List<AdditionalChargeFeeConfigurationData> data;
}

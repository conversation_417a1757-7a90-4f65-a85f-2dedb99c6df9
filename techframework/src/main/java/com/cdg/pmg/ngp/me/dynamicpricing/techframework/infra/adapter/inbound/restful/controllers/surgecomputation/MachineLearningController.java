package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.controllers.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.MlGetFareRequestAggStatsService;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.RegionFareCountAggregateResult;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.api.MachineLearningControllerApi;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper.surgecomputation.MachineLearningMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.CurrentGetFareCountResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
public class MachineLearningController implements MachineLearningControllerApi {

  private final MlGetFareRequestAggStatsService mlGetFareRequestAggStatsService;
  private final MachineLearningMapper machineLearningMapper;

  @Override
  public ResponseEntity<CurrentGetFareCountResponse> getCurrentGetFareCount() {
    List<RegionFareCountAggregateResult> regionFareCountList =
        mlGetFareRequestAggStatsService.getCurrentGetFareCount();

    CurrentGetFareCountResponse response = new CurrentGetFareCountResponse();
    response.setData(machineLearningMapper.mapToFareCountAggregateResult(regionFareCountList));
    return ResponseEntity.ok(response);
  }
}

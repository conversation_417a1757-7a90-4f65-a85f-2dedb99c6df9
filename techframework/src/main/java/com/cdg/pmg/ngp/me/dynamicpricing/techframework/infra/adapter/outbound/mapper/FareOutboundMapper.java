package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.mapper;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.*;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos.*;
import java.util.List;
import org.mapstruct.Mapper;

@Mapper
public interface FareOutboundMapper {
  PlatformFeeOutboundRequest mapToPlatformFeeOutboundRequest(PlatformFeeRequest platformFeeRequest);

  PlatformFeeListOutboundRequest mapToPlatformFeeListOutboundRequest(
      PlatformFeeListRequest platformFeeRequest);

  List<PlatformFeeResponse> mapToPlatformFeeResponse(List<PlatformFeeOutbound> platformFeeOutbound);

  BookingFeeResponse mapToBookingFeeResponse(BookingFeeOutboundResponse bookingFeeOutboundResponse);

  BookingFeeListResponse mapToBookingFeeListResponse(
      BookingFeeListOutboundResponse bookingFeeListOutboundResponse);

  BookingFeeOutboundRequest mapToBookingFeeOutBoundRequest(BookingFeeRequest bookingFeeRequest);

  BookingFeeListOutboundRequest mapToBookingFeeListOutboundRequest(
      BookingFeeListRequest bookingFeeListRequest);

  List<AdditionalChargeFeeConfigResponse> mapToAdditoinalChargeFeeConfigResponseList(
      List<AdditionalChargeFeeConfigurationData> additionalChargeFeeConfigurationDataList);

  AdditionalChargeFeeConfigResponse mapToAdditionalChargeFeeConfigResponse(
      AdditionalChargeFeeConfigurationData additionalChargeFeeConfigurationData);
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.validator;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.annotation.CustomNotBlank;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.aop.exceptions.FieldValidationException;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.Getter;
import org.apache.logging.log4j.util.Strings;
import org.springframework.http.HttpStatus;

/** The type Custom not blank validator. */
@Getter
public class CustomNotBlankValidator implements ConstraintValidator<CustomNotBlank, Object> {

  private String message;

  private Long errorCode;

  private HttpStatus httpStatus;

  @Override
  public void initialize(CustomNotBlank constraintAnnotation) {
    this.message = constraintAnnotation.message();
    this.errorCode = constraintAnnotation.errorCode();
    this.httpStatus = constraintAnnotation.httpStatus();
  }

  @Override
  public boolean isValid(Object value, ConstraintValidatorContext context) {
    if (null == value || (value instanceof String string && Strings.isBlank(string))) {
      throw new FieldValidationException(message, errorCode, httpStatus);
    }
    return true;
  }
}

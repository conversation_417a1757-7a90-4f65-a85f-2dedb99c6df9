package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.MlGetFareRequestAggStatsJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.projection.RegionGetFareCountProjection;
import java.time.Instant;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/**
 * Repository interface for {@link MlGetFareRequestAggStatsJPA} entity. Provides methods to interact
 * with the ml_get_fare_request_agg_stats table.
 */
@Repository
public interface MlGetFareRequestAggStatsJPARepository
    extends JpaRepository<MlGetFareRequestAggStatsJPA, Long> {

  @Query(
      value =
          """
             SELECT pickup_region_id AS regionId,
               SUM(get_fare_count) AS totalGetFareCount
             FROM ml_get_fare_request_agg_stats
             WHERE start_timestamp <= :time
                 AND end_timestamp > :time
             GROUP BY pickup_region_id
          """,
      nativeQuery = true)
  List<RegionGetFareCountProjection> listAllByTimeRange(Instant time);
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.AbstractAuditingEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import java.io.Serial;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Entity class for surge_computation_static_region_based_configurations table. Stores information
 * about static region-based configurations.
 */
@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Entity
@Table(name = "surge_computation_static_region_based_configurations")
public class StaticRegionBasedConfigurationJPA extends AbstractAuditingEntity<Long> {

  private static final Logger log =
      LoggerFactory.getLogger(StaticRegionBasedConfigurationJPA.class);

  @Serial private static final long serialVersionUID = 1L;

  @Id
  @GeneratedValue(
      strategy = GenerationType.SEQUENCE,
      generator = "surgeComputationRegionBasedStaticConfigSequenceGenerator")
  @SequenceGenerator(
      name = "surgeComputationRegionBasedStaticConfigSequenceGenerator",
      sequenceName = "SEQ_surge_computation_static_region_based_configurations__id",
      allocationSize = 1)
  @Column(name = "id")
  private Long id;

  @Column(name = "name", nullable = false, length = 255)
  private String name;

  @Column(name = "version", nullable = false, length = 50)
  private String version;

  @Column(name = "effective_from", nullable = false)
  private Instant effectiveFrom;

  @Column(name = "effective_to")
  private Instant effectiveTo;

  @Column(name = "description")
  private String description;

  @Builder.Default
  @Column(name = "region_values", columnDefinition = "jsonb", nullable = false)
  @JdbcTypeCode(SqlTypes.JSON)
  private List<RegionValue> regionValues = new ArrayList<>();

  @Override
  public Long getId() {
    return id;
  }

  /** Inner class representing a region value entry in the JSONB array. */
  @Getter
  @Setter
  @NoArgsConstructor
  @AllArgsConstructor
  public static class RegionValue {
    private Long regionId;
    private String value;
  }
}

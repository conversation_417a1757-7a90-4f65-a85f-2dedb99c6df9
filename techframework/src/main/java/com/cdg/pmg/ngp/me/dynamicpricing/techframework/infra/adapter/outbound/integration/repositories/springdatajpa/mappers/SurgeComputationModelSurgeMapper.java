package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.mappers;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.H3RegionSurgeEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.SurgeComputationModelSurgeEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.SurgeComputationModelSurgeJPA;
import java.time.Instant;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * Mapper for converting between SurgeComputationModelSurgeEntity and SurgeComputationModelSurgeJPA.
 */
@Mapper
public interface SurgeComputationModelSurgeMapper {

  /**
   * Map a domain entity to a JPA entity.
   *
   * @param entity the domain entity to map
   * @return the mapped JPA entity
   */
  @Mapping(target = "lastUpdDt", source = "lastUpdDt")
  SurgeComputationModelSurgeJPA mapEntityToJpa(H3RegionSurgeEntity entity, Instant lastUpdDt);

  /**
   * Map a JPA entity to a domain entity.
   *
   * @param jpa the JPA entity to map
   * @return the mapped domain entity
   */
  SurgeComputationModelSurgeEntity mapJpaToEntity(SurgeComputationModelSurgeJPA jpa);

  /**
   * Map a list of JPA entities to a list of domain entities.
   *
   * @param jpaList the list of JPA entities to map
   * @return the mapped list of domain entities
   */
  List<SurgeComputationModelSurgeEntity> mapJpaToEntity(
      List<SurgeComputationModelSurgeJPA> jpaList);
}

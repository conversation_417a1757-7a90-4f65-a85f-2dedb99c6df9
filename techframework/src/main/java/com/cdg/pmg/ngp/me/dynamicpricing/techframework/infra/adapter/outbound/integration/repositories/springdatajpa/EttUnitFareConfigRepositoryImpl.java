package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.EttUnitFareConfigRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.EttUnitFareConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.queries.EttUnitFareConfigQueryResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.EttUnitFareJPARepository;
import com.cdg.pmg.ngp.me.dynamicpricing.utils.CollectionUtils;
import java.util.ArrayList;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

@Component
@Primary
@RequiredArgsConstructor
@Slf4j
public class EttUnitFareConfigRepositoryImpl implements EttUnitFareConfigRepository {

  private final EttUnitFareJPARepository ettUnitFareJPARepository;

  @Override
  public EttUnitFareConfigQueryResponse getEttUnitFareConfigs() {
    final var ettUnitFareConfigQueryResponse = new EttUnitFareConfigQueryResponse();
    final var configs = ettUnitFareJPARepository.getEttUnitFareConfigs();
    log.info("Ett unit fare configs size: {}", configs.size());
    if (CollectionUtils.isEmpty(configs)) {
      ettUnitFareConfigQueryResponse.setConfigs(new ArrayList<>());
      return ettUnitFareConfigQueryResponse;
    }

    ettUnitFareConfigQueryResponse.setConfigs(
        configs.stream()
            .map(
                conf ->
                    EttUnitFareConfig.builder()
                        .zoneIds(conf.getZoneIds())
                        .dayOfWeek(conf.getDayOfWeek())
                        .ekm(conf.getEkm())
                        .hr(conf.getHr())
                        .chargeBy(conf.getChargeBy())
                        .taxiType(conf.getTaxiType())
                        .rate(conf.getRate())
                        .build())
            .toList());
    return ettUnitFareConfigQueryResponse;
  }
}

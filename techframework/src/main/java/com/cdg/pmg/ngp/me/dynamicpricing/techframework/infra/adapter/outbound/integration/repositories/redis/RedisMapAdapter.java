package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.redis;

import java.time.Duration;
import java.time.Instant;
import java.util.Date;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;

/** Adapter interface to saving to Redis Map type */
public interface RedisMapAdapter<K, V> {
  /**
   * Get Abstract map type to perform update internal key value
   *
   * @return ConcurrentMap<K, V>
   */
  ConcurrentMap<K, V> getMap();

  /**
   * Set expire for Map
   *
   * @param timeout timeout duration
   * @param unit timeout unit
   * @return true if success otherwise false
   */
  boolean expire(long timeout, TimeUnit unit);

  /**
   * Set expire for Map
   *
   * @param duration timeout duration
   * @return true if success otherwise false
   */
  boolean expire(Duration duration);

  /**
   * Set expire for Map
   *
   * @param instant expire Instant
   * @return true if success otherwise false
   */
  boolean expireAt(Instant instant);

  /**
   * Set expire for Map
   *
   * @param date expire Date
   * @return true if success otherwise false
   */
  boolean expireAt(Date date);

  /**
   * Get EntrySet to iterate over Map
   *
   * @return Set<Entry<K, V>>
   */
  Set<Map.Entry<K, V>> entrySet();
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.mappers;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.SurgeComputationModelApiLogEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.SurgeComputationModelApiLogJPA;
import java.util.List;
import org.mapstruct.Mapper;

/**
 * Mapper for converting between SurgeComputationModelApiLogJPA and
 * SurgeComputationModelApiLogEntity.
 */
@Mapper
public interface SurgeComputationModelApiLogMapper {

  /**
   * Convert a JPA entity to a domain entity.
   *
   * @param jpa the JPA entity to convert
   * @return the corresponding domain entity
   */
  SurgeComputationModelApiLogEntity mapJpaToEntity(SurgeComputationModelApiLogJPA jpa);

  /**
   * Convert a domain entity to a JPA entity.
   *
   * @param entity the domain entity to convert
   * @return the corresponding JPA entity
   */
  SurgeComputationModelApiLogJPA mapEntityToJpa(SurgeComputationModelApiLogEntity entity);

  /**
   * Convert a list of JPA entities to a list of domain entities.
   *
   * @param jpaList the list of JPA entities to convert
   * @return the corresponding list of domain entities
   */
  List<SurgeComputationModelApiLogEntity> mapJpaToEntity(
      List<SurgeComputationModelApiLogJPA> jpaList);
}

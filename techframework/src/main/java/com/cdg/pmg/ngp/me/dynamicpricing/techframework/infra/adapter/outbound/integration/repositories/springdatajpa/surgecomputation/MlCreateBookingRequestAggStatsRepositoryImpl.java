package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.surgecomputation.MlCreateBookingRequestAggStatsRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.MlCreateBookingRequestAggStatsEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.MlCreateBookingRequestAggStatsJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.mappers.MlCreateBookingRequestAggStatsEntityMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.surgecomputation.MlCreateBookingRequestAggStatsJPARepository;
import java.time.Instant;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/** Implementation of {@link MlCreateBookingRequestAggStatsRepository} using Spring Data JPA. */
@Component
@Primary
@RequiredArgsConstructor
@Slf4j
public class MlCreateBookingRequestAggStatsRepositoryImpl
    implements MlCreateBookingRequestAggStatsRepository {

  private final MlCreateBookingRequestAggStatsJPARepository jpaRepository;
  private final MlCreateBookingRequestAggStatsEntityMapper mapper;

  @Override
  @Transactional
  public MlCreateBookingRequestAggStatsEntity save(MlCreateBookingRequestAggStatsEntity entity) {
    MlCreateBookingRequestAggStatsJPA jpaEntity = mapper.mapEntityToJpa(entity);
    MlCreateBookingRequestAggStatsJPA savedJpaEntity = jpaRepository.save(jpaEntity);
    return mapper.mapJpaToEntity(savedJpaEntity);
  }

  @Override
  public List<String> getBookingIdsInTimeRange(final Instant startTime, final Instant endTime) {
    return jpaRepository.findBookingIdsInTimeRange(startTime, endTime);
  }
}

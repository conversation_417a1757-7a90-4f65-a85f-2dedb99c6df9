package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.mapper;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.RegionRainfall;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos.RegionRainfallOutbound;
import java.util.List;
import org.mapstruct.Mapper;

@Mapper
public interface WeatherRetrievalOutboundMapper {

  List<RegionRainfall> mapToRegionRainfallList(
      List<RegionRainfallOutbound> regionRainfallOutbounds);
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.FlatFareAdjustmentConfRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FlatFareAdjustmentConfEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.FlatFareAdjustmentConfJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.FlatFareAdjustmentConfJPARepository;
import com.cdg.pmg.ngp.me.dynamicpricing.utils.CollectionUtils;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

@Component
@Primary
@RequiredArgsConstructor
@Slf4j
public class FlatFareAdjustmentConfRepositoryImpl implements FlatFareAdjustmentConfRepository {
  private final FlatFareAdjustmentConfJPARepository flatFareAdjustmentConfJPARepository;

  @Override
  public List<FlatFareAdjustmentConfEntity> getFlatFareAdjustmentConf() {
    final List<FlatFareAdjustmentConfJPA> configs =
        flatFareAdjustmentConfJPARepository.findAllByIsEnabledIsTrue();

    if (CollectionUtils.isEmpty(configs)) {
      log.info("[getFlatFareAdjustmentConf] FlatFareAdjustmentConf is empty!");
      return new ArrayList<>();
    }
    return configs.stream()
        .map(
            conf ->
                FlatFareAdjustmentConfEntity.builder()
                    .id(conf.getId())
                    .perVal(conf.getPerVal())
                    .fixedVal(conf.getFixedVal())
                    .vehGrp(conf.getVehGrp())
                    .build())
        .toList();
  }
}

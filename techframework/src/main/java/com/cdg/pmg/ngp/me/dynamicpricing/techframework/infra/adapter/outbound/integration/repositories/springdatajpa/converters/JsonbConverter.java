package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.converters;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import java.io.IOException;
import java.sql.SQLException;
import java.util.List;
import org.postgresql.util.PGobject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * JPA converter for JSONB columns. Converts between a List of objects and a JSON string.
 *
 * @param <T> the type of objects in the list
 */
@Converter
public class JsonbConverter<T> implements AttributeConverter<List<T>, Object> {
  private static final Logger log = LoggerFactory.getLogger(JsonbConverter.class);
  private final ObjectMapper objectMapper = new ObjectMapper();
  private final Class<T> type;

  public JsonbConverter(Class<T> type) {
    this.type = type;
  }

  @Override
  public Object convertToDatabaseColumn(List<T> attribute) {
    if (attribute == null || attribute.isEmpty()) {
      return createPGobject("[]");
    }

    try {
      String json = objectMapper.writeValueAsString(attribute);
      return createPGobject(json);
    } catch (JsonProcessingException e) {
      log.error("Error converting list to JSON", e);
      return createPGobject("[]");
    }
  }

  private PGobject createPGobject(String json) {
    try {
      PGobject pgObject = new PGobject();
      pgObject.setType("jsonb");
      pgObject.setValue(json);
      return pgObject;
    } catch (SQLException e) {
      log.error("Error creating PGobject", e);
      return null;
    }
  }

  @Override
  public List<T> convertToEntityAttribute(Object dbData) {
    if (dbData == null) {
      return List.of();
    }

    String jsonString;
    if (dbData instanceof PGobject) {
      jsonString = ((PGobject) dbData).getValue();
    } else if (dbData instanceof String) {
      jsonString = (String) dbData;
    } else {
      log.error("Unexpected database data type: {}", dbData.getClass().getName());
      return List.of();
    }

    if (jsonString == null || jsonString.isEmpty() || "[]".equals(jsonString)) {
      return List.of();
    }

    try {
      return objectMapper.readValue(
          jsonString, objectMapper.getTypeFactory().constructCollectionType(List.class, type));
    } catch (IOException e) {
      log.error("Error converting JSON to list", e);
      return List.of();
    }
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.MlCreateBookingRequestAggStatsJPA;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/**
 * Repository interface for {@link MlCreateBookingRequestAggStatsJPA} entity. Provides methods to
 * interact with the ml_create_booking_request_agg_stats table.
 */
@Repository
public interface MlCreateBookingRequestAggStatsJPARepository
    extends JpaRepository<MlCreateBookingRequestAggStatsJPA, Long> {

  /**
   * Find by booking ID.
   *
   * @param bookingId the booking ID
   * @return the MlCreateBookingRequestAggStatsJPA entity
   */
  Optional<MlCreateBookingRequestAggStatsJPA> findByBookingId(String bookingId);

  @Query(
      value =
          """
                SELECT booking_id
                FROM ml_create_booking_request_agg_stats
                WHERE fare_calc_time >= :startTime
                AND fare_calc_time < :endTime
              """,
      nativeQuery = true)
  List<String> findBookingIdsInTimeRange(Instant startTime, Instant endTime);
}

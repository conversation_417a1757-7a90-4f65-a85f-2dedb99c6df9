package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.aop.exceptions;

import java.io.Serial;
import org.springframework.http.HttpStatus;

/** The type Duplicate resource exception. */
public class DuplicateResourceException extends BaseException {

  @Serial private static final long serialVersionUID = 8106392413180641198L;
  private static final String DUPLICATE_RESOURCE_EXCEPTION_MESSAGE = "Duplicate resource";

  private static final HttpStatus HTTP_STATUS = HttpStatus.BAD_REQUEST;

  /**
   * Instantiates a new Duplicate resource exception.
   *
   * @param message the message
   * @param errorCode the error code
   * @param arguments the arguments
   */
  public DuplicateResourceException(
      final String message, final Long errorCode, final Object... arguments) {
    super(DUPLICATE_RESOURCE_EXCEPTION_MESSAGE, message, errorCode, HTTP_STATUS, arguments);
  }

  /**
   * Instantiates a new Duplicate resource exception.
   *
   * @param message the message
   * @param errorCode the error code
   */
  public DuplicateResourceException(final String message, final Long errorCode) {
    super(DUPLICATE_RESOURCE_EXCEPTION_MESSAGE, message, errorCode, HTTP_STATUS);
  }
}

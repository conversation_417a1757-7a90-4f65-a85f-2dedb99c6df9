package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.validator;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.annotation.CustomMin;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.aop.exceptions.FieldValidationException;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.math.BigDecimal;
import lombok.Getter;
import org.springframework.http.HttpStatus;

/** The type Custom min validator. */
@Getter
public class CustomMinValidator implements ConstraintValidator<CustomMin, Object> {

  private Long value;

  private String message;

  private Long errorCode;

  private HttpStatus httpStatus;

  @Override
  public void initialize(CustomMin constraintAnnotation) {
    this.value = constraintAnnotation.value();
    this.message = constraintAnnotation.message();
    this.errorCode = constraintAnnotation.errorCode();
    this.httpStatus = constraintAnnotation.httpStatus();
  }

  @Override
  public boolean isValid(Object value, ConstraintValidatorContext context) {
    if (null == value
        || (value instanceof Long longValue && longValue < this.value)
        || (value instanceof Integer intValue && intValue < this.value)
        || (value instanceof Double doubleValue && doubleValue < this.value)
        || (value instanceof BigDecimal bigDecimalValue
            && bigDecimalValue.compareTo(BigDecimal.valueOf(this.value)) < 0)) {
      throw new FieldValidationException(message, errorCode, httpStatus);
    }
    return true;
  }
}

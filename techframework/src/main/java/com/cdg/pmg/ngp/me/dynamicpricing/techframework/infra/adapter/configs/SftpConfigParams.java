package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Getter
@Setter
public class SftpConfigParams {
  @Value("${SFTP_ENDPOINT}")
  private String host;

  @Value("${SFTP_USER}")
  private String user;

  @Value("${SFTP_DIRECTORY}")
  private String sftpDir;

  @Value("${SFTP_PRIVATE_KEY}")
  private String privateKey;
}

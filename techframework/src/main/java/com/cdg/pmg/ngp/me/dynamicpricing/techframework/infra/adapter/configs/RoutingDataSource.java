package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs;

import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;
import org.springframework.transaction.support.TransactionSynchronizationManager;

/**
 * A routing datasource that determines which datasource to use based on the transaction's read-only
 * flag.
 *
 * <p>This class routes database operations to either the reader or writer datasource based on
 * whether the current transaction is marked as read-only or not.
 */
public class RoutingDataSource extends AbstractRoutingDataSource {
  public static final String WRITER = "writer";
  public static final String READER = "reader";

  @Override
  protected Object determineCurrentLookupKey() {
    // Use writer for transactions marked as read-write
    // Use reader for read-only transactions
    return TransactionSynchronizationManager.isCurrentTransactionReadOnly() ? READER : WRITER;
  }
}

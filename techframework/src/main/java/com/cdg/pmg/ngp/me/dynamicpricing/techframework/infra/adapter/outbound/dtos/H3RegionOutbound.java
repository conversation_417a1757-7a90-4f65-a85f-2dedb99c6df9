package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.CoordinateModel;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

@Data
public class H3RegionOutbound implements Serializable {
  @Serial private static final long serialVersionUID = 3383892982119472129L;

  private Long regionId;
  private List<CoordinateModel> boundary;
  private String regionName;
}

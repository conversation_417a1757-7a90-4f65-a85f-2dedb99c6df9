package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.mappers;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.FareBreakdownDetailEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.RouteInfo;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.SearchFareBreakdownResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.FlatFareBreakdownDetailJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.custom.FareBreakdownJPACustom;
import org.mapstruct.Mapper;

/** The interface Flat fare breakdown entity mapper. */
@Mapper
public interface FlatFareBreakdownEntityMapper {
  /**
   * Map FareBreakdownDetailEntity to FlatFareBreakdownDetailJPA
   *
   * @param source FlatFareBreakdownDetailJPA
   * @return FlatFareBreakdownDetailJPA flat fare breakdown detail jpa
   */
  FlatFareBreakdownDetailJPA mapToFlatFareBreakdownDetailJpa(FareBreakdownDetailEntity source);

  /**
   * Map FlatFareBreakdownDetailJPA to FareBreakdownDetailEntity
   *
   * @param source FlatFareBreakdownDetailJPA
   * @return FareBreakdownDetailEntity fare breakdown detail entity
   */
  FareBreakdownDetailEntity mapToFareBreakdownDetailEntity(FlatFareBreakdownDetailJPA source);

  /**
   * Map flat fare breakdown detail jpa to route info route info.
   *
   * @param source the source
   * @return the route info
   */
  RouteInfo mapFlatFareBreakdownDetailJPAToRouteInfo(FlatFareBreakdownDetailJPA source);

  /**
   * Map flat fare breakdown custom jpa to search fare breakdown.
   *
   * @param fareBreakdownJPACustomcustom
   * @return the search breakdown response
   */
  SearchFareBreakdownResponse mapFareBreakdownJPACustomToSearchFareBreakdownResponse(
      FareBreakdownJPACustom fareBreakdownJPACustomcustom);
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.adapter;

import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.*;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.*;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.FareService;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.InternalServerException;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos.*;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.mapper.FareOutboundMapper;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class FareServiceAdapter implements FareService {

  private final FareOutboundAdapter fareOutboundAdapter;
  private final FareOutboundMapper fareOutboundMapper;

  @Override
  public List<PlatformFeeResponse> getPlatformFee(PlatformFeeRequest platformFeeRequest) {
    log.info("Feign Fare Service get platform fee with request: {}", platformFeeRequest);
    final String bookingChannel = platformFeeRequest.getBookingChannel();
    final String productId = platformFeeRequest.getProductId();
    final String vehicleGroupId = platformFeeRequest.getVehicleTypeId();

    List<PlatformFeeOutbound> platformFee = new ArrayList<>();
    try {
      final ResponseEntity<PlatformFeeOutboundResponse> platformFeeResponse =
          fareOutboundAdapter.getPlatformFee(bookingChannel, productId, vehicleGroupId);
      if (!ObjectUtils.anyNull(
          platformFeeResponse,
          platformFeeResponse.getBody(),
          platformFeeResponse.getBody().getData())) {
        platformFee = platformFeeResponse.getBody().getData();
      }
    } catch (Exception e) {
      log.error("Failed to get platform fee", e);
    }
    log.info("Feign Fare Service get platform fee with response: {}", platformFee);
    return fareOutboundMapper.mapToPlatformFeeResponse(platformFee);
  }

  @Override
  public List<PlatformFeeResponse> getPlatformFeeByList(PlatformFeeListRequest platformFeeRequest) {
    log.info("Feign Fare Service get platform fee  by list with request: {}", platformFeeRequest);
    final PlatformFeeListOutboundRequest outRequest =
        fareOutboundMapper.mapToPlatformFeeListOutboundRequest(platformFeeRequest);

    List<PlatformFeeOutbound> platformFee = new ArrayList<>();
    try {
      final ResponseEntity<PlatformFeeListOutboundResponse> platformFeeResponse =
          fareOutboundAdapter.getPlatformFeeByList(outRequest);
      if (Objects.nonNull(platformFeeResponse)
          && Objects.nonNull(platformFeeResponse.getBody())
          && Objects.nonNull(platformFeeResponse.getBody().getPlatformFeeList())) {
        platformFee = platformFeeResponse.getBody().getPlatformFeeList();
      }
    } catch (Exception e) {
      log.error("Failed to get platform fee by list", e);
    }
    log.info("Feign Fare Service get platform fee by list with response: {}", platformFee);
    return fareOutboundMapper.mapToPlatformFeeResponse(platformFee);
  }

  @Override
  public BookingFeeResponse getBookingFee(BookingFeeRequest bookingFeeRequest) {
    log.info("Feign Fare Service get booking fee with request: {}", bookingFeeRequest);
    final BookingFeeOutboundRequest bookingFeeOutBoundRequest =
        fareOutboundMapper.mapToBookingFeeOutBoundRequest(bookingFeeRequest);
    BookingFeeOutboundResponse bookingFeeOutBoundResponse = new BookingFeeOutboundResponse();
    try {
      final ResponseEntity<BookingFeeOutboundResponse> bookingFeeResponse =
          fareOutboundAdapter.getBookingFee(bookingFeeOutBoundRequest);
      if (!ObjectUtils.anyNull(bookingFeeResponse, bookingFeeResponse.getBody())) {
        bookingFeeOutBoundResponse = bookingFeeResponse.getBody();
      }
    } catch (Exception e) {
      log.error("Failed to get booking fee", e);
      final String errorMsg =
          MessageFormat.format(
              GET_BOOKING_FEE_FAILED.getMessage(),
              bookingFeeRequest.getVehicleTypeId(),
              bookingFeeRequest.getFlatFareType(),
              bookingFeeRequest.getProductId(),
              bookingFeeRequest.getJobType(),
              bookingFeeRequest.getIsHoliday(),
              bookingFeeRequest.getRequestDate());
      throw new InternalServerException(errorMsg, GET_BOOKING_FEE_FAILED.getErrorCode());
    }
    log.info("Feign Fare Service get booking fee with response: {}", bookingFeeOutBoundResponse);
    return fareOutboundMapper.mapToBookingFeeResponse(bookingFeeOutBoundResponse);
  }

  @Override
  public BookingFeeListResponse getBookingFeeByList(BookingFeeListRequest bookingFeeListRequest) {
    log.info("Feign Fare Service get booking fee by list with request: {}", bookingFeeListRequest);
    final BookingFeeListOutboundRequest bookingFeeOutBoundRequest =
        fareOutboundMapper.mapToBookingFeeListOutboundRequest(bookingFeeListRequest);
    BookingFeeListOutboundResponse bookingFeeOutBoundResponse =
        new BookingFeeListOutboundResponse();
    try {
      final ResponseEntity<BookingFeeListOutboundResponse> bookingFeeResponse =
          fareOutboundAdapter.getBookingFeeByList(bookingFeeOutBoundRequest);
      if (!ObjectUtils.anyNull(bookingFeeResponse, bookingFeeResponse.getBody())) {
        bookingFeeOutBoundResponse = bookingFeeResponse.getBody();
      }
    } catch (Exception e) {
      log.error("Failed to get booking fee by list", e);
      final String errorMsg =
          MessageFormat.format(
              GET_BOOKING_FEE_LIST_FAILED.getMessage(),
              bookingFeeListRequest.getJobType(),
              bookingFeeListRequest.getIsHoliday(),
              bookingFeeListRequest.getRequestDate());
      throw new InternalServerException(errorMsg, GET_BOOKING_FEE_LIST_FAILED.getErrorCode());
    }
    log.info(
        "Feign Fare Service get booking fee by list with response: {}", bookingFeeOutBoundResponse);
    return fareOutboundMapper.mapToBookingFeeListResponse(bookingFeeOutBoundResponse);
  }

  @Override
  public Map<String, List<AdditionalChargeFeeConfigResponse>> getAdditionalChargeFeeConfigMap(
      List<Integer> chargeIds, List<String> chargeTypes, String bookingChannel) {
    log.info(
        "Feign Fare Service get additional charge fee config with request: {},{},{}",
        chargeIds,
        chargeTypes,
        bookingChannel);
    try {
      final ResponseEntity<GetAdditionalChargeFeeConfigurationsListResponse>
          additionalChargeFeeConfigurationResponse =
              fareOutboundAdapter.getAdditionalChargeFeeConfigurationsList(
                  chargeIds, chargeTypes, bookingChannel);
      if (ObjectUtils.isEmpty(additionalChargeFeeConfigurationResponse)) {
        return Map.of();
      }
      GetAdditionalChargeFeeConfigurationsListResponse responseData =
          additionalChargeFeeConfigurationResponse.getBody();
      if (ObjectUtils.isEmpty(responseData)) {
        return Map.of();
      }
      List<AdditionalChargeFeeConfigurationData> additionalChargeFeeConfigDataList =
          responseData.getData();
      if (ObjectUtils.isEmpty(additionalChargeFeeConfigDataList)) {
        return Map.of();
      }
      List<AdditionalChargeFeeConfigResponse> additionalChargeFeeConfigResponseList =
          fareOutboundMapper.mapToAdditoinalChargeFeeConfigResponseList(
              additionalChargeFeeConfigDataList);

      Map<String, List<AdditionalChargeFeeConfigResponse>> additionalChargeFeeConfigMap =
          additionalChargeFeeConfigResponseList.stream()
              .collect(Collectors.groupingBy(AdditionalChargeFeeConfigResponse::getChargeType));

      log.info(
          "Feign Fare Service get additional charge fee config result: {}",
          additionalChargeFeeConfigMap);
      return additionalChargeFeeConfigMap;
    } catch (Exception e) {
      log.error("Failed to get additional charge fee config", e);
      final String errorMsg =
          MessageFormat.format(
              GET_ADDITIONAL_CHARGE_FEE_CONFIG_FAILED.getMessage(),
              chargeIds,
              chargeTypes,
              bookingChannel);
      throw new InternalServerException(
          errorMsg, GET_ADDITIONAL_CHARGE_FEE_CONFIG_FAILED.getErrorCode());
    }
  }

  @Override
  public List<AdditionalChargeFeeConfigResponse> getAdditionalChargeFeeConfigListByChargeId(
      Integer chargeId) {

    log.info("Feign Fare Service get additional charge fee config with request: {}", chargeId);
    try {
      if (ObjectUtils.isEmpty(chargeId)) {
        return List.of();
      }
      ResponseEntity<GetAdditionalChargeFeeConfigurationsListResponse>
          additionalChargeFeeConfigurationByChargeId =
              fareOutboundAdapter.getAdditionalChargeFeeConfigurationByChargeId(chargeId);
      if (ObjectUtils.isEmpty(additionalChargeFeeConfigurationByChargeId)) {
        return List.of();
      }
      GetAdditionalChargeFeeConfigurationsListResponse responseData =
          additionalChargeFeeConfigurationByChargeId.getBody();
      if (ObjectUtils.isEmpty(responseData)) {
        return List.of();
      }
      List<AdditionalChargeFeeConfigurationData> additionalChargeFeeConfigDataList =
          responseData.getData();
      if (ObjectUtils.isEmpty(additionalChargeFeeConfigDataList)) {
        return List.of();
      }
      return fareOutboundMapper.mapToAdditoinalChargeFeeConfigResponseList(
          additionalChargeFeeConfigDataList);
    } catch (Exception e) {
      log.error("Failed to get additional charge fee config", e);
      final String errorMsg =
          MessageFormat.format(
              GET_ADDITIONAL_CHARGE_FEE_CONFIG_BY_ID_FAILED.getMessage(), chargeId);
      throw new InternalServerException(
          errorMsg, GET_ADDITIONAL_CHARGE_FEE_CONFIG_BY_ID_FAILED.getErrorCode());
    }
  }
}

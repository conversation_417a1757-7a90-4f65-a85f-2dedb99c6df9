package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.ModelEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.ModelJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.SurgeComputationModel;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.SurgeComputationModelRequest;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.List;
import org.mapstruct.*;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ModelMapper {

  @Named("auditMapping")
  @BeanMapping(ignoreByDefault = true)
  @Mapping(target = "createdBy", source = "createdBy")
  @Mapping(target = "createdDate", source = "createdDate")
  @Mapping(target = "updatedBy", source = "updatedBy")
  @Mapping(target = "updatedDate", source = "updatedDate")
  ModelJPA mapAuditFields(ModelJPA source, @MappingTarget ModelJPA target);

  @Mapping(target = "id", source = "id")
  @Mapping(target = "modelName", source = "modelName")
  @Mapping(target = "description", source = "description")
  @Mapping(target = "endpointUrl", source = "endpointUrl")
  @Mapping(target = "requestFieldsMappings", source = "requestFieldsMappings")
  @Mapping(target = "createdBy", source = "createdBy")
  @Mapping(target = "createdDate", source = "createdDate")
  @Mapping(target = "updatedBy", source = "updatedBy")
  @Mapping(target = "updatedDate", source = "updatedDate")
  ModelEntity mapJpaToEntity(ModelJPA jpa);

  List<ModelEntity> mapJpaToEntity(List<ModelJPA> jpa);

  @Mapping(target = "id", source = "id")
  @Mapping(target = "modelName", source = "modelName")
  @Mapping(target = "description", source = "description")
  @Mapping(target = "endpointUrl", source = "endpointUrl")
  @Mapping(target = "requestFieldsMappings", source = "requestFieldsMappings")
  @Mapping(target = "createdBy", source = "createdBy")
  @Mapping(target = "createdDate", source = "createdDate")
  @Mapping(target = "updatedBy", source = "updatedBy")
  @Mapping(target = "updatedDate", source = "updatedDate")
  ModelJPA mapEntityToJpa(ModelEntity entity);

  @Mapping(target = "id", source = "id")
  @Mapping(target = "modelName", source = "modelName")
  @Mapping(target = "description", source = "description")
  @Mapping(target = "endpointUrl", source = "endpointUrl")
  @Mapping(target = "requestFieldsMappings", source = "requestFieldsMappings")
  @Mapping(target = "createdBy", source = "createdBy")
  @Mapping(target = "createdDate", source = "createdDate")
  @Mapping(target = "updatedBy", source = "updatedBy")
  @Mapping(target = "updatedDate", source = "updatedDate")
  SurgeComputationModel mapEntityToDto(ModelEntity entity);

  List<SurgeComputationModel> mapEntityToDto(List<ModelEntity> entities);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "modelName", source = "modelName")
  @Mapping(target = "description", source = "description")
  @Mapping(target = "endpointUrl", source = "endpointUrl")
  @Mapping(target = "requestFieldsMappings", source = "requestFieldsMappings")
  @Mapping(target = "createdBy", ignore = true)
  @Mapping(target = "createdDate", ignore = true)
  @Mapping(target = "updatedBy", ignore = true)
  @Mapping(target = "updatedDate", ignore = true)
  ModelEntity mapRequestToEntity(SurgeComputationModelRequest request);

  @AfterMapping
  default void afterEntityToJpaWithAuditUpdate(@MappingTarget ModelJPA target, ModelJPA source) {
    target.setCreatedBy(source.getCreatedBy());
    target.setCreatedDate(source.getCreatedDate());
  }

  @Mapping(target = "modelName", source = "entity.modelName")
  @Mapping(target = "description", source = "entity.description")
  @Mapping(target = "endpointUrl", source = "entity.endpointUrl")
  @Mapping(target = "requestFieldsMappings", source = "entity.requestFieldsMappings")
  @Mapping(target = "updatedBy", source = "entity.updatedBy")
  @Mapping(target = "updatedDate", source = "entity.updatedDate")
  void mapEntityToJpaWithAuditUpdate(@MappingTarget ModelJPA jpa, ModelEntity entity);

  default OffsetDateTime map(Instant instant) {
    return instant != null ? instant.atOffset(ZoneOffset.UTC) : null;
  }

  default Instant map(OffsetDateTime offsetDateTime) {
    return offsetDateTime != null ? offsetDateTime.toInstant() : null;
  }
}

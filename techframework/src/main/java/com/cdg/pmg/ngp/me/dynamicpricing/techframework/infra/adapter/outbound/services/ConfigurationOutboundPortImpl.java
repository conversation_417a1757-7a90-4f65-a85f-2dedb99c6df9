package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.services;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.mapper.NewPricingModelMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.CacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.ConfigurationServiceOutboundPort;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.NewPricingModelConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.DynamicPricingSurgeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.outbound.ZoneInfoRepositoryOutboundPort;
import com.cdg.pmg.ngp.me.dynamicpricing.service.NewPricingModelService;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs.DynamicSurgeProperties;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs.properties.RefreshableProperties;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.mapper.surgecomputation.DynamicSurgeConfigMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.properties.NewPricingModelConfigList;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RefreshScope
@RequiredArgsConstructor
public class ConfigurationOutboundPortImpl implements ConfigurationServiceOutboundPort {
  public static final String NEW_PRICING_MODEL_SIZE = "NEW_PRICING_MODEL_SIZE";
  public static final String DEMAND_SUPPLIES_DISTINCT_ZONE_ID_LIST_KEY =
      "FLEET_ANALYTIC:DEMAND_SUPPLIES:ZONE_IDS";
  public static final String DEMAND_SUPPLIES_ALL_ZONES_V2_KEY =
      "FLEET_ANALYTIC:DEMAND_SUPPLIES:ALL_ZONES_V2";

  private final NewPricingModelConfigList newPricingModelConfigProperties;
  private final NewPricingModelMapper newPricingModelMapper;
  private final ObjectMapper objectMapper;
  private final NewPricingModelService newPricingModelService;
  private final CacheService cacheService;
  private final ZoneInfoRepositoryOutboundPort zoneInfoRepositoryOutboundPort;
  private final RefreshableProperties refreshableProperties;
  private final DynamicSurgeProperties dynamicSurgeProperties;
  private final DynamicSurgeConfigMapper dynamicSurgeConfigMapper;

  @Override
  public List<NewPricingModelConfigEntity> getNewPricingModelConfigEntities(boolean validate) {
    List<String> existingZoneIds = getExistingZoneIds();

    log.info("[getNewPricingModelConfig] Get config for New Pricing Model Config");
    final List<String> finalExistingZoneIds = existingZoneIds;
    return newPricingModelConfigProperties.getItems().stream()
        .map(json -> newPricingModelMapper.parse(objectMapper, json))
        .filter(Objects::nonNull)
        .filter(
            pricingModel ->
                !validate || validateNewPricingModel(pricingModel, finalExistingZoneIds))
        .toList();
  }

  private List<String> getExistingZoneIds() {
    Object existingZoneIds =
        cacheService.getValue(DEMAND_SUPPLIES_DISTINCT_ZONE_ID_LIST_KEY, Object.class);

    if (Objects.isNull(existingZoneIds)) {
      return zoneInfoRepositoryOutboundPort.findAllZones();
    }
    return objectMapper.convertValue(existingZoneIds, new TypeReference<>() {});
  }

  @Override
  public int getTotalSizeNewPricingModelConfigEntities() {
    return newPricingModelConfigProperties.getItems().size();
  }

  @Override
  public DynamicPricingSurgeConfig getDynamicPricingSurgeConfig() {
    return dynamicSurgeConfigMapper.map(dynamicSurgeProperties);
  }

  @Override
  public Map<String, String> loadBookARideConfigurations() {
    return refreshableProperties.getBookARideConfigProperties();
  }

  private boolean validateNewPricingModel(
      NewPricingModelConfigEntity newPricingModelConfigEntity, List<String> existingZoneIds) {

    try {
      if (!existingZoneIds.contains(newPricingModelConfigEntity.getZoneId())) {
        log.info("The zone id {} is not valid", newPricingModelConfigEntity.getZoneId());
        return false;
      }
      newPricingModelService.validateIgnoreZoneCheck(newPricingModelConfigEntity);
    } catch (Exception e) {
      log.info("Validate failed for {}, reason {}", newPricingModelConfigEntity, e.getMessage());
      return false;
    }
    return true;
  }
}

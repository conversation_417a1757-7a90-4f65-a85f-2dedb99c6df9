package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Configuration
@RefreshScope
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DynamicSurgeProperties {

  @Value("${dosExcludeBookingChannels}")
  private String dosExcludeBookingChannels;

  @Value("${dosIncludeJobStatuses}")
  private String dosIncludeJobStatuses;

  @Value("${fleetAnalyticDemand15}")
  private String fleetAnalyticDemand15;

  @Value("${std001Weight}")
  private String std001Weight;

  @Value("${fleetAnalyticDemand30}")
  private String fleetAnalyticDemand30;

  @Value("${fleetAnalyticDemand60}")
  private String fleetAnalyticDemand60;

  /**
   * The get fare count sampling period in minutes.
   *
   * <ul>
   *   <li>"10,0", means between (now - 10) and (now - 0)
   *   <li>"60,30", means between (now - 60) and (now - 30)
   * </ul>
   */
  @Value("${dynamicPricingSurgeSamplingPeriodInMinutes}")
  private String dynamicPricingSurgeSamplingPeriodInMinutes;

  /**
   * The global rate config how many percent request will use region fare, min 0, max 100.
   *
   * <p>e.g.: value = 80, means:
   *
   * <ul>
   *   <li>random num in (0,80] will use region based
   *   <li>random num in (80,100] will use zone based
   * </ul>
   */
  @Value("${globalRateToRegionForGetFare}")
  private int globalRateToRegionForGetFare;
}

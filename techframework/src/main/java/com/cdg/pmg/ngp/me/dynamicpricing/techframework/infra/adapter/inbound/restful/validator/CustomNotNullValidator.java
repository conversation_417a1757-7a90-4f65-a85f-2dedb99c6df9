package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.validator;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.annotation.CustomNotNull;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.aop.exceptions.FieldValidationException;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.Objects;
import lombok.Getter;
import org.springframework.http.HttpStatus;

/** The type Custom not null validator. */
@Getter
public class CustomNotNullValidator implements ConstraintValidator<CustomNotNull, Object> {

  private String message;

  private Long errorCode;

  private HttpStatus httpStatus;

  @Override
  public void initialize(CustomNotNull constraintAnnotation) {
    this.message = constraintAnnotation.message();
    this.errorCode = constraintAnnotation.errorCode();
    this.httpStatus = constraintAnnotation.httpStatus();
  }

  @Override
  public boolean isValid(Object value, ConstraintValidatorContext context) {
    if (Objects.isNull(value)) {
      throw new FieldValidationException(message, errorCode, httpStatus);
    }
    return true;
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.adapter;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos.*;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/** The interface Address outbound adapter */
@FeignClient(name = "${openfeign.addressClient.name}")
public interface AddressOutboundAdapter {

  /**
   * Get generate-route from Address Service
   *
   * @param generateRouteRequest get route request
   * @return route response
   */
  @PostMapping(value = "/v1.0/address/generate-route", consumes = "application/json")
  ResponseEntity<GenerateRouteOutboundResponse> getRoute(
      GenerateRouteOutboundRequest generateRouteRequest);

  /**
   * Get all effective h3 regions
   *
   * @return the effective h3 regions
   */
  @GetMapping(
      value = "/v1.0/h3-regions/definition/search/effective",
      produces = {"application/json"})
  ResponseEntity<EffectiveH3RegionsOutBoundResponse> getEffectiveH3Regions();

  /**
   * Resolve h3 region by geographical coordinates
   *
   * @param request the geographical coordinates
   * @return the h3 regions response
   */
  @PostMapping(
      value = "/v1.0/h3-regions/compute",
      produces = {"application/json"},
      consumes = {"application/json"})
  ResponseEntity<H3RegionComputeOutboundResponse> resolveH3Region(
      @RequestBody List<H3RegionComputeOutboundRequest> request);
}

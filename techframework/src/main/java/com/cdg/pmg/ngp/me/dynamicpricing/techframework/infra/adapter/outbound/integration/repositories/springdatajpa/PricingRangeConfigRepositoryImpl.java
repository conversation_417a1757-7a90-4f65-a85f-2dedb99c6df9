package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa;

import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.NOT_FOUND_PRICING_RANGE_CONFIG;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.PricingRangeConfigRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.PricingRangeConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.NotFoundException;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.PricingRangeConfigJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.mappers.PricingRangeConfigEntityMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.PricingRangeConfigJPARepository;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

@Component
@Primary
@RequiredArgsConstructor
@Slf4j
public class PricingRangeConfigRepositoryImpl implements PricingRangeConfigRepository {

  private final PricingRangeConfigJPARepository pricingRangeConfigJPARepository;
  private final PricingRangeConfigEntityMapper pricingRangeConfigEntityMapper;

  @Override
  public List<PricingRangeConfigEntity> getPricingRangeConfigs() {
    log.info("Start get pricing range configs from database");
    List<PricingRangeConfigJPA> listPricingRangeConfigJPA =
        pricingRangeConfigJPARepository.getPricingRangeConfigs();
    log.info(
        "Get list pricing range configs successfully with {} elements",
        listPricingRangeConfigJPA.size());
    return listPricingRangeConfigJPA.stream()
        .map(pricingRangeConfigEntityMapper::mapPricingRangeConfigJPAToPricingRangeConfig)
        .toList();
  }

  @Override
  public PricingRangeConfigEntity insertPricingRangeConfig(
      final PricingRangeConfigEntity pricingRangeConfigEntity) {
    log.info("Start insert pricing range config");
    PricingRangeConfigJPA pricingRangeConfigJPA =
        pricingRangeConfigEntityMapper.mapPricingRangeConfigEntityToPricingRangeConfigJPA(
            pricingRangeConfigEntity);
    PricingRangeConfigJPA insertedPricingRangeConfigJPA =
        pricingRangeConfigJPARepository.save(pricingRangeConfigJPA);
    log.info(
        "End insert pricing range config with id: {}",
        insertedPricingRangeConfigJPA.getDynamicPricingRangeId());
    return pricingRangeConfigEntityMapper.mapPricingRangeConfigJPAToPricingRangeConfig(
        insertedPricingRangeConfigJPA);
  }

  @Override
  public PricingRangeConfigEntity updatePricingRangeConfig(
      final PricingRangeConfigEntity newPricingRange,
      final PricingRangeConfigEntity oldPricingRange) {
    log.info("Start update pricing range config");
    Integer pricingRangeId = oldPricingRange.getPricingRangeId();

    Optional<PricingRangeConfigJPA> optionalPricingRangeConfigJPA =
        pricingRangeConfigJPARepository.findById(pricingRangeId);
    if (optionalPricingRangeConfigJPA.isEmpty()) {
      log.error("Not found pricing range config with id: {}", pricingRangeId);
      throw new NotFoundException(
          NOT_FOUND_PRICING_RANGE_CONFIG.getMessage(),
          NOT_FOUND_PRICING_RANGE_CONFIG.getErrorCode());
    }

    PricingRangeConfigJPA pricingRangeConfigJPA = optionalPricingRangeConfigJPA.get();
    pricingRangeConfigJPA.setStartPrice(newPricingRange.getStartPrice());
    pricingRangeConfigJPA.setEndPrice(newPricingRange.getEndPrice());
    pricingRangeConfigJPA.setStep(newPricingRange.getStep());
    pricingRangeConfigJPA.setRefreshPeriod(newPricingRange.getRefreshPeriod());
    pricingRangeConfigJPA.setQuoteValidPeriod(newPricingRange.getQuoteValidPeriod());
    pricingRangeConfigJPA.setIsEnabled(newPricingRange.getIsEnabled());
    pricingRangeConfigJPA.setUpdatedBy(newPricingRange.getUpdatedBy());

    PricingRangeConfigJPA updatedPricingRangeConfigJPA =
        pricingRangeConfigJPARepository.save(pricingRangeConfigJPA);

    log.info("End update pricing range config with id: {}", pricingRangeId);
    return pricingRangeConfigEntityMapper.mapPricingRangeConfigJPAToPricingRangeConfig(
        updatedPricingRangeConfigJPA);
  }

  @Override
  public List<PricingRangeConfigEntity> getPricingRangeConfigsByDayAndHour(
      final String day, final String hour) {
    log.info("Start get pricing range configs by day and hour from database");
    List<PricingRangeConfigJPA> listPricingRangeConfigJPA =
        pricingRangeConfigJPARepository.getPricingRangeConfigsByDayAndHour(day, hour);
    log.info(
        "Get list pricing range configs by day and hour successfully with {} elements",
        listPricingRangeConfigJPA.size());
    return listPricingRangeConfigJPA.stream()
        .map(pricingRangeConfigEntityMapper::mapPricingRangeConfigJPAToPricingRangeConfig)
        .toList();
  }
}

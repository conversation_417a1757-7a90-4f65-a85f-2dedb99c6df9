package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.mapper;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.DemandSupplyStatisticsResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.DemandSupplyStatisticsResponseV2;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.RegionDemandSupplyStatistic;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos.DemandSupplyStatisticsOutbound;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos.DemandSupplyStatisticsOutboundV2;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos.RegionDemandSupplyStatisticOutBound;
import java.util.List;
import org.mapstruct.Mapper;

@Mapper
public interface FleetAnalOutboundMapper {

  List<DemandSupplyStatisticsResponse> mapToDemandSupplyStatisticsResponseList(
      List<DemandSupplyStatisticsOutbound> listOutbound);

  List<DemandSupplyStatisticsResponseV2> mapToDemandSupplyStatisticsResponseListV2(
      List<DemandSupplyStatisticsOutboundV2> listOutbound);

  List<RegionDemandSupplyStatistic> mapToRegionDemandSupplyStatisticList(
      List<RegionDemandSupplyStatisticOutBound> listOutbound);
}

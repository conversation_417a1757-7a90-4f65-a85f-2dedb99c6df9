package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.surgecomputation.StaticRegionBasedConfigurationRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticBasedConfigurationEffectiveCheckEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticBasedConfigurationVersionEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticRegionBasedConfigurationEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper.surgecomputation.StaticRegionBasedConfigurationMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.StaticRegionBasedConfigurationJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.projection.StaticBasedConfigurationVersionProjection;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.surgecomputation.StaticRegionBasedConfigurationJPARepository;
import com.cdg.pmg.ngp.me.dynamicpricing.utils.CollectionUtils;
import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 * Implementation of the StaticRegionBasedConfigurationRepository interface. This class provides the
 * implementation for CRUD operations on static region-based configurations.
 */
@Slf4j
@Repository
@Primary
@RequiredArgsConstructor
public class StaticRegionBasedConfigurationRepositoryImpl
    implements StaticRegionBasedConfigurationRepository {

  private final StaticRegionBasedConfigurationJPARepository staticConfigRepository;
  private final StaticRegionBasedConfigurationMapper configMapper;

  @Override
  @Transactional(readOnly = true)
  public List<StaticBasedConfigurationVersionEntity> findAllVersionsByNames(List<String> names) {
    return configMapper.mapVersionProjectionToEntity(
        staticConfigRepository.findAllVersionsDescByNames(names));
  }

  @Override
  @Transactional
  public StaticRegionBasedConfigurationEntity save(
      StaticRegionBasedConfigurationEntity configEntity) {
    StaticRegionBasedConfigurationJPA jpaEntity = configMapper.mapEntityToJpa(configEntity);
    StaticRegionBasedConfigurationJPA savedJpaEntity = staticConfigRepository.save(jpaEntity);
    return configMapper.mapJpaToEntity(savedJpaEntity);
  }

  @Override
  @Transactional(readOnly = true)
  public List<StaticRegionBasedConfigurationEntity> findAllByVersionAndNames(
      final String version, final List<String> names) {
    List<StaticRegionBasedConfigurationJPA> configEntities =
        staticConfigRepository.findAllByVersionAndNameIn(version, names);
    return configMapper.mapJpaToEntity(configEntities);
  }

  @Override
  @Transactional(readOnly = true)
  public List<StaticRegionBasedConfigurationEntity> findAllByNames(final List<String> names) {
    List<StaticRegionBasedConfigurationJPA> configEntities =
        staticConfigRepository.findAllByNameIn(names);
    return configMapper.mapJpaToEntity(configEntities);
  }

  @Override
  @Transactional(readOnly = true)
  public Optional<StaticRegionBasedConfigurationEntity> findById(Long id) {
    Optional<StaticRegionBasedConfigurationJPA> configOptional =
        staticConfigRepository.findById(id);
    return configOptional.map(configMapper::mapJpaToEntity);
  }

  @Override
  @Transactional
  public void updateAuditFields(
      final List<String> configNames, final String updatedBy, final Instant updatedDate) {
    staticConfigRepository.updateAuditFields(configNames, updatedBy, updatedDate);
  }

  @Override
  @Transactional
  public void deleteByNames(final List<String> configNames) {
    staticConfigRepository.deleteByNames(configNames);
  }

  @Override
  @Transactional
  public List<StaticRegionBasedConfigurationEntity> batchCreateStaticRegionBasedConfigurations(
      final List<StaticRegionBasedConfigurationEntity> configurations) {
    /*
     * When process insert, there will be a function populate_effective_to_before_insert_static_region_config
     * to check if there is a previous version with effectiveTo as null and the current effectiveFrom
     * is later than the previous effectiveFrom. If so, update the previous effectiveTo to the moment
     * before the current effectiveFrom.
     */
    List<StaticRegionBasedConfigurationJPA> jpaEntities =
        configMapper.mapEntityToJpa(configurations);
    List<StaticRegionBasedConfigurationJPA> saved = staticConfigRepository.saveAll(jpaEntities);
    return configMapper.mapJpaToEntity(saved);
  }

  @Override
  @Transactional
  public void batchCreateOrUpdateStaticRegionBasedConfigurations(
      final List<StaticRegionBasedConfigurationEntity> configsToCreate,
      final List<StaticRegionBasedConfigurationEntity> configsToUpdate) {

    if (CollectionUtils.isNotEmpty(configsToCreate)) {
      batchCreateStaticRegionBasedConfigurations(configsToCreate);
    }

    if (CollectionUtils.isNotEmpty(configsToUpdate)) {
      for (StaticRegionBasedConfigurationEntity config : configsToUpdate) {
        staticConfigRepository.save(configMapper.mapEntityToJpa(config));
      }
    }
  }

  @Override
  @Transactional(readOnly = true)
  public StaticBasedConfigurationEffectiveCheckEntity effectiveCheck(Long modelId) {
    Instant now = Instant.now();
    StaticBasedConfigurationVersionProjection effectiveVersion =
        staticConfigRepository.findEffectiveVersions(modelId, now);

    boolean isEffective = effectiveVersion != null;
    boolean isWarning = false;

    // There is currently an effective version, will check the effectiveTo is out of date or close
    // to expiration.
    if (isEffective) {
      Instant effectiveTo = effectiveVersion.getEffectiveTo();
      if (effectiveTo != null) {
        if (effectiveTo.isBefore(now) || Duration.between(now, effectiveTo).toDays() <= 5) {
          isWarning = true;
        }
      }
    }
    // There is currently no effective version. Try to get the latest future version for display,
    // but the status is still ineffective
    else {
      Instant recentEffectiveFrom =
          staticConfigRepository.findRecentEffectiveFromByEffectiveFrom(modelId, now);
      if (recentEffectiveFrom != null) {
        effectiveVersion =
            staticConfigRepository.findEffectiveVersions(modelId, recentEffectiveFrom);
        isWarning = true;
      }
    }

    StaticBasedConfigurationEffectiveCheckEntity response =
        new StaticBasedConfigurationEffectiveCheckEntity();
    response.setIsEffective(isEffective);
    response.setIsWarning(isWarning);

    if (effectiveVersion != null) {
      response.setEffectiveFrom(effectiveVersion.getEffectiveFrom());
      response.setEffectiveTo(effectiveVersion.getEffectiveTo());
      response.setVersion(effectiveVersion.getVersion());
    }

    return response;
  }

  @Override
  @Transactional(readOnly = true)
  public List<StaticRegionBasedConfigurationEntity> findByEffectiveTimeRange(
      Long modelId, Instant effectiveTime) {
    List<StaticRegionBasedConfigurationJPA> configEntities =
        staticConfigRepository.findByEffectiveTimeRange(modelId, effectiveTime);

    // If there is no effective config, the most recent data will be obtained
    if (CollectionUtils.isEmpty(configEntities)) {
      Instant recentEffectiveFrom =
          staticConfigRepository.findRecentEffectiveFromByEffectiveFrom(modelId, effectiveTime);
      log.warn(
          """
              There is no effective static region based configuration,
              will obtain the most recent configuration.
              modelId: {}, effectiveTime: {}, recentEffectiveFrom: {}
              """,
          modelId,
          effectiveTime,
          recentEffectiveFrom);
      if (recentEffectiveFrom != null) {
        configEntities =
            staticConfigRepository.findByEffectiveTimeRange(modelId, recentEffectiveFrom);
      }
    }

    return configMapper.mapJpaToEntity(configEntities);
  }
}

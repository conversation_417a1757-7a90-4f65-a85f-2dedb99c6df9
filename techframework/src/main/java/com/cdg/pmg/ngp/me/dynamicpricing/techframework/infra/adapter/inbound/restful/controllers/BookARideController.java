package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.controllers;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.BookARideService;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.api.BookARideControllerApi;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.BookARideResponse;
import io.opentelemetry.api.trace.Span;
import java.time.Instant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

/** Book A Ride Controller. */
@RestController
@Slf4j
@RequiredArgsConstructor
public class BookARideController implements BookARideControllerApi {
  private final BookARideService bookARideService;

  @Override
  public ResponseEntity<BookARideResponse> uploadFileScheduler() {
    bookARideService.uploadFile();
    BookARideResponse response = new BookARideResponse();
    response.traceId(Span.current().getSpanContext().getTraceId());
    response.data("Trigger success doTask");
    response.timestamp(Instant.now().toString());
    return ResponseEntity.ok(response);
  }
}

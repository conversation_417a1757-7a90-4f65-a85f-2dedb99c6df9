package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/** Table to store booking creation fare information for machine learning */
@Entity
@Table(name = "ml_create_booking_request_agg_stats")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class MlCreateBookingRequestAggStatsJPA {

  @Id
  @GeneratedValue(
      strategy = GenerationType.SEQUENCE,
      generator = "mlCreateBookingRequestAggStatsSequenceGenerator")
  @SequenceGenerator(
      name = "mlCreateBookingRequestAggStatsSequenceGenerator",
      sequenceName = "SEQ_ml_create_booking_request_agg_stats__id",
      allocationSize = 1)
  private Long id;

  @Column(name = "booking_id", nullable = false, length = 12, unique = true)
  private String bookingId;

  @Column(name = "fare_id", length = 75)
  private String fareId;

  @Column(name = "fare_calc_time")
  private Instant fareCalcTime;

  @Column(name = "estimated_trip_time")
  private Long estimatedTripTime;

  @Column(name = "distance")
  private Long distance;

  @Column(name = "area_type", length = 12)
  private String areaType;

  @Column(name = "pickup_address_ref", length = 12)
  private String pickupAddressRef;

  @Column(name = "dest_address_ref", length = 12)
  private String destAddressRef;

  @Column(name = "pickup_region_id")
  private Long pickupRegionId;

  @Column(name = "region_version", length = 50)
  private String regionVersion;

  @Column(name = "model_id")
  private Long modelId;

  @Column(name = "metered_base_fare", precision = 5, scale = 2)
  private Double meteredBaseFare;

  @Column(name = "total_fare", precision = 5, scale = 2)
  private BigDecimal totalFare;

  @Column(name = "dp_base_fare_for_surge")
  private Double dpBaseFareForSurge;

  @Column(name = "dp_surge_per")
  private Double dpSurgePer;

  @Column(name = "dp_final_fare")
  private Double dpFinalFare;

  @Column(name = "estimate_fare_lf")
  private BigDecimal estimatedFareLF;

  @Column(name = "estimate_fare_rt")
  private BigDecimal estimatedFareRT;

  @Column(name = "meter_platform_fee_lower")
  private Double meterPlatformFeeLower;

  @Column(name = "meter_platform_fee_upper")
  private Double meterPlatformFeeUpper;

  @Column(name = "flat_platform_fee")
  private Double flatPlatformFee;

  @Column(name = "create_timestamp", nullable = false)
  private Instant createTimestamp;
}

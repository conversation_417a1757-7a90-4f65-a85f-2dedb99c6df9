package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs.properties.RedisServerProperties;
import com.cdg.pmg.ngp.me.dynamicpricing.utils.CollectionUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.Objects;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisClusterConfiguration;
import org.springframework.data.redis.connection.RedisConfiguration;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.GenericToStringSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

@Configuration
@AllArgsConstructor
public class RedisConfig {
  @Bean
  RedisConfiguration redisConfiguration(RedisServerProperties redisServerProperties) {
    RedisConfiguration configuration;

    var clusterProperties = redisServerProperties.getCluster();
    if (Objects.nonNull(clusterProperties)
        && CollectionUtils.isNotEmpty(clusterProperties.getNodes())) {
      configuration = new RedisClusterConfiguration(clusterProperties.getNodes());
    } else {
      configuration =
          new RedisStandaloneConfiguration(
              redisServerProperties.getHost(), redisServerProperties.getPort());
    }

    RedisConfiguration.WithAuthentication redisConfigWithAuth =
        (RedisConfiguration.WithAuthentication) configuration;

    String redisUsr = redisServerProperties.getUsername();
    if (StringUtils.isNotBlank(redisUsr)) {
      redisConfigWithAuth.setUsername(redisUsr);
    }

    String redisPwd = redisServerProperties.getPassword();
    if (StringUtils.isNotBlank(redisPwd)) {
      redisConfigWithAuth.setPassword(redisPwd);
    }

    return configuration;
  }

  @Bean
  LettuceClientConfiguration lettuceClientConfiguration(
      RedisServerProperties redisServerProperties) {
    LettuceClientConfiguration.LettuceClientConfigurationBuilder lettuceClientConfigurationBuilder =
        LettuceClientConfiguration.builder();

    if (Boolean.TRUE.equals(redisServerProperties.getSsl())) {
      lettuceClientConfigurationBuilder.useSsl();
    }

    return lettuceClientConfigurationBuilder.build();
  }

  @Bean
  LettuceConnectionFactory lettuceConnectionFactory(
      RedisConfiguration configuration, LettuceClientConfiguration lettuceClientConfiguration) {
    var factory = new LettuceConnectionFactory(configuration, lettuceClientConfiguration);
    factory.afterPropertiesSet();
    return factory;
  }

  @Bean
  StringRedisTemplate redisTemplate(LettuceConnectionFactory factory, ObjectMapper objectMapper) {
    final StringRedisTemplate redisTemplate = new StringRedisTemplate();

    redisTemplate.setKeySerializer(new StringRedisSerializer());
    redisTemplate.setHashKeySerializer(new GenericToStringSerializer<>(String.class));
    redisTemplate.setHashValueSerializer(new GenericJackson2JsonRedisSerializer(objectMapper));
    redisTemplate.setConnectionFactory(factory);

    return redisTemplate;
  }
}

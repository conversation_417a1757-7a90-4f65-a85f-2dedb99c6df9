package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.StaticTimeBasedConfigurationJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.projection.StaticBasedConfigurationVersionProjection;
import java.time.Instant;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * Repository interface for StaticTimeBasedConfigurationJPA entity. Provides methods for CRUD
 * operations on static time-based configurations. Maps to the
 * surge_computation_static_time_based_configurations table.
 */
@Repository
public interface StaticTimeBasedConfigurationJPARepository
    extends JpaRepository<StaticTimeBasedConfigurationJPA, Long> {

  /**
   * Find all static time-based configurations that are effective at a specific time.
   *
   * @param effectiveTime the time at which the configurations should be effective
   * @return a list of static time-based configurations
   */
  @Query(
      "SELECT c FROM StaticTimeBasedConfigurationJPA c WHERE c.effectiveFrom <= :effectiveTime "
          + "AND (c.effectiveTo IS NULL OR c.effectiveTo > :effectiveTime)")
  List<StaticTimeBasedConfigurationJPA> findByEffectiveTimeRange(
      @Param("effectiveTime") Instant effectiveTime);

  /**
   * Find the most recent effective_from value by the given effectiveTime.
   *
   * @param effectiveTime the time at which the configurations should be effective
   * @return the most recent effective_from value
   */
  @Query(
      value =
          """
        select effective_from
        from surge_computation_static_time_based_configurations
        where effective_from <= :effectiveTime
        order by effective_from desc
        limit 1
    """,
      nativeQuery = true)
  Instant findRecentEffectiveFromByEffectiveFrom(@Param("effectiveTime") Instant effectiveTime);

  @Query(
      value =
          """
    SELECT
        version,
        effective_from as effectiveFrom,
        effective_to as effectiveTo
    FROM surge_computation_static_time_based_configurations
        GROUP BY version, effective_from, effective_to
        ORDER BY version DESC
    """,
      nativeQuery = true)
  List<StaticBasedConfigurationVersionProjection> findAllVersionsDesc();

  @Query(
      value =
          """
    SELECT
        version,
        effective_from as effectiveFrom,
        effective_to as effectiveTo
    FROM surge_computation_static_time_based_configurations
    WHERE effective_from <= :effectiveTime
        AND (effective_to IS NULL OR effective_to > :effectiveTime)
        GROUP BY version, effective_from, effective_to
    """,
      nativeQuery = true)
  StaticBasedConfigurationVersionProjection findEffectiveVersions(
      @Param("effectiveTime") Instant effectiveTime);

  List<StaticTimeBasedConfigurationJPA> findAllByVersion(String version);
}

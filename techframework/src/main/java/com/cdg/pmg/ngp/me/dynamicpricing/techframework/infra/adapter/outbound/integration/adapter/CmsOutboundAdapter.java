package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.adapter;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.CMSConfigItem;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.CMSConfigList;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.CMSConfigRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/** The interface Fleet Analytic outbound adapter. */
@FeignClient(name = "${openfeign.cmsClient.name}")
public interface CmsOutboundAdapter {

  /**
   * Get demand supply statistic from Fleet Analytic Svc
   *
   * @return response
   */
  @GetMapping(value = "/v1.0/settings", consumes = "application/json")
  ResponseEntity<CMSConfigList> getCmsNewPricingModelConfig(
      @RequestParam("searchText") String searchText,
      @RequestParam("service") String service,
      @RequestParam("limit") int limit);

  @PutMapping(value = "/v1.0/settings/{id}", consumes = "application/json")
  ResponseEntity<CMSConfigItem> updateCmsConfig(
      @PathVariable("id") Long id, @RequestBody CMSConfigRequest cmsConfigRequest);

  @PostMapping(value = "/v1.0/settings", consumes = "application/json")
  ResponseEntity<CMSConfigItem> createCmsConfig(@RequestBody CMSConfigRequest cmsConfigRequest);
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class PlatformFeeIdentifyOutbound implements Serializable {
  @Serial private static final long serialVersionUID = 5876743711863097734L;

  private String productId;
  private String vehicleGroupId;
}

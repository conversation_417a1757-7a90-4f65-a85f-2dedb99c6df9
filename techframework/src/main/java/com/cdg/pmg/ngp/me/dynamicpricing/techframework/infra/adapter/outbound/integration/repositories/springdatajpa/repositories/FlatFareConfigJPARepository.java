package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.FlatFareConfigJPA;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

public interface FlatFareConfigJPARepository extends JpaRepository<FlatFareConfigJPA, Integer> {

  @Query(
      "SELECT ff "
          + "FROM FlatFareConfigJPA as ff "
          + "WHERE ff.effectiveDateFrom <= current_timestamp "
          + "AND (ff.effectiveDateTo IS NULL "
          + "OR ff.effectiveDateTo > current_timestamp) "
          + "ORDER BY ff.paramKey")
  @Transactional(readOnly = true)
  List<FlatFareConfigJPA> getAllValidFlatFareConfig();

  @Query(
      "SELECT ff.paramValue " + "FROM FlatFareConfigJPA as ff " + "WHERE ff.paramKey = :paramKey ")
  @Transactional(readOnly = true)
  List<String> getConfByParamkeyFlatFare(String paramKey);

  @Query(
      "SELECT ff "
          + "FROM FlatFareConfigJPA as ff "
          + "WHERE ff.paramKey LIKE concat(:identify, '_START_TIME%') OR "
          + "ff.paramKey LIKE concat(:identify, '_END_TIME%') OR "
          + "ff.paramKey LIKE concat(:identify, '_DAYS%')")
  @Transactional(readOnly = true)
  List<FlatFareConfigJPA> getConfByIdentifyForCalDemandSurge(String identify);
}

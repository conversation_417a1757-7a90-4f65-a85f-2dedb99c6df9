package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.mapper;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.DpsProperties;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs.SystemParamProperties;
import org.mapstruct.Mapper;

/** Configuration mapper */
@Mapper
public interface ConfigurationMapper {

  /**
   * To DPS Properties
   *
   * @param properties the properties
   * @return the dps configuration
   */
  DpsProperties toDpsProperties(SystemParamProperties properties);
}

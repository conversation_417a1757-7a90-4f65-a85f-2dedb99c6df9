package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.adapter;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos.*;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/** The interface Fare outbound adapter */
@FeignClient(name = "${openfeign.fareClient.name}")
public interface FareOutboundAdapter {

  /**
   * Get platform fee from Address Service
   *
   * @param bookingChannel bookingChannel
   * @param productId productId
   * @param vehicleGroupId vehicleGroupId
   * @return PlatformFeeOutboundResponse List
   */
  @GetMapping(value = "/v1.0/fares/platform-fees", consumes = "application/json")
  ResponseEntity<PlatformFeeOutboundResponse> getPlatformFee(
      @RequestParam String bookingChannel,
      @RequestParam String productId,
      @RequestParam String vehicleGroupId);

  @PostMapping(value = "/v1.0/fares/getPlatformFeeByList", consumes = "application/json")
  ResponseEntity<PlatformFeeListOutboundResponse> getPlatformFeeByList(
      PlatformFeeListOutboundRequest platformFeeListOutboundRequest);

  /**
   * Get booking fee from Fare Service
   *
   * @param bookingFeeOutboundRequest request
   * @return response
   */
  @PostMapping(value = "/v1.0/fares/booking-fees", consumes = "application/json")
  ResponseEntity<BookingFeeOutboundResponse> getBookingFee(
      BookingFeeOutboundRequest bookingFeeOutboundRequest);

  /**
   * Get booking fee by list from Fare Service
   *
   * @param bookingFeeOutboundRequest BookingFeeListOutboundRequest
   * @return BookingFeeListOutboundResponse
   */
  @PostMapping(value = "/v1.0/fares/booking-fee-list", consumes = "application/json")
  ResponseEntity<BookingFeeListOutboundResponse> getBookingFeeByList(
      BookingFeeListOutboundRequest bookingFeeOutboundRequest);

  @GetMapping(value = "/v1.0/fares/additional-charge-fees", consumes = "application/json")
  ResponseEntity<GetAdditionalChargeFeeConfigurationsListResponse>
      getAdditionalChargeFeeConfigurationsList(
          @RequestParam(value = "chargeIds", required = false) List<Integer> chargeIds,
          @RequestParam(value = "chargeTypes", required = false) List<String> chargeTypes,
          @RequestParam(value = "bookingChannel", required = false) String bookingChannel);

  @GetMapping(
      value = "/v1.0/fares/additional-charge-fees/{chargeId}",
      consumes = "application/json")
  ResponseEntity<GetAdditionalChargeFeeConfigurationsListResponse>
      getAdditionalChargeFeeConfigurationByChargeId(@PathVariable("chargeId") Integer chargeId);
}

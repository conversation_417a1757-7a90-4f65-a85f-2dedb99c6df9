package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.surgecomputation.StaticTimeBasedConfigurationRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticBasedConfigurationEffectiveCheckEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticBasedConfigurationVersionEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticTimeBasedConfigurationEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper.surgecomputation.StaticTimeBasedConfigurationMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.StaticTimeBasedConfigurationJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.projection.StaticBasedConfigurationVersionProjection;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.surgecomputation.StaticTimeBasedConfigurationJPARepository;
import com.cdg.pmg.ngp.me.dynamicpricing.utils.CollectionUtils;
import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Repository
@Primary
@RequiredArgsConstructor
public class StaticTimeBasedConfigurationRepositoryImpl
    implements StaticTimeBasedConfigurationRepository {

  private final StaticTimeBasedConfigurationJPARepository staticConfigRepository;
  private final StaticTimeBasedConfigurationMapper configMapper;

  @Override
  @Transactional(readOnly = true)
  public List<StaticBasedConfigurationVersionEntity> findAllVersions() {
    return configMapper.mapVersionProjectionToEntity(staticConfigRepository.findAllVersionsDesc());
  }

  @Override
  @Transactional
  public StaticTimeBasedConfigurationEntity save(StaticTimeBasedConfigurationEntity configEntity) {
    StaticTimeBasedConfigurationJPA jpaEntity = configMapper.mapEntityToJpa(configEntity);
    StaticTimeBasedConfigurationJPA savedJpaEntity = staticConfigRepository.save(jpaEntity);
    return configMapper.mapJpaToEntity(savedJpaEntity);
  }

  @Override
  @Transactional(readOnly = true)
  public List<StaticTimeBasedConfigurationEntity> findAllByVersion(String version) {
    List<StaticTimeBasedConfigurationJPA> configEntities =
        staticConfigRepository.findAllByVersion(version);
    return configMapper.mapJpaToEntity(configEntities);
  }

  @Override
  @Transactional(readOnly = true)
  public Optional<StaticTimeBasedConfigurationEntity> findById(Long id) {
    Optional<StaticTimeBasedConfigurationJPA> configOptional = staticConfigRepository.findById(id);
    return configOptional.map(configMapper::mapJpaToEntity);
  }

  @Override
  @Transactional
  public void deleteById(Long id) {
    staticConfigRepository.deleteById(id);
  }

  @Override
  @Transactional
  public List<StaticTimeBasedConfigurationEntity> batchCreateTimeRegionBasedConfigurations(
      final List<StaticTimeBasedConfigurationEntity> configurations) {
    /*
     * When process insert, there will be a function populate_effective_to_before_insert_static_region_config
     * to check if there is a previous version with effectiveTo as null and the current effectiveFrom
     * is later than the previous effectiveFrom. If so, update the previous effectiveTo to the moment
     * before the current effectiveFrom.
     */
    List<StaticTimeBasedConfigurationJPA> jpaEntities = configMapper.mapEntityToJpa(configurations);
    List<StaticTimeBasedConfigurationJPA> saved = staticConfigRepository.saveAll(jpaEntities);
    return configMapper.mapJpaToEntity(saved);
  }

  @Override
  @Transactional(readOnly = true)
  public StaticBasedConfigurationEffectiveCheckEntity effectiveCheck() {
    Instant now = Instant.now();
    StaticBasedConfigurationVersionProjection effectiveVersion =
        staticConfigRepository.findEffectiveVersions(now);

    boolean isEffective = effectiveVersion != null;
    boolean isWarning = false;

    // There is currently an effective version, will check the effectiveTo is out of date or close
    // to expiration.
    if (isEffective) {
      Instant effectiveTo = effectiveVersion.getEffectiveTo();
      if (effectiveTo != null) {
        if (effectiveTo.isBefore(now) || Duration.between(now, effectiveTo).toDays() <= 5) {
          isWarning = true;
        }
      }
    }
    // There is currently no effective version. Try to get the latest future version for display,
    // but the status is still ineffective
    else {
      Instant recentEffectiveFrom =
          staticConfigRepository.findRecentEffectiveFromByEffectiveFrom(now);
      if (recentEffectiveFrom != null) {
        effectiveVersion = staticConfigRepository.findEffectiveVersions(recentEffectiveFrom);
        isWarning = true;
      }
    }

    StaticBasedConfigurationEffectiveCheckEntity response =
        new StaticBasedConfigurationEffectiveCheckEntity();
    response.setIsEffective(isEffective);
    response.setIsWarning(isWarning);

    if (effectiveVersion != null) {
      response.setEffectiveFrom(effectiveVersion.getEffectiveFrom());
      response.setEffectiveTo(effectiveVersion.getEffectiveTo());
      response.setVersion(effectiveVersion.getVersion());
    }

    return response;
  }

  @Override
  @Transactional(readOnly = true)
  public List<StaticTimeBasedConfigurationEntity> findByEffectiveTimeRange(Instant effectiveTime) {
    List<StaticTimeBasedConfigurationJPA> configEntities =
        staticConfigRepository.findByEffectiveTimeRange(effectiveTime);

    // If there is no effective config, the most recent data will be obtained
    if (CollectionUtils.isEmpty(configEntities)) {
      Instant recentEffectiveFrom =
          staticConfigRepository.findRecentEffectiveFromByEffectiveFrom(effectiveTime);
      log.warn(
          """
              There is no effective static time based configuration,
              will obtain the most recent configuration.
              effectiveTime: {}, recentEffectiveFrom: {}
              """,
          effectiveTime,
          recentEffectiveFrom);
      if (recentEffectiveFrom != null) {
        configEntities = staticConfigRepository.findByEffectiveTimeRange(recentEffectiveFrom);
      }
    }

    return configMapper.mapJpaToEntityWithAppliedHoursList(configEntities);
  }

  @Override
  @Transactional
  public StaticTimeBasedConfigurationEntity update(
      StaticTimeBasedConfigurationEntity configEntity) {
    Optional<StaticTimeBasedConfigurationJPA> existingConfigOptional =
        staticConfigRepository.findById(configEntity.getId());
    if (existingConfigOptional.isEmpty()) {
      return null;
    }

    StaticTimeBasedConfigurationJPA existingJpaEntity = existingConfigOptional.get();
    configMapper.mapEntityToJpaWithAuditUpdate(existingJpaEntity, configEntity);

    StaticTimeBasedConfigurationJPA updatedJpaEntity =
        staticConfigRepository.save(existingJpaEntity);
    return configMapper.mapJpaToEntity(updatedJpaEntity);
  }
}

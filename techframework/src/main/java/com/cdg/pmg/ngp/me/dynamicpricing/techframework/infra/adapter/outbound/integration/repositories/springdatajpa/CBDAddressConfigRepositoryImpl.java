package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.CBDAddressConfigRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.LocationSurchargeConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.custom.CBDSurchargeJPACustom;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.mappers.CBDSurchargeMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.CBDAddressConfigJPARepository;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

@Component
@Primary
@RequiredArgsConstructor
@Slf4j
public class CBDAddressConfigRepositoryImpl implements CBDAddressConfigRepository {

  private final CBDAddressConfigJPARepository cbdAddressConfigJPARepository;
  private final CBDSurchargeMapper mapper;

  @Override
  public void insertCBDAddress(Set<String> insertCBDAddressList) {
    cbdAddressConfigJPARepository.insertBatchOnConflict(
        insertCBDAddressList.toArray(String[]::new));
  }

  @Override
  public void deleteCBDAddress(Set<String> deleteCBDAddressList) {
    cbdAddressConfigJPARepository.deleteBatchByAddressRef(
        deleteCBDAddressList.toArray(String[]::new));
  }

  @Override
  public List<LocationSurchargeConfigEntity> getLocConfigByAddressRefList(
      List<String> addressRefList) {
    List<CBDSurchargeJPACustom> locConfigs =
        cbdAddressConfigJPARepository.getLocConfigByAddressRefList(
            addressRefList.toArray(String[]::new));
    if (ObjectUtils.isNotEmpty(locConfigs)) {
      return mapper.toLocationSurchargeConfigEntity(locConfigs);
    }
    return new ArrayList<>();
  }
}

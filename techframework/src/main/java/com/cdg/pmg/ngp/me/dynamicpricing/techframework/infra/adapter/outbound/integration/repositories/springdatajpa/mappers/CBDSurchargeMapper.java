package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.mappers;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.LocationSurchargeConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.custom.CBDSurchargeJPACustom;
import java.util.List;
import org.mapstruct.Mapper;

@Mapper
public interface CBDSurchargeMapper {

  List<LocationSurchargeConfigEntity> toLocationSurchargeConfigEntity(
      List<CBDSurchargeJPACustom> source);
}

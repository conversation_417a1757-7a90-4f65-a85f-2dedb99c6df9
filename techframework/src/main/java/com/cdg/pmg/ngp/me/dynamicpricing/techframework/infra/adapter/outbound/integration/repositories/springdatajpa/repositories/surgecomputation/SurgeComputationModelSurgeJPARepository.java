package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.SurgeComputationModelSurgeJPA;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * Repository interface for SurgeComputationModelSurgeJPA entity. Provides methods for CRUD
 * operations on surge values calculated by the surge computation model service. Maps to the
 * surge_computation_model_surges table.
 */
@Repository
public interface SurgeComputationModelSurgeJPARepository
    extends JpaRepository<SurgeComputationModelSurgeJPA, Long> {

  /**
   * Find surge entity by region ID.
   *
   * @param modelId the model ID to search for
   * @param regionId the region ID to search for
   * @return an optional surge entity for the specified region
   */
  Optional<SurgeComputationModelSurgeJPA> findOneByModelIdAndRegionId(Long modelId, Long regionId);

  /**
   * Find all surge entities by model ID.
   *
   * @param modelId the model ID to search for
   * @return a list of surge entities for the specified model
   */
  List<SurgeComputationModelSurgeJPA> findAllByModelId(Long modelId);
}

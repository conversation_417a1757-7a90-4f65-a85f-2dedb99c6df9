package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PlatformFeeOutboundResponse implements Serializable {
  @Serial private static final long serialVersionUID = 2201319905825770352L;

  private List<PlatformFeeOutbound> data;
  private String timestamp;
}

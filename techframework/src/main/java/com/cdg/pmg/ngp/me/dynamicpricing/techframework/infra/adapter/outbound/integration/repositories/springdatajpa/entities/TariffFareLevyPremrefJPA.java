package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "tariff_fare_levy_premref")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TariffFareLevyPremrefJPA {
  @Id
  @Column(name = "pk")
  private Integer id;

  @Column(name = "revision_id")
  private Integer revisionId;

  @Column(name = "product_id")
  private String productId;

  @Column(name = "veh_type_id")
  private Integer vehTypeId;

  @Column(name = "tariff_type_code")
  private String tariffTypeCode;

  @Column(name = "fare_amt")
  private Double fareAmt;

  @Column(name = "levy_amt")
  private Double levyAmt;
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.adapter;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;

/** The interface Fleet Analytic outbound adapter. */
@FeignClient(name = "${openfeign.fleetAnalyticClient.name}")
public interface FleetAnalyticOutboundAdapter {

  /**
   * Get demand supply statistic from Fleet Analytic Svc
   *
   * @return response
   */
  @GetMapping(
      value = "/v1.0/fleet-analytic/demand-supply-statistics",
      consumes = "application/json")
  ResponseEntity<DemandSupplyStatisticsOutboundResponse> getDemandSupplyStatistics();

  @GetMapping(
      value = "/v2.0/fleet-analytic/demand-supply-statistics",
      consumes = "application/json")
  ResponseEntity<DemandSupplyStatisticsOutboundV2Response> getDemandSupplyStatisticsV2();

  @PostMapping(
      value = "/v1.0/fleet-analytic/region/cal-demand-supply",
      consumes = "application/json",
      produces = "application/json")
  ResponseEntity<RegionDemandSupplyStatisticsResponse> calculateRegionDemandSupplyStatistics(
      CalculateRegionDemandSupplyStatisticRequest request);
}

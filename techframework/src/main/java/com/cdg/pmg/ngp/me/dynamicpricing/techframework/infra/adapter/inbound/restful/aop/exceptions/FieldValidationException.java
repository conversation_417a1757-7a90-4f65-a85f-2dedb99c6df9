package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.aop.exceptions;

import jakarta.validation.ValidationException;
import java.io.Serial;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import org.springframework.http.HttpStatus;

/** The type Field validation exception. */
@Getter
public class FieldValidationException extends ValidationException {

  @Serial private static final long serialVersionUID = 6786749145940855920L;
  private static final String FIELD_VALIDATION_EXCEPTION = "Field validation exception";
  private static final HttpStatus HTTP_STATUS = HttpStatus.BAD_REQUEST;

  private final transient String message;
  private final transient Long errorCode;
  private final transient HttpStatus httpStatus;

  private final transient FieldError fieldViolation;

  /**
   * Instantiates a new Field validation exception.
   *
   * @param message the message
   * @param errorCode the error code
   * @param httpStatus the http status
   */
  public FieldValidationException(
      final String message, final Long errorCode, final HttpStatus httpStatus) {
    this.message = message;
    this.errorCode = errorCode;
    this.httpStatus = httpStatus;
    this.fieldViolation = null;
  }

  /**
   * Instantiates a new Field validation exception.
   *
   * @param message the message
   * @param errorCode the error code
   * @param fieldError the field error
   */
  public FieldValidationException(
      final String message, final Long errorCode, final FieldError fieldError) {
    super(FIELD_VALIDATION_EXCEPTION);
    this.message = message;
    this.errorCode = errorCode;
    this.httpStatus = HTTP_STATUS;
    this.fieldViolation = fieldError;
  }

  /**
   * Instantiates a new Field validation exception.
   *
   * @param message the message
   * @param errorCode the error code
   */
  public FieldValidationException(final String message, final Long errorCode) {
    super(FIELD_VALIDATION_EXCEPTION);
    this.message = message;
    this.errorCode = errorCode;
    this.httpStatus = HTTP_STATUS;
    this.fieldViolation = null;
  }

  /** The type Field error. */
  @Getter
  @Builder
  @AllArgsConstructor
  public static class FieldError {
    private final String field;
    private final String message;
    private final Object[] arguments;

    private Long code;

    /**
     * Instantiates a new Field error.
     *
     * @param field the field
     * @param message the message
     * @param arguments the arguments
     */
    public FieldError(String field, String message, Object[] arguments) {
      this.field = field;
      this.message = message;
      this.arguments = arguments;
    }

    /**
     * Of field error.
     *
     * @param field the field
     * @param message the message
     * @return the field error
     */
    public static FieldError of(final String field, final String message) {
      return new FieldError(field, message, new Object[0]);
    }

    /**
     * Of field error.
     *
     * @param field the field
     * @param message the message
     * @param arguments the arguments
     * @return the field error
     */
    public static FieldError of(final String field, final String message, Object... arguments) {
      return new FieldError(field, message, arguments);
    }
  }
}

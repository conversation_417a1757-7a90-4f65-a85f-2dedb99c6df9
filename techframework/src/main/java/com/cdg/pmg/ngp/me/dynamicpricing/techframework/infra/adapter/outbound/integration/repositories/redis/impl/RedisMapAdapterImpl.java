package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.redis.impl;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.redis.RedisMapAdapter;
import java.time.Duration;
import java.time.Instant;
import java.util.Date;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.support.collections.RedisMap;

@RequiredArgsConstructor
public class RedisMapAdapterImpl<K, V> implements RedisMapAdapter<K, V> {
  private final RedisMap<K, V> redisMap;

  @Override
  public ConcurrentMap<K, V> getMap() {
    return redisMap;
  }

  @Override
  public boolean expire(Duration duration) {
    var result = redisMap.expire(duration);
    if (result != null) {
      return result;
    }
    return false;
  }

  @Override
  public boolean expire(long timeout, TimeUnit unit) {
    var result = redisMap.expire(timeout, unit);
    if (result != null) {
      return result;
    }
    return false;
  }

  @Override
  public boolean expireAt(Instant instant) {
    var result = redisMap.expireAt(instant);
    if (result != null) {
      return result;
    }
    return false;
  }

  @Override
  public boolean expireAt(Date date) {
    var result = redisMap.expireAt(date);
    if (result != null) {
      return result;
    }
    return false;
  }

  @Override
  public Set<Map.Entry<K, V>> entrySet() {
    return redisMap.entrySet();
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa;

import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.ERROR_MAP_TO_ROUTE_INFO;
import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.FAILED_TO_CREATE_FARE_BREAKDOWN;
import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.NOT_FOUND_ROUTE_INFO;
import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.SEARCH_FARE_BREAKDOWN_REQUEST_INVALID;
import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.TRIP_ID_BLANK;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.FlatFareBreakDownRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FareBreakdownDetailEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.RouteInfo;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.SearchFareBreakdownResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.BadRequestException;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.InternalServerException;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.NotFoundException;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.FlatFareBreakdownDetailJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.custom.FareBreakdownJPACustom;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.mappers.FlatFareBreakdownEntityMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.FlatFareBreakdownJPARepository;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

@Component
@Primary
@RequiredArgsConstructor
@Slf4j
public class FlatFareBreakdownRepositoryImpl implements FlatFareBreakDownRepository {
  private final FlatFareBreakdownJPARepository flatFareBreakdownJPARepository;
  private final FlatFareBreakdownEntityMapper breakdownEntityMapper;

  @Override
  public boolean isExisted(String bookingId) {
    final FlatFareBreakdownDetailJPA flatFareBreakdownDetailJPA =
        flatFareBreakdownJPARepository.findByBookingId(bookingId);
    return Objects.nonNull(flatFareBreakdownDetailJPA);
  }

  @Override
  public void createFareBreakdown(FareBreakdownDetailEntity fareBreakdownDetailEntity) {
    log.info("Start create fare breakdown to database");
    try {
      FlatFareBreakdownDetailJPA fareBreakdownDetailJpa =
          breakdownEntityMapper.mapToFlatFareBreakdownDetailJpa(fareBreakdownDetailEntity);
      FlatFareBreakdownDetailJPA fareBreakdownDetailJpaResult =
          flatFareBreakdownJPARepository.save(fareBreakdownDetailJpa);
      log.info(
          "Create fare breakdown in database successfully: {} .", fareBreakdownDetailJpaResult);
    } catch (Exception exception) {
      log.info(
          "Create fare breakdown in database failed with exception cause = {}, message = {}",
          exception.getCause(),
          exception.getMessage());
      // TODO: use Enum for the error msg and code
      throw new InternalServerException(
          FAILED_TO_CREATE_FARE_BREAKDOWN.getMessage(),
          FAILED_TO_CREATE_FARE_BREAKDOWN.getErrorCode());
    }
  }

  @Override
  public RouteInfo getGeneratedRouteByTripId(final String tripId) {
    if (tripId.isBlank()) {
      log.error("Trip id is blank");
      throw new BadRequestException(TRIP_ID_BLANK.getMessage(), TRIP_ID_BLANK.getErrorCode());
    }

    List<FlatFareBreakdownDetailJPA> flatFareBreakdownDetailJPA =
        flatFareBreakdownJPARepository.findByTripId(tripId);
    if (ObjectUtils.isEmpty(flatFareBreakdownDetailJPA)) {
      log.error("Can not found route info by tripId: {}", tripId);
      throw new NotFoundException(
          NOT_FOUND_ROUTE_INFO.getMessage(), NOT_FOUND_ROUTE_INFO.getErrorCode());
    }
    try {
      FlatFareBreakdownDetailJPA latestRecord =
          flatFareBreakdownDetailJPA.get(flatFareBreakdownDetailJPA.size() - 1);
      return breakdownEntityMapper.mapFlatFareBreakdownDetailJPAToRouteInfo(latestRecord);
    } catch (Exception e) {
      log.error("Error at getGeneratedRouteByTripId: {}", e.getMessage());
      throw new InternalServerException(
          ERROR_MAP_TO_ROUTE_INFO.getMessage(), ERROR_MAP_TO_ROUTE_INFO.getErrorCode());
    }
  }

  @Override
  public SearchFareBreakdownResponse searchFareBreakdown(
      String fareId, String tripId, String bookingId) {
    if (ObjectUtils.allNull(fareId, tripId, bookingId)) {
      log.error("searchFareBreakdown: Invalid search fare breakdown request");
      throw new BadRequestException(
          SEARCH_FARE_BREAKDOWN_REQUEST_INVALID.getMessage(),
          SEARCH_FARE_BREAKDOWN_REQUEST_INVALID.getErrorCode());
    }

    try {
      log.info("Start search fare breakdown from db");
      final List<FareBreakdownJPACustom> fareBreakdownJPACustom =
          flatFareBreakdownJPARepository.searchFareBreakdown(fareId, tripId, bookingId);
      FareBreakdownJPACustom latestRecord = null;

      if (!ObjectUtils.isEmpty(fareBreakdownJPACustom)) {
        latestRecord = fareBreakdownJPACustom.get(fareBreakdownJPACustom.size() - 1);
      }
      return breakdownEntityMapper.mapFareBreakdownJPACustomToSearchFareBreakdownResponse(
          latestRecord);
    } catch (Exception ex) {
      log.error("searchFareBreakdown exception: {}", ex.getMessage());
      throw new InternalServerException(ex.getMessage(), 500L);
    }
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.FareTypeConfigJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.custom.EttUnitFareJPACustom;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

public interface EttUnitFareJPARepository extends JpaRepository<FareTypeConfigJPA, Integer> {
  @Query(
      value =
          "SELECT ',' || STRING_AGG(zone_id, ',' ORDER BY zone_id) || ',' AS ZoneIds, "
              + "       day_of_week AS DayOfWeek, "
              + "       ekm AS Ekm, "
              + "       hr AS Hr, "
              + "       charge_by AS ChargeBy, "
              + "       taxi_type AS TaxiType, "
              + "       rate AS Rate "
              + "FROM ett_unit_fare "
              + "WHERE zone_id IN "
              + "      (SELECT z.zone_id "
              + "       FROM zone_info z "
              + "       WHERE  cbd_zone = 'Y' "
              + "         AND zone_id NOT IN ('00', '52', '152', '53', '153')) "
              + "GROUP BY day_of_week, "
              + "         ekm, "
              + "         hr, "
              + "         charge_by, "
              + "         taxi_type, "
              + "         rate "
              + "UNION ALL "
              + "SELECT ',' || STRING_AGG(zone_id, ',' ORDER BY zone_id) || ',' AS zone_ids, "
              + "       day_of_week, "
              + "       ekm, "
              + "       hr, "
              + "       charge_by, "
              + "       taxi_type, "
              + "       rate "
              + "FROM ett_unit_fare "
              + "WHERE zone_id IN ('00') "
              + "GROUP BY day_of_week, "
              + "         ekm, "
              + "         hr, "
              + "         charge_by, "
              + "         taxi_type, "
              + "         rate "
              + "UNION ALL "
              + "SELECT ',' || STRING_AGG(zone_id, ',' ORDER BY zone_id) || ',' AS zone_ids, "
              + "       day_of_week, "
              + "       ekm, "
              + "       hr, "
              + "       charge_by, "
              + "       taxi_type, "
              + "       rate "
              + "FROM ett_unit_fare "
              + "WHERE zone_id IN "
              + "      (SELECT z.zone_id "
              + "       FROM zone_info z "
              + "       WHERE UPPER(z.zone_desc) LIKE '%TAMPINES%SIMEI%') "
              + "GROUP BY day_of_week, "
              + "         ekm, "
              + "         hr, "
              + "         charge_by, "
              + "         taxi_type, "
              + "         rate "
              + "UNION ALL "
              + "SELECT ',' || STRING_AGG(zone_id, ',' ORDER BY zone_id) || ',' AS zone_ids, "
              + "       day_of_week, "
              + "       ekm, "
              + "       hr, "
              + "       charge_by, "
              + "       taxi_type, "
              + "       rate "
              + "FROM ett_unit_fare "
              + "WHERE zone_id IN "
              + "      (SELECT z.zone_id "
              + "       FROM zone_info z "
              + "       WHERE UPPER(z.zone_desc) LIKE 'HOUGANG%') "
              + "GROUP BY day_of_week, "
              + "         ekm, "
              + "         hr, "
              + "         charge_by, "
              + "         taxi_type, "
              + "         rate",
      nativeQuery = true)
  @Transactional(readOnly = true)
  List<EttUnitFareJPACustom> getEttUnitFareConfigs();
}

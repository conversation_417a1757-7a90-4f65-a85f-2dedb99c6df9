package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.surgecomputation.SurgeComputationModelSurgeRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.H3RegionSurgeEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.SurgeComputationModelSurgeEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.SurgeComputationModelSurgeJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.mappers.SurgeComputationModelSurgeMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.surgecomputation.ModelJPARepository;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.surgecomputation.SurgeComputationModelSurgeJPARepository;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * Implementation of the SurgeComputationModelSurgeRepository interface. Provides methods for CRUD
 * operations on surge values calculated by the surge computation model service.
 */
@Component
@Primary
@RequiredArgsConstructor
@Slf4j
public class SurgeComputationModelSurgeRepositoryImpl
    implements SurgeComputationModelSurgeRepository {

  private final ModelJPARepository modelJPARepository;
  private final SurgeComputationModelSurgeJPARepository jpaRepository;
  private final SurgeComputationModelSurgeMapper mapper;

  /**
   * Batch upsert surge entities.
   *
   * @param modelId the model ID from {@link
   *     com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.ModelEntity}
   * @param entities a list of the surge entities to save
   */
  @Override
  @Transactional
  public void batchUpsertSurges(final Long modelId, final List<H3RegionSurgeEntity> entities) {
    if (log.isDebugEnabled()) {
      log.info(
          "Starting batch upsert for modelId: {} of {} surge entities", modelId, entities.size());
    }

    List<SurgeComputationModelSurgeJPA> allExistingSurges = jpaRepository.findAllByModelId(modelId);

    saveOrUpdateSurges(modelId, entities, allExistingSurges);
    deleteSurgesIfHas(entities, allExistingSurges);

    if (log.isDebugEnabled()) {
      log.info("Batch upsert for modelId: {}, successfully completed", modelId);
    }
  }

  @Override
  @Transactional(readOnly = true)
  public List<SurgeComputationModelSurgeEntity> findByModelName(final String modelName) {
    Long modelId = modelJPARepository.findIdByModelName(modelName);
    return findByModelId(modelId);
  }

  @Override
  @Transactional(readOnly = true)
  public List<SurgeComputationModelSurgeEntity> findByModelId(final Long modelId) {
    if (modelId == null) {
      return List.of();
    }
    List<SurgeComputationModelSurgeJPA> jpaEntities = jpaRepository.findAllByModelId(modelId);
    return mapper.mapJpaToEntity(jpaEntities);
  }

  @Override
  @Transactional(readOnly = true)
  public Optional<SurgeComputationModelSurgeEntity> findByModelIdAndRegionId(
      final Long modelId, final Long regionId) {
    if (modelId == null || regionId == null) {
      return Optional.empty();
    }
    Optional<SurgeComputationModelSurgeJPA> jpaEntity =
        jpaRepository.findOneByModelIdAndRegionId(modelId, regionId);
    return jpaEntity.map(mapper::mapJpaToEntity);
  }

  private void saveOrUpdateSurges(
      final Long modelId,
      final List<H3RegionSurgeEntity> entities,
      final List<SurgeComputationModelSurgeJPA> allExistingSurges) {
    Map<Long, SurgeComputationModelSurgeJPA> existingSurgeMap =
        allExistingSurges.stream()
            .collect(
                Collectors.toMap(SurgeComputationModelSurgeJPA::getRegionId, Function.identity()));

    Instant now = Instant.now();

    List<SurgeComputationModelSurgeJPA> toSave = new ArrayList<>();
    for (H3RegionSurgeEntity entity : entities) {
      SurgeComputationModelSurgeJPA jpaEntity = mapper.mapEntityToJpa(entity, now);
      SurgeComputationModelSurgeJPA existingSurge = existingSurgeMap.get(entity.getRegionId());

      if (existingSurge != null) {
        jpaEntity.setId(existingSurge.getId()); // Preserve the ID
        jpaEntity.setPrevSurge(existingSurge.getSurge()); // Set previous surge to current value
      }

      jpaEntity.setModelId(modelId);
      toSave.add(jpaEntity);
    }

    jpaRepository.saveAll(toSave);
  }

  private void deleteSurgesIfHas(
      final List<H3RegionSurgeEntity> entities,
      final List<SurgeComputationModelSurgeJPA> allExistingSurges) {
    // Extract all region IDs from input entities
    Set<Long> newRegionIds =
        entities.stream().map(H3RegionSurgeEntity::getRegionId).collect(Collectors.toSet());
    List<SurgeComputationModelSurgeJPA> toDelete =
        allExistingSurges.stream()
            .filter(surge -> !newRegionIds.contains(surge.getRegionId()))
            .collect(Collectors.toList());

    // Execute batch delete (if there are any records to be deleted)
    if (!toDelete.isEmpty()) {
      jpaRepository.deleteAll(toDelete);
      log.info("Deleted {} surge entities", toDelete.size());
    }
  }
}

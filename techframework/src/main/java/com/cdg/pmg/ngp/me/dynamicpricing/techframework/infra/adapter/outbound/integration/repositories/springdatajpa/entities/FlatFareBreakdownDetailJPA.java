package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "flat_fare_breakdown_detail")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class FlatFareBreakdownDetailJPA {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "flat_fare_breakdown_id")
  private Integer flatFareBreakdownId;

  @Column(name = "booking_id")
  private String bookingId;

  @Column(name = "fare_id")
  private String fareId;

  @Column(name = "trip_id")
  private String tripId;

  @Column(name = "encoded_polyline")
  private String encodedPolyline;

  @Column(name = "pickup_address_ref")
  private String pickupAddressRef;

  @Column(name = "pickup_address_lat")
  private Double pickupAddressLat;

  @Column(name = "pickup_address_lng")
  private Double pickupAddressLng;

  @Column(name = "pickup_zone_id")
  private String pickupZoneId;

  @Column(name = "dest_address_ref")
  private String destAddressRef;

  @Column(name = "dest_address_lat")
  private Double destAddressLat;

  @Column(name = "dest_address_lng")
  private Double destAddressLng;

  @Column(name = "dest_zone_id")
  private String destZoneId;

  @Column(name = "intermediate_address_ref")
  private String intermediateAddrRef;

  @Column(name = "intermediate_address_lat")
  private Double intermediateAddrLat;

  @Column(name = "intermediate_address_lng")
  private Double intermediateAddrLng;

  @Column(name = "intermediate_zone_id")
  private String intermediateZoneId;

  @Column(name = "req_date")
  private Timestamp requestDate;

  @Column(name = "flatdown_rate")
  private Double flagDownRate;

  @Column(name = "tier_1_fare")
  private Double tier1Fare;

  @Column(name = "tier_2_fare")
  private Double tier2Fare;

  @Column(name = "wait_time_fare")
  private Double waitTimeFare;

  @Column(name = "peak_hour_fare")
  private Double peakHrFare;

  @Column(name = "mid_night_fare")
  private Double midNightFare;

  @Column(name = "hourly_surcharge")
  private Double hourlySurcharge;

  @Column(name = "booking_fee")
  private Double bookingFee;

  @Column(name = "loc_surcharge")
  private Double locSurcharge;

  @Column(name = "event_surcharge")
  private Double eventSurcharge;

  @Column(name = "additional_surcharge")
  private Double additionalSurcharge;

  @Column(name = "multi_destination_surcharge")
  private Double multiDestSurcharge;

  @Column(name = "routing_distance")
  private Long routingDistance;

  @Column(name = "ett")
  private Long ett;

  @Column(name = "ideal_time")
  private Double idealTime;

  @Column(name = "cal_method")
  private String calMethod;

  @Column(name = "dp_surge_per")
  private Double dpSurgePercent;

  @Column(name = "dp_surge_amt")
  private Double dpSurgeAmount;

  @Column(name = "dp_applied_surge_amt")
  private Double dpAppliedSurgeAmount;

  @Column(name = "dp_base_fare_for_surge")
  private Double dpBaseFareForSurge;

  @Column(name = "dp_final_fare")
  private Double dpFinalFare;

  @Column(name = "dp_batch_key")
  private Integer dpBatchKey;

  @Column(name = "metered_base_fare")
  private Double meteredBaseFare;

  @Column(name = "total_fare")
  private BigDecimal totalFare;

  @Column(name = "estimate_fare_lf")
  private BigDecimal estimatedFareLF;

  @Column(name = "estimate_fare_rt")
  private BigDecimal estimatedFareRT;

  @Column(name = "flat_platform_fee_id")
  private Long flatPlatformFeeId;

  @Column(name = "flat_platform_fee")
  private Double flatPlatformFee;

  @Column(name = "meter_platform_fee_id")
  private Long meterPlatformFeeId;

  @Column(name = "meter_platform_fee_lower")
  private Double meterPlatformFeeLower;

  @Column(name = "meter_platform_fee_upper")
  private Double meterPlatformFeeUpper;

  @Column(name = "updated_by")
  private String updatedBy;
}

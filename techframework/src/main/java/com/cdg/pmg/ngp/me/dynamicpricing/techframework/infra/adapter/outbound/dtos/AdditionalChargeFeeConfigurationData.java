package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdditionalChargeFeeConfigurationData implements Serializable {

  private static final long serialVersionUID = 1L;
  private Integer chargeId;
  private String chargeType;
  private String chargeKey;
  private BigDecimal chargeValue;
  private String chargeDescription;
  private String chargeFormula;
  private String bookingChannel;
}

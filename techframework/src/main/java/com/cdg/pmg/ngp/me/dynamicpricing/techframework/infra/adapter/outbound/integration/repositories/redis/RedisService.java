package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.redis;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Adapter interface to communicate with Redis
 *
 * <AUTHOR>
 */
public interface RedisService {

  /**
   * Set Key list_value pair to redis.
   *
   * @param <T> type data
   * @param key key of cache
   * @param listData list data
   */
  <T> void setListValue(final String key, List<T> listData);

  /**
   * Set Key-value pair to redis store. The data will be serialize into json String before insert
   * This method will not set expired time for the new key-value.
   *
   * @param <T>
   * @param key
   * @param data
   */
  <T> void setValue(final String key, T data);

  /**
   * Set Key-value pair to redis store. The data will be serialize into json String before insert
   * This method will set expired time for the new key-value. if expireDuration <= 0 will set as
   * default expire time defined in ${me.redis.defaultExpiredTimeInSecond}
   *
   * @param <T>
   * @param key key of the pair
   * @param data value to store
   * @param expireDuration expiration duration in seconds.
   */
  <T> void setValue(final String key, T data, int expireDuration);

  /**
   * Set Key-value pair to redis store. The data will be serialized into json String before insert
   * This method will set expired time for the new key-value. if expireDuration <= 0 will set as
   * default expire time defined in ${me.redis.defaultExpiredTimeInSecond}
   *
   * @param <T>
   * @param key key of the pair
   * @param data value to store
   * @param expireDuration expiration duration in seconds.
   */
  <T> void setValue(final String key, T data, long expireDuration);

  /**
   * Get the value from the Redis store and deserialized to the specified type. If not existed
   * return null
   *
   * @param <T>
   * @param key
   * @param valueType
   * @return The converted objects
   */
  <T> T getValue(final String key, Class<T> valueType);

  /**
   * Delete the key from Redis store
   *
   * @param key
   */
  void deleteByKey(final String key);

  /**
   * Set expire time
   *
   * @param key
   * @param expireDuration
   */
  void setExpire(final String key, int expireDuration);

  /**
   * Set expire time
   *
   * @param key
   * @param expireDuration
   */
  void setExpire(final String key, long expireDuration);

  /**
   * Get list of list T object by multi key
   *
   * @param <T> Value Class
   * @param keys map keys
   * @return adapter to using redis map
   */
  <T> List<List<T>> getMultiValueList(final Set<String> keys, Class<T> valueType);

  /**
   * Get list of maps by multi key
   *
   * @param <K> Key Class
   * @param <V> Value Class
   * @param keys map keys
   * @return adapter to using redis map
   */
  <K, V> List<Map<K, V>> getMultiValueMap(Set<String> keys, Class<K> keyType, Class<V> valueType);

  /**
   * Get data json by key
   *
   * @param key the type parameter
   * @return the String
   */
  String getStringValueByKey(String key);
  /**
   * Gets keys list in cache by pattern.
   *
   * @param pattern the pattern of the keys
   * @return the Set of keys
   */
  Set<String> getKeysByPattern(String pattern);

  /**
   * Gets list value in cache by key.
   *
   * @param <T> Value Class
   * @param key of cache
   * @return the list of value
   */
  <T> List<T> getListValue(String key, Class<T> typeOfList);

  /**
   * Gets map value in cache by key.
   *
   * @param <K> Key Class
   * @param <V> Value Class
   * @param key of cache
   * @return the map of value
   */
  <K, V> Map<K, V> getMapValue(String key, Class<K> keyType, Class<V> valueType);

  /**
   * Delete keys from Redis store
   *
   * @param keys
   */
  void deleteByKeys(final Set<String> keys);
}

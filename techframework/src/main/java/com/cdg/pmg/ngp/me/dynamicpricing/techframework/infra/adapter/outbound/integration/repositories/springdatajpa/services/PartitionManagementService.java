package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.services;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.config.PartitionConfiguration;
import java.util.List;
import javax.sql.DataSource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service to automatically manage database partitions for the surge_computation_model_api_logs
 * table.
 *
 * <p>This service is specifically designed to handle automatic partition management for the {@code
 * surge_computation_model_api_logs} table, which stores API call logs for surge computation models.
 * The table is partitioned by Singapore timezone months to optimize performance and manage
 * high-volume data.
 *
 * <h3>Key Features:</h3>
 *
 * <ul>
 *   <li><strong>Automatic Partition Creation:</strong> Creates monthly partitions based on
 *       Singapore timezone
 *   <li><strong>Startup Management:</strong> Ensures required partitions exist during application
 *       startup
 *   <li><strong>Scheduled Maintenance:</strong> Daily tasks to create future partitions and cleanup
 *       old ones
 *   <li><strong>Manual Operations:</strong> Provides methods for emergency partition management
 *   <li><strong>Monitoring Support:</strong> Offers partition information and health checking
 *       capabilities
 * </ul>
 *
 * <h3>Partition Strategy:</h3>
 *
 * <ul>
 *   <li><strong>Partition Key:</strong> singapore_month (DATE column generated from timestamp)
 *   <li><strong>Partition Naming:</strong> surge_computation_model_api_logs_YYYY_MM
 *   <li><strong>Time Zone:</strong> Asia/Singapore (UTC+8)
 *   <li><strong>Granularity:</strong> Monthly partitions for optimal balance of performance and
 *       manageability
 * </ul>
 *
 * <h3>Configuration:</h3>
 *
 * All behavior is controlled via {@link PartitionConfiguration} properties:
 *
 * <ul>
 *   <li>{@code app.partition.startup-months-ahead} - Partitions created on startup
 *   <li>{@code app.partition.scheduled-months-ahead} - Partitions maintained by scheduler
 *   <li>{@code app.partition.months-to-keep} - Data retention period
 *   <li>{@code app.partition.enable-cleanup} - Enable/disable automatic cleanup
 *   <li>{@code app.partition.enable-scheduled-maintenance} - Enable/disable scheduled tasks
 * </ul>
 *
 * <p><strong>Target Table:</strong> {@code surge_computation_model_api_logs}
 *
 * <p><strong>Database Functions Used:</strong> {@code ensure_monthly_partitions_exist()}, {@code
 * cleanup_old_log_partitions()}
 *
 * @see PartitionConfiguration
 * @see
 *     com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.SurgeComputationModelApiLogEntity
 * @since 1.0
 * <AUTHOR> Pricing Service Team
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PartitionManagementService {

  private final DataSource dataSource;
  private final PartitionConfiguration partitionConfig;

  /**
   * Ensures required partitions exist when the application starts. Creates partitions for current
   * month + 3 future months.
   */
  @EventListener(ApplicationReadyEvent.class)
  public void ensurePartitionsOnStartup() {
    log.info("Ensuring required partitions exist on application startup...");
    try {
      List<String> createdPartitions =
          ensureMonthlyPartitionsExist(partitionConfig.getStartupMonthsAhead());
      if (!createdPartitions.isEmpty()) {
        log.info(
            "Created {} new partitions on startup: {}",
            createdPartitions.size(),
            createdPartitions);
      } else {
        log.info("All required partitions already exist");
      }
    } catch (Exception e) {
      log.error("Failed to ensure partitions exist on startup", e);
      // Don't throw exception to prevent application startup failure
    }
  }

  /**
   * Scheduled task to create future partitions and cleanup old ones. Runs according to configured
   * cron expression and timezone.
   */
  @Scheduled(
      cron = "#{@partitionConfiguration.maintenanceCron}",
      zone = "#{@partitionConfiguration.maintenanceTimezone}")
  public void scheduledPartitionMaintenance() {
    if (!partitionConfig.isEnableScheduledMaintenance()) {
      log.debug("Scheduled partition maintenance is disabled");
      return;
    }

    log.info("Starting scheduled partition maintenance...");
    try {
      // Create partitions for configured months ahead
      List<String> createdPartitions =
          ensureMonthlyPartitionsExist(partitionConfig.getScheduledMonthsAhead());
      if (!createdPartitions.isEmpty()) {
        log.info("Created {} new partitions: {}", createdPartitions.size(), createdPartitions);
      }

      // Optional: Cleanup old partitions if enabled
      if (partitionConfig.isEnableCleanup()) {
        List<String> droppedPartitions = cleanupOldPartitions(partitionConfig.getMonthsToKeep());
        if (!droppedPartitions.isEmpty()) {
          log.info("Dropped {} old partitions: {}", droppedPartitions.size(), droppedPartitions);
        }
      }

      log.info("Scheduled partition maintenance completed successfully");
    } catch (Exception e) {
      log.error("Failed to complete scheduled partition maintenance", e);
    }
  }

  /**
   * Ensures that monthly partitions exist for the specified number of months ahead.
   *
   * @param monthsAhead Number of months ahead to create partitions for
   * @return List of newly created partition names
   */
  @Transactional
  public List<String> ensureMonthlyPartitionsExist(int monthsAhead) {
    JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);

    try {
      String sql = "SELECT ensure_monthly_partitions_exist(?)";

      // Use RowMapper to handle PostgreSQL array properly
      List<String> createdPartitions =
          jdbcTemplate.queryForObject(
              sql,
              (rs, rowNum) -> {
                java.sql.Array sqlArray = rs.getArray(1);
                if (sqlArray == null) {
                  return List.<String>of();
                }

                String[] arrayData = (String[]) sqlArray.getArray();
                return arrayData != null ? List.of(arrayData) : List.<String>of();
              },
              monthsAhead);

      if (createdPartitions != null && !createdPartitions.isEmpty()) {
        log.debug("Successfully created partitions: {}", createdPartitions);
      }

      return createdPartitions != null ? createdPartitions : List.of();
    } catch (Exception e) {
      log.error("Failed to ensure monthly partitions exist for {} months ahead", monthsAhead, e);
      throw new RuntimeException("Failed to create required partitions", e);
    }
  }

  /**
   * Cleans up old partitions to prevent accumulation of too many partition tables.
   *
   * @param monthsToKeep Number of months of data to keep
   * @return List of dropped partition names
   */
  @Transactional
  public List<String> cleanupOldPartitions(int monthsToKeep) {
    JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);

    try {
      String sql = "SELECT cleanup_old_log_partitions(?)";

      // Use RowMapper to handle PostgreSQL array properly
      List<String> droppedPartitions =
          jdbcTemplate.queryForObject(
              sql,
              (rs, rowNum) -> {
                java.sql.Array sqlArray = rs.getArray(1);
                if (sqlArray == null) {
                  return List.<String>of();
                }

                String[] arrayData = (String[]) sqlArray.getArray();
                return arrayData != null ? List.of(arrayData) : List.<String>of();
              },
              monthsToKeep);

      if (droppedPartitions != null && !droppedPartitions.isEmpty()) {
        log.info("Successfully dropped old partitions: {}", droppedPartitions);
      }

      return droppedPartitions != null ? droppedPartitions : List.of();
    } catch (Exception e) {
      log.error("Failed to cleanup old partitions (keeping {} months)", monthsToKeep, e);
      throw new RuntimeException("Failed to cleanup old partitions", e);
    }
  }

  /**
   * Manually triggers partition creation for emergency cases. This can be called from a REST
   * endpoint or management interface.
   *
   * @param monthsAhead Number of months ahead to create partitions for
   * @return Summary of operation results
   */
  public String createPartitionsManually(int monthsAhead) {
    try {
      List<String> createdPartitions = ensureMonthlyPartitionsExist(monthsAhead);

      if (createdPartitions.isEmpty()) {
        return String.format(
            "No new partitions needed. All partitions for next %d months already exist.",
            monthsAhead);
      } else {
        return String.format(
            "Successfully created %d new partitions: %s",
            createdPartitions.size(), String.join(", ", createdPartitions));
      }
    } catch (Exception e) {
      log.error("Manual partition creation failed", e);
      return "Failed to create partitions: " + e.getMessage();
    }
  }

  /**
   * Gets information about existing partitions for monitoring purposes.
   *
   * @return List of partition information
   */
  public List<PartitionInfo> getPartitionInfo() {
    JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);

    String sql =
        """
        SELECT
            tablename,
            pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size_pretty,
            pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
        FROM pg_tables
        WHERE tablename LIKE 'surge_computation_model_api_logs_%'
        AND tablename ~ '^surge_computation_model_api_logs_\\d{4}_\\d{2}$'
        ORDER BY tablename
        """;

    return jdbcTemplate.query(
        sql,
        (rs, rowNum) ->
            PartitionInfo.builder()
                .tableName(rs.getString("tablename"))
                .sizePretty(rs.getString("size_pretty"))
                .sizeBytes(rs.getLong("size_bytes"))
                .build());
  }

  /** Data class to hold partition information */
  @lombok.Builder
  @lombok.Data
  public static class PartitionInfo {
    private String tableName;
    private String sizePretty;
    private long sizeBytes;
  }
}

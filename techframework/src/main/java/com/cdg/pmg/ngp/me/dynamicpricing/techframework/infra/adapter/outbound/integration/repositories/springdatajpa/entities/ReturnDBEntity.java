package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.AbstractAggregateRoot;

@Entity
@Table(name = "Returns")
@NoArgsConstructor
@AllArgsConstructor
public class ReturnDBEntity extends AbstractAggregateRoot<ReturnDBEntity> {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }
}

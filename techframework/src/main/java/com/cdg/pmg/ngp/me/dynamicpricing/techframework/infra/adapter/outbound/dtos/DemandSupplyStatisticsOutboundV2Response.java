package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DemandSupplyStatisticsOutboundV2Response implements Serializable {
  @Serial private static final long serialVersionUID = 1L;

  private List<DemandSupplyStatisticsOutboundV2> data;
}

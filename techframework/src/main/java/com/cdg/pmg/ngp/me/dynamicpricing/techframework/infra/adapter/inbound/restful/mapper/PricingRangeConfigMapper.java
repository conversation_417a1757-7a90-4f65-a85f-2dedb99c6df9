package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper;

import com.cdg.pmg.ngp.me.dynamicpricing.commands.PricingRangeConfigCommand;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.PricingRangeConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.PricingRangeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.PricingRangeConfigRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper
public interface PricingRangeConfigMapper {
  /**
   * Map PricingRangeConfigEntity to PricingRangeConfig
   *
   * @param source Pricing range config entity
   * @return PricingRangeConfig Pricing range config
   */
  PricingRangeConfig mapPricingRangeConfigEntityToPricingRangeConfig(
      PricingRangeConfigEntity source);

  /**
   * Map PricingRangeConfigRequest to PricingRangeConfigCommand
   *
   * @param source Pricing range config request
   * @return PricingRangeConfigCommand Pricing range config command
   */
  @Mapping(target = "createdBy", source = "source.user")
  @Mapping(target = "updatedBy", source = "source.user")
  PricingRangeConfigCommand mapPricingRangeConfigRequestToPricingRangeConfigCommand(
      PricingRangeConfigRequest source);
}

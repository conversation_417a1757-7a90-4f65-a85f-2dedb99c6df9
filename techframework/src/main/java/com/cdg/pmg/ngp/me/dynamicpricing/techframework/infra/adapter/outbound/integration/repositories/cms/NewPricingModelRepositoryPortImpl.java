package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.cms;

import static com.cdg.pmg.ngp.me.dynamicpricing.constants.CommonConstant.NEW_PRICING_MODEL_CMS_KEY;
import static com.cdg.pmg.ngp.me.dynamicpricing.constants.CommonConstant.NEW_PRICING_MODEL_SUFFIX;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.mapper.NewPricingModelMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.CMSConfigItem;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.CreateCMSConfigRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.CMSServiceOutboundPort;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.NewPricingModelConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.outbound.NewPricingModelRepositoryPort;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs.SpringSecurityAuditorAware;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.time.OffsetDateTime;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class NewPricingModelRepositoryPortImpl implements NewPricingModelRepositoryPort {

  private final SpringSecurityAuditorAware springSecurityAuditorAware;
  private final NewPricingModelMapper newPricingModelMapper;
  private final ObjectMapper objectMapper;
  private final CMSServiceOutboundPort cmsServiceOutboundPort;

  @Value("${spring.cloud.config.name}")
  private String configService;

  @Override
  public NewPricingModelConfigEntity update(
      NewPricingModelConfigEntity newPricingModelConfigEntity) {
    String updatedBy = springSecurityAuditorAware.getCurrentAuditor().orElse(configService);
    newPricingModelConfigEntity.setUpdatedBy(updatedBy);
    newPricingModelConfigEntity.setUpdatedDt(OffsetDateTime.now());
    cmsServiceOutboundPort.updateCMSConfig(
        newPricingModelConfigEntity.getId(),
        newPricingModelMapper.toJson(objectMapper, newPricingModelConfigEntity));
    return newPricingModelConfigEntity;
  }

  @Override
  public NewPricingModelConfigEntity create(
      NewPricingModelConfigEntity newPricingModelConfigEntity, int index) {
    String createdBy = springSecurityAuditorAware.getCurrentAuditor().orElse(configService);
    newPricingModelConfigEntity.setCreatedBy(createdBy);
    newPricingModelConfigEntity.setCreatedDt(OffsetDateTime.now());
    newPricingModelConfigEntity.setIndex(index);
    CreateCMSConfigRequest createCmsConfigRequest =
        CreateCMSConfigRequest.builder()
            .service(configService)
            .key(NEW_PRICING_MODEL_CMS_KEY + "[" + index + "]")
            .value(newPricingModelMapper.toJson(objectMapper, newPricingModelConfigEntity))
            .createdBy(createdBy)
            .description(NewPricingModelRepositoryPort.generateDescriptionKey(index))
            .build();
    CMSConfigItem response = cmsServiceOutboundPort.createCmsConfig(createCmsConfigRequest);
    newPricingModelConfigEntity.setId(response.getId());
    // Update the id in value json since when create new the id is null
    update(newPricingModelConfigEntity);
    return newPricingModelConfigEntity;
  }

  @Override
  public List<NewPricingModelConfigEntity> getListNewPricingModelConfigEntityInCms() {
    var newPricingConfigResponse =
        cmsServiceOutboundPort.getCMSBySearchText(NEW_PRICING_MODEL_SUFFIX);
    return newPricingModelMapper.mapToNewPricingModelConfigEntities(
        objectMapper, newPricingConfigResponse);
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import java.time.OffsetDateTime;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class BookingFeeOutboundRequest implements Serializable {

  @Serial private static final long serialVersionUID = -7707491769528834690L;

  private String jobType;
  private Boolean isHoliday;
  private String vehicleTypeId;
  private String productId;
  private OffsetDateTime requestDate;
  private String flatFareType;
}

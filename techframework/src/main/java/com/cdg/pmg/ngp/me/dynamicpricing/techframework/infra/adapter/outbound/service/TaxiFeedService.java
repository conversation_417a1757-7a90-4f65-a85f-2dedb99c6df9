package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.service;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.FareUploadConfiguration;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.TaxiFeed;
import java.util.List;
import java.util.Map;

@FunctionalInterface
public interface TaxiFeedService {

  /**
   * @param cmsConfigData
   * @param fareUploadConfiguration
   * @param vehicleGroupIdAndProductIdListMap
   * @return TaxiFeed proto
   * @since Builds a taxi feed based on the given configuration parameters.
   */
  TaxiFeed buildTaxiFeed(
      final Map<String, String> cmsConfigData,
      final FareUploadConfiguration fareUploadConfiguration,
      final Map<String, List<String>> vehicleGroupIdAndProductIdListMap);
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.mapper.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.DynamicPricingSurgeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs.DynamicSurgeProperties;
import org.mapstruct.Mapper;

@Mapper
public interface DynamicSurgeConfigMapper {

  DynamicPricingSurgeConfig map(DynamicSurgeProperties dynamicSurgeProperties);
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.adapter;

import static com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant.CACHE_KEY_GET_FARE_COUNT_PREFIX;
import static com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant.COLON;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.farecounter.RequestCounterService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.H3Region;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.AddressService;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.FareCountAggregateResult;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.GetFareCountEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.SurgeAreaTypeEnum;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.LongAdder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.StringRedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * Dynamic Pricing Request Count Service Use Redis Sorted Set to store request records and support
 * time window statistics
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RequestCounterServiceAdapter implements RequestCounterService {

  private static final String LEFT_BRACKET = "(";
  private static final Duration DATA_STORAGE_IN_MINUTE = Duration.ofMinutes(30);
  private static final long BATCH_SYNC_INTERVAL = 30000; // 30 seconds batch sync to Redis

  // Local cache: redis key -> request timestamp queue
  private final Map<String, ConcurrentLinkedQueue<GetFareCountEntity>> localRequestCache =
      new ConcurrentHashMap<>();

  private final StringRedisTemplate redisTemplate;
  private final ObjectMapper objectMapper;
  private final AddressService addressService;

  @PostConstruct
  public void init() {
    log.info("RequestCounterServiceAdapter initialized");
  }

  @PreDestroy
  public void destroy() {
    syncAllLocalDataToRedis();
    log.info("RequestCounterServiceAdapter destroyed, all data synced to Redis");
  }

  /** Get request count for specified time window */
  @Override
  public List<FareCountAggregateResult> getRequestCount(
      String endpoint, Instant startTime, Instant endTime) {
    if (startTime == null || endTime == null) {
      log.error(
          "[getRequestCount] startTime or endTime is null, startTime: {}, endTime: {}",
          startTime,
          endTime);
      return Collections.emptyList();
    }
    List<String> allRegionKeys = buildAllRegionKeys(endpoint);
    return aggregateFromZset(allRegionKeys, startTime, endTime);
  }

  /** Get request count for all regions under specified endpoint */
  @Override
  public Map<Long, Long> getRequestCountByRegions(
      String endpoint, Instant startTime, Instant endTime) {
    log.info(
        "[getRequestCountByRegions] Start get request counts for endpoint={}, startTime={}, endTime={}",
        endpoint,
        startTime,
        endTime);

    if (endpoint == null || startTime == null || endTime == null) {
      log.error(
          "[getRequestCountByRegions] endpoint or startTime or endTime is null, endpoint: {}, startTime: {}, endTime: {}",
          endpoint,
          startTime,
          endTime);
      return null;
    }

    try {
      List<String> allRegionKeys = buildAllRegionKeys(endpoint);

      if (allRegionKeys.isEmpty()) {
        log.warn("[getRequestCountByRegions] No matching keys found for endpoint: {}", endpoint);
        return null;
      }

      Map<Long, Long> result = new HashMap<>();
      long startTimestamp = startTime.toEpochMilli();
      long endTimestamp = endTime.toEpochMilli();
      String start = String.valueOf(startTimestamp);
      String end = LEFT_BRACKET + endTimestamp;

      // Use pipeline for batch operations to reduce network roundtrips
      List<Object> pipelineResults =
          redisTemplate.executePipelined(
              (RedisCallback<Object>)
                  connection -> {
                    StringRedisConnection stringConn = (StringRedisConnection) connection;

                    for (String regionKey : allRegionKeys) {
                      // Here use closed-open interval -> [start,end)
                      stringConn.execute("ZCOUNT", regionKey, start, end);
                    }
                    return null;
                  });

      // Process pipeline results
      for (int i = 0; i < allRegionKeys.size(); i++) {
        String key = allRegionKeys.get(i);
        Long regionId = extractRegionIdFromRedisKey(key, endpoint);

        if (regionId == null) {
          log.warn("[getRequestCountByRegions] Failed to extract regionId from key: {}", key);
          continue;
        }

        Long count = (Long) pipelineResults.get(i);
        result.put(regionId, count != null ? count : 0L);

        if (log.isDebugEnabled()) {
          log.debug(
              "[getRequestCountByRegions] Request count for endpoint={}, regionId={}: {}",
              endpoint,
              regionId,
              count);
        }
      }

      log.info(
          "[getRequestCountByRegions] Retrieved request counts for endpoint={}, regions={}, startTime={}, endTime={}, start={}, end={}",
          endpoint,
          result.size(),
          startTime,
          endTime,
          start,
          end);

      return result;
    } catch (Exception e) {
      log.error(
          "[getRequestCountByRegions] Failed to get request counts by regions for endpoint={}, startTime={}, endTime={}",
          endpoint,
          startTime,
          endTime,
          e);
      return null;
    }
  }

  /** Record single request to local cache */
  @Override
  public void recordEndpointRequest(String endpoint, GetFareCountEntity getFareCountEntity) {
    if (endpoint == null || getFareCountEntity == null) {
      return;
    }

    try {
      String redisKey = buildRedisKey(endpoint, getFareCountEntity.getRegionId());
      localRequestCache
          .computeIfAbsent(redisKey, k -> new ConcurrentLinkedQueue<>())
          .offer(getFareCountEntity);

      if (log.isDebugEnabled()) {
        log.debug(
            "Recorded request to local cache: endpoint={}, regionId={}, requestTime={}",
            endpoint,
            getFareCountEntity.getRegionId(),
            getFareCountEntity.getRequestTime());
      }
    } catch (Exception e) {
      log.error(
          "Failed to record request to local cache: endpoint={}, regionId={}, requestTime={}",
          endpoint,
          getFareCountEntity.getRegionId(),
          getFareCountEntity.getRequestTime(),
          e);
    }
  }

  /** Scheduled task to sync local data to Redis */
  @Scheduled(fixedRate = BATCH_SYNC_INTERVAL)
  public void syncLocalDataToRedis() {
    try {
      syncAllLocalDataToRedis();
    } catch (Exception e) {
      log.error("Failed to sync local data to Redis", e);
    }
  }

  private void syncAllLocalDataToRedis() {
    if (localRequestCache.isEmpty()) {
      return;
    }

    // Collect all data for batch operation
    Map<String, Set<ZSetOperations.TypedTuple<String>>> batchData = new HashMap<>();
    int totalRequestsToSync = 0;

    for (Map.Entry<String, ConcurrentLinkedQueue<GetFareCountEntity>> entry :
        localRequestCache.entrySet()) {
      String redisKey = entry.getKey();
      ConcurrentLinkedQueue<GetFareCountEntity> queue = entry.getValue();

      if (queue.isEmpty()) {
        continue;
      }

      try {
        Set<ZSetOperations.TypedTuple<String>> tuples = new HashSet<>();
        int count = 0;

        GetFareCountEntity entity;
        while ((entity = queue.poll()) != null) {
          String value = objectMapper.writeValueAsString(entity);
          long requestTime = entity.getRequestTime().toEpochMilli();
          tuples.add(ZSetOperations.TypedTuple.of(value, (double) requestTime));
          count++;
        }

        if (count == 0) {
          continue;
        }

        batchData.put(redisKey, tuples);
        totalRequestsToSync += count;

        if (log.isDebugEnabled()) {
          log.debug("Prepared {} requests for sync to Redis key: {}", count, redisKey);
        }
      } catch (Exception e) {
        log.error("Failed to prepare data for sync for Redis key: {}", redisKey, e);
      }
    }

    if (batchData.isEmpty()) {
      return;
    }

    try {
      batchSyncToRedis(batchData);
      log.info(
          "Successfully synced {} requests across {} keys to Redis",
          totalRequestsToSync,
          batchData.size());
    } catch (Exception e) {
      log.error("Failed to batch sync data to Redis", e);
    }
  }

  private void batchSyncToRedis(Map<String, Set<ZSetOperations.TypedTuple<String>>> batchData) {
    try {
      // Use Redis pipelining for batch to cache new data
      addNewData(batchData);

      // Use Redis pipelining for batch to remove the expired data
      cleanUpOlderData(batchData);
    } catch (Exception e) {
      log.error("Failed to execute batch sync to Redis", e);
      throw e;
    }
  }

  private void addNewData(final Map<String, Set<ZSetOperations.TypedTuple<String>>> batchData) {
    redisTemplate.executePipelined(
        (RedisCallback<Object>)
            connection -> {
              for (Map.Entry<String, Set<ZSetOperations.TypedTuple<String>>> entry :
                  batchData.entrySet()) {
                String redisKey = entry.getKey();
                Set<ZSetOperations.TypedTuple<String>> tuples = entry.getValue();

                byte[] keyBytes = redisKey.getBytes();
                for (ZSetOperations.TypedTuple<String> tuple : tuples) {
                  if (tuple == null || tuple.getValue() == null || tuple.getScore() == null) {
                    log.warn("Invalid tuple data, skipping: tuple={}", tuple);
                    continue;
                  }

                  byte[] valueBytes = tuple.getValue().getBytes();
                  double score = tuple.getScore();
                  connection.zAdd(keyBytes, score, valueBytes);
                }
              }
              return null;
            });
  }

  private void cleanUpOlderData(
      final Map<String, Set<ZSetOperations.TypedTuple<String>>> batchData) {
    // Will clean up data older than 30 minutes
    long cleanupThreshold = Instant.now().minus(DATA_STORAGE_IN_MINUTE).toEpochMilli();
    redisTemplate.executePipelined(
        (RedisCallback<Object>)
            connection -> {
              StringRedisConnection stringConn = (StringRedisConnection) connection;
              for (String keyToCleanup : batchData.keySet()) {
                stringConn.zRemRangeByScore(keyToCleanup, -1, cleanupThreshold);

                if (log.isDebugEnabled()) {
                  log.debug(
                      "Cleaned expired data for key: {}, threshold: {}",
                      keyToCleanup,
                      cleanupThreshold);
                }
              }
              return null;
            });
  }

  private List<FareCountAggregateResult> aggregateFromZset(
      List<String> regionKeys, Instant startTime, Instant endTime) {

    if (regionKeys.isEmpty()) {
      return Collections.emptyList();
    }

    long startTimestamp = startTime.toEpochMilli();
    long endTimestamp = endTime.toEpochMilli();

    Map<String, LongAdder> aggregationMap = new ConcurrentHashMap<>();

    int batchSize = 40;
    List<List<String>> batches = partitionList(regionKeys, batchSize);

    log.info("Processing {} regions in {} batches", regionKeys.size(), batches.size());

    batches.parallelStream()
        .forEach(batch -> processBatch(batch, startTimestamp, endTimestamp, aggregationMap));

    List<FareCountAggregateResult> results = convertToResults(aggregationMap);
    log.info("Aggregation completed, total results: {}", results.size());

    return results;
  }

  // Use Pipeline to batch fetch data, then process sequentially
  private void processBatch(
      List<String> regionKeys,
      long startTimestamp,
      long endTimestamp,
      Map<String, LongAdder> aggregationMap) {
    try {
      String start = String.valueOf(startTimestamp);
      String end = LEFT_BRACKET + endTimestamp;

      List<Object> pipelineResults =
          redisTemplate.executePipelined(
              (RedisCallback<Object>)
                  connection -> {
                    StringRedisConnection stringConn = (StringRedisConnection) connection;

                    for (String regionKey : regionKeys) {
                      // Here use closed-open interval -> [start,end)
                      stringConn.execute("ZRANGEBYSCORE", regionKey, start, end);
                    }
                    return null;
                  });

      for (int i = 0; i < regionKeys.size(); i++) {
        String regionKey = regionKeys.get(i);

        @SuppressWarnings("unchecked")
        ArrayList<String> dataSet = (ArrayList<String>) pipelineResults.get(i);

        if (dataSet != null && !dataSet.isEmpty()) {
          processRegionData(regionKey, startTimestamp, endTimestamp, dataSet, aggregationMap);
        }
      }

    } catch (Exception e) {
      log.error("Failed to process batch: {}", regionKeys, e);
    }
  }

  private void processRegionData(
      String regionKey,
      long startTimestamp,
      long endTimestamp,
      ArrayList<String> dataSet,
      Map<String, LongAdder> aggregationMap) {

    for (String value : dataSet) {
      try {
        GetFareCountEntity entity = deserializeEntity(value);
        if (entity != null) {
          String aggregationKey = buildAggregationKey(entity, startTimestamp, endTimestamp);

          aggregationMap.computeIfAbsent(aggregationKey, k -> new LongAdder()).increment();
        }
      } catch (Exception e) {
        log.debug("Failed to process entity in region {}: {}", regionKey, value);
      }
    }
  }

  private GetFareCountEntity deserializeEntity(String value) {
    try {
      return objectMapper.readValue(value, GetFareCountEntity.class);
    } catch (Exception e) {
      log.debug("Failed to deserialize entity: {}", value);
      return null;
    }
  }

  // Build aggregation key: minuteTimestamp:regionId:regionVersion:areaType:modelId
  private String buildAggregationKey(
      GetFareCountEntity entity, long startTimestamp, long endTimestamp) {

    return String.join(
        COLON,
        String.valueOf(startTimestamp),
        String.valueOf(endTimestamp),
        String.valueOf(entity.getRegionId()),
        entity.getRegionVersion(),
        entity.getAreaType().getValue(),
        String.valueOf(entity.getModelId()));
  }

  private List<FareCountAggregateResult> convertToResults(Map<String, LongAdder> aggregationMap) {
    List<FareCountAggregateResult> results = new ArrayList<>();

    for (Map.Entry<String, LongAdder> entry : aggregationMap.entrySet()) {
      try {
        String[] parts = entry.getKey().split(COLON);
        if (parts.length == 6) {
          long startTimestamp = Long.parseLong(parts[0]);
          long endTimestamp = Long.parseLong(parts[1]);

          FareCountAggregateResult result =
              FareCountAggregateResult.builder()
                  .startTimestamp(startTimestamp)
                  .endTimestamp(endTimestamp)
                  .h3RegionId(parseLong(parts[2]))
                  .regionVersion(parts[3])
                  .areaType(SurgeAreaTypeEnum.valueOf(parts[4].toUpperCase()))
                  .modelId(parseLong(parts[5]))
                  .getFareCount(entry.getValue().intValue())
                  .build();

          results.add(result);
        }
      } catch (Exception e) {
        log.warn("Failed to parse aggregation key: {}", entry.getKey());
      }
    }

    return results;
  }

  private static Long parseLong(final String longStr) {
    if (longStr == null || longStr.equals("null")) {
      return null;
    }
    return Long.valueOf(longStr);
  }

  private <T> List<List<T>> partitionList(List<T> list, int batchSize) {
    List<List<T>> batches = new ArrayList<>();
    for (int i = 0; i < list.size(); i += batchSize) {
      int end = Math.min(i + batchSize, list.size());
      batches.add(list.subList(i, end));
    }
    return batches;
  }

  private List<String> buildAllRegionKeys(final String endpoint) {
    List<H3Region> effectiveH3Regions = addressService.getEffectiveH3RegionsFromCache();
    return effectiveH3Regions.stream().map(v -> buildRedisKey(endpoint, v.getRegionId())).toList();
  }

  private String buildRedisKey(String endpoint, Long regionId) {
    return CACHE_KEY_GET_FARE_COUNT_PREFIX + endpoint + COLON + regionId;
  }

  private Long extractRegionIdFromRedisKey(String redisKey, String endpoint) {
    try {
      String expectedPrefix = CACHE_KEY_GET_FARE_COUNT_PREFIX + endpoint + COLON;
      if (!redisKey.startsWith(expectedPrefix)) {
        return null;
      }

      String regionIdStr = redisKey.substring(expectedPrefix.length());
      return Long.parseLong(regionIdStr);

    } catch (NumberFormatException e) {
      log.warn("[getRequestCountByRegions] Invalid regionId format in Redis key: {}", redisKey);
      return null;
    }
  }
}

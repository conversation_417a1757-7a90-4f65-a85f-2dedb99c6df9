package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.scheduler;

import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.ERROR_TO_UPLOAD_FILE;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.BookARideConfigService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.BookARideService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.FareCalculationService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.S2CellService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.ConfigurationServiceOutboundPort;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.BookARideConfigsConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.DynamicSurgesEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FareUploadConfiguration;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.LocationSurchargeConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.S2CellEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.InternalServerException;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.TaxiFeed;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs.sftp.UploadGateway;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.service.TaxiFeedService;
import com.cdg.pmg.ngp.me.dynamicpricing.utils.FareUploadUtils;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * FareUploadScheduler is a component responsible for scheduling and executing the upload of fare
 * data in the book-a-ride system. The uploadFile method orchestrates the entire process, starting
 * from loading configurations to processing the data for upload.
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class FareUploadScheduler implements BookARideService {
  private final BookARideConfigService bookARideConfigService;
  private final S2CellService s2Service;
  private final FareCalculationService fareCalculationService;
  private final TaxiFeedService taxiFeedService;
  private final UploadGateway uploadGateway;
  private final ConfigurationServiceOutboundPort configurationServiceOutboundPort;

  private void loadS2CellListCache(final List<S2CellEntity> s2CellList) {
    s2CellList.addAll(s2Service.getS2CellList().getS2CellList());
  }

  public void loadHolidayConfiguration(final List<String> holidayConfig) {
    final List<String> holidayConfigs = bookARideConfigService.getHolidayConfigs();
    holidayConfig.addAll(holidayConfigs);
  }

  private void loadSurgeConfiguration(final Map<String, Integer> surgeConfig) {
    final List<DynamicSurgesEntity> dynpSurges = bookARideConfigService.getDynpSurges();
    dynpSurges.forEach(surge -> surgeConfig.put(surge.getZoneId(), surge.getSurge()));
  }

  private Map<String, List<String>> buildProductList(Map<String, String> bookRideConfig) {
    final String vehicleGroupIds =
        bookRideConfig.get(BookARideConfigsConstant.BOOK_RIDE_VEHICLE_GROUP);
    final List<String> productIds =
        List.of(
            bookRideConfig
                .get(BookARideConfigsConstant.BOOK_RIDE_PRODUCT)
                .split(BookARideConfigsConstant.SEPARATE_CHARACTER));
    final Map<String, List<String>> vehicleGroupProductIds = new HashMap<>();
    for (String vehicleProduct :
        vehicleGroupIds.split(BookARideConfigsConstant.SEPARATE_CHARACTER)) {
      vehicleGroupProductIds.put(vehicleProduct, productIds);
    }
    return vehicleGroupProductIds;
  }

  private void loadLocationSurChargeConfiguration(
      final Map<Integer, List<LocationSurchargeConfigEntity>> locationSurChargeConfig,
      final List<String> holidayList,
      final Date currentDate) {
    final List<LocationSurchargeConfigEntity> locationSurchargeConfigs =
        bookARideConfigService.getLocationSurchargeConfigs(currentDate, holidayList);

    locationSurChargeConfig.putAll(
        locationSurchargeConfigs.stream()
            .collect(Collectors.groupingBy(LocationSurchargeConfigEntity::getLocationId)));
  }

  public FareUploadConfiguration loadFareUploadConfigurations(Date currentDate) {
    final FareUploadConfiguration fareUploadConfiguration =
        FareUploadConfiguration.builder().build();
    fareUploadConfiguration.setBookARideConfiguration(
        configurationServiceOutboundPort.loadBookARideConfigurations());
    loadHolidayConfiguration(fareUploadConfiguration.getHolidayConfig());
    loadSurgeConfiguration(fareUploadConfiguration.getSurgeConfig());
    loadLocationSurChargeConfiguration(
        fareUploadConfiguration.getLocationSurChargeConfig(),
        fareUploadConfiguration.getHolidayConfig(),
        currentDate);
    loadFlatFareConfiguration(fareUploadConfiguration.getFlatFareConfig());
    loadS2CellListCache(fareUploadConfiguration.getS2CellList());
    fareUploadConfiguration.setDynamicPricingTimeConfig(
        FareUploadUtils.getDynamicPricingTimeConfig(fareUploadConfiguration.getFlatFareConfig()));
    fareUploadConfiguration.setPeakHourRateConfig(
        FareUploadUtils.getPeakHourOrMidnightRates(
            fareUploadConfiguration.getFlatFareConfig(),
            BookARideConfigsConstant.PEAK_HOUR_PREFIX));
    fareUploadConfiguration.setMidNightRateConfig(
        FareUploadUtils.getPeakHourOrMidnightRates(
            fareUploadConfiguration.getFlatFareConfig(),
            BookARideConfigsConstant.MID_NIGHT_PREFIX));
    fareUploadConfiguration.setEndPointSurchargeAreas(
        FareUploadUtils.mapAreaIdsToIndex(fareUploadConfiguration.getS2CellList()));
    return fareUploadConfiguration;
  }

  private void loadFlatFareConfiguration(final Map<String, String> flatFareConfiguration) {
    final Map<String, String> flatFareResponse = bookARideConfigService.getFlatFareConfigs();
    flatFareConfiguration.putAll(flatFareResponse);
  }

  private void loadBookingFareConfigurations(
      final FareUploadConfiguration fareUploadConfiguration, final Date reqDate) {
    bookARideConfigService.getBookingFareConfigs(fareUploadConfiguration, reqDate);
  }

  @Override
  public void uploadFile() {
    Date currentDate = new Date();
    log.info("Fare Upload Schedule Job starting ...");
    final FareUploadConfiguration fareUploadConfiguration =
        loadFareUploadConfigurations(currentDate);
    final Map<String, String> bookARideConfiguration =
        configurationServiceOutboundPort.loadBookARideConfigurations();
    final Map<String, List<String>> productList = buildProductList(bookARideConfiguration);
    fareCalculationService.calculateFare(currentDate, fareUploadConfiguration, productList);
    loadBookingFareConfigurations(fareUploadConfiguration, currentDate);
    TaxiFeed taxiFeed =
        taxiFeedService.buildTaxiFeed(bookARideConfiguration, fareUploadConfiguration, productList);
    if (taxiFeed == null) {
      log.error("Proto was not available");
      throw new NullPointerException();
    }
    String fileName = bookARideConfiguration.get(BookARideConfigsConstant.FILE_NAME);
    Path path = Paths.get(fileName);
    try {
      File file = Files.write(path, taxiFeed.toByteArray()).toFile();
      uploadGateway.upload(file);
      log.info("Uploading to FTP Completed");
    } catch (Exception e) {
      log.error("Error in uploading Book-A-Ride file to FTP", e);
      throw new InternalServerException(
          ERROR_TO_UPLOAD_FILE.getMessage(), ERROR_TO_UPLOAD_FILE.getErrorCode());
    } finally {
      try {
        Files.deleteIfExists(path);
      } catch (IOException e) {
        log.error("Delete file exception: {}", e.getMessage());
      }
    }
  }
}

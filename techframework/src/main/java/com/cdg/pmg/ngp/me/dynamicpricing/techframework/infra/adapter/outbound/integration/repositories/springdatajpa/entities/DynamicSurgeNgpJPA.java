package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "dynp_surges_ngp")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DynamicSurgeNgpJPA {

  @Id
  @Column(name = "zone_id")
  private String zoneId;

  @Column(name = "surge")
  private Integer surge;

  @Column(name = "surge_low")
  private Integer surgeLow;

  @Column(name = "surge_high")
  private Integer surgeHigh;

  @Column(name = "demand_recent")
  private Integer demandRecent;

  @Column(name = "demand_previous")
  private Integer demandPrevious;

  @Column(name = "demand_predicted")
  private Integer demandPredicted;

  @Column(name = "supply")
  private Integer supply;

  @Column(name = "prev_surge")
  private Integer prevSurge;

  @Column(name = "last_upd_dt")
  private Timestamp lastUpdDt;

  @Column(name = "excess_demand")
  private Integer excessDemand;

  @Column(name = "batch_key")
  private Integer batchKey;

  @Column(name = "demand_predicted_15")
  private Integer demandPredicted15;

  @Column(name = "zone_price_model")
  private String zonePriceModel;

  @Column(name = "unmet_15")
  private Double unmet15;

  @Column(name = "unmet_15_pre")
  private Double previousUnmet15;

  @Column(name = "demand_15")
  private Integer demand15;

  @Column(name = "excess_demand_15")
  private Integer excessDemand15;
}

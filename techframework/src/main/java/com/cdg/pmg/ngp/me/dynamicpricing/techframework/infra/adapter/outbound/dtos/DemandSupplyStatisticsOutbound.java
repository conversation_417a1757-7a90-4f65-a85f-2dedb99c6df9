package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import lombok.*;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DemandSupplyStatisticsOutbound implements Serializable {
  @Serial private static final long serialVersionUID = 1L;

  private String zoneId;
  private Integer recentDemand;
  private Integer previousDemand;
  private Integer predictedDemand;
  private Integer supply;
  private Integer batchCounter;
  private Integer excessDemand;
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.controllers.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.MlGetFareRequestAggStatsService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.SurgeFactorCalculationService;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.ModelSurgeDataEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.PagedSurgeValuesResult;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.SurgeValuesFilterCriteria;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.api.SurgeComputationManagementApi;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.CurrentSurgeValuesResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.ModelSurgeData;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.PaginationInfo;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.RegionSurgeValue;
import io.opentelemetry.api.trace.Span;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller for surge computation management operations. This controller implements the
 * SurgeComputationManagementApi interface.
 */
@RestController
@RequiredArgsConstructor
@Slf4j
public class SurgeComputationManagementController implements SurgeComputationManagementApi {

  private final SurgeFactorCalculationService surgeFactorCalculationService;
  private final MlGetFareRequestAggStatsService mlGetFareRequestAggStatsService;

  /**
   * Endpoint to trigger the calculation of surge factors. This is an asynchronous operation that
   * initiates the calculation process.
   *
   * @return ResponseEntity with no content (204) if successful, or an error status
   */
  @Override
  public ResponseEntity<Void> calculateSurgeFactor() {
    Instant triggerTime = Instant.now().truncatedTo(ChronoUnit.MILLIS);
    log.info(
        "[calculateSurgeFactor] Received request to calculate surge factor, trigger time: {}",
        triggerTime);

    // 1. calculate get fare count detail async for table ml_get_fare_request_agg_stats
    calculateGetFareRequestAggStatsAsync(triggerTime);

    try {
      boolean initiated = surgeFactorCalculationService.calculateSurgeFactor(triggerTime);

      if (initiated) {
        log.info("[calculateSurgeFactor] Surge factor calculation initiated successfully");
        return ResponseEntity.noContent().build();
      } else {
        log.error("[calculateSurgeFactor] Failed to initiate surge factor calculation");
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
      }
    } catch (Exception e) {
      log.error(
          "[calculateSurgeFactor] Error processing surge factor calculation request: {}",
          e.getMessage(),
          e);
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
    }
  }

  private void calculateGetFareRequestAggStatsAsync(Instant triggerTime) {
    CompletableFuture.runAsync(
            () -> {
              log.info(
                  "[calculateSurgeFactor] Start calculate get fare request agg stats, triggerTime: {}",
                  triggerTime);
              mlGetFareRequestAggStatsService.aggregateGetFareCountEveryMinute(triggerTime);
              log.info("[calculateSurgeFactor] End calculate get fare request agg stats");
            })
        .orTimeout(20, TimeUnit.SECONDS)
        .exceptionally(
            ex -> {
              if (ex instanceof TimeoutException) {
                log.error(
                    "[calculateSurgeFactor] Calculating get fare request agg stats timed out after {} seconds",
                    20);
              } else {
                log.error(
                    "[calculateSurgeFactor] Failed to calculate get fare request agg stats: {}",
                    ex.getMessage(),
                    ex);
              }
              return null;
            });
  }

  /**
   * Endpoint to retrieve current H3 region surge values across all models from cache. This endpoint
   * is optimized for external monitoring services and frequent calls. Supports pagination and
   * filtering to handle large datasets efficiently.
   *
   * @param page Page number for pagination (0-based)
   * @param size Number of models per page
   * @param modelIds Comma-separated list of model IDs to filter by
   * @param modelNames Comma-separated list of model names to filter by
   * @return ResponseEntity with current surge values or an error status
   */
  @Override
  public ResponseEntity<CurrentSurgeValuesResponse> getCurrentSurgeValues(
      Integer page, Integer size, String modelIds, String modelNames) {
    log.debug(
        "Received request to get current surge values for monitoring - page: {}, size: {}, modelIds: {}, modelNames: {}",
        page,
        size,
        modelIds,
        modelNames);

    try {
      // Build filter criteria
      SurgeValuesFilterCriteria filterCriteria =
          buildFilterCriteria(page, size, modelIds, modelNames);

      // Get paginated surge values
      PagedSurgeValuesResult pagedResult =
          surgeFactorCalculationService.getCurrentSurgeValuesFromCache(filterCriteria);

      CurrentSurgeValuesResponse response = createCurrentSurgeValuesResponse(pagedResult);

      log.debug(
          "Successfully retrieved current surge values for {} models (page {}/{})",
          pagedResult.getData().size(),
          pagedResult.getPage() + 1,
          pagedResult.getTotalPages());
      return ResponseEntity.ok(response);

    } catch (Exception e) {
      log.error("Error retrieving current surge values: {}", e.getMessage(), e);
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
    }
  }

  /** Creates the API response from domain entities. */
  private CurrentSurgeValuesResponse createCurrentSurgeValuesResponse(
      List<ModelSurgeDataEntity> modelSurgeDataList) {

    List<ModelSurgeData> modelSurgeDataDtos =
        modelSurgeDataList.stream().map(this::mapToModelSurgeData).collect(Collectors.toList());

    CurrentSurgeValuesResponse response = new CurrentSurgeValuesResponse();
    response.setData(modelSurgeDataDtos);
    response.setTimestamp(OffsetDateTime.now());
    response.setTraceId(Span.current().getSpanContext().getTraceId());

    return response;
  }

  /** Builds filter criteria from request parameters. */
  private SurgeValuesFilterCriteria buildFilterCriteria(
      Integer page, Integer size, String modelIds, String modelNames) {
    SurgeValuesFilterCriteria.SurgeValuesFilterCriteriaBuilder builder =
        SurgeValuesFilterCriteria.builder()
            .page(page != null ? page : 0)
            .size(size != null ? size : 20);

    // Parse model IDs if provided
    if (modelIds != null && !modelIds.trim().isEmpty()) {
      List<Long> modelIdList =
          Arrays.stream(modelIds.split(","))
              .map(String::trim)
              .filter(s -> !s.isEmpty())
              .map(Long::parseLong)
              .collect(Collectors.toList());
      builder.modelIds(modelIdList);
    }

    // Parse model names if provided
    if (modelNames != null && !modelNames.trim().isEmpty()) {
      List<String> modelNameList =
          Arrays.stream(modelNames.split(","))
              .map(String::trim)
              .filter(s -> !s.isEmpty())
              .collect(Collectors.toList());
      builder.modelNames(modelNameList);
    }

    return builder.build();
  }

  /** Creates the API response from paginated domain entities. */
  private CurrentSurgeValuesResponse createCurrentSurgeValuesResponse(
      PagedSurgeValuesResult pagedResult) {
    List<ModelSurgeData> modelSurgeDataDtos =
        pagedResult.getData().stream().map(this::mapToModelSurgeData).collect(Collectors.toList());

    PaginationInfo paginationInfo = new PaginationInfo();
    paginationInfo.setPage(pagedResult.getPage());
    paginationInfo.setSize(pagedResult.getSize());
    paginationInfo.setTotalElements((int) pagedResult.getTotalElements());
    paginationInfo.setTotalPages(pagedResult.getTotalPages());
    paginationInfo.setHasNext(pagedResult.isHasNext());
    paginationInfo.setHasPrevious(pagedResult.isHasPrevious());

    CurrentSurgeValuesResponse response = new CurrentSurgeValuesResponse();
    response.setData(modelSurgeDataDtos);
    response.setPagination(paginationInfo);
    response.setTimestamp(OffsetDateTime.now());
    response.setTraceId(Span.current().getSpanContext().getTraceId());

    return response;
  }

  /** Maps domain entity to API DTO. */
  private ModelSurgeData mapToModelSurgeData(ModelSurgeDataEntity entity) {
    ModelSurgeData dto = new ModelSurgeData();
    dto.setModelId(entity.getModelId());
    dto.setModelName(entity.getModelName());

    List<RegionSurgeValue> regionSurgeValues =
        entity.getRegions().stream().map(this::mapToRegionSurgeValue).collect(Collectors.toList());
    dto.setRegions(regionSurgeValues);

    return dto;
  }

  /** Maps H3RegionSurgeEntity to RegionSurgeValue DTO. */
  private RegionSurgeValue mapToRegionSurgeValue(
      com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.H3RegionSurgeEntity entity) {
    RegionSurgeValue dto = new RegionSurgeValue();
    dto.setRegionId(entity.getRegionId());
    dto.setSurge(entity.getSurge());
    return dto;
  }
}

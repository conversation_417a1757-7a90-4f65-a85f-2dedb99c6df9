package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serial;
import java.io.Serializable;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "s2cell")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class S2CellJPA implements Serializable {

  @Serial private static final long serialVersionUID = 1493628712332623034L;

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  @Setter(AccessLevel.NONE)
  private Long s2CellSeqId;

  @Column(name = "s2_cell_id")
  private String s2CellId;

  @Column(name = "s2_cell_token_Id")
  private String s2CellTokenId;

  @Column(name = "s2_cell_lat")
  private Double s2CellLatitude;

  @Column(name = "s2_cell_long")
  private Double s2CellLongitude;

  @Column(name = "s2_cell_level")
  private Integer s2CellLevel;

  @Column(name = "zone_info_id")
  private String s2CellZoneId;

  @Column(name = "s2_cell_desc")
  private String s2CellDesc;

  @Column(name = "s2_cell_location_id")
  private String s2CellLocationId;

  @Column(name = "s2_cell_location_desc")
  private String s2CellLocDesc;

  @Column(name = "created_dt")
  private String createAt;

  @Column(name = "created_by")
  private String createBy;

  @Column(name = "updated_dt")
  private String updateAt;

  @Column(name = "updated_by")
  private String updateBy;
}

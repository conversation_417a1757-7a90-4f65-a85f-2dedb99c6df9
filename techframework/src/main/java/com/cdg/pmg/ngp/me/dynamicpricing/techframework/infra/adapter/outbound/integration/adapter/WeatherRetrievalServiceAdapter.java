package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.adapter;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.RegionRainfall;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.WeatherRetrievalService;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos.WeatherRetrievalOutboundResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.mapper.WeatherRetrievalOutboundMapper;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class WeatherRetrievalServiceAdapter implements WeatherRetrievalService {
  private final WeatherRetrievalOutboundAdapter weatherRetrievalOutboundAdapter;
  private final WeatherRetrievalOutboundMapper mapper;

  @Override
  public List<RegionRainfall> getRainfall() {
    try {
      return Optional.ofNullable(weatherRetrievalOutboundAdapter.getRainfall())
          .map(ResponseEntity::getBody)
          .map(WeatherRetrievalOutboundResponse::getData)
          .map(mapper::mapToRegionRainfallList)
          .orElseGet(
              () -> {
                log.warn("Empty weather retrieval response");
                return Collections.emptyList();
              });
    } catch (Exception e) {
      log.error("Failed to get region rainfall, {}", e.getMessage(), e);
      return Collections.emptyList();
    }
  }
}

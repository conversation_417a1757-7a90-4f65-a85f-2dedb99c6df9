package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "flat_fare_conf")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class FlatFareConfigJPA {

  @Id
  @Column(name = "param_id")
  private Integer id;

  @Column(name = "param_key")
  private String paramKey;

  @Column(name = "param_value")
  private String paramValue;

  @Column(name = "param_effective_from_dt")
  private Instant effectiveDateFrom;

  @Column(name = "param_effective_till_dt")
  private Instant effectiveDateTo;
}

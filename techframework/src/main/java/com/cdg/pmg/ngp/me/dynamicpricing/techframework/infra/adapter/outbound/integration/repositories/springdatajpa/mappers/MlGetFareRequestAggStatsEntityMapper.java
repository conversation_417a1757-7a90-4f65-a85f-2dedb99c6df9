package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.mappers;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.MlGetFareRequestAggStatsEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.RegionGetFareCountEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.MlGetFareRequestAggStatsJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.projection.RegionGetFareCountProjection;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/** Mapper for converting between MlGetFareRequestAggStatsJPA and MlGetFareRequestAggStatsEntity. */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MlGetFareRequestAggStatsEntityMapper {

  /**
   * Convert a JPA entity to a domain entity.
   *
   * @param jpa the JPA entity to convert
   * @return the corresponding domain entity
   */
  MlGetFareRequestAggStatsEntity mapJpaToEntity(MlGetFareRequestAggStatsJPA jpa);

  List<RegionGetFareCountEntity> mapProjectionToEntity(List<RegionGetFareCountProjection> jpa);

  /**
   * Convert a domain entity to a JPA entity.
   *
   * @param entity the domain entity to convert
   * @return the corresponding JPA entity
   */
  MlGetFareRequestAggStatsJPA mapEntityToJpa(MlGetFareRequestAggStatsEntity entity);

  List<MlGetFareRequestAggStatsJPA> mapEntityToJpa(List<MlGetFareRequestAggStatsEntity> entities);
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.mappers;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.PricingRangeConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.PricingRangeConfigJPA;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper
public interface PricingRangeConfigEntityMapper {
  /**
   * Map PricingRangeConfigJPA to PricingRangeConfigEntity
   *
   * @param source Pricing range config JPA
   * @return PricingRangeConfigEntity Pricing range config entity
   */
  @Mapping(target = "pricingRangeId", source = "source.dynamicPricingRangeId")
  PricingRangeConfigEntity mapPricingRangeConfigJPAToPricingRangeConfig(
      PricingRangeConfigJPA source);

  /**
   * Map PricingRangeConfigEntity to PricingRangeConfigJPA
   *
   * @param source Pricing range config entity
   * @return PricingRangeConfigJPA Pricing range config JPA
   */
  @Mapping(target = "dynamicPricingRangeId", source = "source.pricingRangeId")
  PricingRangeConfigJPA mapPricingRangeConfigEntityToPricingRangeConfigJPA(
      PricingRangeConfigEntity source);
}

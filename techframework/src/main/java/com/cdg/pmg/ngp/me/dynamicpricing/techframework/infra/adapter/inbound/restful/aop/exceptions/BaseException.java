package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.aop.exceptions;

import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.DomainException;
import lombok.Getter;
import org.springframework.http.HttpStatus;

/** The type Domain exception. */
@Getter
public class BaseException extends DomainException {
  private final transient HttpStatus httpStatus;

  /**
   * Instantiates a new Base exception.
   *
   * @param message the message
   * @param errorCode the error code
   * @param httpStatus the http status
   */
  public BaseException(String message, Long errorCode, HttpStatus httpStatus) {
    super(message, errorCode);
    this.httpStatus = httpStatus;
  }

  /**
   * Instantiates a new Base exception.
   *
   * @param message the message
   * @param errorCode the error code
   * @param httpStatus the http status
   * @param arguments the arguments
   */
  public BaseException(String message, Long errorCode, HttpStatus httpStatus, Object... arguments) {
    super(message, errorCode, arguments);
    this.httpStatus = httpStatus;
  }

  /**
   * Instantiates a new Base exception.
   *
   * @param messageString the message string
   * @param message the message
   * @param errorCode the error code
   * @param httpStatus the http status
   * @param arguments the arguments
   */
  public BaseException(
      String messageString,
      String message,
      Long errorCode,
      HttpStatus httpStatus,
      Object... arguments) {
    super(messageString, message, errorCode, arguments);
    this.httpStatus = httpStatus;
  }
}

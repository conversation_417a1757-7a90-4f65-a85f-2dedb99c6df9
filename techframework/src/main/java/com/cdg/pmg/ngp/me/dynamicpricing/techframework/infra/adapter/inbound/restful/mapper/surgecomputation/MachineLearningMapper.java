package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.RegionFareCountAggregateResult;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.FareCountAggregateResult;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MachineLearningMapper {

  List<FareCountAggregateResult> mapToFareCountAggregateResult(
      List<RegionFareCountAggregateResult> regionFareCountList);
}

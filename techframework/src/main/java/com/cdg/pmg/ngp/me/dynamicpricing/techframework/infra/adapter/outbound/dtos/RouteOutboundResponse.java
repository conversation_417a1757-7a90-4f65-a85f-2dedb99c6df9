package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RouteOutboundResponse implements Serializable {
  @Serial private static final long serialVersionUID = 1L;
  private Long distanceMeters;
  private String duration;
  private String staticDuration;
  private PolylineOutboundResponse polyline;
  private String description;
}

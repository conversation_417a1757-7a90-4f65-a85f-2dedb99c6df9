package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

@Data
public class EffectiveH3RegionsOutBoundResponse implements Serializable {
  @Serial private static final long serialVersionUID = 5401066870834721461L;

  private String regionVersion;
  private LocalDateTime effectiveFrom;
  private LocalDateTime effectiveTo;
  private List<H3RegionOutbound> regions;
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper;

import com.cdg.pmg.ngp.me.dynamicpricing.commands.FareTypeConfigCommand;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FareTypeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.FareTypeConfigRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.FareTypeConfigResponseData;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

@Mapper
public interface FareTypeConfigMapper {
  @Named("convertDateToString")
  default String convertDateToString(Date date) {
    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
    return simpleDateFormat.format(date);
  }

  @Named("convertInstantToOffsetDateTimeUtc")
  static String convertInstantToOffsetDateTimeUtc(Instant instant) {
    return OffsetDateTime.ofInstant(instant, ZoneOffset.UTC).toString();
  }

  /**
   * Map list FareTypeConfig to list FareTypeConfig model
   *
   * @param source List Fare type config
   * @return List<com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.FareTypeConfig> List fare
   *     type config model
   */
  @Mapping(target = "startDate", expression = "java(convertDateToString(source.getStartDate()))")
  @Mapping(target = "endDate", expression = "java(convertDateToString(source.getEndDate()))")
  @Mapping(
      target = "createdDate",
      source = "source.createdDate",
      qualifiedByName = "convertInstantToOffsetDateTimeUtc")
  @Mapping(
      target = "updatedDate",
      source = "source.updatedDate",
      qualifiedByName = "convertInstantToOffsetDateTimeUtc")
  List<com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.FareTypeConfig>
      mapFareTypeConfigDomainToFareTypeConfigTech(List<FareTypeConfig> source);

  /**
   * Map FareTypeConfigRequest to FareTypeConfigCommand
   *
   * @param source Fare type config request
   * @return FareTypeConfigCommand Fare type config command
   */
  @Mapping(target = "defaultFixed", source = "source.configRequest.value")
  @Mapping(target = "defaultPercent", source = "source.configRequest.defaultPercent")
  @Mapping(target = "day", source = "source.configRequest.day")
  @Mapping(target = "hour", source = "source.configRequest.hour")
  @Mapping(target = "startDate", source = "source.configRequest.effectiveDate")
  @Mapping(target = "endDate", source = "source.configRequest.effectiveDateTo")
  FareTypeConfigCommand mapFareTypeConfigRequestToFareTypeConfigCommand(
      FareTypeConfigRequest source);

  /**
   * Map FareTypeConfig to FareTypeConfigResponseData
   *
   * @param source Fare type config
   * @return FareTypeConfigResponseData Fare type config response data
   */
  FareTypeConfigResponseData mapFareTypeConfigToFareTypeConfigResponseData(FareTypeConfig source);
}

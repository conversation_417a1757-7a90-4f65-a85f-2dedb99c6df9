package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "zone_info")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ZoneInfoJPA {
  @Id
  @Column(name = "zone_id", nullable = false, length = 5)
  private String zoneId;

  @Column(name = "default_method", length = 12)
  private String defaultMethod;

  @Column(name = "max_booking", precision = 5)
  private BigDecimal maxBooking;

  @Column(name = "booking_count", precision = 5)
  private BigDecimal bookingCount;

  @Column(name = "taxi_count", precision = 5)
  private BigDecimal taxiCount;

  @Column(name = "time_period", length = 10)
  private String timePeriod;

  @Column(name = "max_ds_ratio", precision = 5, scale = 2)
  private BigDecimal maxDsRatio;

  @Column(name = "dispatch_count", precision = 5)
  private BigDecimal dispatchCount;

  @Column(name = "ds_ratio", precision = 5, scale = 2)
  private BigDecimal dsRatio;

  @Column(name = "zone_desc", length = 30)
  private String zoneDesc;

  @Column(name = "zone_addr_ref", length = 10)
  private String zoneAddrRef;

  @Column(name = "rainy_flag", nullable = false)
  private boolean rainyFlag;

  @Column(name = "report_frequency", precision = 3)
  private BigDecimal reportFrequency;

  @Column(name = "closest_dispatch_perimeter", precision = 5)
  private BigDecimal closestDispatchPerimeter;

  @Column(name = "cbd_zone")
  private Boolean cbdZone;

  @Column(name = "charging_zone_id", nullable = false, length = 5)
  private String chargingZoneId;

  @Column(name = "updated_by", length = 20)
  private String updatedBy;

  @Column(name = "updated_dt")
  private Timestamp updatedDt;

  @Column(name = "buffer_dispatch_perimeter", precision = 5)
  private BigDecimal bufferDispatchPerimeter;

  @Column(name = "closest_dispatch_routing_sec", precision = 5)
  private BigDecimal closestDispatchRoutingSec;

  @Column(name = "version", length = 5)
  private String version;

  @Column(name = "auto_bid_perimeter", nullable = false, precision = 5)
  private BigDecimal autoBidPerimeter;
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class PlatformFeeOutboundRequest implements Serializable {
  @Serial private static final long serialVersionUID = 6964690958117534234L;

  private String bookingChannel;
  private String productId;
  private String vehicleGroupId;
}

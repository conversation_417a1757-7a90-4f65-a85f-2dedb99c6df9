package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.S2CellJPA;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;

public interface S2CellJPARepository extends JpaRepository<S2CellJPA, Long> {
  List<S2CellJPA> findAll();
}

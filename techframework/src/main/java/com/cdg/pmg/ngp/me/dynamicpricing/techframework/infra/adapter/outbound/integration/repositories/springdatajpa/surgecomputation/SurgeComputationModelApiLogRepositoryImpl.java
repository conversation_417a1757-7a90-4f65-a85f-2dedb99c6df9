package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.surgecomputation.SurgeComputationModelApiLogRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.SurgeComputationModelApiLogEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.SurgeComputationModelApiLogJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.mappers.SurgeComputationModelApiLogMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.surgecomputation.SurgeComputationModelApiLogJPARepository;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * Implementation of the SurgeComputationModelApiLogRepository interface. Provides methods for CRUD
 * operations on surge computation model API logs.
 */
@Component
@Primary
@RequiredArgsConstructor
@Slf4j
public class SurgeComputationModelApiLogRepositoryImpl
    implements SurgeComputationModelApiLogRepository {

  private final SurgeComputationModelApiLogJPARepository jpaRepository;
  private final SurgeComputationModelApiLogMapper mapper;

  @Override
  @Transactional
  public SurgeComputationModelApiLogEntity save(SurgeComputationModelApiLogEntity entity) {
    SurgeComputationModelApiLogJPA jpaEntity = mapper.mapEntityToJpa(entity);
    SurgeComputationModelApiLogJPA savedJpaEntity = jpaRepository.save(jpaEntity);
    return mapper.mapJpaToEntity(savedJpaEntity);
  }

  @Override
  @Transactional(readOnly = true)
  public List<SurgeComputationModelApiLogEntity> findByModelId(Long modelId) {
    List<SurgeComputationModelApiLogJPA> jpaEntities = jpaRepository.findByModelId(modelId);
    return mapper.mapJpaToEntity(jpaEntities);
  }
}

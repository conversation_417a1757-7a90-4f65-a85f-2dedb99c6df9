package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.CompanyHolidayRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.CompanyHolidayJPARepository;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

@Component
@Primary
@RequiredArgsConstructor
public class CompanyHolidayRepositoryImpl implements CompanyHolidayRepository {
  private final CompanyHolidayJPARepository companiHolidayJPARepository;

  @Override
  public List<String> getCompanyHolidays() {
    return companiHolidayJPARepository.getCompanyHolidays();
  }

  @Override
  public String getCurrentHoliday() {
    return companiHolidayJPARepository.getCurrentHoliday();
  }

  @Override
  public String getCurrentHolidayWithSingTimezone() {
    return companiHolidayJPARepository.getCurrentHolidayUsingSingTimeZone();
  }
}

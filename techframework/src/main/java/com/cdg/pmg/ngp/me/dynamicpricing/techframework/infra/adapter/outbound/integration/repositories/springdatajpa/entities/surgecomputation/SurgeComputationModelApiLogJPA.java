package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

/**
 * Entity class for surge_computation_model_api_logs table. Stores information about API calls to
 * surge computation models.
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Entity
@Table(name = "surge_computation_model_api_logs")
public class SurgeComputationModelApiLogJPA {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  private Long id;

  @Column(name = "model_id", nullable = false)
  private Long modelId;

  @Column(name = "model_name", nullable = false)
  private String modelName;

  @Column(name = "endpoint_url", nullable = false)
  private String endpointUrl;

  @Column(name = "create_timestamp", nullable = false)
  private Instant createTimestamp;

  @Column(name = "status_code", nullable = false)
  private Integer statusCode;

  @JdbcTypeCode(SqlTypes.JSON)
  @Column(name = "request_params", columnDefinition = "jsonb", nullable = false)
  private Object requestParams;

  @JdbcTypeCode(SqlTypes.JSON)
  @Column(name = "response_body", columnDefinition = "jsonb", nullable = false)
  private Object responseBody;
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.custom;

public interface CBDSurchargeJPACustom {
  Integer getLocationId();

  String getLocationName();

  String getAddressRef();

  String getFareType();

  String getChargeBy();

  Double getSurchargeValue();

  String getProductId();

  String getStartTime();

  String getEndTime();

  String getDayIndicator();
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs.properties;

import com.cdg.pmg.ngp.me.dynamicpricing.constants.BookARideConfigsConstant;
import java.util.HashMap;
import java.util.Map;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@Getter
@Setter
@RefreshScope
public class RefreshableProperties {
  private String bookRideSchedulingMin;
  private String feedId;

  private String currencyCode;

  private String bookRideVehicleGroup;

  private String bookRideProduct;

  private String comfortLocalisedName;

  private String meteredLocalisedName;

  private String comfortInternalName;

  private String meteredInternalName;

  private String waitingTimeSeconds;

  private String fileName;

  private String comfortLowRangeEstimateMultiplierValue;

  private String meteredLowRangeEstimateMultiplierValue;

  private String comfortHighRangeEstimateMultiplierValue;

  private String meteredHighRangeEstimateMultiplierValue;

  private String minimumFixed;

  private String bookRideProdCategory;

  private String carIconId;

  public Map<String, String> getBookARideConfigProperties() {
    Map<String, String> configMap = new HashMap<>();
    configMap.put(BookARideConfigsConstant.BOOK_RIDE_SCHEDULING_MIN, bookRideSchedulingMin);
    configMap.put(BookARideConfigsConstant.FEED_ID, feedId);
    configMap.put(BookARideConfigsConstant.CURRENCY_CODE, currencyCode);
    configMap.put(BookARideConfigsConstant.BOOK_RIDE_VEHICLE_GROUP, bookRideVehicleGroup);
    configMap.put(BookARideConfigsConstant.BOOK_RIDE_PRODUCT, bookRideProduct);
    configMap.put(BookARideConfigsConstant.COMFORT_LOCALISED_NAME, comfortLocalisedName);
    configMap.put(BookARideConfigsConstant.METERED_LOCALISED_NAME, meteredLocalisedName);
    configMap.put(BookARideConfigsConstant.COMFORT_INTERNAL_NAME, comfortInternalName);
    configMap.put(BookARideConfigsConstant.METERED_INTERNAL_NAME, meteredInternalName);
    configMap.put(BookARideConfigsConstant.WAITING_TIME_SECONDS, waitingTimeSeconds);
    configMap.put(BookARideConfigsConstant.FILE_NAME, fileName);
    configMap.put(
        "COMFORT_" + BookARideConfigsConstant.LOW_RANGE_ESTIMATE_MULTIPLIER_VALUE,
        comfortLowRangeEstimateMultiplierValue);
    configMap.put(
        "METERED_" + BookARideConfigsConstant.LOW_RANGE_ESTIMATE_MULTIPLIER_VALUE,
        meteredLowRangeEstimateMultiplierValue);
    configMap.put(
        "COMFORT_" + BookARideConfigsConstant.HIGH_RANGE_ESTIMATE_MULTIPLIER_VALUE,
        comfortHighRangeEstimateMultiplierValue);
    configMap.put(
        "METERED_" + BookARideConfigsConstant.HIGH_RANGE_ESTIMATE_MULTIPLIER_VALUE,
        meteredHighRangeEstimateMultiplierValue);
    configMap.put(BookARideConfigsConstant.MINIMUM_FIXED, minimumFixed);
    configMap.put(BookARideConfigsConstant.BOOK_RIDE_PROD_CATEGORY, bookRideProdCategory);
    configMap.put(BookARideConfigsConstant.CAR_ICON_ID, carIconId);
    return configMap;
  }
}

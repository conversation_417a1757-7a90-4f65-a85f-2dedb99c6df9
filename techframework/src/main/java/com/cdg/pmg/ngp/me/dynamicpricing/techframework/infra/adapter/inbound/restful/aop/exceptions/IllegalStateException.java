package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.aop.exceptions;

import java.io.Serial;
import org.springframework.http.HttpStatus;

/** The type Illegal state exception. */
public class IllegalStateException extends BaseException {

  @Serial private static final long serialVersionUID = -3312074409042759984L;
  private static final String ILLEGAL_STATE_EXCEPTION_MESSAGE = "Illegal state";

  private static final HttpStatus HTTP_STATUS = HttpStatus.BAD_REQUEST;

  /**
   * Instantiates a new Illegal state exception.
   *
   * @param message the message
   * @param errorCode the error code
   */
  public IllegalStateException(final String message, final Long errorCode) {
    super(ILLEGAL_STATE_EXCEPTION_MESSAGE, message, errorCode, HTTP_STATUS);
  }

  /**
   * Instantiates a new Illegal state exception.
   *
   * @param message the message
   * @param errorCode the error code
   * @param arguments the arguments
   */
  public IllegalStateException(
      final String message, final Long errorCode, final Object... arguments) {
    super(ILLEGAL_STATE_EXCEPTION_MESSAGE, message, errorCode, HTTP_STATUS, arguments);
  }
}

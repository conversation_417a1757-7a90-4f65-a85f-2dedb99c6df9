package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration properties for automatic partition management of the
 * surge_computation_model_api_logs table.
 *
 * <p>This configuration class manages the behavior of automatic partition creation, maintenance,
 * and cleanup for the {@code surge_computation_model_api_logs} table, which is partitioned by
 * Singapore timezone months.
 *
 * <p>All properties use the {@code app.partition} prefix and can be configured via
 * application.properties.
 *
 * @see PartitionManagementService
 */
@Configuration
@ConfigurationProperties(prefix = "app.partition")
@Data
public class PartitionConfiguration {

  /** Number of months ahead to create partitions for during startup. Default: 3 months */
  private int startupMonthsAhead = 3;

  /**
   * Number of months ahead to create partitions for during scheduled maintenance. Default: 6 months
   */
  private int scheduledMonthsAhead = 6;

  /** Number of months of historical data to keep before cleanup. Default: 36 months (3 years) */
  private int monthsToKeep = 4;

  /**
   * Whether to enable automatic partition cleanup. ⚠️ WARNING: When enabled, permanently deletes
   * old partitions and data. Default: false (disabled for safety)
   */
  private boolean enableCleanup = false;

  /** Whether to enable scheduled partition maintenance. Default: true */
  private boolean enableScheduledMaintenance = true;

  /** Cron expression for scheduled partition maintenance. Default: Daily at 1 AM Singapore time */
  private String maintenanceCron = "0 0 1 * * ?";

  /** Timezone for scheduled partition maintenance. Default: Asia/Singapore */
  private String maintenanceTimezone = "Asia/Singapore";
}

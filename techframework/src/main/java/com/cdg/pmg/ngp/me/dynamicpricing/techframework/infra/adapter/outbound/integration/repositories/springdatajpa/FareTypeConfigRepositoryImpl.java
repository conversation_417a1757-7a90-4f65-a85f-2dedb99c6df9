package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.FareTypeConfigRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FareTypeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.FareTypeConfigJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.custom.FareTypeConfigJPACustom;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.mappers.FareTypeConfigEntityMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.FareTypeConfigJPARepository;
import com.cdg.pmg.ngp.me.dynamicpricing.utils.CollectionUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

@Component
@Primary
@RequiredArgsConstructor
@Slf4j
public class FareTypeConfigRepositoryImpl implements FareTypeConfigRepository {
  private final FareTypeConfigJPARepository fareTypeConfigJPARepository;
  private final FareTypeConfigEntityMapper fareTypeConfigEntityMapper;

  @Override
  public List<FareTypeConfig> getFareTypeConfigs() {
    final List<FareTypeConfigJPACustom> configs = fareTypeConfigJPARepository.getFareTypeConfig();
    if (CollectionUtils.isEmpty(configs)) {
      log.info("The fare type config from database is empty!");
      return new ArrayList<>();
    }
    return configs.stream()
        .map(
            conf ->
                FareTypeConfig.builder()
                    .fareTypeId(conf.getFareTypeId())
                    .fareType(conf.getFareType())
                    .day(conf.getDay())
                    .hour(conf.getHour())
                    .fixedValue(conf.getFixedValue())
                    .defaultFixed(conf.getDefaultFixed())
                    .percentValue(conf.getPercentValue())
                    .defaultPercent(conf.getDefaultPercent())
                    .startDate(conf.getStartDate())
                    .endDate(conf.getEndDate())
                    .vehGrp(conf.getVehGrp())
                    .dayPriority(conf.getDayPriority())
                    .createdDate(conf.getCreatedDate())
                    .createdBy(conf.getCreatedBy())
                    .updatedDate(conf.getUpdatedDate())
                    .updatedBy(conf.getUpdatedBy())
                    .build())
        .toList();
  }

  @Override
  public List<FareTypeConfig> getParamConfigByListFareType(final Set<String> listFareType) {
    log.info("Start list fare type configs from db");
    List<FareTypeConfigJPA> fareTypeConfigEntityList =
        fareTypeConfigJPARepository.getFareTypeConfigByListFareType(listFareType);

    log.info(
        "Get list fare type configs from db successfully: {} fare type configs.",
        fareTypeConfigEntityList.size());

    List<FareTypeConfig> result = new ArrayList<>();
    for (FareTypeConfigJPA fareTypeConfigEntity : fareTypeConfigEntityList) {
      FareTypeConfig fareTypeConfig =
          fareTypeConfigEntityMapper.mapFareTypeConfigEntityToFareTypeConfig(fareTypeConfigEntity);
      result.add(fareTypeConfig);
    }
    return result;
  }

  @Override
  public FareTypeConfig createFareTypeConfig(final FareTypeConfig fareTypeConfig) {
    log.info("Start create fare type config");
    FareTypeConfigJPA fareTypeConfigEntity =
        fareTypeConfigEntityMapper.mapFareTypeConfigToFareTypeConfigEntity(fareTypeConfig);
    FareTypeConfigJPA createdFareTypeConfig =
        fareTypeConfigJPARepository.save(fareTypeConfigEntity);
    log.info("Create fare type config successfully: {} .", createdFareTypeConfig.getFareType());

    return fareTypeConfigEntityMapper.mapFareTypeConfigEntityToFareTypeConfig(
        createdFareTypeConfig);
  }

  @Override
  public FareTypeConfig updateFareTypeConfig(final FareTypeConfig fareTypeConfig) {
    log.info("Start update fare type config");
    String fareType = fareTypeConfig.getFareType();

    FareTypeConfigJPA fareTypeConfigEntity = fareTypeConfigJPARepository.findByFareType(fareType);

    fareTypeConfigEntity.setDefaultFixed(fareTypeConfig.getDefaultFixed());
    fareTypeConfigEntity.setDefaultPercent(fareTypeConfig.getDefaultPercent());
    fareTypeConfigEntity.setStartDate(fareTypeConfig.getStartDate());
    fareTypeConfigEntity.setEndDate(fareTypeConfig.getEndDate());
    fareTypeConfigEntity.setDay(fareTypeConfig.getDay());
    fareTypeConfigEntity.setHour(fareTypeConfig.getHour());
    fareTypeConfigEntity.setUpdatedBy(fareTypeConfig.getUpdatedBy());
    FareTypeConfigJPA updatedFareTypeConfig =
        fareTypeConfigJPARepository.save(fareTypeConfigEntity);
    log.info("Update fare type config successfully: {} .", updatedFareTypeConfig.getFareType());

    return fareTypeConfigEntityMapper.mapFareTypeConfigEntityToFareTypeConfig(
        updatedFareTypeConfig);
  }
}

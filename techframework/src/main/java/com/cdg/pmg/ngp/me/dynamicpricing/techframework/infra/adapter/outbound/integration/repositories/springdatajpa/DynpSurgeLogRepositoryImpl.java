package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.DynpSurgeLogsRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.DynpSurgeLogsJPARepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

@Component
@Primary
@RequiredArgsConstructor
@Slf4j
public class DynpSurgeLogRepositoryImpl implements DynpSurgeLogsRepository {
  private final DynpSurgeLogsJPARepository dynpSurgeLogsJPARepository;

  @Override
  public void insertDynpSurgesLog() {
    try {
      dynpSurgeLogsJPARepository.insertDynpSurgeLogs();
    } catch (Exception exception) {
      log.error("Insert dynp_surge_log error: {}", exception.getMessage());
    }
  }

  @Override
  public void insertDynpSurgesLogV2() {
    try {
      dynpSurgeLogsJPARepository.insertDynpSurgeLogsV2();
    } catch (Exception exception) {
      log.error("Insert dynp_surge_log error: {}", exception.getMessage());
    }
  }
}

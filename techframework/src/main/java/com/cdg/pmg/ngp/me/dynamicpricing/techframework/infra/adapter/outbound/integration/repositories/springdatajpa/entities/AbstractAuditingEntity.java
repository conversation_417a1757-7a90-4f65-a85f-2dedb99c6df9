package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Column;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.MappedSuperclass;
import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

/**
 * Base abstract class for entities which will hold definitions for created, updated, created by,
 * updated by attributes.
 */
@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@MappedSuperclass
@EntityListeners(AuditingEntityListener.class)
@JsonIgnoreProperties(
    value = {"createdBy", "createdDate", "updatedBy", "updatedDate"},
    allowGetters = true)
public abstract class AbstractAuditingEntity<T> implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  public abstract T getId();

  @CreatedBy
  @Column(name = "created_by", nullable = false, length = 255, updatable = false)
  private String createdBy;

  @CreatedDate
  @Column(name = "created_dt", updatable = false)
  @Builder.Default
  private Instant createdDate = Instant.now();

  @LastModifiedBy
  @Column(name = "updated_by", length = 255)
  private String updatedBy;

  @LastModifiedDate
  @Column(name = "updated_dt")
  @Builder.Default
  private Instant updatedDate = Instant.now();
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.mappers;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.S2CellEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.S2CellJPA;
import org.mapstruct.Mapper;

@Mapper
public interface S2CellEntityMapper {
  S2CellEntity mapS2CellJpaToEntity(S2CellJPA s2CellJpa);
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.FlatFareAdjustmentConfJPA;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;

public interface FlatFareAdjustmentConfJPARepository
    extends JpaRepository<FlatFareAdjustmentConfJPA, Long> {

  List<FlatFareAdjustmentConfJPA> findAllByIsEnabledIsTrue();
}

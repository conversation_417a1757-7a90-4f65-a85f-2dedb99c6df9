package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos;

import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class H3RegionComputeOutbound extends H3RegionOutbound {
  @Serial private static final long serialVersionUID = -8874434720708556867L;

  private String regionVersion;
  private Double lat;
  private Double lng;
}

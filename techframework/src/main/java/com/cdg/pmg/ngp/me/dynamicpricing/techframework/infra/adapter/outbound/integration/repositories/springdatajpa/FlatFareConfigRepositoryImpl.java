package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.FlatFareConfigRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FlatFareConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.FlatFareConfigJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.FlatFareConfigJPARepository;
import com.cdg.pmg.ngp.me.dynamicpricing.utils.CollectionUtils;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

@Component
@Primary
@RequiredArgsConstructor
@Slf4j
public class FlatFareConfigRepositoryImpl implements FlatFareConfigRepository {
  private final FlatFareConfigJPARepository flatFareConfigJPARepository;

  @Override
  public List<FlatFareConfig> getAllFlatFareConfig() {
    final List<FlatFareConfigJPA> configs = flatFareConfigJPARepository.getAllValidFlatFareConfig();

    if (CollectionUtils.isEmpty(configs)) {
      log.info("The flat fare config from database is empty!");
      return new ArrayList<>();
    }
    return configs.stream()
        .map(conf -> new FlatFareConfig(conf.getParamKey(), conf.getParamValue()))
        .toList();
  }

  @Override
  public List<String> getConfByParamkeyFlatFare(String paramKey) {
    return flatFareConfigJPARepository.getConfByParamkeyFlatFare(paramKey);
  }

  @Override
  public List<FlatFareConfig> getConfByIdentifyForCalDemandSurge(String identify) {
    final List<FlatFareConfigJPA> configs =
        flatFareConfigJPARepository.getConfByIdentifyForCalDemandSurge(identify);
    if (CollectionUtils.isEmpty(configs)) {
      log.info("The flat fare config find by identify from database is empty!");
      return new ArrayList<>();
    }
    return configs.stream()
        .map(conf -> new FlatFareConfig(conf.getParamKey(), conf.getParamValue()))
        .toList();
  }
}

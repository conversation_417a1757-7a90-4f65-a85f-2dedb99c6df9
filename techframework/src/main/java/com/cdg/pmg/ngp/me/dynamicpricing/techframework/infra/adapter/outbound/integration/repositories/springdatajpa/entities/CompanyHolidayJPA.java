package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "company_holiday")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CompanyHolidayJPA {
  @Id
  @Column(name = "holiday_type_id")
  private String holidayTypeId;

  @Column(name = "ph_date")
  private String phDate;
}

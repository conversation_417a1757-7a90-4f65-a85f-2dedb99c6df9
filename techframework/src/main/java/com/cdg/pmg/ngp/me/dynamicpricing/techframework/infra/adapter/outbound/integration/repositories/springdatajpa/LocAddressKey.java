package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import java.io.Serial;
import java.io.Serializable;
import java.util.Objects;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@Getter
@Setter
@Embeddable
public class LocAddressKey implements Serializable {
  @Serial private static final long serialVersionUID = 1L;

  @Column(name = "location_id")
  private String locationId;

  @Column(name = "address_ref")
  private String addressRef;

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    LocAddressKey that = (LocAddressKey) o;
    return Objects.equals(locationId, that.locationId)
        && Objects.equals(addressRef, that.addressRef);
  }

  @Override
  public int hashCode() {
    return Objects.hash(locationId, addressRef);
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.DynamicSurgesEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.DynamicSurgeData;
import java.util.List;
import org.mapstruct.Mapper;

@Mapper
public interface DynamicSurgeDataMapper {

  DynamicSurgeData map(DynamicSurgesEntity source);

  List<DynamicSurgeData> map(List<DynamicSurgesEntity> source);
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.ZoneInfoJPA;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface ZoneInfoJPARepository extends JpaRepository<ZoneInfoJPA, String> {
  @Query("SELECT zoneId from ZoneInfoJPA")
  List<String> findAllZoneIds();

  @Query(
      value = "SELECT EXISTS (SELECT 1 from zone_info WHERE zone_id = :zoneId)",
      nativeQuery = true)
  boolean checkZoneIdExists(@Param("zoneId") String zoneId);
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.cms;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.NewPricingConfigChangeTrackingService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.ConfigurationServiceOutboundPort;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.ConfigKeyValueConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.NewPricingModelConfigChangeEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.NewPricingModelConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.EntityChangeTypeEnum;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs.DynamicSurgeProperties;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.mappers.NewPricingModelConfigChangeMapper;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.cloud.context.scope.refresh.RefreshScopeRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;

/**
 * This class listens for configuration changes in the dynamic pricing model. It detects changes in
 * pricing configurations and surge properties, and tracks them for auditing and business logic
 * updates. The class reacts to Spring Cloud's {@link
 * org.springframework.cloud.context.scope.refresh.RefreshScopeRefreshedEvent} (via kafka
 * ngp.common.cms.refresh_setting topic) ensuring the system remains in sync with configuration
 * changes.
 *
 * <p>Primary responsibilities:
 *
 * <ul>
 *   <li>Monitor and compare current pricing configurations against previous ones.
 *   <li>Track changes in surge-related properties.
 *   <li>Persist detected changes for further processing and auditing.
 * </ul>
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class NewPricingModelConfigurationChangeListener {

  private final NewPricingConfigChangeTrackingService newPricingConfigChangeTrackingService;
  private final ConfigurationServiceOutboundPort configurationOutboundPort;
  private final NewPricingModelConfigChangeMapper newPricingModelConfigChangeMapper;
  private final DynamicSurgeProperties dynamicSurgeProperties;
  private DynamicSurgeProperties previousSurgeProperties;

  private List<NewPricingModelConfigEntity> previousEntities;

  /**
   * Initializes the listener by capturing the initial state of pricing configurations and surge
   * properties. This ensures that subsequent changes can be detected accurately.
   */
  @PostConstruct
  public void init() {
    previousEntities = getAllSortedPricingConfigEntities();
    previousSurgeProperties = cloneCurrentSurgeProperties();

    log.info(
        "Init NewPricingModelConfigurationChangeListener found: {} configs, DynamicSurgeProperties: {}",
        previousEntities.size(),
        previousSurgeProperties);
  }

  /**
   * Clones the current dynamic surge properties for comparison with future updates.
   *
   * @return a deep copy of the current {@link DynamicSurgeProperties}.
   */
  private DynamicSurgeProperties cloneCurrentSurgeProperties() {
    return DynamicSurgeProperties.builder()
        .dosExcludeBookingChannels(dynamicSurgeProperties.getDosExcludeBookingChannels())
        .dosIncludeJobStatuses(dynamicSurgeProperties.getDosIncludeJobStatuses())
        .fleetAnalyticDemand15(dynamicSurgeProperties.getFleetAnalyticDemand15())
        .fleetAnalyticDemand30(dynamicSurgeProperties.getFleetAnalyticDemand30())
        .fleetAnalyticDemand60(dynamicSurgeProperties.getFleetAnalyticDemand60())
        .dynamicPricingSurgeSamplingPeriodInMinutes(
            dynamicSurgeProperties.getDynamicPricingSurgeSamplingPeriodInMinutes())
        .std001Weight(dynamicSurgeProperties.getStd001Weight())
        .build();
  }

  /**
   * Listens for {@link RefreshScopeRefreshedEvent} to detect changes in configuration and trigger
   * appropriate checks for updates.
   */
  @EventListener(RefreshScopeRefreshedEvent.class)
  public void listen() {
    checkPricingConfigs();
    checkSurgeProperties();
  }

  /** Checks for changes in surge properties and tracks any updates. */
  private void checkSurgeProperties() {
    DynamicSurgeProperties currentSurgeProperties = cloneCurrentSurgeProperties();

    Field[] fields = DynamicSurgeProperties.class.getDeclaredFields();
    List<ConfigKeyValueConfigEntity> keyValueConfigs = new ArrayList<>();

    for (Field field : fields) {
      try {
        ReflectionUtils.makeAccessible(field);
        Object curValue = field.get(currentSurgeProperties);
        Object prevVal = field.get(previousSurgeProperties);
        String propName = field.getName();
        if (!Objects.equals(curValue, prevVal)) {
          keyValueConfigs.add(
              ConfigKeyValueConfigEntity.builder()
                  .key(propName)
                  .value(String.valueOf(prevVal))
                  .build());
          keyValueConfigs.add(
              ConfigKeyValueConfigEntity.builder()
                  .key(propName)
                  .value(String.valueOf(curValue))
                  .build());
        }
      } catch (IllegalAccessException e) {
        log.error("Error when check surge fields", e);
      }
    }
    newPricingConfigChangeTrackingService.saveAllPropertyChanges(keyValueConfigs);
    previousSurgeProperties = currentSurgeProperties;
  }

  /**
   * Compares the current and previous pricing configurations, and tracks additions, updates, or
   * deletions as necessary.
   */
  private void checkPricingConfigs() {
    log.info("Perform update new pricing config if any changes");
    List<NewPricingModelConfigEntity> currentEntities = getAllSortedPricingConfigEntities();
    int pSize = previousEntities.size();
    int curSize = currentEntities.size();
    if (pSize == curSize) {
      handleUpdate(previousEntities, currentEntities);
    } else if (pSize < curSize) {
      handleAddNew(previousEntities, currentEntities);
    } else {
      handleDelete(previousEntities, currentEntities);
    }
    previousEntities = currentEntities;
  }

  /**
   * Fetches and sorts pricing configuration entities for consistent comparison.
   *
   * @return a sorted list of {@link NewPricingModelConfigEntity}.
   */
  private @NotNull List<NewPricingModelConfigEntity> getAllSortedPricingConfigEntities() {
    return configurationOutboundPort.getNewPricingModelConfigEntities(false).stream()
        .sorted(Comparator.comparing(NewPricingModelConfigEntity::getIndex))
        .toList();
  }

  /**
   * Handles the deletion of pricing configurations by tracking removed entities.
   *
   * @param previousEntities the list of previous entities.
   * @param currentEntities the list of current entities.
   */
  private void handleDelete(
      List<NewPricingModelConfigEntity> previousEntities,
      List<NewPricingModelConfigEntity> currentEntities) {
    Set<Long> remainingIds =
        currentEntities.stream()
            .map(NewPricingModelConfigEntity::getId)
            .collect(Collectors.toSet());

    List<NewPricingModelConfigChangeEntity> deletedEntities = new ArrayList<>();
    for (NewPricingModelConfigEntity previousEntity : previousEntities) {
      if (!remainingIds.contains(previousEntity.getId())) {
        NewPricingModelConfigChangeEntity entity =
            newPricingModelConfigChangeMapper.mapToNewPricingModelConfigChangeEntity(
                previousEntity);
        entity.setEntityChangeType(EntityChangeTypeEnum.DELETE);
        deletedEntities.add(entity);
      }
    }

    newPricingConfigChangeTrackingService.saveAll(deletedEntities);
  }

  /**
   * Handles the addition of new pricing configurations.
   *
   * @param previousEntities the list of previous entities.
   * @param currentEntities the list of current entities.
   */
  private void handleAddNew(
      List<NewPricingModelConfigEntity> previousEntities,
      List<NewPricingModelConfigEntity> currentEntities) {
    List<NewPricingModelConfigChangeEntity> changeEntities =
        currentEntities.subList(previousEntities.size(), currentEntities.size()).stream()
            .map(newPricingModelConfigChangeMapper::mapToNewPricingModelConfigChangeEntity)
            .toList();
    changeEntities.forEach(e -> e.setEntityChangeType(EntityChangeTypeEnum.ADD_NEW));
    newPricingConfigChangeTrackingService.saveAll(changeEntities);
  }

  /**
   * Handles updates to existing pricing configurations by tracking changes.
   *
   * @param previousEntities the list of previous entities.
   * @param currentEntities the list of current entities.
   */
  private void handleUpdate(
      List<NewPricingModelConfigEntity> previousEntities,
      List<NewPricingModelConfigEntity> currentEntities) {
    for (int i = 0; i < previousEntities.size(); i++) {
      if (!Objects.equals(previousEntities.get(i), currentEntities.get(i))) {
        NewPricingModelConfigChangeEntity previousChangeEntity =
            newPricingModelConfigChangeMapper.mapToNewPricingModelConfigChangeEntity(
                previousEntities.get(i));
        previousChangeEntity.setEntityChangeType(EntityChangeTypeEnum.UPDATE);
        NewPricingModelConfigChangeEntity currentChangeEntity =
            newPricingModelConfigChangeMapper.mapToNewPricingModelConfigChangeEntity(
                currentEntities.get(i));
        currentChangeEntity.setEntityChangeType(EntityChangeTypeEnum.UPDATE);
        newPricingConfigChangeTrackingService.saveAll(
            List.of(previousChangeEntity, currentChangeEntity));
      }
    }
  }
}

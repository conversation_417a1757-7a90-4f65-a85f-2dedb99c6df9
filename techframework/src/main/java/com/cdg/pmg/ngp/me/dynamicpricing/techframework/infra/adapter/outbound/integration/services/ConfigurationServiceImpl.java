package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.services;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.DpsProperties;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.DpsPropertiesService;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs.SystemParamProperties;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.mapper.ConfigurationMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ConfigurationServiceImpl implements DpsPropertiesService {
  private final SystemParamProperties systemParamProperties;
  private final ConfigurationMapper mapper;

  @Override
  public DpsProperties getDpsProperties() {
    return mapper.toDpsProperties(systemParamProperties);
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs.sftp;

import java.io.File;
import org.springframework.integration.annotation.Gateway;
import org.springframework.integration.annotation.MessagingGateway;

@MessagingGateway
@FunctionalInterface
public interface UploadGateway {
  /**
   * Request upload.
   *
   * @param file Uploads a given file to the SFTP server.
   */
  @Gateway(requestChannel = "toSftpChannel")
  void upload(File file);
}

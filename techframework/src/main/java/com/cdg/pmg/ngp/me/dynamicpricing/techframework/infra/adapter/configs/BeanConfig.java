package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.FilterType;

@Configuration
@AllArgsConstructor
@ComponentScan(
    basePackages = {"com.cdg.pmg.ngp.me.dynamicpricing"},
    includeFilters = {
      @ComponentScan.Filter(
          type = FilterType.ANNOTATION,
          classes = {ServiceComponent.class}),
      @ComponentScan.Filter(type = FilterType.REGEX, pattern = ".*Mapper.{0,}Impl")
    })
public class BeanConfig {
  // ConfigManagementService configManagementService;
  //
  // FlatFareConfigService configService;
  //
  // @Bean
  // public void loadToCacheAllConfig() {
  // configManagementService.reloadAllConfig();
  // }
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.annotation;

import static java.lang.annotation.ElementType.ANNOTATION_TYPE;
import static java.lang.annotation.ElementType.CONSTRUCTOR;
import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.ElementType.TYPE_USE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.validator.CustomMinValidator;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Repeatable;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import org.springframework.http.HttpStatus;

/** The interface Custom not null. */
@Constraint(validatedBy = CustomMinValidator.class)
@Target({METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE})
@Retention(RUNTIME)
@Repeatable(CustomMin.List.class)
@Documented
public @interface CustomMin {

  /**
   * Message string.
   *
   * @return the string
   */
  String message() default "{javax.validation.constraints.Min.message}";

  /**
   * Groups class [ ].
   *
   * @return the class [ ]
   */
  Class<?>[] groups() default {};

  /**
   * Payload class [ ].
   *
   * @return the class [ ]
   */
  Class<? extends Payload>[] payload() default {};

  /**
   * Value long.
   *
   * @return value the element must be higher or equal to
   */
  long value();

  /**
   * Status code int.
   *
   * @return the int
   */
  long errorCode() default 1L;

  /**
   * Http status http status.
   *
   * @return the http status
   */
  HttpStatus httpStatus() default HttpStatus.BAD_REQUEST;

  /** The interface List. */
  @Target({METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE})
  @Retention(RUNTIME)
  @Documented
  @interface List {
    /**
     * Value custom min [ ].
     *
     * @return the custom min [ ]
     */
    CustomMin[] value();
  }
}

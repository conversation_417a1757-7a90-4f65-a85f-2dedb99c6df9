package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.adapter;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.CMSConfigItem;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.CMSConfigList;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.CMSConfigRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.CreateCMSConfigRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.CMSServiceOutboundPort;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs.SpringSecurityAuditorAware;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class CMSServiceAdapter implements CMSServiceOutboundPort {

  @Value("${spring.cloud.config.name}")
  private String serviceName;

  private final CmsOutboundAdapter cmsOutboundAdapter;

  private final SpringSecurityAuditorAware springSecurityAuditorAware;

  @Override
  public CMSConfigList getCMSBySearchText(String searchText) {
    return cmsOutboundAdapter
        .getCmsNewPricingModelConfig(searchText, serviceName, Integer.MAX_VALUE)
        .getBody();
  }

  @Override
  public void updateCMSConfig(Long id, String value) {
    String updatedBy = springSecurityAuditorAware.getCurrentAuditor().orElse(serviceName);
    cmsOutboundAdapter.updateCmsConfig(
        id, CMSConfigRequest.builder().updatedBy(updatedBy).value(value).build());
  }

  @Override
  public CMSConfigItem createCmsConfig(CreateCMSConfigRequest createCmsConfigRequest) {
    String updatedBy = springSecurityAuditorAware.getCurrentAuditor().orElse(serviceName);
    createCmsConfigRequest.setUpdatedBy(updatedBy);
    return cmsOutboundAdapter.createCmsConfig(createCmsConfigRequest).getBody();
  }
}

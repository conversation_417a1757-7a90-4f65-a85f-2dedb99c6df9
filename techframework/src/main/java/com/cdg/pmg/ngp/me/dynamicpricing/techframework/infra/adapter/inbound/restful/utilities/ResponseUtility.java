package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.utilities;

import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.UUID;
import org.springframework.stereotype.Component;

/** Utility class for handling HTTP response-related operations. */
@Component
public class ResponseUtility {

  /**
   * Gets the current timestamp in UTC.
   *
   * @return the current timestamp in UTC
   */
  public OffsetDateTime getCurrentTimestamp() {
    return OffsetDateTime.now(ZoneOffset.UTC);
  }

  /**
   * Generates a trace ID for the response.
   *
   * @return a trace ID
   */
  public String getTraceId() {
    return UUID.randomUUID().toString().replace("-", "");
  }
}

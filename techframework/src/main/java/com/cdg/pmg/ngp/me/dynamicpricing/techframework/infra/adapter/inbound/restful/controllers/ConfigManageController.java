package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.controllers;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.DynamicPricingConfigSet;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.LocationSurchargeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.CmsConfigService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.ConfigManagementService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.LocationSurchargeService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.cbdcharge.CBDAddressConfigService;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.DynamicSurgesEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.ConfigTypeEnum;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.BadRequestException;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.api.ConfigManagementApi;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper.CBDAddressMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper.CmsPricingConfigMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper.DynamicSurgeDataMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper.LocationSurchargeConfigMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.*;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@Slf4j
public class ConfigManageController implements ConfigManagementApi {
  private final LocationSurchargeConfigMapper locMapper;
  private final ConfigManagementService configManagementService;
  private final CmsConfigService cmsConfigService;
  private final CmsPricingConfigMapper mapper;
  private final DynamicSurgeDataMapper dynamicSurgeDataMapper;
  private final LocationSurchargeService locationSurchargeService;
  private final CBDAddressMapper cbdAddressMapper;

  private final CBDAddressConfigService cbdAddressConfigService;

  private static final String LOAD_CONFIGS_SUCCESS_MSG = "Loaded configs successful!";
  private static final String EXCLUDE_BOOKING_CHANNELS_DESCRIPTION_KEY =
      "dosExcludeBookingChannels";
  private static final String JOB_STATUSES_DESCRIPTION_KEY = "dosIncludeJobStatuses";

  @Override
  public ResponseEntity<LoadConfigsSuccessResponse> loadConfigs() {
    String responseMsg = configManagementService.reloadAllConfig();
    LoadConfigsSuccessResponse response = new LoadConfigsSuccessResponse();
    response.setData(responseMsg);
    return ResponseEntity.ok(response);
  }

  @Override
  public ResponseEntity<LoadConfigsSuccessResponse> reloadConfigs(@RequestParam String configType) {
    if (!EnumUtils.isValidEnum(ConfigTypeEnum.class, configType)) {
      throw new BadRequestException(
          ErrorEnum.INVALID_CONFIG_TYPE.getMessage(), ErrorEnum.INVALID_CONFIG_TYPE.getErrorCode());
    }

    String responseMsg =
        switch (ConfigTypeEnum.valueOf(configType)) {
          case ALL -> configManagementService.reloadAllConfig();
          case FLAT_FARE -> configManagementService.reloadFlatFareConfig();
          case FARE_TYPE -> configManagementService.reloadFareTypeConfig();
          case COMPANY_HOLIDAY -> configManagementService.reloadCompanyHolidayConfig();
          case LOCATION_SURCHARGE -> configManagementService.reloadLocationSurchargeConfig();
          case DYNP_SURGE -> configManagementService.reloadDynpSurgeConfig();
          case FLAT_FARE_ADJUSTMENT -> configManagementService.reloadFareFareAdjustmentConfig();
        };

    LoadConfigsSuccessResponse response = new LoadConfigsSuccessResponse();
    response.setData(responseMsg);
    return ResponseEntity.ok(response);
  }

  @RequestMapping(
      method = RequestMethod.GET,
      value = "/v1.0/pricing/config/location-surcharge/all",
      produces = {"application/json"},
      consumes = {"application/json"})
  public ResponseEntity<List<LocationSurchargeConfig>> getAllLocationSurchargeConfig(
      @RequestParam String dayInWeek) {
    log.info("CONTROLLER - Get all Location Surcharge Config");

    final List<LocationSurchargeConfig> locConfig =
        configManagementService.getAllLocationSurchargeConfig(dayInWeek);

    return ResponseEntity.ok(locConfig);
  }

  @RequestMapping(
      method = RequestMethod.GET,
      value = "/v1.0/pricing/config/dos-surge-cache",
      produces = {"application/json"},
      consumes = {"application/json"})
  public ResponseEntity<List<DynamicSurgesEntity>> getDOSSurgeCache() {
    log.info("CONTROLLER - Get all dos surge config on Cache");

    final List<DynamicSurgesEntity> dosSurgeConfig = configManagementService.getAllDOSSurgeCache();

    return ResponseEntity.ok(dosSurgeConfig);
  }

  @RequestMapping(
      method = RequestMethod.GET,
      value = "/v1.0/pricing/config/fare-type-cache",
      produces = {"application/json"},
      consumes = {"application/json"})
  public ResponseEntity<DynamicPricingConfigSet> getFareTypeConfigCache() {
    log.info("CONTROLLER - Get all fare type config on Cache");

    final DynamicPricingConfigSet fareTypeConfigCache =
        configManagementService.getAllFareTypeConfigCache();

    return ResponseEntity.ok(fareTypeConfigCache);
  }

  @Override
  public ResponseEntity<NewPricingModelConfigListResponse> getNewPricingModelConfig() {
    return ResponseEntity.ok(
        mapper.toNewPricingModelItemResponse(
            cmsConfigService.getListNewPricingModelConfigEntityInCms()));
  }

  @Override
  public ResponseEntity<NewPricingModelConfigResponse> updateNewPricingModelConfig(
      NewPricingModelRequest updateNewPricingModelRequest) {
    return ResponseEntity.ok(
        mapper.toNewPricingModelResponse(
            cmsConfigService.updateNewPricingModelConfigEntityInCms(
                mapper.toUpdateNewPricingModelCommand(updateNewPricingModelRequest))));
  }

  @Override
  public ResponseEntity<NewPricingModelConfigResponse> createNewPricingModelConfig(
      NewPricingModelRequest newPricingModelRequest) {
    return ResponseEntity.ok(
        mapper.toNewPricingModelResponse(
            cmsConfigService.createNewPricingConfigModelConfigEntityInCms(
                mapper.toUpdateNewPricingModelCommand(newPricingModelRequest))));
  }

  @Override
  public ResponseEntity<ExcludeBookingChannelResponse> addExcludeBookingChannel(
      ExcludeBookingChannelRequest excludeBookingChannelRequest) {
    cmsConfigService.addNewConfig(
        EXCLUDE_BOOKING_CHANNELS_DESCRIPTION_KEY, excludeBookingChannelRequest.getBookingChannel());
    return ResponseEntity.ok(mapper.toExcludeBookingChannelResponse(excludeBookingChannelRequest));
  }

  @Override
  public ResponseEntity<ExcludeBookingChannelResponse> deleteExcludeBookingChannel(
      ExcludeBookingChannelRequest excludeBookingChannelRequest) {
    cmsConfigService.deleteNewConfig(
        EXCLUDE_BOOKING_CHANNELS_DESCRIPTION_KEY, excludeBookingChannelRequest.getBookingChannel());
    return ResponseEntity.ok(mapper.toExcludeBookingChannelResponse(excludeBookingChannelRequest));
  }

  @Override
  public ResponseEntity<ExcludeBookingChannelListResponse> getExcludeBookingChannel() {
    var bookingChannels =
        cmsConfigService
            .getConfigsByDescriptionKey(EXCLUDE_BOOKING_CHANNELS_DESCRIPTION_KEY)
            .getItems();
    var response = new ExcludeBookingChannelListResponse();
    response.setItems(bookingChannels);
    return ResponseEntity.ok(response);
  }

  @Override
  public ResponseEntity<JobStatusResponse> addJobStatus(JobStatusRequest jobStatusRequest) {
    cmsConfigService.addNewConfig(JOB_STATUSES_DESCRIPTION_KEY, jobStatusRequest.getJobStatus());
    return ResponseEntity.ok(mapper.toJobStatusReponse(jobStatusRequest));
  }

  @Override
  public ResponseEntity<JobStatusResponse> deleteJobStatus(JobStatusRequest jobStatusRequest) {
    cmsConfigService.deleteNewConfig(JOB_STATUSES_DESCRIPTION_KEY, jobStatusRequest.getJobStatus());
    return ResponseEntity.ok(mapper.toJobStatusReponse(jobStatusRequest));
  }

  @Override
  public ResponseEntity<JobStatusListResponse> getJobStatus() {
    var bookingChannels =
        cmsConfigService.getConfigsByDescriptionKey(JOB_STATUSES_DESCRIPTION_KEY).getItems();
    var response = new JobStatusListResponse();
    response.setItems(bookingChannels);
    return ResponseEntity.ok(response);
  }

  @Override
  public ResponseEntity<GetPickupLocationSurchargeResponse> getLocationSurchargeConfig(
      GetPickupLocationSurchargeRequest getPickupLocationSurchargeRequest) {
    LocationSurchargeConfig locationSurchargeConfig =
        locationSurchargeService.getLocationSurchargeConfig(
            locMapper.map(getPickupLocationSurchargeRequest));
    return ResponseEntity.ok(locMapper.map(locationSurchargeConfig));
  }

  @Override
  public ResponseEntity<Void> updateCbdAddress(UpdateCBDAddressRequest updateCBDAddressRequest) {
    cbdAddressConfigService.updateCBDAddress(
        cbdAddressMapper.mapToCBDAddressRequest(updateCBDAddressRequest));
    return ResponseEntity.status(HttpStatus.NO_CONTENT).body(null);
  }

  @Override
  public ResponseEntity<Void> reloadCbdConfigCache(LocReloadCacheRequest locReloadCacheRequest) {
    configManagementService.reloadCBDAddressConfig(
        cbdAddressMapper.toLocReloadCache(locReloadCacheRequest));
    return ResponseEntity.status(HttpStatus.NO_CONTENT).body(null);
  }

  @Override
  public ResponseEntity<DynamicSurgeResponse> getDynamicSurgeFromCache() {
    List<DynamicSurgesEntity> dynamicSurges = configManagementService.getAllDOSSurgeCache();
    DynamicSurgeResponse response = new DynamicSurgeResponse();
    response.setData(dynamicSurgeDataMapper.map(dynamicSurges));
    return ResponseEntity.ok(response);
  }
}

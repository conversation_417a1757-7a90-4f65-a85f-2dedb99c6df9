package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.utilities;

import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/** Utility class for handling HTTP request-related operations. */
@UtilityClass
@Slf4j
public class RequestUtils {

  /**
   * Extracts the user ID from the X-User-Id header in the current request.
   *
   * @return the user ID from the header
   * @throws IllegalArgumentException if the header is missing or empty, or if the request context
   *     is not available
   */
  public static String getUserIdFromHeader() {
    ServletRequestAttributes attributes =
        (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
    if (attributes != null) {
      String userId = attributes.getRequest().getHeader("X-User-Id");
      if (userId == null || userId.trim().isEmpty()) {
        throw new IllegalArgumentException("X-User-Id header is required");
      }
      return userId;
    }
    throw new IllegalArgumentException("Request context is not available");
  }
}

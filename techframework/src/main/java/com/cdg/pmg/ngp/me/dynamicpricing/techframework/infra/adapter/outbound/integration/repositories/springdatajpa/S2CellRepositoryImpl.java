package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.S2CellRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.S2CellEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.mappers.S2CellEntityMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.S2CellJPARepository;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

@AllArgsConstructor
@ServiceComponent
@Slf4j
public class S2CellRepositoryImpl implements S2CellRepository {
  private final S2CellJPARepository s2CellRepository;
  private final S2CellEntityMapper mapper;

  @Override
  @Transactional(readOnly = true)
  public List<S2CellEntity> getAllS2Cell() {
    return s2CellRepository.findAll().stream().map(mapper::mapS2CellJpaToEntity).toList();
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.custom;

import java.time.Instant;
import java.time.LocalDate;

public interface FareTypeConfigJPACustom {
  Integer getFareTypeId();

  String getFareType();

  String getDay();

  String getHour();

  Integer getVehGrp();

  Double getFixedValue();

  Double getPercentValue();

  Double getDefaultFixed();

  Double getDefaultPercent();

  LocalDate getStartDate();

  LocalDate getEndDate();

  Integer getDayPriority();

  Instant getCreatedDate();

  Instant getUpdatedDate();

  String getCreatedBy();

  String getUpdatedBy();
}

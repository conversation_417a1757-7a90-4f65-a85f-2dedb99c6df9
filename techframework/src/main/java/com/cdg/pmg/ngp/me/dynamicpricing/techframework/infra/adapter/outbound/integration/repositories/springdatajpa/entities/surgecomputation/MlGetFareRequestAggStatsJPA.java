package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation;

import jakarta.persistence.*;
import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/** Table to store get fare count information for machine learning */
@Entity
@Table(name = "ml_get_fare_request_agg_stats")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class MlGetFareRequestAggStatsJPA {

  @Id
  @GeneratedValue(
      strategy = GenerationType.SEQUENCE,
      generator = "mlGetFareRequestAggStatsSequenceGenerator")
  @SequenceGenerator(
      name = "mlGetFareRequestAggStatsSequenceGenerator",
      sequenceName = "SEQ_ml_get_fare_request_agg_stats__id",
      allocationSize = 1)
  private Long id;

  @Column(name = "start_timestamp", nullable = false)
  private Instant startTimestamp;

  @Column(name = "end_timestamp", nullable = false)
  private Instant endTimestamp;

  @Column(name = "area_type", nullable = false, length = 12)
  private String areaType;

  @Column(name = "pickup_region_id")
  private Long pickupRegionId;

  @Column(name = "region_version", length = 50)
  private String regionVersion;

  @Column(name = "model_id")
  private Long modelId;

  @Column(name = "get_fare_count")
  private Integer getFareCount;

  @Column(name = "create_timestamp", nullable = false)
  private Instant createTimestamp;
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.aop.exceptions;

import java.io.Serial;
import org.springframework.http.HttpStatus;

/** The type Resource not found exception. */
public class ResourceNotFoundException extends BaseException {

  @Serial private static final long serialVersionUID = -3913425394767970473L;
  private static final String RESOURCE_NOT_FOUND_EXCEPTION_MESSAGE = "Resource not found";
  private static final HttpStatus HTTP_STATUS = HttpStatus.NOT_FOUND;

  /**
   * Instantiates a new Resource not found exception.
   *
   * @param message the message
   * @param errorCode the error code
   */
  public ResourceNotFoundException(final String message, final Long errorCode) {
    super(RESOURCE_NOT_FOUND_EXCEPTION_MESSAGE, message, errorCode, HTTP_STATUS);
  }

  /**
   * Instantiates a new Resource not found exception.
   *
   * @param message the message
   * @param errorCode the error code
   * @param arguments the arguments
   */
  public ResourceNotFoundException(
      final String message, final Long errorCode, final Object... arguments) {
    super(RESOURCE_NOT_FOUND_EXCEPTION_MESSAGE, message, errorCode, HTTP_STATUS, arguments);
  }
}

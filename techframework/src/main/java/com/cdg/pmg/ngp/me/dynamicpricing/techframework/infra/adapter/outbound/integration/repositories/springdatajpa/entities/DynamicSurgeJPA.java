package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "dynp_surges")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DynamicSurgeJPA {

  /** Identifies the zone to which the dynamic-price-surge is applicable to. */
  @Id
  @Column(name = "zone_id")
  private String zoneId;

  /** The dynamic-surge-factor. Negative values indicates de-surging. */
  @Column(name = "surge")
  private Integer surge;

  /**
   * Represents the minimum value that the dynamic surge factor can decrease to. This value acts as
   * a floor for surge adjustments in pricing calculations.
   */
  @Column(name = "surge_low")
  private Integer surgeLow;

  /**
   * Represents the upper limit for the dynamic surge factor. This value defines the maximum value
   * that the surge factor can increase to during pricing calculations.
   */
  @Column(name = "surge_high")
  private Integer surgeHigh;

  /**
   * Represents the most recent demand value in the zone, measured within a 30-minute window. This
   * field captures the most up-to-date demand statistics for dynamic pricing calculations.
   */
  @Column(name = "demand_recent")
  private Integer demandRecent;

  /**
   * Represents the historical demand value in the zone. This field stores the demand data from the
   * previous relevant time period, which can be used for dynamic pricing analysis and comparison
   * with current or predicted demand.
   */
  @Column(name = "demand_previous")
  private Integer demandPrevious;

  /**
   * Represents the predicted demand value in the zone. This field provides an estimate of future
   * demand based on historical trends, recent demand data, or other predictive models.
   */
  @Column(name = "demand_predicted")
  private Integer demandPredicted;

  /**
   * Represents the supply value in a specific zone used for dynamic pricing calculations. The
   * supply refers to the available vehicles in the zone that can meet the demand. This value is
   * utilized in determining surge factors and ensuring an optimal balance between supply and
   * demand.
   */
  @Column(name = "supply")
  private Integer supply;

  /**
   * Represents the previous dynamic surge factor for a specific zone. This value is used in dynamic
   * pricing calculations to compare the most recent surge adjustment with prior surge values.
   */
  @Column(name = "prev_surge")
  private Integer prevSurge;

  /**
   * Represents the timestamp of the last update for the entity. This field is used to track when
   * the entity was last modified, enabling mechanisms for auditing, synchronization, or monitoring
   * changes over time.
   */
  @Column(name = "last_upd_dt")
  private Timestamp lastUpdDt;

  /**
   * Represents the excess demand in a specific zone for dynamic pricing calculations. This field
   * indicates the surplus of demand relative to the available supply, which can influence pricing
   * adjustments such as surge pricing.
   */
  @Column(name = "excess_demand")
  private Integer excessDemand;

  /** Batch key for the calculation of sub-zone-mappings */
  @Column(name = "batch_key")
  private Integer batchKey;

  /**
   * Represents the predicted demand value over a 15-minute interval in a specific zone. This field
   * provides a short-term estimate of future demand, based on available data such as historical
   * trends, current demand, or predictive models, and is used in dynamic pricing calculations to
   * optimize pricing strategies.
   */
  @Column(name = "demand_predicted_15")
  private Integer demandPredicted15;

  /**
   * Represents the pricing model associated with a specific zone. This field indicates the model or
   * strategy applied to calculate the prices dynamically within a given zone.
   */
  @Column(name = "zone_price_model")
  private String zonePriceModel;

  /**
   * Represents the unmet demand in a specific zone for a 15-minute interval. This field indicates
   * the demand that could not be satisfied during the specified time period and is used in dynamic
   * pricing calculations to analyze demand-supply gaps.
   */
  @Column(name = "unmet_m2")
  private Integer unmet15;

  /**
   * Represents the number of unmet instances or requirements as recorded in the previous 15-minute
   * interval. This variable is mapped to the database column "unmet_m1". The value is stored as an
   * Integer and can be null if there is no recorded data for the specified interval.
   */
  @Column(name = "unmet_m1")
  private Integer unmetPrev15;

  /**
   * Represents the demand value for a specific period (15th minute of an hour or similar). The
   * value is stored as an Integer and corresponds to the "q_m1" column in the database. This
   * variable is typically used to track or store demand-related data for analysis or computation.
   */
  @Column(name = "q_m1")
  private Integer demand15;
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.validator;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.annotation.CustomNotEmpty;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.aop.exceptions.FieldValidationException;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.List;
import lombok.Getter;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;

/** The type Custom not empty validator. */
@Getter
public class CustomNotEmptyValidator implements ConstraintValidator<CustomNotEmpty, Object> {

  private String message;

  private Long errorCode;

  private HttpStatus httpStatus;

  @Override
  public void initialize(CustomNotEmpty constraintAnnotation) {
    this.message = constraintAnnotation.message();
    this.errorCode = constraintAnnotation.errorCode();
    this.httpStatus = constraintAnnotation.httpStatus();
  }

  @Override
  public boolean isValid(Object value, ConstraintValidatorContext context) {
    if (null == value
        || (value instanceof List<?> list && CollectionUtils.isEmpty(list))
        || (value instanceof String string && "".equalsIgnoreCase(string))) {
      throw new FieldValidationException(message, errorCode, httpStatus);
    }
    return true;
  }
}

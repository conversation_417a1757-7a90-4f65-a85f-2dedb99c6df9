package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.controllers;

import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.UPDATE_FARE_TYPE_CONFIG_PARSE_ERROR;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.FareTypeConfigService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.PricingRangeConfigService;
import com.cdg.pmg.ngp.me.dynamicpricing.commands.FareTypeConfigCommand;
import com.cdg.pmg.ngp.me.dynamicpricing.commands.PricingRangeConfigCommand;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FareTypeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.PricingRangeConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.DomainException;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.api.ParameterConfigControllerApi;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper.FareTypeConfigMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper.PricingRangeConfigMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.FareTypeConfigRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.FareTypeConfigResponseData;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.FareTypeConfigsResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.PricingRangeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.PricingRangeConfigRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.PricingRangeConfigsResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.UpdateSuccessResponse;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@Slf4j
public class ParameterConfigController implements ParameterConfigControllerApi {

  private final FareTypeConfigService fareTypeConfigService;
  private final FareTypeConfigMapper fareTypeConfigMapper;
  private final PricingRangeConfigService pricingRangeConfigService;
  private final PricingRangeConfigMapper pricingRangeConfigMapper;

  @Override
  public ResponseEntity<FareTypeConfigsResponse> getParamConfigsByListFareType(
      List<String> listFareType) {
    log.info("Start get param config by list fare type");
    Set<String> setFareType = new HashSet<>(listFareType);
    List<FareTypeConfig> fareTypeConfigs =
        fareTypeConfigService.getParamConfigsByListFareType(setFareType);

    List<com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.FareTypeConfig> data =
        fareTypeConfigMapper.mapFareTypeConfigDomainToFareTypeConfigTech(fareTypeConfigs);

    FareTypeConfigsResponse response = new FareTypeConfigsResponse();
    response.setData(data);

    log.info("End get param config by list fare type");
    return ResponseEntity.ok(response);
  }

  @Override
  public ResponseEntity<UpdateSuccessResponse> insertOrUpdateFareTypeConfig(
      FareTypeConfigRequest fareTypeConfigRequest) {
    log.info("Start insert or update fare type config");
    FareTypeConfigCommand command =
        fareTypeConfigMapper.mapFareTypeConfigRequestToFareTypeConfigCommand(fareTypeConfigRequest);

    UpdateSuccessResponse response = new UpdateSuccessResponse();
    try {
      FareTypeConfig fareTypeConfig = fareTypeConfigService.insertOrUpdateFareTypeConfig(command);
      log.info("fareTypeConfig: {}", fareTypeConfig);
      FareTypeConfigResponseData fareTypeConfigResponseData =
          fareTypeConfigMapper.mapFareTypeConfigToFareTypeConfigResponseData(fareTypeConfig);
      List<FareTypeConfigResponseData> data = new ArrayList<>();
      data.add(fareTypeConfigResponseData);
      response.setData(data);
    } catch (ParseException e) {
      log.error("ParseException in updateFareTypeConfig", e);
      throw new DomainException(
          UPDATE_FARE_TYPE_CONFIG_PARSE_ERROR.getMessage(),
          UPDATE_FARE_TYPE_CONFIG_PARSE_ERROR.getErrorCode());
    }

    log.info("End create or update fare type config");
    return ResponseEntity.ok(response);
  }

  @Override
  public ResponseEntity<PricingRangeConfigsResponse> getPricingRangeConfigs() {
    log.info("Start get pricing range configs");
    List<PricingRangeConfigEntity> pricingRangeConfigEntity =
        pricingRangeConfigService.getPricingRangeConfigs();

    List<PricingRangeConfig> data =
        pricingRangeConfigEntity.stream()
            .map(pricingRangeConfigMapper::mapPricingRangeConfigEntityToPricingRangeConfig)
            .toList();

    PricingRangeConfigsResponse response = new PricingRangeConfigsResponse();
    response.setData(data);
    log.info("End get pricing range configs");
    return ResponseEntity.ok(response);
  }

  @Override
  public ResponseEntity<PricingRangeConfigsResponse> insertOrUpdatePricingRangeConfig(
      PricingRangeConfigRequest pricingRangeConfigRequest) {
    log.info("Start insert or update pricing range config");
    PricingRangeConfigCommand command =
        pricingRangeConfigMapper.mapPricingRangeConfigRequestToPricingRangeConfigCommand(
            pricingRangeConfigRequest);

    PricingRangeConfigEntity pricingRangeConfigEntity =
        pricingRangeConfigService.insertOrUpdatePricingRangeConfig(command);

    List<PricingRangeConfig> data = new ArrayList<>();
    data.add(
        pricingRangeConfigMapper.mapPricingRangeConfigEntityToPricingRangeConfig(
            pricingRangeConfigEntity));

    PricingRangeConfigsResponse response = new PricingRangeConfigsResponse();
    response.setData(data);
    log.info("End insert or update pricing range config");
    return ResponseEntity.ok(response);
  }
}

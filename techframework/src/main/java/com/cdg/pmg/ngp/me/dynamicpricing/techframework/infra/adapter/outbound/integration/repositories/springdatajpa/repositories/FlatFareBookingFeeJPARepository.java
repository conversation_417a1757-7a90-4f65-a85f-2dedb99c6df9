package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.TariffFareLevyPremrefJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.custom.FlatFareBookingFeeJPACustom;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface FlatFareBookingFeeJPARepository
    extends JpaRepository<TariffFareLevyPremrefJPA, Integer> {
  @Query(
      value =
          "SELECT joinTable.PRODUCT_ID as productId, "
              + "joinTable.VEH_TYPE_ID as vehTypeId, "
              + "joinTable.TARIFF_TYPE_CODE as tariffTypeCode, "
              + "joinTable.FARE_AMT as fareAmt, "
              + "joinTable.LEVY_AMT as levyAmt, "
              + "to_char(to_date(joinTable.START_TIME, 'hh24:mi:ss'), 'hh24:mi:ss') as startTime, "
              + "to_char(to_date(joinTable.END_TIME, 'hh24:mi:ss'), 'hh24:mi:ss') as endTime, "
              + "nullif(Ph_Applicable, '') "
              + "|| ',' || "
              + "nullif(VALID_MON, '') "
              + "|| ',' || "
              + "nullif(VALID_TUE, '') "
              + "|| ',' || "
              + "nullif(VALID_WED, '') "
              + "|| ',' || "
              + "nullif(VALID_THU, '') "
              + "|| ',' || "
              + "nullif(VALID_FRI, '') "
              + "|| ',' || "
              + "nullif(VALID_SAT, '') "
              + "|| ',' || "
              + "nullif(VALID_SUN, '') "
              + "AS applicableDays "
              + "FROM ( "
              + "SELECT PRODUCT_ID, "
              + "VEH_TYPE_ID, "
              + "TARIFF_TYPE_CODE, "
              + "FARE_AMT, "
              + "LEVY_AMT, "
              + "CASE WHEN tc.time_code = 'OTHER' then '' "
              + "ELSE START_TIME END AS START_TIME, "
              + "CASE WHEN tc.time_code = 'OTHER' then '' "
              + "ELSE END_TIME END AS END_TIME, "
              + "CASE WHEN tc.ph_applicable = 'Y' then 'HOL' "
              + "END AS PH_APPLICABLE, "
              + "CASE WHEN tc.valid_on_monday = 'Y' then 'MON' "
              + "END AS VALID_MON, "
              + "CASE WHEN tc.valid_on_tuesday = 'Y' then 'TUE' "
              + "END AS VALID_TUE, "
              + "CASE WHEN tc.valid_on_wednesday = 'Y' then 'WED' "
              + "END AS VALID_WED, "
              + "CASE WHEN tc.valid_on_thursday = 'Y' then 'THU' "
              + "END AS VALID_THU, "
              + "CASE WHEN tc.valid_on_friday = 'Y' then 'FRI' "
              + "END AS VALID_FRI, "
              + "CASE WHEN tc.valid_on_saturday = 'Y' then 'SAT' "
              + "END AS VALID_SAT, "
              + "CASE WHEN tc.valid_on_sunday = 'Y' then 'SUN' "
              + "END AS VALID_SUN "
              + "FROM tariff_fare_levy_premref tfl "
              + "JOIN tariff_revision tr ON tfl.revision_id = tr.revision_id "
              + "LEFT JOIN company_surcharge_time_code tc ON tfl.applicable_time_period = tc.time_code "
              + "WHERE tfl.revision_id = "
              + "CAST(( "
              + "SELECT sp.param_value "
              + "FROM system_param sp "
              + "WHERE param_key = 'FARE_REVISION_ID_PUBL_FLPR') AS INTEGER) "
              + "AND tr.end_dt > current_date "
              + "AND tr.revision_type = 'FLPR' "
              + "AND tariff_type_code IN ('PDT', 'CBKC', 'ABKC') "
              + "AND account_id = 'PUBL' "
              + "AND PRODUCT_ID in ('GUD-001', 'GUA-001', 'OWT-001', 'STD001', 'FLAT-001')) AS joinTable ",
      nativeQuery = true)
  @Transactional(readOnly = true)
  List<FlatFareBookingFeeJPACustom> getFlatFareBookingFeeConfigs();
}

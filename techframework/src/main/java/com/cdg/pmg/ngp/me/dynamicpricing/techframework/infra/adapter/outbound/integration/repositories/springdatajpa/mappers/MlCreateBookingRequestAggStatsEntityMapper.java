package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.mappers;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.MlCreateBookingRequestAggStatsEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.MlCreateBookingRequestAggStatsJPA;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * Mapper for converting between MlCreateBookingRequestAggStatsJPA and
 * MlCreateBookingRequestAggStatsEntity.
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MlCreateBookingRequestAggStatsEntityMapper {

  /**
   * Convert a JPA entity to a domain entity.
   *
   * @param jpa the JPA entity to convert
   * @return the corresponding domain entity
   */
  MlCreateBookingRequestAggStatsEntity mapJpaToEntity(MlCreateBookingRequestAggStatsJPA jpa);

  /**
   * Convert a domain entity to a JPA entity.
   *
   * @param entity the domain entity to convert
   * @return the corresponding JPA entity
   */
  MlCreateBookingRequestAggStatsJPA mapEntityToJpa(MlCreateBookingRequestAggStatsEntity entity);

  /**
   * Convert a list of JPA entities to a list of domain entities.
   *
   * @param jpaList the list of JPA entities to convert
   * @return the corresponding list of domain entities
   */
  List<MlCreateBookingRequestAggStatsEntity> mapJpaToEntity(
      List<MlCreateBookingRequestAggStatsJPA> jpaList);

  /**
   * Convert a list of domain entities to a list of JPA entities.
   *
   * @param entityList the list of domain entities to convert
   * @return the corresponding list of JPA entities
   */
  List<MlCreateBookingRequestAggStatsJPA> mapEntityToJpa(
      List<MlCreateBookingRequestAggStatsEntity> entityList);
}

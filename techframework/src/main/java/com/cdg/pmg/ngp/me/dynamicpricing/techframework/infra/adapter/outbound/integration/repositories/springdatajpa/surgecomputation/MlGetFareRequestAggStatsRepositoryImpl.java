package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.surgecomputation.MlGetFareRequestAggStatsRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.MlGetFareRequestAggStatsEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.RegionGetFareCountEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.MlGetFareRequestAggStatsJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.projection.RegionGetFareCountProjection;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.mappers.MlGetFareRequestAggStatsEntityMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.surgecomputation.MlGetFareRequestAggStatsJPARepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;

/** Implementation of {@link MlGetFareRequestAggStatsRepository} using Spring Data JPA. */
@Component
@Primary
@RequiredArgsConstructor
@Slf4j
public class MlGetFareRequestAggStatsRepositoryImpl implements MlGetFareRequestAggStatsRepository {

  private final MlGetFareRequestAggStatsJPARepository jpaRepository;
  private final MlGetFareRequestAggStatsEntityMapper mapper;

  @Override
  @Transactional
  public void saveAll(List<MlGetFareRequestAggStatsEntity> entities) {
    List<MlGetFareRequestAggStatsJPA> jpaEntities = mapper.mapEntityToJpa(entities);
    jpaRepository.saveAll(jpaEntities);
  }

  @Override
  @Transactional(readOnly = true)
  public List<RegionGetFareCountEntity> listAllByTimeRange(final Instant time) {
    List<RegionGetFareCountProjection> jpaEntities = jpaRepository.listAllByTimeRange(time);
    return mapper.mapProjectionToEntity(jpaEntities);
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs.sftp;

import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.ERROR_TO_PARSING_JSON;

import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.InternalServerException;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs.SftpConfigParams;
import java.io.File;
import java.nio.charset.StandardCharsets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.sshd.sftp.client.SftpClient;
import org.springframework.context.annotation.Bean;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.expression.common.LiteralExpression;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.integration.file.remote.session.CachingSessionFactory;
import org.springframework.integration.file.remote.session.SessionFactory;
import org.springframework.integration.sftp.outbound.SftpMessageHandler;
import org.springframework.integration.sftp.session.DefaultSftpSessionFactory;
import org.springframework.integration.sftp.session.SftpRemoteFileTemplate;
import org.springframework.messaging.MessageHandler;
import org.springframework.stereotype.Component;

/**
 * Configuration class for setting up SFTP (Secure File Transfer Protocol) upload functionality.
 * This class is responsible for configuring and initializing components required for SFTP file
 * transfers. It uses {@link SftpConfigParams} for the configuration parameters like host, private
 * key, and user credentials.
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class UploadConfiguration {

  private final SftpConfigParams sftpConfigParams;

  private static final String ERROR_CREATE_SESSION_FACTORY = "Error Parsing json to object";

  /**
   * Configuration Session Factory.
   *
   * @return {@link SessionFactory}
   */
  @Bean
  public SessionFactory<SftpClient.DirEntry> defaultSessionFactory() {
    try {
      log.info("host: " + sftpConfigParams.getHost());
      log.info("privateKey: " + sftpConfigParams.getPrivateKey());
      log.info("user: " + sftpConfigParams.getUser());
      final DefaultSftpSessionFactory sftpSessionFactory = new DefaultSftpSessionFactory(true);
      sftpSessionFactory.setHost(sftpConfigParams.getHost());
      // Set Private Key
      byte[] privateKeyBytes = sftpConfigParams.getPrivateKey().getBytes(StandardCharsets.UTF_8);
      sftpSessionFactory.setPrivateKey(new ByteArrayResource(privateKeyBytes));
      sftpSessionFactory.setUser(sftpConfigParams.getUser());
      sftpSessionFactory.setAllowUnknownKeys(true);

      return new CachingSessionFactory<>(sftpSessionFactory);
    } catch (Exception e) {
      log.error("Unable to create defaultSessionFactory ", e);
      throw new InternalServerException(
          ERROR_TO_PARSING_JSON.getMessage(), ERROR_TO_PARSING_JSON.getErrorCode());
    }
  }

  /**
   * Configures a {@link MessageHandler} for uploading files to an SFTP server with file
   * replacement. If a file with the same name exists on the server, it will be replaced. This
   * handler uses {@link SftpRemoteFileTemplate} for remote file operations. It sets the file
   * existence mode to REPLACE, indicating that existing files will be overwritten. The handler is
   * triggered by messages sent to the 'toSftpChannel' input channel.
   *
   * @return A configured {@link MessageHandler} for handling file uploads with replacement to the
   *     SFTP server. It throws {@link IllegalArgumentException} for payloads that are not of type
   *     {@link File}.
   */
  @Bean
  @ServiceActivator(inputChannel = "toSftpChannel")
  public MessageHandler createFileReplacingUploadHandler() {

    SftpMessageHandler handler = new SftpMessageHandler(defaultSessionFactory());
    handler.setRemoteDirectoryExpression(new LiteralExpression(sftpConfigParams.getSftpDir()));
    handler.setFileNameGenerator(
        message -> {
          if (message.getPayload() instanceof File) {
            return ((File) message.getPayload()).getName();
          } else {
            throw new IllegalArgumentException("Invalid file format");
          }
        });
    return handler;
  }
}

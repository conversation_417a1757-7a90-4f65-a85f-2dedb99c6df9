package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.FlatFareBreakdownDetailJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.custom.FareBreakdownJPACustom;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

/** The interface Flat fare breakdown jpa repository. */
public interface FlatFareBreakdownJPARepository
    extends JpaRepository<FlatFareBreakdownDetailJPA, String> {

  /**
   * Find by trip id flat fare breakdown detail jpa.
   *
   * @param tripId the trip id
   * @return the flat fare breakdown detail jpa
   */
  @Transactional(readOnly = true)
  List<FlatFareBreakdownDetailJPA> findByTripId(String tripId);

  /**
   * Find by booking id flat fare breakdown detail jpa.
   *
   * @param bookingId the booking id
   * @return the flat fare breakdown detail jpa
   */
  @Transactional(readOnly = true)
  FlatFareBreakdownDetailJPA findByBookingId(String bookingId);

  /**
   * Find by tripId, tripId, bookingId.
   *
   * @param fareId the fare id
   * @param tripId the trip id
   * @param bookingId the booking id
   * @return the flat fare breakdown
   */
  @Query(
      value =
          "SELECT "
              + "fbd.fare_id AS fareId, "
              + "fbd.booking_id AS bookingId, "
              + "fbd.trip_id AS tripId, "
              + "fbd.flatdown_rate AS flagDownRate, "
              + "fbd.wait_time_fare AS waitTimeFare, "
              + "fbd.routing_distance AS routingDistance, "
              + "fbd.ett AS ett, "
              + "fbd.dp_final_fare AS dpFinalFare, "
              + "fbd.total_fare AS totalFare, "
              + "fbd.estimate_fare_lf AS estimatedFareLF, "
              + "fbd.estimate_fare_rt AS estimatedFareRT, "
              + "fbd.flat_platform_fee_id AS flatPlatformFeeId, "
              + "fbd.flat_platform_fee AS flatPlatformFee, "
              + "fbd.meter_platform_fee_id AS meterPlatformFeeId, "
              + "fbd.meter_platform_fee_lower AS meterPlatformFeeLower, "
              + "fbd.meter_platform_fee_upper AS meterPlatformFeeUpper, "
              + "fbd.updated_by AS updatedBy, "
              + "fbd.metered_base_fare AS meteredBaseFare "
              + "FROM FLAT_FARE_BREAKDOWN_DETAIL fbd "
              + "WHERE (:fareId IS NULL OR fbd.fare_id = :fareId) "
              + "AND (:tripId IS NULL OR fbd.trip_id = :tripId) "
              + "AND (:bookingId IS NULL OR fbd.booking_id = :bookingId)",
      nativeQuery = true)
  @Transactional(readOnly = true)
  List<FareBreakdownJPACustom> searchFareBreakdown(String fareId, String tripId, String bookingId);
}

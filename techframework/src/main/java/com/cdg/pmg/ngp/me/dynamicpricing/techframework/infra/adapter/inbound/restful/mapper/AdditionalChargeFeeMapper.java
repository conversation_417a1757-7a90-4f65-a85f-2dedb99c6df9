package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.additionalcharge.AdditionalChargeFeeData;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.AdditionalChargeFee;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class AdditionalChargeFeeMapper {

  private AdditionalChargeFeeMapper() {}

  public static AdditionalChargeFee mapToAdditionalChargeFeeData(
      AdditionalChargeFeeData additionalChargeFeeData) {
    if (additionalChargeFeeData == null) {
      return null;
    }

    AdditionalChargeFee additionalChargeFee = new AdditionalChargeFee();

    additionalChargeFee.setChargeId(additionalChargeFeeData.getChargeId());
    additionalChargeFee.setChargeType(additionalChargeFeeData.getChargeType());
    additionalChargeFee.setChargeAmt(additionalChargeFeeData.getChargeAmt());
    additionalChargeFee.setChargeThreshold(additionalChargeFeeData.getChargeThreshold());
    additionalChargeFee.setChargeUpperLimit(additionalChargeFeeData.getChargeUpperLimit());
    additionalChargeFee.setChargeLowerLimit(additionalChargeFeeData.getChargeLowerLimit());

    return additionalChargeFee;
  }

  public static List<AdditionalChargeFee> mapToAdditionalChargeFeeDataList(
      List<AdditionalChargeFeeData> additionalChargeFeeDataList) {
    if (additionalChargeFeeDataList == null) {
      return null;
    }

    List<AdditionalChargeFee> list =
        new ArrayList<AdditionalChargeFee>(additionalChargeFeeDataList.size());
    for (AdditionalChargeFeeData additionalChargeFeeData : additionalChargeFeeDataList) {
      list.add(mapToAdditionalChargeFeeData(additionalChargeFeeData));
    }

    return list;
  }
}

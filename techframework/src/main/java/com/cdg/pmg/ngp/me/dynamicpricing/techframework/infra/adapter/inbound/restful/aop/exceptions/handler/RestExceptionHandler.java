package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.aop.exceptions.handler;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.Response;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.BadRequestException;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.DomainException;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.InternalServerException;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.NotFoundException;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.TimeoutException;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.aop.exceptions.BaseException;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.aop.exceptions.FieldValidationException;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.language.ResourceLanguage;
import io.opentelemetry.api.trace.Span;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.ConstraintViolationException;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

/** The type Rest exception handler. */
@ControllerAdvice
@Slf4j
@RequiredArgsConstructor
public class RestExceptionHandler {
  private final ResourceLanguage language;

  /**
   * Handle exception response.
   *
   * @param exception the exception
   * @return the response
   */
  @ResponseBody
  @ResponseStatus(HttpStatus.BAD_REQUEST)
  @ExceptionHandler(value = FieldValidationException.class)
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = "400",
            description = "Invalid payload",
            content = {
              @Content(
                  mediaType = "application/json",
                  schema = @Schema(implementation = Response.class))
            }),
      })
  Response<Object> handleException(final FieldValidationException exception) {
    log.error(getRequestId(), exception);
    var response = Response.fail(null, exception.getMessage(), exception.getErrorCode());
    response.setTraceId(Span.current().getSpanContext().getTraceId());
    return response;
  }

  /**
   * Handle exception response.
   *
   * @param exception the exception
   * @return the response
   */
  @ResponseBody
  @ResponseStatus(HttpStatus.BAD_REQUEST)
  @ExceptionHandler(value = IllegalArgumentException.class)
  Response<Object> handleException(final IllegalArgumentException exception) {
    log.error(getRequestId(), exception);
    var response = Response.fail(null, exception.getMessage(), null);
    response.setTraceId(Span.current().getSpanContext().getTraceId());
    return response;
  }

  /**
   * Handle exception response.
   *
   * @param exception the exception
   * @return the response
   */
  @ResponseBody
  @ResponseStatus(HttpStatus.BAD_REQUEST)
  @ExceptionHandler(value = ConstraintViolationException.class)
  Response<Object> handleException(final ConstraintViolationException exception) {
    log.error(getRequestId(), exception);
    var response = Response.fail(null, exception.getMessage(), null);
    response.setTraceId(Span.current().getSpanContext().getTraceId());
    return response;
  }

  /**
   * Handle exception response entity.
   *
   * @param exception the exception
   * @return the response entity
   */
  @ResponseBody
  @ExceptionHandler(value = BaseException.class)
  public ResponseEntity<Response<Object>> handleException(final BaseException exception) {
    log.error(getRequestId(), exception);
    var response =
        Response.fail(
            language.get(exception.getMessage(), exception.getArguments()),
            exception.getErrorCode());
    response.setTraceId(Span.current().getSpanContext().getTraceId());
    return ResponseEntity.status(exception.getHttpStatus()).body(response);
  }

  /**
   * Handle exception response entity.
   *
   * @param exception the exception
   * @return the response entity
   */
  @ResponseBody
  @ExceptionHandler(value = DomainException.class)
  public ResponseEntity<Response<Object>> handleException(final DomainException exception) {
    log.error(getRequestId(), exception);
    var response =
        Response.fail(
            language.get(exception.getMessage(), exception.getArguments()),
            exception.getErrorCode());
    response.setTraceId(Span.current().getSpanContext().getTraceId());
    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
  }

  /**
   * Handle exception response entity.
   *
   * @param exception the exception
   * @return the response entity
   */
  @ResponseBody
  @ExceptionHandler(value = InternalServerException.class)
  public ResponseEntity<Response<Object>> handleException(final InternalServerException exception) {
    log.error(getRequestId(), exception);
    var response =
        Response.fail(
            language.get(exception.getMessage(), exception.getArguments()),
            exception.getErrorCode());
    response.setTraceId(Span.current().getSpanContext().getTraceId());
    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
  }

  /**
   * Handle exception response entity.
   *
   * @param exception the exception
   * @return the response entity
   */
  @ResponseBody
  @ExceptionHandler(value = NotFoundException.class)
  public ResponseEntity<Response<Object>> handleException(final NotFoundException exception) {
    log.error(getRequestId(), exception);
    var response =
        Response.fail(
            language.get(exception.getMessage(), exception.getArguments()),
            exception.getErrorCode());
    response.setTraceId(Span.current().getSpanContext().getTraceId());
    return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
  }

  /**
   * Handle exception response entity.
   *
   * @param exception the exception
   * @return the response entity
   */
  @ResponseBody
  @ExceptionHandler(value = BadRequestException.class)
  public ResponseEntity<Response<Object>> handleException(final BadRequestException exception) {
    log.error(getRequestId(), exception);
    var response =
        Response.fail(
            language.get(exception.getMessage(), exception.getArguments()),
            exception.getErrorCode());
    response.setTraceId(Span.current().getSpanContext().getTraceId());
    return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
  }

  /**
   * Handle exception response entity.
   *
   * @param exception the exception
   * @return the response entity
   */
  @ResponseBody
  @ExceptionHandler(value = TimeoutException.class)
  public ResponseEntity<Response<Object>> handleException(final TimeoutException exception) {
    log.error(getRequestId(), exception);
    var response =
        Response.fail(
            language.get(exception.getMessage(), exception.getArguments()),
            exception.getErrorCode());
    response.setTraceId(Span.current().getSpanContext().getTraceId());
    return ResponseEntity.status(HttpStatus.REQUEST_TIMEOUT).body(response);
  }

  /**
   * Handle exception base response.
   *
   * @param exception the exception
   * @return the base response
   */
  @ResponseBody
  @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
  @ExceptionHandler(value = Throwable.class)
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = {
              @Content(
                  mediaType = "application/json",
                  schema = @Schema(implementation = Response.class))
            })
      })
  Response<Object> handleException(final Throwable exception) {
    log.error(getRequestId(), exception);
    Response<Object> response = Response.fail(language.get(exception.getMessage()), null);
    response.setTraceId(Span.current().getSpanContext().getTraceId());
    return response;
  }

  /**
   * Handle exception response.
   *
   * @param exception the exception
   * @return the response
   */
  @ResponseBody
  @ResponseStatus(HttpStatus.BAD_REQUEST)
  @ExceptionHandler(value = MethodArgumentNotValidException.class)
  public Response<List<FieldValidationException.FieldError>> handleException(
      MethodArgumentNotValidException exception) {
    log.error(getRequestId(), exception);

    var violations =
        exception.getFieldErrors().stream()
            .map(
                fieldError ->
                    FieldValidationException.FieldError.builder()
                        .message(fieldError.getDefaultMessage())
                        .field(fieldError.getField())
                        .build())
            .toList();

    var response = Response.fail(violations, exception.getMessage(), null);
    response.setTraceId(Span.current().getSpanContext().getTraceId());
    return response;
  }

  /**
   * Handle exception response.
   *
   * @param exception the exception
   * @return the response
   */
  @ResponseBody
  @ResponseStatus(HttpStatus.BAD_REQUEST)
  @ExceptionHandler(value = BindException.class)
  public Response<List<FieldValidationException.FieldError>> handleException(
      BindException exception) {
    log.error(getRequestId(), exception);
    var violations =
        exception.getFieldErrors().stream()
            .map(
                fieldError ->
                    FieldValidationException.FieldError.of(
                        fieldError.getField(),
                        fieldError.getDefaultMessage(),
                        fieldError.getArguments()))
            .toList();

    var response = Response.fail(violations, "fail", null);
    response.setTraceId(Span.current().getSpanContext().getTraceId());
    return response;
  }

  private static String getRequestId() {
    return MDC.get("request.id");
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.StaticRegionBasedConfigurationJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.projection.StaticBasedConfigurationVersionProjection;
import java.time.Instant;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * Repository interface for StaticRegionBasedConfigurationJPA entity. Provides methods for CRUD
 * operations on static region-based configurations. Maps to the
 * surge_computation_static_region_based_configurations table.
 */
@Repository
public interface StaticRegionBasedConfigurationJPARepository
    extends JpaRepository<StaticRegionBasedConfigurationJPA, Long> {

  /**
   * Find all static region-based configurations that are effective at a specific time.
   *
   * @param modelId the model id from surge_computation_models
   * @param effectiveTime the time at which the configurations should be effective
   * @return a list of static region-based configurations
   */
  @Query(
      value =
          """
                    SELECT
                        src.*
                    FROM surge_computation_models scm
                    CROSS JOIN LATERAL jsonb_array_elements(scm.request_fields_mappings) as mapping(value)
                    INNER JOIN surge_computation_static_region_based_configurations src
                        ON src.name = mapping.value->>'mappingConfigurationName'
                    WHERE scm.id = :modelId
                        AND mapping.value->>'mappingType' = 'STATIC_REGION_BASED_CONFIGURATION'
                        AND src.effective_from <= :effectiveTime
                        AND (src.effective_to IS NULL OR src.effective_to > :effectiveTime)
              """,
      nativeQuery = true)
  List<StaticRegionBasedConfigurationJPA> findByEffectiveTimeRange(
      @Param("modelId") Long modelId, @Param("effectiveTime") Instant effectiveTime);

  /**
   * Find the most recent effective_from value by the given effectiveTime.
   *
   * @param modelId the model id from surge_computation_models
   * @param effectiveTime the time at which the configurations should be effective
   * @return the most recent effective_from value
   */
  @Query(
      value =
          """
            SELECT DISTINCT
                src.effective_from
            FROM surge_computation_models scm
            CROSS JOIN LATERAL jsonb_array_elements(scm.request_fields_mappings) as mapping(value)
            INNER JOIN surge_computation_static_region_based_configurations src
                ON src.name = mapping.value->>'mappingConfigurationName'
            WHERE scm.id = :modelId
                AND mapping.value->>'mappingType' = 'STATIC_REGION_BASED_CONFIGURATION'
                AND src.effective_from <= :effectiveTime
            ORDER BY src.effective_from DESC
    """,
      nativeQuery = true)
  Instant findRecentEffectiveFromByEffectiveFrom(
      @Param("modelId") Long modelId, @Param("effectiveTime") Instant effectiveTime);

  @Query(
      value =
          """
    SELECT
        version,
        effective_from as effectiveFrom,
        effective_to as effectiveTo
    FROM surge_computation_static_region_based_configurations
    WHERE name in (:configNames)
        GROUP BY version, effective_from, effective_to
        ORDER BY version DESC
    """,
      nativeQuery = true)
  List<StaticBasedConfigurationVersionProjection> findAllVersionsDescByNames(
      List<String> configNames);

  List<StaticRegionBasedConfigurationJPA> findAllByVersionAndNameIn(
      String version, List<String> names);

  List<StaticRegionBasedConfigurationJPA> findAllByNameIn(List<String> names);

  @Modifying
  @Query(
      value =
          """
        update surge_computation_static_region_based_configurations
            set updated_by = :updatedBy,
                updated_dt = :updatedDate
        where name in (:configNames)
    """,
      nativeQuery = true)
  void updateAuditFields(List<String> configNames, String updatedBy, Instant updatedDate);

  @Modifying
  @Query(
      value =
          """
        delete from surge_computation_static_region_based_configurations
        where name in (:configNames)
    """,
      nativeQuery = true)
  void deleteByNames(List<String> configNames);

  @Query(
      value =
          """
                    SELECT DISTINCT
                        src.version,
                        src.effective_from as effectiveFrom,
                        src.effective_to as effectiveTo
                    FROM surge_computation_models scm
                    CROSS JOIN LATERAL jsonb_array_elements(scm.request_fields_mappings) as mapping(value)
                    INNER JOIN surge_computation_static_region_based_configurations src
                        ON src.name = mapping.value->>'mappingConfigurationName'
                    WHERE scm.id = :modelId
                        AND mapping.value->>'mappingType' = 'STATIC_REGION_BASED_CONFIGURATION'
                        AND src.effective_from <= :effectiveTime
                        AND (src.effective_to IS NULL OR src.effective_to > :effectiveTime)
                    ORDER BY src.effective_from
              """,
      nativeQuery = true)
  StaticBasedConfigurationVersionProjection findEffectiveVersions(
      @Param("modelId") Long modelId, @Param("effectiveTime") Instant effectiveTime);
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.mapper;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.DynamicSurgesEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.DynamicSurgeNgpJPA;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper
public interface DynamicSurgeMapper {

  @Mapping(source = "demandPredicted15", target = "predictedDemand15")
  DynamicSurgesEntity toDynamicSurgesEntity(DynamicSurgeNgpJPA source);

  @Mapping(target = "demandPredicted15", source = "predictedDemand15")
  DynamicSurgeNgpJPA toDynamicSurgeNgpJPA(DynamicSurgesEntity source);

  List<DynamicSurgesEntity> toListDynamicSurgesEntities(List<DynamicSurgeNgpJPA> source);

  List<DynamicSurgeNgpJPA> toListDynamicSurgesJPAEntities(List<DynamicSurgesEntity> source);
}

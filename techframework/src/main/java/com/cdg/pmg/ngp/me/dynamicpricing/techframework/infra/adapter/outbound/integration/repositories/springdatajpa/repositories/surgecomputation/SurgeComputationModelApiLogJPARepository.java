package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.SurgeComputationModelApiLogJPA;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * Repository interface for SurgeComputationModelApiLogJPA entity. Provides methods for CRUD
 * operations on surge computation model API logs. Maps to the surge_computation_model_api_logs
 * table.
 */
@Repository
public interface SurgeComputationModelApiLogJPARepository
    extends JpaRepository<SurgeComputationModelApiLogJPA, Long> {

  /**
   * Find API logs by model ID.
   *
   * @param modelId the model ID to search for
   * @return a list of API logs for the specified model
   */
  List<SurgeComputationModelApiLogJPA> findByModelId(Long modelId);
}

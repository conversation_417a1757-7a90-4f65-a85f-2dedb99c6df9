package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.mappers;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.FareTypeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.FareTypeConfigJPA;
import org.mapstruct.Mapper;

@Mapper
public interface FareTypeConfigEntityMapper {
  /**
   * Map FareTypeConfig to FareTypeConfigJPA
   *
   * @param source Fare type config
   * @return FareTypeConfigJPA Fare type config JPA
   */
  FareTypeConfigJPA mapFareTypeConfigToFareTypeConfigEntity(FareTypeConfig source);

  /**
   * Map FareTypeConfigJPA to FareTypeConfig
   *
   * @param source Fare type config JPA
   * @return FareTypeConfig Fare type config
   */
  FareTypeConfig mapFareTypeConfigEntityToFareTypeConfig(FareTypeConfigJPA source);
}

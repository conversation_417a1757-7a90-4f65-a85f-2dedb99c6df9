package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CalculateRegionDemandSupplyStatisticRequest implements Serializable {
  @Serial private static final long serialVersionUID = -2657437411147593771L;

  /** The booking ids, if empty, will return all the default demand supply statistics. */
  private List<String> bookingIds;
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.FlatFareBookingFeeRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FlatFareBookingFeeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.custom.FlatFareBookingFeeJPACustom;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.FlatFareBookingFeeJPARepository;
import com.cdg.pmg.ngp.me.dynamicpricing.utils.CollectionUtils;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

@Component
@Primary
@RequiredArgsConstructor
@Slf4j
public class FlatFareBookingFeeRepositoryImpl implements FlatFareBookingFeeRepository {
  private final FlatFareBookingFeeJPARepository flatFareBookingFeeJPARepository;

  @Override
  public List<FlatFareBookingFeeConfig> getFlatFareBookingFeeConfigs() {
    final List<FlatFareBookingFeeJPACustom> configs =
        flatFareBookingFeeJPARepository.getFlatFareBookingFeeConfigs();
    if (CollectionUtils.isEmpty(configs)) {
      log.info("The flat fare booking fee config from database is empty!");
      return new ArrayList<>();
    }
    return configs.stream()
        .map(
            conf ->
                FlatFareBookingFeeConfig.builder()
                    .productId(conf.getProductId())
                    .vehTypeId(conf.getVehTypeId())
                    .tariffTypeCode(conf.getTariffTypeCode())
                    .fareAmt(conf.getFareAmt())
                    .levyAmt(conf.getLevyAmt())
                    .startTime(conf.getStartTime())
                    .endTime(conf.getEndTime())
                    .applicableDays(conf.getApplicableDays())
                    .build())
        .toList();
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.mapper;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.*;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos.*;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Named;

@Mapper
public interface AddressOutboundMapper {

  GenerateRouteOutboundRequest mapToGenerateRouteOutboundRequest(GenerateRouteRequest request);

  @Named("mapToGenerateRouteResponse")
  default GenerateRouteResponse mapToGenerateRouteResponse(GenerateRouteOutboundResponse response) {
    GenerateRouteResponse generateRouteResponse = new GenerateRouteResponse();
    generateRouteResponse.setDistanceMeters(response.getRoutes().get(0).getDistanceMeters());
    generateRouteResponse.setDuration(response.getRoutes().get(0).getDuration());
    generateRouteResponse.setStaticDuration(response.getRoutes().get(0).getStaticDuration());
    generateRouteResponse.setEncodedPolyline(
        response.getRoutes().get(0).getPolyline().getEncodedPolyline());
    generateRouteResponse.setDescription(response.getRoutes().get(0).getDescription());
    return generateRouteResponse;
  }

  EffectiveH3RegionsResponse mapToEffectiveH3RegionsResponse(
      EffectiveH3RegionsOutBoundResponse effectiveH3RegionsOutBoundResponse);

  List<H3RegionComputeOutboundRequest> mapToH3RegionComputeOutboundRequest(
      List<H3RegionComputeRequest> request);

  List<H3RegionComputeResponse> mapToH3RegionComputeResponse(
      List<H3RegionComputeOutbound> h3RegionComputeOutbounds);
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.FareTypeConfigJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.custom.FareTypeConfigJPACustom;
import java.util.List;
import java.util.Set;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface FareTypeConfigJPARepository extends JpaRepository<FareTypeConfigJPA, Integer> {
  @Query(
      value =
          "SELECT ft.fare_type_id as fareTypeId, "
              + "ft.fare_type as fareType, "
              + "ft.day as day, "
              + "ft.hour as hour, "
              + "ft.default_fixed as defaultFixed, "
              + "ft.default_perc as defaultPercent, "
              + "ft.start_date as startDate, "
              + "ft.end_date as endDate, "
              + "ft.created_dt as createdDate, "
              + "ft.created_by as createdBy, "
              + "ft.updated_dt as updatedDate, "
              + "ft.updated_by as updatedBy, "
              + "vg.veh_grp as vehGrp, "
              + "vg.fixed_val as fixedValue, "
              + "vg.perc_val as percentValue, "
              + "CASE WHEN ft.day = 'HOL' THEN 1 "
              + "WHEN ft.day = 'ALL' THEN 3 "
              + "ELSE 2 END AS dayPriority "
              + "FROM FARE_TYPE_CONF as ft "
              + "LEFT JOIN FARE_VEH_GRP_CONF AS vg ON vg.fare_type_id = ft.fare_type_id "
              + "WHERE ft.end_date > current_timestamp "
              + "AND ft.start_date < current_timestamp "
              + "ORDER BY ft.fare_type, dayPriority, ft.hour DESC, vg.veh_grp",
      nativeQuery = true)
  @Transactional(readOnly = true)
  List<FareTypeConfigJPACustom> getFareTypeConfig();

  @Query(
      value =
          "SELECT ftc.fare_type_id, "
              + "ftc.fare_type, "
              + "ftc.day, "
              + "ftc.hour, "
              + "ftc.default_fixed, "
              + "ftc.default_perc, "
              + "ftc.start_date, "
              + "ftc.end_date, "
              + "ftc.created_by, "
              + "ftc.created_dt, "
              + "ftc.updated_by, "
              + "ftc.updated_dt "
              + "FROM fare_type_conf ftc "
              + "WHERE ftc.fare_type IN :listFareType "
              + "AND ft.end_date > current_timestamp "
              + "AND ft.start_date < current_timestamp "
              + "ORDER BY ftc.updated_dt DESC",
      nativeQuery = true)
  @Transactional(readOnly = true)
  List<FareTypeConfigJPA> getFareTypeConfigByListFareType(
      @Param("listFareType") Set<String> listFareType);

  @Query(
      value =
          "SELECT ftc.fare_type_id, "
              + "ftc.fare_type, "
              + "ftc.day, "
              + "ftc.hour, "
              + "ftc.default_fixed, "
              + "ftc.default_perc, "
              + "ftc.start_date, "
              + "ftc.end_date, "
              + "ftc.created_by, "
              + "ftc.created_dt, "
              + "ftc.updated_by, "
              + "ftc.updated_dt "
              + "FROM fare_type_conf ftc "
              + "WHERE ftc.fare_type = :fareType "
              + "AND ft.end_date > current_timestamp "
              + "AND ft.start_date < current_timestamp "
              + "ORDER BY ftc.updated_dt DESC "
              + "LIMIT 1",
      nativeQuery = true)
  FareTypeConfigJPA findByFareType(String fareType);
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.controllers.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.StandardInputService;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StandardInputEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.api.SurgeComputationStandardInputsApi;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper.surgecomputation.StandardInputMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.GetStandardInputsResponse;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller for retrieving standard inputs. This controller implements the
 * SurgeComputationStandardInputsApi interface and uses Spring's dependency injection to find all
 * beans that implement the StandardInputService interface.
 */
@RestController
@RequiredArgsConstructor
@Slf4j
public class StandardInputController implements SurgeComputationStandardInputsApi {

  private final StandardInputService standardInputService;
  private final StandardInputMapper standardInputMapper;

  /**
   * Retrieves all objects which implement the StandardInputService interface, gets their names and
   * descriptions, and returns them in the response.
   *
   * @return a ResponseEntity containing a GetStandardInputsResponse with all standard inputs
   */
  @Override
  public ResponseEntity<GetStandardInputsResponse> getStandardInputs() {
    log.info("Retrieving all standard inputs");

    try {
      List<StandardInputEntity> standardInputs = standardInputService.getStandardInputs();

      // Create and return the response
      GetStandardInputsResponse response = new GetStandardInputsResponse();
      response.setData(standardInputMapper.mapToStandardInput(standardInputs));

      return new ResponseEntity<>(response, HttpStatus.OK);
    } catch (Exception e) {
      log.error("Error retrieving standard inputs: {}", e.getMessage(), e);
      return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}

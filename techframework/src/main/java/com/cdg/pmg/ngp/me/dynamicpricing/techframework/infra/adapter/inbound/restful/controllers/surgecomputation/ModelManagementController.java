package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.controllers.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.ModelService;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.ModelEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.api.SurgeComputationModelManagementApi;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper.surgecomputation.ModelMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.utilities.RequestUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.SurgeComputationModel;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.SurgeComputationModelListResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.SurgeComputationModelRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.SurgeComputationModelResponse;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller for managing surge computation models. This controller implements the
 * SurgeComputationManagementApi interface and delegates business logic to the
 * SurgeComputationService.
 */
@RestController
@RequiredArgsConstructor
@Slf4j
public class ModelManagementController implements SurgeComputationModelManagementApi {

  private final ModelService modelService;
  private final ModelMapper modelMapper;

  @Override
  public ResponseEntity<SurgeComputationModelResponse> createSurgeComputationModel(
      SurgeComputationModelRequest request) {
    // Get the X-User-Id header
    String userId = RequestUtils.getUserIdFromHeader();

    // Convert request to domain entity
    ModelEntity requestEntity = modelMapper.mapRequestToEntity(request);

    // Call service to create the model with audit information
    ModelEntity createdEntity = modelService.createSurgeComputationModel(requestEntity, userId);

    // Convert response to API model
    SurgeComputationModelResponse response = createResponse(createdEntity);

    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @Override
  public ResponseEntity<Void> deleteSurgeComputationModel(Long id) {
    // Get the X-User-Id header
    // This is required by the API spec, even though we don't use it for auditing in the delete
    // operation
    String userId = RequestUtils.getUserIdFromHeader();

    boolean deleted = modelService.deleteSurgeComputationModel(id, userId);

    if (deleted) {
      return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    } else {
      return new ResponseEntity<>(HttpStatus.NOT_FOUND);
    }
  }

  @Override
  public ResponseEntity<SurgeComputationModelListResponse> getAllSurgeComputationModels() {
    List<ModelEntity> entities = modelService.getAllSurgeComputationModels();

    // Convert entities to API models
    List<SurgeComputationModel> models = modelMapper.mapEntityToDto(entities);

    // Create response
    SurgeComputationModelListResponse response = new SurgeComputationModelListResponse();
    response.setData(models);
    response.setTimestamp(OffsetDateTime.now(ZoneOffset.UTC));
    response.setTraceId(UUID.randomUUID().toString().replace("-", ""));

    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @Override
  public ResponseEntity<SurgeComputationModelResponse> getSurgeComputationModelById(Long id) {
    ModelEntity entity = modelService.getSurgeComputationModelById(id);

    if (entity == null) {
      return new ResponseEntity<>(HttpStatus.NOT_FOUND);
    }

    // Convert entity to API model
    SurgeComputationModelResponse response = createResponse(entity);

    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @Override
  public ResponseEntity<SurgeComputationModelResponse> updateSurgeComputationModel(
      Long id, SurgeComputationModelRequest request) {
    // Get the user ID
    String userId = RequestUtils.getUserIdFromHeader();

    // Convert request to domain entity
    ModelEntity requestEntity = modelMapper.mapRequestToEntity(request);

    // Call service to update the model with audit information
    ModelEntity updatedEntity = modelService.updateSurgeComputationModel(id, requestEntity, userId);

    if (updatedEntity == null) {
      return new ResponseEntity<>(HttpStatus.NOT_FOUND);
    }

    return ResponseEntity.ok(createResponse(updatedEntity));
  }

  /** Creates a response with the given entity */
  private SurgeComputationModelResponse createResponse(ModelEntity entity) {
    SurgeComputationModel model = modelMapper.mapEntityToDto(entity);

    SurgeComputationModelResponse response = new SurgeComputationModelResponse();
    response.setData(model);
    response.setTimestamp(OffsetDateTime.now(ZoneOffset.UTC));
    response.setTraceId(UUID.randomUUID().toString().replace("-", ""));

    return response;
  }
}

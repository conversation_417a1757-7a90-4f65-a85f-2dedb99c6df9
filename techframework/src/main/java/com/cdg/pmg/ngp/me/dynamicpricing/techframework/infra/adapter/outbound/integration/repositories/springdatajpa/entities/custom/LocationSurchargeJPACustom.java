package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.custom;

import lombok.*;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LocationSurchargeJPACustom {
  private Integer locationId;
  private String locationName;
  private String addressRef;
  private String fareType;
  private String chargeBy;
  private Double surchargeValue;
  private String productId;
  private String startTime;
  private String endTime;
  private String dayIndicator;
}

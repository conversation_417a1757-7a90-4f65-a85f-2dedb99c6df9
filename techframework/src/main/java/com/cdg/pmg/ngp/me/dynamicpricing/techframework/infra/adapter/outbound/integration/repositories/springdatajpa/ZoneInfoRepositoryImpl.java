package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa;

import com.cdg.pmg.ngp.me.dynamicpricing.outbound.ZoneInfoRepositoryOutboundPort;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.ZoneInfoJPARepository;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@AllArgsConstructor
public class ZoneInfoRepositoryImpl implements ZoneInfoRepositoryOutboundPort {

  private final ZoneInfoJPARepository zoneInfoJPARepository;

  @Override
  @Transactional(readOnly = true)
  public boolean checkZoneIdExists(String zoneId) {
    return zoneInfoJPARepository.checkZoneIdExists(zoneId);
  }

  @Override
  @Transactional(readOnly = true)
  public List<String> findAllZones() {
    return zoneInfoJPARepository.findAllZoneIds();
  }
}

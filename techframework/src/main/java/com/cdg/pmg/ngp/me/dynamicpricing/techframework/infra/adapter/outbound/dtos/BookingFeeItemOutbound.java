package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BookingFeeItemOutbound implements Serializable {
  @Serial private static final long serialVersionUID = -7707491769528834690L;

  private Integer vehicleTypeId;
  private String productId;
  private String flatFareType;
  private Double bookingFee;
}

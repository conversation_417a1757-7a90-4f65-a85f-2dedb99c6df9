package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs.properties;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

@Component
@Data
@Validated
@ConfigurationProperties(prefix = "me.redis")
public class RedisAppProperties {
  @NotNull private int defaultExpiredTimeInSecond;
}

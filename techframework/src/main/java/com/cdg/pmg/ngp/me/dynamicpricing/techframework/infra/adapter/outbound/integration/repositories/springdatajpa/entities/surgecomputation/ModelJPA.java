package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.RequestFieldMapping;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.MappingTypeEnum;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.AbstractAuditingEntity;
import jakarta.persistence.*;
import java.io.Serial;
import java.util.List;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "surge_computation_models")
public class ModelJPA extends AbstractAuditingEntity<Long> {

  @Serial private static final long serialVersionUID = 1L;

  @Id
  @GeneratedValue(
      strategy = GenerationType.SEQUENCE,
      generator = "surgeComputationModelSequenceGenerator")
  @SequenceGenerator(
      name = "surgeComputationModelSequenceGenerator",
      sequenceName = "SEQ_surge_computation_models__id",
      allocationSize = 1)
  private Long id;

  @Column(name = "model_name", nullable = false)
  private String modelName;

  @Column(name = "description")
  private String description;

  @Column(name = "endpoint_url")
  private String endpointUrl;

  @Column(name = "request_fields_mappings", columnDefinition = "jsonb")
  @JdbcTypeCode(SqlTypes.JSON)
  private List<RequestFieldMapping> requestFieldsMappings;

  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class RequestFieldMapping {
    private MappingTypeEnum mappingType;
    private String requestParameterName;
    private String mappingConfigurationName;
  }
}

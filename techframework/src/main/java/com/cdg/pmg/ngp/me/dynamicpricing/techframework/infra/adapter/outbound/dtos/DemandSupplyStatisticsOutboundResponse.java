package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.*;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DemandSupplyStatisticsOutboundResponse implements Serializable {
  @Serial private static final long serialVersionUID = 1L;

  private List<DemandSupplyStatisticsOutbound> data;
}

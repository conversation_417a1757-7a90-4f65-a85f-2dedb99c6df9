package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.properties;

import java.util.List;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@RefreshScope
@Configuration
@Getter
@Setter
@ConfigurationProperties(prefix = "new-pricing-model-config")
@NoArgsConstructor
public class NewPricingModelConfigList {
  private List<String> items;
}

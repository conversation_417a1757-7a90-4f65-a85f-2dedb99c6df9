package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities;

import com.cdg.pmg.ngp.me.dynamicpricing.enums.EntityChangeTypeEnum;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serial;
import java.time.OffsetDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Entity
@Table(name = "new_pricing_model_config_change")
public class NewPricingModelConfigChangeJPAEntity extends AbstractAuditingEntity<String> {

  @Serial private static final long serialVersionUID = 1L;

  @Id
  @Column(name = "unique_payload_hash")
  private String uniquePayloadHash;

  @Enumerated(EnumType.STRING)
  @Column(name = "entity_change_type")
  private EntityChangeTypeEnum entityChangeType;

  @Column(name = "config_id")
  private Long configId;

  @Column(name = "index")
  private Integer index;

  @Column(name = "zone_id")
  private String zoneId;

  @Column(name = "start_dt")
  private OffsetDateTime startDt;

  @Column(name = "end_dt")
  private OffsetDateTime endDt;

  @Column(name = "additional_surge_high")
  private Integer additionalSurgeHigh;

  @Column(name = "surge_high_tier_rate")
  private Double surgeHighTierRate;

  @Column(name = "unmet_rate_1")
  private Double unmetRate1;

  @Column(name = "unmet_rate_2")
  private Double unmetRate2;

  @Column(name = "negative_demand_supply_down_rate")
  private Double negativeDemandSupplyDownRate;

  @Column(name = "k1")
  private Double k1;

  @Column(name = "k2")
  private Double k2;

  @Column(name = "k3")
  private Double k3;

  @Column(name = "k4")
  private Double k4;

  @Column(name = "k5")
  private Double k5;

  @Column(name = "k6")
  private Double k6;

  @Column(name = "k7")
  private Double k7;

  @Column(name = "k8")
  private Double k8;

  @Column(name = "zone_price_version")
  private String zonePriceVersion;

  @Column(name = "config_created_dt")
  private OffsetDateTime configCreatedDt;

  @Column(name = "config_updated_dt")
  private OffsetDateTime configUpdatedDt;

  @Column(name = "config_created_by")
  private String createdBy;

  @Column(name = "config_updated_by")
  private String updatedBy;

  @Override
  public String getId() {
    return uniquePayloadHash;
  }
}

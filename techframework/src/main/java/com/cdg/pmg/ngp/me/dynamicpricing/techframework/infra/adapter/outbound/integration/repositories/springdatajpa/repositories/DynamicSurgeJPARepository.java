package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.DynamicSurgeJPA;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

public interface DynamicSurgeJPARepository extends JpaRepository<DynamicSurgeJPA, String> {
  @Query("SELECT ds FROM DynamicSurgeJPA as ds ")
  @Transactional(readOnly = true)
  List<DynamicSurgeJPA> getDynpSurges();
}

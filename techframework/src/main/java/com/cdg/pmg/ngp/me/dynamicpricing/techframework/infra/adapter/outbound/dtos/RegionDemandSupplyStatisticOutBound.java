package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

@Data
public class RegionDemandSupplyStatisticOutBound implements Serializable {
  @Serial private static final long serialVersionUID = 432448530804558070L;

  private Long regionId;
  private Integer comfortRideDemand;
  private Integer meterDemand;
  private Integer comfortRideUnmetDemand;
  private Integer meterUnmetDemand;
}

package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs;

import javax.sql.DataSource;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

/**
 * This JDBC config uses for LocationSurchargeJPARepository only to avoid RAM issue.
 *
 * <p>Before that This repository use JPA, and consume up to 3GB of RAM. It makes the service
 * unstable.
 *
 * <p>This configuration uses the primary datasource which is a routing datasource that
 * automatically routes to the reader database for read-only operations and to the writer database
 * for write operations based on the transaction's read-only flag.
 *
 * <p>Ticket monitoring: NGPME-8108
 */
@Configuration
@RequiredArgsConstructor
public class JdbcTemplateConfig {

  private final DataSource dataSource;

  @Bean
  public NamedParameterJdbcTemplate jdbcTemplateConfiguration() {
    return new NamedParameterJdbcTemplate(dataSource);
  }
}

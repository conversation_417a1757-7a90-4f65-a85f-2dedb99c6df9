package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.custom.LocationSurchargeJPACustom;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.core.namedparam.SqlParameterSource;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 * This repository need to use JDBC to avoid RAM issue.
 *
 * <p>Before that This repository use JPA, and consume up to 3GB of RAM. It makes the service
 * unstable.
 *
 * <p>Ticket monitoring: NGPME-8108
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class LocationSurchargeJPARepository {
  private final NamedParameterJdbcTemplate namedParameterJdbcTemplate;

  @Transactional(readOnly = true)
  public List<LocationSurchargeJPACustom> getLocationSurchargeConfigs(int limit, int offset) {
    log.info("Querying location surcharge with limit = {}, offset = {}", limit, offset);
    String query =
        "SELECT "
            + "t.location_id AS LocationId, "
            + "t.location_name AS LocationName, "
            + "t.address_ref AS AddressRef, "
            + "t.fare_type AS FareType, "
            + "t.charge_by AS ChargeBy, "
            + "t.surcharge_value AS SurchargeValue, "
            + "t.product_id AS ProductId, "
            + "t.start_time AS StartTime, "
            + "t.end_time AS EndTime, "
            + "t.day_indicator AS DayIndicator "
            + "FROM (SELECT l.location_id, "
            + "             l.location_name, "
            + "             la.address_ref, "
            + "             lsc.fare_type, "
            + " lsc.day_indicator, "
            + " lsc.charge_by, "
            + " lsc.start_time, "
            + " lsc.end_time, "
            + " lsc.product_id, "
            + " lsc.surcharge_value "
            + "FROM loc_surcharge lsc "
            + "JOIN loc_address la ON lsc.location_id = la.location_id "
            + "JOIN location l ON lsc.location_id = l.location_id "
            + "ORDER BY lsc.location_id) t "
            + "GROUP BY location_id, "
            + "         location_name, "
            + "         address_ref, "
            + "         fare_type, "
            + "         charge_by, "
            + "         surcharge_value, "
            + "         product_id, "
            + "         start_time, "
            + "         end_time, "
            + "         day_indicator "
            + "       LIMIT :limit "
            + " OFFSET :offset"; // query to be run

    SqlParameterSource namedParameters =
        new MapSqlParameterSource().addValue("offset", offset).addValue("limit", limit);

    return namedParameterJdbcTemplate.query(
        query, namedParameters, new BeanPropertyRowMapper<>(LocationSurchargeJPACustom.class));
  }
}

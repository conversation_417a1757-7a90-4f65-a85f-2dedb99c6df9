package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.PricingRangeRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.queries.PricingRangeCalDemandSurgeQueryResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.custom.PricingRangeCalDemandSurgeJPACustom;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.PricingRangeJPARepository;
import com.cdg.pmg.ngp.me.dynamicpricing.utils.CollectionUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

@Component
@Primary
@RequiredArgsConstructor
public class PricingRangeRepositoryImpl implements PricingRangeRepository {
  private final PricingRangeJPARepository pricingRangeJPARepository;

  @Override
  public List<PricingRangeCalDemandSurgeQueryResponse> getDynpConfigForDemandSurge(
      boolean isHoliday) {
    final List<PricingRangeCalDemandSurgeJPACustom> dynpConfigs =
        pricingRangeJPARepository.getPricingRangeConfigs(isHoliday);
    if (CollectionUtils.isEmpty(dynpConfigs)) {
      return new ArrayList<>();
    }

    return dynpConfigs.stream()
        .map(mapPricingRangeEntityToPricingRangeCalDemandQueryResponse())
        .toList();
  }

  @Override
  public List<PricingRangeCalDemandSurgeQueryResponse> getDynpConfigForDemandSurgeV2(
      boolean isHoliday) {
    final List<PricingRangeCalDemandSurgeJPACustom> dynpConfigs =
        pricingRangeJPARepository.getPricingRangeConfigsV2(isHoliday);
    if (CollectionUtils.isEmpty(dynpConfigs)) {
      return new ArrayList<>();
    }

    return dynpConfigs.stream()
        .map(mapPricingRangeEntityToPricingRangeCalDemandQueryResponse())
        .toList();
  }

  private static Function<
          PricingRangeCalDemandSurgeJPACustom, PricingRangeCalDemandSurgeQueryResponse>
      mapPricingRangeEntityToPricingRangeCalDemandQueryResponse() {
    return config ->
        PricingRangeCalDemandSurgeQueryResponse.builder()
            .surgeLow(config.getSurgeLow())
            .surgeHigh(config.getSurgeHigh())
            .stepNegative(config.getStepNegative())
            .stepPositive(config.getStepPositive())
            .zoneId(config.getZoneId())
            .build();
  }
}

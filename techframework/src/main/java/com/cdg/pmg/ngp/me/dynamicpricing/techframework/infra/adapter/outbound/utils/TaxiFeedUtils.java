package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.utils;

import static com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.CommonUtils.getIterableFromIterator;
import static com.cdg.pmg.ngp.me.dynamicpricing.constants.BookARideConfigsConstant.ONE_THOUSAND;
import static com.cdg.pmg.ngp.me.dynamicpricing.constants.CommonConstant.UNDERSCORE;
import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.ERROR_BUILDING_TAXI_AREA;
import static com.cdg.pmg.ngp.me.dynamicpricing.utils.StringUtils.convertStringToLong;

import com.cdg.pmg.ngp.me.dynamicpricing.constants.BookARideConfigsConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FareUploadConfiguration;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.LocationSurchargeConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.S2CellEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.VGProductFareBean;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.InternalServerException;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.ProductCategory;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.TaxiArea;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.TaxiFare;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.TaxiGeoData;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.TaxiProduct;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@UtilityClass
@Slf4j
public class TaxiFeedUtils {
  public static List<TaxiArea> buildTaxiAreas(FareUploadConfiguration fareUploadConfiguration) {
    List<TaxiArea> taxiAreaList = new ArrayList<>();
    try {
      log.info("Building TaxiArea");
      Map<String, List<S2CellEntity>> s2CellsWithLocationMap =
          fareUploadConfiguration.getS2CellList().stream()
              .filter(s2Cell -> s2Cell.getS2CellLocationId() != null)
              .collect(Collectors.groupingBy(S2CellEntity::getS2CellLocationId));
      for (Map.Entry<String, List<S2CellEntity>> s2CellEntry : s2CellsWithLocationMap.entrySet()) {
        TaxiArea.Builder taxiAreaBuilder = TaxiArea.newBuilder();
        List<S2CellEntity> s2CellList = s2CellEntry.getValue();
        Iterator<Long> s2CellItr =
            s2CellList.stream().map(s2Cell -> convertStringToLong(s2Cell.getS2CellId())).iterator();

        String s2CellDesc = s2CellList.get(0).getS2CellLocDesc();
        int lastIndex = s2CellDesc.lastIndexOf(" N");
        String locName = s2CellDesc.substring(0, lastIndex);

        taxiAreaBuilder.setName(locName);
        taxiAreaBuilder.addAllS2Cells(getIterableFromIterator(s2CellItr));
        taxiAreaList.add(taxiAreaBuilder.build());
      }
      TaxiArea.Builder taxiAreaBuilderRest = TaxiArea.newBuilder();
      taxiAreaBuilderRest.setName(BookARideConfigsConstant.REST);
      List<Long> s2CellsIdsWithoutLocaitonList =
          fareUploadConfiguration.getS2CellList().stream()
              .filter(s2Cell -> s2Cell.getS2CellLocationId() == null)
              .map(s2Cell -> convertStringToLong(s2Cell.getS2CellId()))
              .toList();

      Iterator<Long> s2CellItrRest = s2CellsIdsWithoutLocaitonList.iterator();

      taxiAreaBuilderRest.addAllS2Cells(getIterableFromIterator(s2CellItrRest));
      taxiAreaList.add(taxiAreaBuilderRest.build());
      log.info("End of building TaxiArea");
      return taxiAreaList;
    } catch (Exception e) {
      log.error("Exception occurred in taxi area build : ", e);
      throw new InternalServerException(
          ERROR_BUILDING_TAXI_AREA.getMessage(), ERROR_BUILDING_TAXI_AREA.getErrorCode());
    }
  }

  public static List<TaxiProduct> buildTaxiProducts(
      FareUploadConfiguration fareUploadConfiguration,
      Map<String, String> cmsConfigData,
      Map<String, List<String>> vehicleGroupIdAndProductIdListMap,
      Integer productCategoryValue) {
    List<TaxiProduct> taxiProductList = new ArrayList<>();
    log.info("Building products");
    TaxiProduct.Builder taxiProductBuilder = TaxiProduct.newBuilder();
    vehicleGroupIdAndProductIdListMap.forEach(
        (vehicleGroupId, productIdList) ->
            productIdList.forEach(
                productId -> {
                  TaxiProduct taxiProduct =
                      taxiProductBuilder
                          .setProductId(productId.concat("_").concat(vehicleGroupId))
                          .setCurrencyCode(
                              cmsConfigData.get(BookARideConfigsConstant.CURRENCY_CODE))
                          .putLocalizedNames(
                              BookARideConfigsConstant.EN,
                              cmsConfigData.get(
                                  productId.concat(BookARideConfigsConstant.LOCALISED_NAME)))
                          .setProductCategory(ProductCategory.forNumber(productCategoryValue))
                          .setInternalName(
                              cmsConfigData
                                  .get(productId.concat(BookARideConfigsConstant.INTERNAL_NAME))
                                  .concat("_")
                                  .concat(vehicleGroupId))
                          .setFare(
                              buildTaxiFare(
                                  fareUploadConfiguration,
                                  cmsConfigData,
                                  productId,
                                  Integer.parseInt(vehicleGroupId)))
                          .setGeoData(
                              buildTaxiGeoData(
                                  fareUploadConfiguration,
                                  cmsConfigData,
                                  productId,
                                  Integer.parseInt(vehicleGroupId)))
                          .setCarIconId(cmsConfigData.get(BookARideConfigsConstant.CAR_ICON_ID))
                          .build();
                  taxiProductList.add(taxiProduct);
                }));
    log.info("End of building products");
    return taxiProductList;
  }

  /**
   * Configures the tiered fare structure for a taxi fare by creating segments within a piecewise
   * linear function.
   *
   * @param tierStart The starting distance of the fare tier.
   * @param tierEnd The ending distance of the fare tier.
   * @param tierCountMeter The distance increment for each fare segment within the tier.
   * @param tierFare The fare for each segment within the tier.
   * @param piecewiseLinearFunctionBuilder The builder for the
   *     PiecewiseLinearRate.PiecewiseLinearFunction to which segments are added.
   * @param inclusive Boolean flag indicating whether to include a segment for each increment (true)
   *     or only the final segment (false).
   */
  public static void setTierFare(
      Double tierStart,
      Double tierEnd,
      Double tierCountMeter,
      Double tierFare,
      TaxiFare.PiecewiseLinearRate.PiecewiseLinearFunction.Builder piecewiseLinearFunctionBuilder,
      boolean inclusive) {
    Double tierFareDistance = tierStart;
    if (tierCountMeter < 0) {
      throw new IllegalArgumentException("tierCountMeter cannot be less than 0");
    }
    while (true) {
      boolean addSegment = false;
      if (tierFareDistance < tierEnd) {
        if (inclusive) {
          piecewiseLinearFunctionBuilder.addSegments(
              TaxiFare.PiecewiseLinearRate.PiecewiseLinearFunction.Segment.newBuilder()
                  .setStart(tierFareDistance / ONE_THOUSAND)
                  .setPrice(tierFare)
                  .build());
        }

        tierFareDistance += tierCountMeter;
        if (tierCountMeter == 0) {
          addSegment = true;
        }
      } else {
        addSegment = true;
      }

      if (addSegment) {
        piecewiseLinearFunctionBuilder.addSegments(
            TaxiFare.PiecewiseLinearRate.PiecewiseLinearFunction.Segment.newBuilder()
                .setStart(tierEnd / ONE_THOUSAND)
                .setPrice(tierFare)
                .build());
        break;
      }
    }
  }

  public static TaxiFare buildTaxiFare(
      FareUploadConfiguration fareUploadConfiguration,
      Map<String, String> cmsConfigData,
      String productId,
      int vehicleGroupId) {
    TaxiFare.Builder taxiFareBuilder = TaxiFare.newBuilder();

    log.info("Building Taxi Fare");
    VGProductFareBean vGProductFareBean =
        fareUploadConfiguration.getVgProductFareMap().get(vehicleGroupId).get(productId);

    TaxiFare.StandardRate.Builder standardRateBuilder =
        TaxiFare.StandardRate.newBuilder()
            .setBase(vGProductFareBean.getFlagDown())
            .setMinimumFixed(
                Double.parseDouble(cmsConfigData.get(BookARideConfigsConstant.MINIMUM_FIXED)))
            .setFees(vGProductFareBean.getBookingFee());

    String lowRangeEstimateMulConf =
        productId
            .concat(UNDERSCORE)
            .concat(BookARideConfigsConstant.LOW_RANGE_ESTIMATE_MULTIPLIER_VALUE);

    String highRangeEstimateMulConf =
        productId
            .concat(UNDERSCORE)
            .concat(BookARideConfigsConstant.HIGH_RANGE_ESTIMATE_MULTIPLIER_VALUE);

    standardRateBuilder.setLowRangeEstimateMultiplier(
        TaxiFare.StandardRate.DoubleValue.newBuilder()
            .setValue(Double.parseDouble(cmsConfigData.get(lowRangeEstimateMulConf)))
            .build());

    standardRateBuilder.setHighRangeEstimateMultiplier(
        TaxiFare.StandardRate.DoubleValue.newBuilder()
            .setValue(Double.parseDouble(cmsConfigData.get(highRangeEstimateMulConf)))
            .build());

    TaxiFare.PiecewiseLinearRate.PiecewiseLinearFunction.Builder piecewiseLinearFunctionBuilder =
        TaxiFare.PiecewiseLinearRate.PiecewiseLinearFunction.newBuilder();

    setTierFare(
        vGProductFareBean.getTier1Start(),
        vGProductFareBean.getTier1End(),
        vGProductFareBean.getTier1PerCountMeter(),
        vGProductFareBean.getTier1Fare(),
        piecewiseLinearFunctionBuilder,
        true);

    setTierFare(
        vGProductFareBean.getTier2Start(),
        vGProductFareBean.getTier2End(),
        vGProductFareBean.getTier2PerCountMeter(),
        vGProductFareBean.getTier2Fare(),
        piecewiseLinearFunctionBuilder,
        false);

    taxiFareBuilder.setPiecewiseLinearRate(
        TaxiFare.PiecewiseLinearRate.newBuilder()
            .setStandardRate(standardRateBuilder.build())
            .setPricePerKmFunction(piecewiseLinearFunctionBuilder)
            .setPricePerMinuteFunction(
                TaxiFare.PiecewiseLinearRate.PiecewiseLinearFunction.newBuilder()
                    .addSegments(
                        TaxiFare.PiecewiseLinearRate.PiecewiseLinearFunction.Segment.newBuilder()
                            .setStart(0d)
                            .setPrice(vGProductFareBean.getWaitTimeFare())
                            .build())
                    .build()));

    taxiFareBuilder.addAllEndpointsSurcharges(
        prepareSurcharges(fareUploadConfiguration, productId));
    log.info("End of building Taxi Fare");
    return taxiFareBuilder.build();
  }

  public static TaxiGeoData buildTaxiGeoData(
      FareUploadConfiguration fareUploadConfiguration,
      Map<String, String> cmsConfigData,
      String productId,
      int vehicleGroupId) {
    final TaxiGeoData.Builder taxiGeoDataBuilder = TaxiGeoData.newBuilder();

    VGProductFareBean vGProductFareBean =
        fareUploadConfiguration.getVgProductFareMap().get(vehicleGroupId).get(productId);

    List<S2CellEntity> s2CellsList = fareUploadConfiguration.getS2CellList();
    final int waitingTimeSeconds =
        Integer.parseInt(cmsConfigData.get(BookARideConfigsConstant.WAITING_TIME_SECONDS));
    for (S2CellEntity s2Cells : s2CellsList) {
      if (fareUploadConfiguration.getSurgeConfig() != null
          && fareUploadConfiguration.getSurgeConfig().containsKey(s2Cells.getS2CellZoneId())) {
        addWaitingAndFareMultiplier(
            fareUploadConfiguration,
            s2Cells,
            taxiGeoDataBuilder,
            waitingTimeSeconds,
            vGProductFareBean.isSurgeRequired());

      } else {
        taxiGeoDataBuilder.addWaitingTimesSeconds(
            TaxiGeoData.WaitingTimeEntry.newBuilder()
                .setKey((long) Double.parseDouble(s2Cells.getS2CellId()))
                .setValue(waitingTimeSeconds)
                .build());
      }
    }
    return taxiGeoDataBuilder.build();
  }

  public static void addWaitingAndFareMultiplier(
      FareUploadConfiguration fareUploadConfiguration,
      S2CellEntity s2Cells,
      TaxiGeoData.Builder taxiGeoDataBuilder,
      int waitingTimeSeconds,
      boolean isSurgeRequired) {
    taxiGeoDataBuilder.addWaitingTimesSeconds(
        TaxiGeoData.WaitingTimeEntry.newBuilder()
            .setKey((long) Double.parseDouble(s2Cells.getS2CellId()))
            .setValue(waitingTimeSeconds)
            .build());
    if (isSurgeRequired) {
      double surgeVal =
          fareUploadConfiguration.getSurgeConfig().get(s2Cells.getS2CellZoneId()) / 100.0;
      BigDecimal surgeValInBD = BigDecimal.valueOf(surgeVal);
      if (surgeValInBD.compareTo(BigDecimal.ZERO) != 0) {
        surgeVal = 1 + surgeVal;
        taxiGeoDataBuilder.addFareMultipliers(
            TaxiGeoData.FareMultiplierEntry.newBuilder()
                .setKey((long) Double.parseDouble(s2Cells.getS2CellId()))
                .setValue(surgeVal)
                .build());
      }
    }
  }

  public static List<TaxiFare.EndpointsSurcharge> prepareSurcharges(
      FareUploadConfiguration fareUploadConfiguration, String productId) {
    List<TaxiFare.EndpointsSurcharge> endpointsSurchargeList = new ArrayList<>();
    Map<String, List<S2CellEntity>> s2CellListHavingLocation =
        fareUploadConfiguration.getS2CellList().stream()
            .filter(s2Cell -> !StringUtils.isEmpty(s2Cell.getS2CellLocationId()))
            .collect(Collectors.groupingBy(S2CellEntity::getS2CellLocationId));
    s2CellListHavingLocation.forEach(
        (s2CellLocationId, s2CellList) -> {
          String s2CellDesc = s2CellList.get(0).getS2CellLocDesc();
          Integer pickUpIndex =
              fareUploadConfiguration.getLocationPickupIndex().get(s2CellLocationId);
          List<LocationSurchargeConfigEntity> locSurchargeBeanList =
              fareUploadConfiguration
                  .getLocationSurChargeConfig()
                  .get(Integer.parseInt(s2CellLocationId));
          if (locSurchargeBeanList != null) {
            LocationSurchargeConfigEntity locationSurChargeConfig =
                locSurchargeBeanList.stream()
                    .filter(
                        locSurcharge ->
                            locSurcharge.getChargeBy().equals(BookARideConfigsConstant.PICK_UP)
                                && productId.equals(locSurcharge.getProductId()))
                    .findFirst()
                    .orElse(null);
            prepareEndpointSurchargeList(
                fareUploadConfiguration,
                locationSurChargeConfig,
                endpointsSurchargeList,
                pickUpIndex,
                s2CellDesc,
                productId);
          }
        });
    prepareEndpointSurchargeList(
        fareUploadConfiguration,
        null,
        endpointsSurchargeList,
        fareUploadConfiguration.getLocationPickupIndex().get("0"),
        BookARideConfigsConstant.REST,
        productId);
    return endpointsSurchargeList;
  }

  public static void prepareEndpointSurchargeList(
      FareUploadConfiguration fareUploadConfiguration,
      LocationSurchargeConfigEntity locationSurChargeConfig,
      List<TaxiFare.EndpointsSurcharge> endpointsSurchargeList,
      Integer pickUpIndex,
      String s2CellDesc,
      String productId) {
    fareUploadConfiguration
        .getLocationPickupIndex()
        .forEach(
            (locSurchargeMapKey, dropOffIndex) -> {
              double pickupSurchargeValue =
                  locationSurChargeConfig == null ? 0 : locationSurChargeConfig.getSurchargeValue();
              List<LocationSurchargeConfigEntity> destLocSurchargeList =
                  fareUploadConfiguration
                      .getLocationSurChargeConfig()
                      .get(Integer.parseInt(locSurchargeMapKey));
              double totalSurchareValue = pickupSurchargeValue;
              if (destLocSurchargeList != null) {
                LocationSurchargeConfigEntity filteredDestLocSurByProductId =
                    destLocSurchargeList.stream()
                        .filter(
                            locSurcharge ->
                                BookARideConfigsConstant.DEST.equals(locSurcharge.getChargeBy())
                                    && productId.equals(locSurcharge.getProductId()))
                        .findFirst()
                        .orElse(null);
                double destSurchargeValue =
                    filteredDestLocSurByProductId == null
                        ? 0
                        : filteredDestLocSurByProductId.getSurchargeValue();
                totalSurchareValue += destSurchargeValue;
              }
              addEndPointSurchargeList(
                  totalSurchareValue,
                  endpointsSurchargeList,
                  pickUpIndex,
                  s2CellDesc,
                  dropOffIndex);
            });
  }

  public static void addEndPointSurchargeList(
      double totalSurchargeValue,
      List<TaxiFare.EndpointsSurcharge> endpointsSurchargeList,
      Integer pickUpIndex,
      String s2CellDesc,
      Integer dropOffIndex) {
    BigDecimal totalSurchargeValueInBigDecimal = BigDecimal.valueOf(totalSurchargeValue);
    if (totalSurchargeValueInBigDecimal.compareTo(BigDecimal.ZERO) > 0) {
      endpointsSurchargeList.add(
          TaxiFare.EndpointsSurcharge.newBuilder()
              .setName(s2CellDesc)
              .setPickupAreaIndex(pickUpIndex)
              .setDropoffAreaIndex(dropOffIndex)
              .setAmount(totalSurchargeValue)
              .build());
    }
  }
}

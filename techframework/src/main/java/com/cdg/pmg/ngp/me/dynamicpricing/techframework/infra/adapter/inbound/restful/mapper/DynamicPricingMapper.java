package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.MultiFareResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.H3RegionComputeRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.commands.StoreFareBreakdownCommandRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.commands.StoreFareBreakdownCommandResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.*;
import com.cdg.pmg.ngp.me.dynamicpricing.queries.MultiFareRequestQuery;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/** The interface Dynamic pricing mapper. */
@Mapper
public interface DynamicPricingMapper {

  /**
   * Map to est fare request query est fare request query.
   *
   * @param request the request
   * @return the est fare request query
   */
  MultiFareRequestQuery mapToEstFareRequestQuery(GetEstimatedFareInboundRequest request);

  /**
   * Map to get estimated fare inbound response get estimated fare inbound response.
   *
   * @param response the response
   * @return the get estimated fare inbound response
   */
  GetEstimatedFareInboundResponse mapToGetEstimatedFareInboundResponse(MultiFareResponse response);

  /**
   * Map to FareDetailInboundResponse
   *
   * @param fareDetail input
   * @return FareDetailInboundResponse
   */
  GetFareDetailResponse mapToGetFareDetailResponse(FareBreakdownDetailEntity fareDetail);

  /**
   * Map to store fare breakdown request query store fare breakdown command request.
   *
   * @param request the request
   * @return the store fare breakdown command request
   */
  StoreFareBreakdownCommandRequest mapToStoreFareBreakdownRequestQuery(
      StoreFareBreakdownInboundRequest request);

  /**
   * Map to store fare breakdown inbound response data store fare breakdown inbound response data.
   *
   * @param response the response
   * @return the store fare breakdown inbound response data
   */
  StoreFareBreakdownInboundResponseData mapToStoreFareBreakdownInboundResponseData(
      StoreFareBreakdownCommandResponse response);

  /**
   * Map to validate fare entity validate fare entity.
   *
   * @param request the request
   * @return the validate fare entity
   */
  ValidateFareEntity mapToValidateFareEntity(ValidateFareRequest request);

  /**
   * Map generated route entity to generated route generated route.
   *
   * @param source the source
   * @return the generated route
   */
  GeneratedRoute mapGeneratedRouteEntityToGeneratedRoute(GeneratedRouteEntity source);

  /**
   * Map SearchFareBreakdownInboundRequest to SearchFareBreakdownRequestEntity.
   *
   * @param request the request
   * @return SearchFareBreakdownRequestEntity
   */
  SearchFareBreakdownRequestEntity mapToSearchBreakdownRequest(
      SearchFareBreakdownInboundRequest request);

  /**
   * Map SearchFareBreakdownResponse to SearchFareBreakdownInboundResponse.
   *
   * @param response the response
   * @return SearchFareBreakdownInboundResponse
   */
  SearchFareBreakdownInboundResponse mapToSearchFareBreakdownResponse(
      SearchFareBreakdownResponse response);

  @Mapping(target = "lat", source = "pickupAddressLat")
  @Mapping(target = "lng", source = "pickupAddressLng")
  H3RegionComputeRequest mapToH3RegionComputeRequest(GetEstimatedFareInboundRequest request);
}

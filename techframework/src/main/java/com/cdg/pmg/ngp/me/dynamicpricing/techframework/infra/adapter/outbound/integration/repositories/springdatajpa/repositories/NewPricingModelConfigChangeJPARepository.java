package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.NewPricingModelConfigChangeJPAEntity;
import org.springframework.data.jpa.repository.JpaRepository;

public interface NewPricingModelConfigChangeJPARepository
    extends JpaRepository<NewPricingModelConfigChangeJPAEntity, String> {}

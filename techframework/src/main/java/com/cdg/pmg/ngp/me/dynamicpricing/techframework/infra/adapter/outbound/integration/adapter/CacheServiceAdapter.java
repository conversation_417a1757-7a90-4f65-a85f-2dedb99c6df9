package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.adapter;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.CacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.redis.RedisService;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/** The type Cache outbound adapter. */
@Service
@RequiredArgsConstructor
public class CacheServiceAdapter implements CacheService {

  private final RedisService redisService;

  @Override
  public <T> void setListValue(String key, List<T> data) {
    redisService.setListValue(key, data);
  }

  @Override
  public <T> void setValue(String key, T data) {
    redisService.setValue(key, data);
  }

  @Override
  public <T> void setValue(String key, T data, int expireDuration) {
    redisService.setValue(key, data, expireDuration);
  }

  @Override
  public <T> void setValue(String key, T data, long expireDuration) {
    redisService.setValue(key, data, expireDuration);
  }

  @Override
  public <T> T getValue(String key, Class<T> valueType) {
    return redisService.getValue(key, valueType);
  }

  @Override
  public void deleteByKey(String key) {
    redisService.deleteByKey(key);
  }

  @Override
  public void setExpire(String key, int expireDuration) {
    redisService.setExpire(key, expireDuration);
  }

  @Override
  public void setExpire(String key, long expireDuration) {
    redisService.setExpire(key, expireDuration);
  }

  @Override
  public <T> List<List<T>> getMultiValueList(Set<String> keys, Class<T> valueType) {
    return redisService.getMultiValueList(keys, valueType);
  }

  @Override
  public <K, V> List<Map<K, V>> getMultiValueMap(
      Set<String> keys, Class<K> keyType, Class<V> valueType) {
    return redisService.getMultiValueMap(keys, keyType, valueType);
  }

  @Override
  public String getStringValue(final String key) {
    return redisService.getStringValueByKey(key);
  }

  @Override
  public Set<String> getKeysByPattern(String pattern) {
    return redisService.getKeysByPattern(pattern);
  }

  @Override
  public <T> List<T> getListValue(String key, Class<T> typeOfList) {
    return redisService.getListValue(key, typeOfList);
  }

  @Override
  public <K, V> Map<K, V> getMapValue(String key, Class<K> keyType, Class<V> valueType) {
    return redisService.getMapValue(key, keyType, valueType);
  }

  @Override
  public Set<String> getKeysByPattern(String subKey, String prefix, String suffix) {
    final var pattern = prefix + subKey + suffix;
    return redisService.getKeysByPattern(pattern);
  }

  @Override
  public void deleteByKeys(Set<String> keys) {
    redisService.deleteByKeys(keys);
  }

  @Override
  public java.util.List<String> getCompanyHoliday() {
    return redisService.getListValue(
        RedisKeyConstant.DYNAMIC_PRICING
            + RedisKeyConstant.COLON
            + RedisKeyConstant.COMPANY_HOLIDAY,
        String.class);
  }
}

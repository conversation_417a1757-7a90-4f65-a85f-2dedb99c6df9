<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <include file="schema/NGPME-6105_create_schema.sql" relativeToChangelogFile="true"/>
    <include file="schema/NGPME-6018_create_seq_for_fare_type_conf.sql" relativeToChangelogFile="true"/>
    <include file="schema/NGPME-6421_create_flat_fare_breakdown_detail.sql" relativeToChangelogFile="true"/>
    <include file="schema/NGPME-7869-hotfix-datatype-FARE_VEH_GRP_CONF.sql" relativeToChangelogFile="true"/>
    <include file="schema/NGPME-7868-hotfix-datatype-FARE_TYPE_CONF.sql" relativeToChangelogFile="true"/>
    <include file="schema/NGPME-7955_create_dynp_surge_upstream.sql" relativeToChangelogFile="true"/>
    <include file="schema/NGPME-7955_add_collumn_dynp_surge.sql" relativeToChangelogFile="true"/>
    <include file="schema/NGPME-7993_alter_flat_fare_breakdown_remove_unique.sql" relativeToChangelogFile="true"/>
    <include file="schema/NGPME-7913_update_seq_for_fare_breakdown_details.sql" relativeToChangelogFile="true"/>
    <include file="schema/NGPME-8398_add_column_multi_stop_surcharge.sql" relativeToChangelogFile="true"/>
    <include file="schema/NGPME-8266-add-columns-dynp_surges_ngp.sql" relativeToChangelogFile="true"/>
    <include file="schema/NGPME-9163_add_table_new_pricing_model_config_change.sql" relativeToChangelogFile="true"/>
    <include file="schema/NGPME-9164_add_table_key_value_changes.sql" relativeToChangelogFile="true"/>
    <include file="schema/NGPME-7825_create_s2cell_schema.sql" relativeToChangelogFile="true"/>
    <include file="schema/NGPME-9428-adjust_dynp_surge_logs_table.sql" relativeToChangelogFile="true"/>
    <include file="schema/NGPME-10449_create_seq_for_LOCATION_table_ddl_20250430.sql" relativeToChangelogFile="true"/>
    <include file="schema/NGPME-10449_create_seq_for_LOC_SURCHARGE_table_ddl_20250430.sql" relativeToChangelogFile="true"/>
    <include file="schema/NGPME-10887_create_flatfare_adjustment_conf_table.sql" relativeToChangelogFile="true"/>
    <include file="schema/NGPME-10650-create-flat_fare_breakdown_detail.TRIP_ID-index.sql" relativeToChangelogFile="true"/>
    <include file="schema/NGPME-9429-add-new-columns-for-additional-charges.sql" relativeToChangelogFile="true"/>

    <!--
        IMPORTANT: Starting from this point, we are adopting a new naming convention for better organization and readability.

        NEW NAMING CONVENTION:
        - Use sequential numbering: 0001_xxx.sql, 0002_xxx.sql, 0003_xxx.sql, etc.
        - This approach provides clear execution order and makes it easier to understand the migration sequence
        - Previous files used JIRA ticket numbers (NGPME-XXXX_xxx.sql) which made it difficult to determine execution order

        GUIDANCE FOR FUTURE DEVELOPERS:
        1. Use format: [XX_descriptive_name.sql] where XX is the next sequential number (0001, 0002, 0003, etc.)
        2. Choose descriptive names that clearly indicate what the migration does
        3. Always increment the number from the last entry in this changelog
        4. Example: If the last file is "0001_xxx.sql", your next file should be "0002_your_migration_name.sql"

        This approach improves maintainability and makes the migration history much clearer.
    -->
    <include file="schema/0001_create_surge_computation_model.sql" relativeToChangelogFile="true"/>
    <include file="schema/0002_create_static_time_based_configuration.sql" relativeToChangelogFile="true"/>
    <include file="schema/0003_create_static_region_based_configuration.sql" relativeToChangelogFile="true"/>
    <include file="schema/0004_create_surge_computation_region_model_distribution.sql" relativeToChangelogFile="true"/>
    <include file="schema/0005_create_surge_computation_model_surges.sql" relativeToChangelogFile="true"/>
    <include file="schema/0006_create_ml_create_booking_request_agg_stats.sql" relativeToChangelogFile="true"/>
    <include file="schema/0007_create_partitioned_table_surge_computation_model_api_logs.sql" relativeToChangelogFile="true"/>
    <include file="schema/0008_create_ml_get_fare_request_agg_stats.sql" relativeToChangelogFile="true"/>
    <include file="schema/0009_add_index_on_create_ml_booking_request_agg_stats.sql" relativeToChangelogFile="true"/>
    <include file="schema/0010_alter_static_time_based_configuration.sql" relativeToChangelogFile="true"/>
    <include file="schema/0011_add_index_on_ml_get_fare_request_agg_stats.sql" relativeToChangelogFile="true"/>
</databaseChangeLog>

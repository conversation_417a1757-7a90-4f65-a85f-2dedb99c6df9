--liquibase formatted sql
--changeset <EMAIL>:NGPME-9164_add_table_key_value_changes.sql
--add table config_key_value_change
CREATE TABLE config_key_value_change (
    unique_payload_hash         VARCHAR(255),
    key                         VARCHAR(512) NOT NULL,
    value                       TEXT,
    created_dt                  timestamp      NOT NULL ,
    created_by                  varchar(50)    NOT NULL ,
    updated_dt                  timestamp      NULL     ,
    updated_by                  varchar(50)    NULL,
    CONSTRAINT pk_config_key_value_change PRIMARY KEY (unique_payload_hash, key)
);


-- Add comments on the table
COMMENT ON TABLE config_key_value_change IS 'Description of your table';

-- Add comments on the columns
COMMENT ON COLUMN config_key_value_change.unique_payload_hash IS 'Unique hash for the payload';
COMMENT ON COLUMN config_key_value_change.key IS 'Key associated with the value';
COMMENT ON COLUMN config_key_value_change.value IS 'Value corresponding to the key';
COMMENT ON COLUMN config_key_value_change.created_dt IS 'Timestamp when the record was created';
COMMENT ON COLUMN config_key_value_change.created_by IS 'User who created the record';
COMMENT ON COLUMN config_key_value_change.updated_dt IS 'Timestamp when the record was last updated';
COMMENT ON COLUMN config_key_value_change.updated_by IS 'User who last updated the record';
--rollback DROP TABLE IF EXISTS config_key_value_change;
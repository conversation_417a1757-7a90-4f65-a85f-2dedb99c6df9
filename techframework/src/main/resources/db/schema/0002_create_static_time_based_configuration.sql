--liquibase formatted sql
--changeset <EMAIL>:0002_create_static_time_based_configuration.sql splitStatements:false
--create surge_computation_static_time_based_configurations table with applied_hours as JSONB and audit table with triggers

-- Create the day_of_week enum type
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'day_of_week_enum') THEN
        CREATE TYPE day_of_week_enum AS ENUM ('MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT', 'SUN', 'PUBLIC_HOLIDAY');
    END IF;
END$$;

-- Create a cast from varchar to day_of_week_enum
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_cast WHERE castsource = 'varchar'::regtype AND casttarget = 'day_of_week_enum'::regtype) THEN
        CREATE CAST (varchar AS day_of_week_enum) WITH INOUT AS IMPLICIT;
    END IF;
END$$;

-- Create the main table
CREATE TABLE IF NOT EXISTS surge_computation_static_time_based_configurations (
    id                          BIGINT not null
        constraint PK_SURGE_COMPUTATION_static_time_based_CONFIGURATIONS
            primary key,
    name                        VARCHAR(255) not null,
    version                     VARCHAR(50) not null,
    effective_from              TIMESTAMP not null,
    effective_to                TIMESTAMP,
    description                 TEXT,
    applied_hours               JSONB not null,
    created_by                  VARCHAR(255) not null default 'SYSTEM',
    created_dt                  TIMESTAMP not null default CURRENT_TIMESTAMP,
    updated_by                  VARCHAR(255),
    updated_dt                  TIMESTAMP,
    -- Basic constraint: ensure applied_hours is a JSON array
    CONSTRAINT models_is_array CHECK (jsonb_typeof(applied_hours) = 'array')
);

COMMENT ON TABLE surge_computation_static_time_based_configurations is 'Table to store surge computation time-based static configurations';
COMMENT ON COLUMN surge_computation_static_time_based_configurations.id is 'Primary key for the surge computation time-based static configuration';
COMMENT ON COLUMN surge_computation_static_time_based_configurations.name is 'Name of the surge computation time-based static configuration';
COMMENT ON COLUMN surge_computation_static_time_based_configurations.version is 'Version of the configuration';
COMMENT ON COLUMN surge_computation_static_time_based_configurations.effective_from is 'Date and time from which the configuration is effective';
COMMENT ON COLUMN surge_computation_static_time_based_configurations.effective_to is 'Date and time until which the configuration is effective (null means indefinite)';
COMMENT ON COLUMN surge_computation_static_time_based_configurations.description is 'Description of the configuration';
COMMENT ON COLUMN surge_computation_static_time_based_configurations.applied_hours is 'JSONB array of applied hours with day_of_week, hour_of_day, and value. Example: [{"day_of_week": "MON", "hour_of_day": 8, "value": "1.2"}, {"day_of_week": "PUBLIC_HOLIDAY", "hour_of_day": 18, "value": "1.5"}]';
COMMENT ON COLUMN surge_computation_static_time_based_configurations.created_by is 'User who created the record';
COMMENT ON COLUMN surge_computation_static_time_based_configurations.created_dt is 'Date and time when the record was created';
COMMENT ON COLUMN surge_computation_static_time_based_configurations.updated_by is 'User who last updated the record';
COMMENT ON COLUMN surge_computation_static_time_based_configurations.updated_dt is 'Date and time when the record was last updated';

-- Create the sequence for the primary key
CREATE SEQUENCE IF NOT EXISTS SEQ_surge_computation_static_time_based_configurations__id AS BIGINT
    INCREMENT 1
    START 1
    MINVALUE 1
    CACHE 1;

ALTER TABLE surge_computation_static_time_based_configurations ALTER COLUMN id SET DEFAULT nextval('SEQ_surge_computation_static_time_based_configurations__id');

CREATE OR REPLACE FUNCTION populate_effective_to_before_insert_static_time_config() RETURNS TRIGGER AS $$
BEGIN
    UPDATE surge_computation_static_time_based_configurations
    SET effective_to = NEW.effective_from - INTERVAL '1 SECOND'
    WHERE name = NEW.name
      AND effective_to IS NULL
      AND effective_from < NEW.effective_from;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS before_insert_static_time_config_trigger ON surge_computation_static_time_based_configurations;
CREATE TRIGGER before_insert_static_time_config_trigger
    BEFORE INSERT
    ON surge_computation_static_time_based_configurations
    FOR EACH ROW
EXECUTE FUNCTION populate_effective_to_before_insert_static_time_config();

-- Create the audit table
CREATE TABLE IF NOT EXISTS surge_computation_static_time_based_configurations_audit (
    audit_id                    BIGSERIAL PRIMARY KEY,
    operation_type              CHAR(1) NOT NULL,
    operation_timestamp         TIMESTAMP NOT NULL,
    operation_user              VARCHAR(255),
    id                          BIGINT,
    name                        VARCHAR(255),
    version                     VARCHAR(50),
    effective_from              TIMESTAMP,
    effective_to                TIMESTAMP,
    description                 TEXT,
    applied_hours               JSONB,
    surge_computation_model_id  BIGINT,
    created_by                  VARCHAR(255),
    created_dt                  TIMESTAMP,
    updated_by                  VARCHAR(255),
    updated_dt                  TIMESTAMP
);

COMMENT ON TABLE surge_computation_static_time_based_configurations_audit is 'Audit table for surge_computation_static_time_based_configurations';
COMMENT ON COLUMN surge_computation_static_time_based_configurations_audit.audit_id is 'Primary key for the audit record';
COMMENT ON COLUMN surge_computation_static_time_based_configurations_audit.operation_type is 'Type of operation (I=Insert, U=Update, D=Delete)';
COMMENT ON COLUMN surge_computation_static_time_based_configurations_audit.operation_timestamp is 'Timestamp when the operation occurred';
COMMENT ON COLUMN surge_computation_static_time_based_configurations_audit.operation_user is 'User who performed the operation';

-- Create the audit trigger
CREATE OR REPLACE PROCEDURE insert_surge_computation_static_time_based_configurations_audit(data surge_computation_static_time_based_configurations, action text, schema_name text)
  LANGUAGE plpgsql
AS $$
BEGIN
  EXECUTE format('
    INSERT INTO %I.surge_computation_static_time_based_configurations_audit(
        operation_type,
        operation_timestamp,
        operation_user,
        id,
        name,
        version,
        effective_from,
        effective_to,
        description,
        applied_hours,
        created_by,
        created_dt,
        updated_by,
        updated_dt
    )
    VALUES(
      $1,
      NOW(),
      $2.updated_by,
      $2.id,
      $2.name,
      $2.version,
      $2.effective_from,
      $2.effective_to,
      $2.description,
      $2.applied_hours,
      $2.created_by,
      $2.created_dt,
      $2.updated_by,
      $2.updated_dt
    );
    ', schema_name) USING action, data;
END;
$$;

-- Function creation
CREATE OR REPLACE FUNCTION audit_surge_computation_static_time_based_configurations() RETURNS TRIGGER AS
$$
  DECLARE
    current_schema_name text;
  BEGIN
    current_schema_name := TG_TABLE_SCHEMA;

    IF (TG_OP = 'DELETE') THEN
      EXECUTE 'CALL insert_surge_computation_static_time_based_configurations_audit($1, ''D'', $2);' USING OLD, current_schema_name;
    ELSIF (TG_OP = 'INSERT' ) THEN
      EXECUTE 'CALL insert_surge_computation_static_time_based_configurations_audit($1, ''I'', $2);' USING NEW, current_schema_name;
    ELSIF (TG_OP = 'UPDATE')  THEN
      EXECUTE 'CALL insert_surge_computation_static_time_based_configurations_audit($1, ''U'', $2);' USING NEW, current_schema_name;
    END IF;

    RETURN NULL; -- result is ignored since this is an AFTER trigger
  END;
$$ LANGUAGE plpgsql;


-- Create the trigger
DROP TRIGGER IF EXISTS audit_surge_computation_static_time_based_configurations_trigger ON surge_computation_static_time_based_configurations;
CREATE TRIGGER audit_surge_computation_static_time_based_configurations_trigger
    AFTER INSERT OR UPDATE OR DELETE
    ON surge_computation_static_time_based_configurations
    FOR EACH ROW
    EXECUTE FUNCTION audit_surge_computation_static_time_based_configurations();

-- Create unique index for name and version
CREATE UNIQUE INDEX IF NOT EXISTS idx_static_time_based_configurations_unique
    ON surge_computation_static_time_based_configurations(name, version);

-- Create index for effective date range to improve query performance
CREATE INDEX IF NOT EXISTS idx_static_time_based_configurations_effective_dates
    ON surge_computation_static_time_based_configurations(effective_from, effective_to);

-- Create index for version and effective date range to improve query performance
CREATE INDEX IF NOT EXISTS idx_static_time_based_configurations_version_effective_dates
    ON surge_computation_static_time_based_configurations(version desc, effective_from, effective_to);

-- Create a function to check for overlapping date ranges for the same name
CREATE OR REPLACE FUNCTION check_time_based_overlapping_date_ranges()
    RETURNS TRIGGER AS $$
BEGIN
    -- Skip the check for the audit table
    IF TG_TABLE_NAME = 'surge_computation_static_time_based_configurations_audit' THEN
        RETURN NEW;
    END IF;

    -- Check if there are any overlapping date ranges for the same name
    IF EXISTS (
        SELECT 1 FROM surge_computation_static_time_based_configurations
        WHERE name = NEW.name
          AND id != NEW.id
          AND (
            -- Case 1: NEW.effective_from falls within an existing range
            (NEW.effective_from >= effective_from AND
             (effective_to IS NULL OR NEW.effective_from <= effective_to))
                OR
                -- Case 2: NEW.effective_to falls within an existing range
            (NEW.effective_to IS NOT NULL AND
             NEW.effective_to >= effective_from AND
             (effective_to IS NULL OR NEW.effective_to <= effective_to))
                OR
                -- Case 3: NEW range completely contains an existing range
            (NEW.effective_from <= effective_from AND
             (NEW.effective_to IS NULL OR
              (effective_to IS NOT NULL AND NEW.effective_to >= effective_to)))
            )
    ) THEN
        RAISE EXCEPTION 'Overlapping effective date ranges are not allowed for the same configuration name';
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to check for overlapping date ranges before insert or update
DROP TRIGGER IF EXISTS check_time_based_overlapping_date_ranges_trigger ON surge_computation_static_time_based_configurations;
CREATE TRIGGER check_time_based_overlapping_date_ranges_trigger
    BEFORE INSERT OR UPDATE
    ON surge_computation_static_time_based_configurations
    FOR EACH ROW
EXECUTE FUNCTION check_time_based_overlapping_date_ranges();

--rollback DROP TRIGGER IF EXISTS check_time_based_overlapping_date_ranges_trigger ON surge_computation_static_time_based_configurations;
--rollback DROP FUNCTION IF EXISTS check_time_based_overlapping_date_ranges();
--rollback DROP TRIGGER IF EXISTS audit_surge_computation_static_time_based_configurations_trigger ON surge_computation_static_time_based_configurations;
--rollback DROP FUNCTION IF EXISTS audit_surge_computation_static_time_based_configurations();
--rollback DROP PROCEDURE IF EXISTS insert_surge_computation_static_time_based_configurations_audit(surge_computation_static_time_based_configurations, text, text);
--rollback DROP TABLE IF EXISTS surge_computation_static_time_based_configurations_audit;
--rollback DROP INDEX IF EXISTS idx_static_time_based_configurations_unique;
--rollback DROP INDEX IF EXISTS idx_static_time_based_configurations_effective_dates;
--rollback DROP INDEX IF EXISTS idx_static_time_based_configurations_version_effective_dates;
--rollback DROP FUNCTION IF EXISTS populate_effective_to_before_insert_static_time_config();
--rollback DROP TRIGGER IF EXISTS before_insert_static_time_config_trigger ON surge_computation_static_time_based_configurations;
--rollback ALTER TABLE surge_computation_static_time_based_configurations ALTER COLUMN id DROP DEFAULT;
--rollback DROP SEQUENCE IF EXISTS SEQ_surge_computation_static_time_based_configurations__id;
--rollback DROP TABLE IF EXISTS surge_computation_static_time_based_configurations;
--rollback DROP TYPE IF EXISTS day_of_week_enum;

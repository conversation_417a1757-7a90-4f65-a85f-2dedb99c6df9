--liquibase formatted sql
--changeset <EMAIL>:0007_create_partitioned_table_surge_computation_model_api_logs.sql splitStatements:false
--create surge_computation_model_api_logs partitioned table

/*
 * =====================================================================================
 * SURGE COMPUTATION MODEL API LOGS - PARTITIONED TABLE CREATION
 * =====================================================================================
 *
 * This script creates the surge_computation_model_api_logs table as a partitioned table
 * from the beginning, optimized for time-based queries using Singapore timezone.
 *
 * EXECUTION FLOW:
 * 1. CREATE TIMEZONE FUNCTION      → Singapore timezone calculation function
 * 2. CREATE PARTITION FUNCTION     → Partition management with automatic indexing
 * 3. CREATE MANAGEMENT FUNCTION    → Automatic partition creation for current and future months
 * 4. CREATE SEQUENCE               → Auto-increment sequence for ID column
 * 5. CREATE MAIN TABLE             → surge_computation_model_api_logs (partitioned by month)
 * 6. ADD COMMENTS                  → Comprehensive table and column documentation
 * 7. SET OWNERSHIP                 → Link sequence to table for proper dependency management
 * 8. CREATE INITIAL PARTITIONS     → Initial monthly partitions (13 months: 6 back + current + 6 forward)
 * 9. CREATE CLEANUP FUNCTION       → Automatic partition cleanup function for maintenance
 *
 * PARTITIONING STRATEGY:
 * - Partitioned by Singapore timezone month (singapore_month column)
 * - Automatic partition creation for current +/- 6 months (13 total partitions initially)
 * - Each partition includes optimized indexes for performance
 * - Supports historical data and future growth
 *
 * IDEMPOTENCY:
 * - Uses IF NOT EXISTS throughout for safe repeated execution
 * - Can be run multiple times without errors or data loss
 * - Suitable for both fresh deployments and re-deployments
 *
 * PERFORMANCE FEATURES:
 * - Monthly partitioning reduces query scope for time-based searches
 * - Automatic indexes on each partition (model_name+timestamp, status_code, model_id)
 * - Singapore timezone calculation for accurate business logic alignment
 * - Built-in partition cleanup for long-term maintenance
 *
 * =====================================================================================
 */

-- Step 1: Create Singapore timezone calculation function
CREATE OR REPLACE FUNCTION get_singapore_month(ts TIMESTAMP)
    RETURNS DATE AS $$
BEGIN
    RETURN DATE_TRUNC('month', ts AT TIME ZONE 'Asia/Singapore')::DATE;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Step 2: Create partition management function with automatic indexing
CREATE OR REPLACE FUNCTION create_monthly_partition_for_logs(target_month DATE, table_name TEXT DEFAULT 'surge_computation_model_api_logs')
    RETURNS TEXT AS $$
DECLARE
    partition_name TEXT;
    start_date DATE;
    end_date DATE;
    sql_stmt TEXT;
BEGIN
    start_date := DATE_TRUNC('month', target_month);
    end_date := start_date + INTERVAL '1 month';
    partition_name := 'surge_computation_model_api_logs_' || TO_CHAR(start_date, 'YYYY_MM');

    -- Create partition with date range boundaries
    sql_stmt := format(
            'CREATE TABLE IF NOT EXISTS %I PARTITION OF %I
             FOR VALUES FROM (%L) TO (%L)',
            partition_name, table_name, start_date, end_date
                );
    EXECUTE sql_stmt;

    -- Create performance indexes on each partition
    -- Index 1: Model name + timestamp (DESC) for recent API calls by model
    EXECUTE format(
            'CREATE INDEX IF NOT EXISTS idx_%s_model_timestamp
             ON %I (model_name, create_timestamp DESC)',
            partition_name, partition_name
            );

    -- Index 2: Status code for error analysis and monitoring
    EXECUTE format(
            'CREATE INDEX IF NOT EXISTS idx_%s_status
             ON %I (status_code)',
            partition_name, partition_name
            );

    -- Index 3: Model ID for foreign key lookups and joins
    EXECUTE format(
            'CREATE INDEX IF NOT EXISTS idx_%s_model_id
             ON %I (model_id)',
            partition_name, partition_name
            );

    RETURN partition_name;
END;
$$ LANGUAGE plpgsql;

-- Step 3: Create automatic partition management function for current and future months
CREATE OR REPLACE FUNCTION ensure_monthly_partitions_exist(months_ahead INTEGER DEFAULT 3)
    RETURNS TEXT[] AS $$
DECLARE
    singapore_current_date DATE;
    target_month DATE;
    i INTEGER;
    created_partitions TEXT[] := '{}';
    partition_name TEXT;
BEGIN
    singapore_current_date := (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Singapore')::DATE;

    -- Create partitions for current month + specified months ahead
    FOR i IN 0..months_ahead LOOP
            target_month := DATE_TRUNC('month', singapore_current_date) + (i || ' month')::INTERVAL;

            -- Check if partition already exists before creating
            IF NOT EXISTS (
                SELECT 1 FROM pg_class c
                                  JOIN pg_namespace n ON n.oid = c.relnamespace
                WHERE c.relname = 'surge_computation_model_api_logs_' || TO_CHAR(target_month, 'YYYY_MM')
                  AND n.nspname = current_schema()
            ) THEN
                partition_name := create_monthly_partition_for_logs(target_month);
                created_partitions := array_append(created_partitions, partition_name);
            END IF;
        END LOOP;

    RETURN created_partitions;
END;
$$ LANGUAGE plpgsql;

-- Step 4: Create sequence for primary key auto-increment
CREATE SEQUENCE IF NOT EXISTS surge_computation_model_api_logs_id_seq AS BIGINT
    INCREMENT 1
    START 1
    MINVALUE 1
    CACHE 1;

-- Step 5: Create main partitioned table with comprehensive structure
CREATE TABLE IF NOT EXISTS surge_computation_model_api_logs (
    id               BIGINT NOT NULL DEFAULT nextval('surge_computation_model_api_logs_id_seq'),
    model_id         BIGINT       NOT NULL,
    model_name       VARCHAR(255) NOT NULL,
    endpoint_url     VARCHAR(255) NOT NULL,
    create_timestamp TIMESTAMP    NOT NULL DEFAULT (now() at time zone 'utc')::timestamp,
    status_code      INT          NOT NULL,
    request_params   JSONB        NOT NULL,
    response_body    JSONB        NOT NULL,
    singapore_month  DATE         NOT NULL DEFAULT (get_singapore_month((now() at time zone 'utc')::timestamp)),
    PRIMARY KEY (id, singapore_month)
) PARTITION BY RANGE (singapore_month);

-- Step 6: Add comprehensive table and column documentation
COMMENT ON TABLE surge_computation_model_api_logs IS 'Partitioned table for surge computation model API logs, organized by Singapore timezone months for optimal time-based queries and maintenance';
COMMENT ON COLUMN surge_computation_model_api_logs.id IS 'Primary key for the surge computation model API logs, auto-incremented';
COMMENT ON COLUMN surge_computation_model_api_logs.model_id IS 'Foreign key reference to the surge computation model ID';
COMMENT ON COLUMN surge_computation_model_api_logs.model_name IS 'Name/version identifier of the surge computation model used';
COMMENT ON COLUMN surge_computation_model_api_logs.endpoint_url IS 'API endpoint URL that was called for the surge computation';
COMMENT ON COLUMN surge_computation_model_api_logs.create_timestamp IS 'Timestamp when the API request was made (stored in UTC)';
COMMENT ON COLUMN surge_computation_model_api_logs.status_code IS 'HTTP response status code (200, 404, 500, etc.) for monitoring and debugging';
COMMENT ON COLUMN surge_computation_model_api_logs.request_params IS 'JSONB field storing dynamic request parameters (region_id, demand, supply, etc.)';
COMMENT ON COLUMN surge_computation_model_api_logs.response_body IS 'JSONB field storing API response data (surge_factor, status, error details, etc.)';
COMMENT ON COLUMN surge_computation_model_api_logs.singapore_month IS 'Partition key: first day of month in Singapore timezone for data organization';

-- Step 7: Set sequence ownership for proper dependency management
ALTER SEQUENCE surge_computation_model_api_logs_id_seq OWNED BY surge_computation_model_api_logs.id;

-- Step 8: Create initial partition set covering past and future months
DO $$
    DECLARE
        singapore_current_date DATE;
        target_month DATE;
        i INTEGER;
    BEGIN
        singapore_current_date := (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Singapore')::DATE;

        -- Create 13 partitions: 6 months ago + current month + 6 months ahead
        -- This provides coverage for historical data import and future growth
        FOR i IN -6..6 LOOP
                target_month := DATE_TRUNC('month', singapore_current_date) + (i || ' month')::INTERVAL;
                PERFORM create_monthly_partition_for_logs(target_month, 'surge_computation_model_api_logs');
            END LOOP;
    END $$;

-- Step 9: Create partition cleanup function for maintenance
CREATE OR REPLACE FUNCTION cleanup_old_log_partitions(months_to_keep INTEGER DEFAULT 24)
    RETURNS TEXT[] AS $$
DECLARE
    cutoff_date DATE;
    partition_record RECORD;
    dropped_partitions TEXT[] := '{}';
BEGIN
    -- Calculate cutoff date based on Singapore timezone
    cutoff_date := DATE_TRUNC('month', (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Singapore')::DATE - (months_to_keep || ' months')::INTERVAL);

    -- Find and drop partitions older than cutoff date
    FOR partition_record IN
        SELECT schemaname, tablename
        FROM pg_tables
        WHERE tablename LIKE 'surge_computation_model_api_logs_%'
          AND tablename ~ '^surge_computation_model_api_logs_\d{4}_\d{2}$'
          AND TO_DATE(SUBSTRING(tablename FROM '\d{4}_\d{2}'), 'YYYY_MM') < cutoff_date
        LOOP
            EXECUTE 'DROP TABLE IF EXISTS ' || quote_ident(partition_record.schemaname) || '.' || quote_ident(partition_record.tablename);
            dropped_partitions := array_append(dropped_partitions, partition_record.tablename);
        END LOOP;

    RETURN dropped_partitions;
END;
$$ LANGUAGE plpgsql;

--rollback DROP TABLE IF EXISTS surge_computation_model_api_logs CASCADE;
--rollback DROP SEQUENCE IF EXISTS surge_computation_model_api_logs_id_seq;
--rollback DROP FUNCTION IF EXISTS cleanup_old_log_partitions(INTEGER);
--rollback DROP FUNCTION IF EXISTS ensure_monthly_partitions_exist(INTEGER);
--rollback DROP FUNCTION IF EXISTS create_monthly_partition_for_logs(DATE, TEXT);
--rollback DROP FUNCTION IF EXISTS get_singapore_month(TIMESTAMP);
--liquibase formatted sql
--changeset <EMAIL>:NGPME-6018_create_seq_for_fare_type_conf.sql
--create the sequence for fare_type_conf table

CREATE SEQUENCE IF NOT EXISTS SEQ_fare_type_conf__fare_type_id AS BIGINT
    INCREMENT 1
    START 1
    MINVALUE 1
    CACHE 1;

SELECT setval('SEQ_fare_type_conf__fare_type_id', (SELECT MAX(fare_type_id) FROM fare_type_conf));

ALTER TABLE fare_type_conf ALTER COLUMN fare_type_id SET DEFAULT nextval('SEQ_fare_type_conf__fare_type_id')

--rollback not required

--liquibase formatted sql
--changeset <EMAIL>:0004_create_surge_computation_region_model_distribution.sql splitStatements:false
--create surge_computation_region_model_distribution table with audit table and triggers

-- Create the main table
CREATE TABLE IF NOT EXISTS surge_computation_region_model_distribution
(
    id             BIGINT                              NOT NULL
        CONSTRAINT PK_SURGE_COMPUTATION_REGION_MODEL_DISTRIBUTION PRIMARY KEY,
    region_id      BIGINT                              NOT NULL,
    models         JSONB                               NOT NULL,
    effective_from TIMESTAMP                           NOT NULL,
    effective_to   TIMESTAMP,
    created_by     VARCHAR(255)                        NOT NULL,
    created_dt     TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_by     <PERSON><PERSON><PERSON><PERSON>(255),
    updated_dt     TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (region_id, effective_from, effective_to),
    -- Basic constraint: ensure models is a JSON array
    CONSTRAINT models_is_array CHECK (jsonb_typeof(models) = 'array')
);

COMMENT ON COLUMN surge_computation_region_model_distribution.id IS 'Unique identifier for each record';
COMMENT ON COLUMN surge_computation_region_model_distribution.region_id IS 'The H3 region id from ngp_me_address.h3_region_definitions';
COMMENT ON COLUMN surge_computation_region_model_distribution.models IS 'JSON array of model distributions with modelId and percentage [{modelId: 1, percentage: 50}, {modelId: 2, percentage: 50}]';
COMMENT ON COLUMN surge_computation_region_model_distribution.effective_from IS 'When this record becomes valid';
COMMENT ON COLUMN surge_computation_region_model_distribution.effective_to IS 'When this record is no longer valid, NULL means it is valid indefinitely';
COMMENT ON COLUMN surge_computation_region_model_distribution.created_by IS 'User who created the record';
COMMENT ON COLUMN surge_computation_region_model_distribution.created_dt IS 'Timestamp of record creation';
COMMENT ON COLUMN surge_computation_region_model_distribution.updated_by IS 'User who last updated the record';
COMMENT ON COLUMN surge_computation_region_model_distribution.updated_dt IS 'Timestamp of last update';

-- Create the sequence for the primary key
CREATE SEQUENCE IF NOT EXISTS SEQ_surge_computation_region_model_distribution__id AS BIGINT
    INCREMENT 1
    START 1
    MINVALUE 1
    CACHE 1;

ALTER TABLE surge_computation_region_model_distribution
    ALTER COLUMN id SET DEFAULT nextval('SEQ_surge_computation_region_model_distribution__id');

-- Create index to improve query performance
CREATE INDEX IF NOT EXISTS idx_surge_region_effective_time
    ON surge_computation_region_model_distribution (effective_from, effective_to);

-- Create a GIN index for the JSONB column to improve query performance
CREATE INDEX IF NOT EXISTS idx_surge_region_models_gin
    ON surge_computation_region_model_distribution USING GIN (models);

-- ==========================================
-- Function: prevent_time_overlap
-- Purpose : Prevent overlapping effective time ranges
--           for the same region_id, handling NULL effective_to values
-- Trigger : BEFORE INSERT OR UPDATE
-- ==========================================
CREATE OR REPLACE FUNCTION prevent_time_overlap()
    RETURNS TRIGGER AS
$$
DECLARE
    overlap_count INT;
BEGIN
    -- Check for overlapping time ranges with existing records
    -- Consider two cases:
    -- 1. When new record's effective_to is NOT NULL: standard overlap detection
    -- 2. When new record's effective_to is NULL: check overlap with any record starting after new effective_from

    IF NEW.effective_to IS NOT NULL THEN
        -- Standard overlap detection (new record has end time)
        SELECT COUNT(*)
        INTO overlap_count
        FROM surge_computation_region_model_distribution
        WHERE region_id = NEW.region_id
          AND id IS DISTINCT FROM NEW.id  -- Skip comparing with itself on update
          AND (
            -- Normal time range overlap detection
            (NEW.effective_from < effective_to OR effective_to IS NULL)
                AND
            (effective_from < NEW.effective_to)
            );
    ELSE
        -- When new record's effective_to is NULL (infinity)
        SELECT COUNT(*)
        INTO overlap_count
        FROM surge_computation_region_model_distribution
        WHERE region_id = NEW.region_id
          AND id IS DISTINCT FROM NEW.id  -- Skip comparing with itself on update
          AND (
            -- Check any record that starts after new effective_from
            -- or overlaps with new effective_from
            effective_from >= NEW.effective_from
                OR
            (effective_from < NEW.effective_from AND (effective_to > NEW.effective_from OR effective_to IS NULL))
            );
    END IF;

    -- If overlapping records exist, throw exception to block the operation
    IF overlap_count > 0 THEN
        RAISE EXCEPTION 'Time range overlaps with an existing entry for region %.',
            NEW.region_id;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create time overlap validation trigger
DROP TRIGGER IF EXISTS prevent_time_overlap_trigger ON surge_computation_region_model_distribution;
CREATE TRIGGER prevent_time_overlap_trigger
    BEFORE INSERT OR UPDATE
    ON surge_computation_region_model_distribution
    FOR EACH ROW
EXECUTE FUNCTION prevent_time_overlap();

-- ==========================================
-- Function: validate_total_percentage
-- Purpose : Validate that model percentages sum to 100%
-- Trigger : BEFORE INSERT OR UPDATE
-- ==========================================
CREATE OR REPLACE FUNCTION validate_total_percentage()
    RETURNS TRIGGER AS
$$
DECLARE
    total_percentage NUMERIC(5, 2);
BEGIN
    -- Calculate percentage sum in one operation
    SELECT COALESCE(SUM((value->>'percentage')::NUMERIC(5,2)), 0) INTO total_percentage
    FROM jsonb_array_elements(NEW.models) AS arr(value);

    -- Check if sum equals 100% (accounting for floating point precision)
    IF total_percentage < 99.99 OR total_percentage > 100.01 THEN
        RAISE EXCEPTION 'The sum of all percentages must be 100%%, but got %', total_percentage;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create percentage validation trigger
DROP TRIGGER IF EXISTS validate_total_percentage_trigger ON surge_computation_region_model_distribution;
CREATE TRIGGER validate_total_percentage_trigger
    BEFORE INSERT OR UPDATE
    ON surge_computation_region_model_distribution
    FOR EACH ROW
EXECUTE FUNCTION validate_total_percentage();

-- ==========================================
-- Function: populate_effective_to_before_insert_model_distribution
-- Purpose : Populate effective_to before insert
-- Trigger : BEFORE INSERT
-- ==========================================
CREATE OR REPLACE FUNCTION populate_effective_to_before_insert_model_distribution() RETURNS TRIGGER AS $$
BEGIN
    UPDATE surge_computation_region_model_distribution
    SET effective_to = NEW.effective_from - INTERVAL '1 SECOND'
    WHERE region_id = NEW.region_id
      AND effective_to IS NULL
      AND effective_from < NEW.effective_from;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS before_insert_model_distribution_trigger ON surge_computation_region_model_distribution;
CREATE TRIGGER before_insert_model_distribution_trigger
    BEFORE INSERT
    ON surge_computation_region_model_distribution
    FOR EACH ROW
EXECUTE FUNCTION populate_effective_to_before_insert_model_distribution();

-- Create audit table
CREATE TABLE IF NOT EXISTS surge_computation_region_model_distribution_audit
(
    id               BIGSERIAL PRIMARY KEY,
    region_id        BIGINT        NOT NULL,
    models           JSONB         NOT NULL,
    effective_from   TIMESTAMP     NOT NULL,
    effective_to     TIMESTAMP,
    change_type      VARCHAR(10)   NOT NULL CHECK (change_type IN ('INSERT', 'UPDATE', 'DELETE')),
    change_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    changed_by       VARCHAR(255)  NOT NULL
);

-- Audit trigger function
CREATE OR REPLACE FUNCTION audit_surge_computation_region_model_distribution()
    RETURNS TRIGGER AS
$$
DECLARE
    operation       TEXT;
    affected_record RECORD;
BEGIN
    -- Determine operation type
    IF (TG_OP = 'INSERT') THEN
        operation := 'INSERT';
        affected_record := NEW;
    ELSIF (TG_OP = 'UPDATE') THEN
        operation := 'UPDATE';
        affected_record := NEW;
    ELSIF (TG_OP = 'DELETE') THEN
        operation := 'DELETE';
        affected_record := OLD;
    ELSE
        RAISE WARNING 'Unknown operation: %', TG_OP;
        RETURN NULL;
    END IF;

    -- Insert audit record
    INSERT INTO surge_computation_region_model_distribution_audit
    (region_id,
     models,
     effective_from,
     effective_to,
     change_type,
     changed_by)
    VALUES (affected_record.region_id,
            affected_record.models,
            affected_record.effective_from,
            affected_record.effective_to,
            operation,
            COALESCE(affected_record.updated_by, affected_record.created_by));

    -- Return appropriate record based on operation type
    IF TG_OP = 'DELETE' THEN
        RETURN OLD;
    ELSE
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Bind audit function to trigger
DROP TRIGGER IF EXISTS surge_computation_region_model_distribution_audit_trigger
    ON surge_computation_region_model_distribution;
CREATE TRIGGER surge_computation_region_model_distribution_audit_trigger
    AFTER INSERT OR UPDATE OR DELETE
    ON surge_computation_region_model_distribution
    FOR EACH ROW
EXECUTE FUNCTION audit_surge_computation_region_model_distribution();


-- rollback DROP TRIGGER IF EXISTS surge_computation_region_model_distribution_audit_trigger ON surge_computation_region_model_distribution;
-- rollback DROP FUNCTION IF EXISTS audit_surge_computation_region_model_distribution();
-- rollback DROP TABLE IF EXISTS surge_computation_region_model_distribution_audit;
-- rollback DROP TRIGGER IF EXISTS before_insert_static_region_config_trigger ON surge_computation_region_model_distribution;
-- rollback DROP FUNCTION IF EXISTS populate_effective_to_before_insert_static_region_config();
-- rollback DROP TRIGGER IF EXISTS validate_total_percentage_trigger ON surge_computation_region_model_distribution;
-- rollback DROP FUNCTION IF EXISTS validate_total_percentage();
-- rollback DROP TRIGGER IF EXISTS prevent_time_overlap_trigger ON surge_computation_region_model_distribution;
-- rollback DROP FUNCTION IF EXISTS prevent_time_overlap();
-- rollback DROP INDEX IF EXISTS idx_surge_region_models_gin;
-- rollback DROP INDEX IF EXISTS idx_surge_region_effective_time;
-- rollback DROP SEQUENCE IF EXISTS SEQ_surge_computation_region_model_distribution__id;
-- rollback DROP TABLE IF EXISTS surge_computation_region_model_distribution;
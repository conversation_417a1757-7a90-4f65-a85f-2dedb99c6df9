--liquibase formatted sql
--changeset <EMAIL>:0008_create_ml_get_fare_request_agg_stats.sql splitStatements:false
--create ml_get_fare_request_agg_stats table

CREATE TABLE IF NOT EXISTS ml_get_fare_request_agg_stats
(
    id               bigint    not null
        constraint pk_ml_get_fare_request_agg_stats
            primary key,
    start_timestamp  timestamp not null,
    end_timestamp    timestamp not null,
    area_type        varchar(12) not null,
    pickup_region_id bigint,
    region_version   varchar(50),
    model_id         bigint,
    get_fare_count   int not null default 0,
    create_timestamp timestamp not null
);

COMMENT ON TABLE ml_get_fare_request_agg_stats is 'Table to store get fare count information for machine learning';
COMMENT ON COLUMN ml_get_fare_request_agg_stats.id is 'Primary key';
COMMENT ON COLUMN ml_get_fare_request_agg_stats.area_type is 'The area type, "zone" or "region"';
COMMENT ON COLUMN ml_get_fare_request_agg_stats.pickup_region_id is 'The region id from address service';
COMMENT ON COLUMN ml_get_fare_request_agg_stats.region_version is 'The region version from address service, e.g.: 0.0.1';
COMMENT ON COLUMN ml_get_fare_request_agg_stats.model_id is 'The model id from table surge_computation_models, only has value when area_type = "region"';
COMMENT ON COLUMN ml_get_fare_request_agg_stats.get_fare_count is 'The count for user querying to the multi-fare API';
COMMENT ON COLUMN ml_get_fare_request_agg_stats.create_timestamp is 'The create timestamp of this record';

-- Create the sequence for the primary key
CREATE SEQUENCE IF NOT EXISTS SEQ_ml_get_fare_request_agg_stats__id AS BIGINT
    INCREMENT 1
    START 1
    MINVALUE 1
    CACHE 1;

ALTER TABLE ml_get_fare_request_agg_stats
    ALTER COLUMN id SET DEFAULT nextval('SEQ_ml_get_fare_request_agg_stats__id');


CREATE INDEX IF NOT EXISTS idx_ml_get_fare_request_agg_stats_create_timestamp
    ON ml_get_fare_request_agg_stats (create_timestamp desc );



--rollback DROP TABLE IF EXISTS ml_get_fare_request_agg_stats;
--rollback DROP SEQUENCE IF EXISTS SEQ_ml_get_fare_request_agg_stats__id;
--rollback DROP INDEX IF EXISTS idx_ml_get_fare_request_agg_stats_create_timestamp;

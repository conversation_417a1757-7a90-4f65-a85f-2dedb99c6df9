--liquibase formatted sql
--changeset myathin<PERSON><PERSON>@comfortdelgro.com:NGPME-10449_create_seq_for_LOCATION_table_ddl_20250430.sql
--create the sequence for LOCATION table

CREATE SEQUENCE IF NOT EXISTS SEQ_location__location_id AS BIGINT
    INCREMENT 1
    START 1
    MINVALUE 1
    CACHE 1;

SELECT setval('SEQ_location__location_id', (SELECT MAX(location_id) FROM location));

ALTER TABLE location ALTER COLUMN location_id SET DEFAULT nextval('SEQ_location__location_id')

--rollback not required

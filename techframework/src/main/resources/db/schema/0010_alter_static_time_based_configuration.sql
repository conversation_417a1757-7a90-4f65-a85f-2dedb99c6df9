--liquibase formatted sql
--changeset <EMAIL>:0010_alter_static_time_based_configuration.sql splitStatements:false
--add timezone_offset column to surge_computation_static_time_based_configurations and surge_computation_static_time_based_configurations_audit

DO $$
    BEGIN
        IF NOT EXISTS (
            SELECT 1
            FROM information_schema.columns
            WHERE table_name = 'surge_computation_static_time_based_configurations'
              AND column_name = 'timezone_offset'
        ) THEN
            ALTER TABLE surge_computation_static_time_based_configurations
                ADD COLUMN timezone_offset VARCHAR(16);

            COMMENT ON COLUMN surge_computation_static_time_based_configurations.timezone_offset IS 'The time zone used is applicable only applied_hours column, example: "+08:00", "+00:00", "-08:00", "+08:30"';
        END IF;
    END $$;

DO $$
    BEGIN
        IF NOT EXISTS (
            SELECT 1
            FROM information_schema.columns
            WHERE table_name = 'surge_computation_static_time_based_configurations_audit'
              AND column_name = 'timezone_offset'
        ) THEN
            ALTER TABLE surge_computation_static_time_based_configurations_audit
                ADD COLUMN timezone_offset VARCHAR(16);

            COMMENT ON COLUMN surge_computation_static_time_based_configurations_audit.timezone_offset IS 'The time zone used is applicable only applied_hours column, example: "+08:00", "+00:00", "-08:00", "+08:30"';
        END IF;
    END $$;




-- Create the audit trigger
CREATE OR REPLACE PROCEDURE insert_surge_computation_static_time_based_configurations_audit(data surge_computation_static_time_based_configurations, action text, schema_name text)
    LANGUAGE plpgsql
AS $$
BEGIN
    EXECUTE format('
    INSERT INTO %I.surge_computation_static_time_based_configurations_audit(
        operation_type,
        operation_timestamp,
        operation_user,
        id,
        name,
        version,
        effective_from,
        effective_to,
        description,
        applied_hours,
        created_by,
        created_dt,
        updated_by,
        updated_dt,
        timezone_offset
    )
    VALUES(
      $1,
      NOW(),
      $2.updated_by,
      $2.id,
      $2.name,
      $2.version,
      $2.effective_from,
      $2.effective_to,
      $2.description,
      $2.applied_hours,
      $2.created_by,
      $2.created_dt,
      $2.updated_by,
      $2.updated_dt,
      $2.timezone_offset
    );
    ', schema_name) USING action, data;
END;
$$;
--liquibase formatted sql
--changeset <EMAIL>:NGPME-7955_create_dynp_surge_upstream.sql
--create dynp_surges_ngp table

CREATE TABLE IF NOT EXISTS dynp_surges_ngp (
    zone_id            VARCHAR(5)    NOT NULL ,
    surge              NUMERIC       NOT NULL ,
    surge_low          NUMERIC       NOT NULL ,
    surge_high         NUMERIC       NOT NULL ,
    demand_recent      NUMERIC       NOT NULL ,
    demand_previous    NUMERIC       NOT NULL ,
    demand_predicted   NUMERIC       NOT NULL ,
    supply             NUMERIC       NOT NULL ,
    excess_demand      NUMERIC       NULL     ,
    last_upd_dt        TIMESTAMP     NOT NULL ,
    prev_surge         NUMERIC       NULL     ,
    batch_key          NUMERIC       NOT NULL ,
    created_dt         timestamp     NOT NULL ,
    created_by         varchar(50)   NOT NULL ,
    updated_dt         timestamp     NULL     ,
    updated_by         varchar(50)   NULL
    );

-- <PERSON><PERSON> comments
COMMENT ON TABLE dynp_surges_ngp IS 'Temporary table for dynamic pricing surge in NGP';
COMMENT ON COLUMN dynp_surges_ngp.zone_id          IS 'Identifies the zone to which the dynamic price surge is applicable.';
COMMENT ON COLUMN dynp_surges_ngp.surge            IS 'The dynamic purge surge factor. Negative values indicates desurging.';
COMMENT ON COLUMN dynp_surges_ngp.surge_low        IS 'The configured low value till which surge_factor can drop.';
COMMENT ON COLUMN dynp_surges_ngp.surge_high       IS 'The configured high value till which surge_factor can increase.';
COMMENT ON COLUMN dynp_surges_ngp.demand_recent    IS 'The demand in the zone in the recent duration. Eg. between 30 minute ago and now.';
COMMENT ON COLUMN dynp_surges_ngp.demand_previous  IS 'The demand in the zone in the previous duration. Eg. between 60 minute ago and 30 minute ago.';
COMMENT ON COLUMN dynp_surges_ngp.demand_predicted IS 'The predicted demand in the zone in the next duration.';
COMMENT ON COLUMN dynp_surges_ngp.supply           IS 'The supply as of now.';
COMMENT ON COLUMN dynp_surges_ngp.excess_demand    IS 'demand_predicted minus supply';
COMMENT ON COLUMN dynp_surges_ngp.last_upd_dt      IS 'Datetime of last DML activity on this record.';
COMMENT ON COLUMN dynp_surges_ngp.prev_surge       IS 'Surge value before this record was last updated.';
COMMENT ON COLUMN dynp_surges_ngp.batch_key        IS 'batch key configured to calculate the sub zone mapping';
COMMENT ON COLUMN dynp_surges_ngp.created_dt       IS 'Auditable created date and time';
COMMENT ON COLUMN dynp_surges_ngp.created_by       IS 'Auditable created by username';
COMMENT ON COLUMN dynp_surges_ngp.updated_dt       IS 'Auditable updated date and time';
COMMENT ON COLUMN dynp_surges_ngp.updated_by       IS 'Auditable updated by username';
--rollback DROP TABLE IF EXISTS dynp_surges_ngp
-- Create constraints
ALTER TABLE dynp_surges_ngp
DROP CONSTRAINT IF EXISTS PK_dynp_surges_ngp__zone_id CASCADE;
ALTER TABLE dynp_surges_ngp
    ADD CONSTRAINT PK_dynp_surges_ngp__zone_id PRIMARY KEY (zone_id);


-- Create other components (manual)
ALTER TABLE dynp_surges_ngp
    ALTER COLUMN created_dt SET DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE dynp_surges_ngp
    ALTER COLUMN created_by SET DEFAULT 'system';
-- End create table "dynp_surges_ngp"

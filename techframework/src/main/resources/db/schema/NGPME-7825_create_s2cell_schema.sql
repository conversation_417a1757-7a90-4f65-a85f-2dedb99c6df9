--liquibase formatted sql
--changeset <EMAIL>:NGPME-7825_create_s2cell_schema.sql
--init S2Cell table

-- Create table "S2Cell"
CREATE TABLE IF NOT EXISTS S2Cell
(
    id                    BIGINT PRIMARY KEY,
    s2_cell_id            TEXT,
    s2_cell_token_id      TEXT,
    s2_cell_lat           DOUBLE PRECISION,
    s2_cell_long          DOUBLE PRECISION,
    s2_cell_level         INT,
    zone_info_id          TEXT,
    s2_cell_desc          TEXT,
    s2_cell_location_id   TEXT,
    s2_cell_location_desc TEXT,
    created_dt            TEXT,
    created_by            TEXT,
    updated_dt            TEXT,
    updated_by            TEXT
);

--rollback empty
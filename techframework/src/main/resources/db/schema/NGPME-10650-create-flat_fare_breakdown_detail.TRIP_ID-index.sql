--liquibase formatted sql
--changeset <EMAIL>:NGPME-10650-create-flat_fare_breakdown_detail.TRIP_ID-index.sql runInTransaction:false
--create the index for flat_fare_breakdown_detail table

-- Add index for flat_fare_breakdown_detail column TRIP_ID
CREATE INDEX CONCURRENTLY idx_flat_fare_breakdown_detail_trip_id
    ON ngp_me_dynamic_prc.flat_fare_breakdown_detail (TRIP_ID);

-- rollback DROP INDEX IF EXISTS idx_flat_fare_breakdown_detail_trip_id;
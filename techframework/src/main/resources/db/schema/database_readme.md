# Database Schema Documentation

## Table: `surge_computation_model_api_logs`

### Overview

The `surge_computation_model_api_logs` table stores API call logs for surge computation models, designed for monitoring, debugging, and performance analysis. The table uses **monthly partitioning based on Singapore timezone** for optimal performance and data management.

### Table Structure

```sql
CREATE TABLE surge_computation_model_api_logs (
    id               BIGINT NOT NULL DEFAULT nextval('surge_computation_model_api_logs_id_seq'),
    model_id         BIGINT       NOT NULL,
    model_name       VARCHAR(255) NOT NULL,
    endpoint_url     VARCHAR(255) NOT NULL,
    create_timestamp TIMESTAMP    NOT NULL DEFAULT (now() at time zone 'utc')::timestamp,
    status_code      INT          NOT NULL,
    request_params   JSONB        NOT NULL,
    response_body    JSONB        NOT NULL,
    singapore_month  DATE         NOT NULL DEFAULT (get_singapore_month((now() at time zone 'utc')::timestamp)),
    PRIMARY KEY (id, singapore_month)
) PARTITION BY RANGE (singapore_month);
```

#### Key Columns

| Column | Type | Description |
|--------|------|-------------|
| `id` | BIGINT | Auto-increment primary key |
| `model_id` | BIGINT | Reference to surge computation model |
| `model_name` | VARCHAR(255) | Model name/version identifier |
| `endpoint_url` | VARCHAR(255) | API endpoint called |
| `create_timestamp` | TIMESTAMP | UTC timestamp of API call |
| `status_code` | INT | HTTP response status code |
| `request_params` | JSONB | Request parameters (region_id, demand, supply, etc.) |
| `response_body` | JSONB | Response data (surge_factor, status, errors, etc.) |
| `singapore_month` | DATE | Partition key: Singapore timezone month |

### Partitioning Strategy

- **Monthly partitions** based on Singapore timezone (`singapore_month` column)
- **Automatic creation** of 13 initial partitions (6 months back + current + 6 months ahead)
- **Naming convention**: `surge_computation_model_api_logs_YYYY_MM`
- **Performance indexes** automatically created on each partition:
  - `(model_name, create_timestamp DESC)` - for time-based model queries
  - `(status_code)` - for error analysis
  - `(model_id)` - for model-specific lookups

### Database Functions

#### Core Functions
- `get_singapore_month(ts TIMESTAMP)` - Convert UTC timestamp to Singapore timezone month
- `create_monthly_partition_for_logs(target_month DATE)` - Create partition with indexes
- `ensure_monthly_partitions_exist(months_ahead INTEGER)` - Ensure future partitions exist
- `cleanup_old_log_partitions(months_to_keep INTEGER)` - Remove old partitions

#### Usage Examples
```sql
-- Create partitions for next 6 months
SELECT ensure_monthly_partitions_exist(6);

-- Clean up partitions older than 24 months
SELECT cleanup_old_log_partitions(24);
```

### Query Patterns

#### Optimal Performance (with partition key)
```sql
-- Single partition query
SELECT * FROM surge_computation_model_api_logs 
WHERE singapore_month = '2024-06-01'::date
  AND model_name = 'surge_model_v1'
ORDER BY create_timestamp DESC;

-- Multi-partition range query
SELECT * FROM surge_computation_model_api_logs 
WHERE singapore_month BETWEEN '2024-01-01'::date AND '2024-06-01'::date
  AND status_code >= 400;
```

#### Standard Performance (time-based)
```sql
-- Recent model performance analysis
SELECT 
    model_name,
    COUNT(*) as total_calls,
    COUNT(*) FILTER (WHERE status_code >= 400) as error_calls
FROM surge_computation_model_api_logs 
WHERE create_timestamp >= NOW() - INTERVAL '7 days'
GROUP BY model_name;
```

### Monitoring

#### Partition Status
```sql
-- Check partition sizes
SELECT 
    tablename,
    pg_size_pretty(pg_total_relation_size(tablename)) as size
FROM pg_tables 
WHERE tablename LIKE 'surge_computation_model_api_logs_%'
ORDER BY tablename;
```

#### Data Distribution
```sql
-- Check record count per partition
SELECT 
    schemaname||'.'||tablename as partition_name,
    n_tup_ins as estimated_rows
FROM pg_stat_user_tables 
WHERE relname LIKE 'surge_computation_model_api_logs_%'
ORDER BY relname;
```

### Migration and Deployment

The table is created by Liquibase changeset `0007_create_partitioned_table_surge_computation_model_api_logs.sql`:

1. **Creates functions** for timezone conversion and partition management
2. **Creates partitioned table** with proper structure and constraints
3. **Generates initial partitions** (13 months coverage)
4. **Sets up indexes** automatically on each partition
5. **Provides cleanup functions** for maintenance

#### Requirements
- **PostgreSQL 10+** (for declarative partitioning)
- **Production tested**: PostgreSQL 14.15

#### Rollback Support
```sql
--rollback DROP TABLE IF EXISTS surge_computation_model_api_logs CASCADE;
--rollback DROP SEQUENCE IF EXISTS surge_computation_model_api_logs_id_seq;
--rollback DROP FUNCTION IF EXISTS cleanup_old_log_partitions(INTEGER);
--rollback DROP FUNCTION IF EXISTS ensure_monthly_partitions_exist(INTEGER);
--rollback DROP FUNCTION IF EXISTS create_monthly_partition_for_logs(DATE, TEXT);
--rollback DROP FUNCTION IF EXISTS get_singapore_month(TIMESTAMP);
```

### Common Issues & Solutions

| Issue | Symptom | Solution |
|-------|---------|----------|
| **Missing Partition** | Insert fails: "no partition found" | Run `SELECT ensure_monthly_partitions_exist(3);` |
| **Poor Performance** | Queries scan all partitions | Include `singapore_month` in WHERE clause |
| **Storage Growth** | Disk space issues | Run `SELECT cleanup_old_log_partitions(24);` |

### Best Practices

1. **Include partition key** (`singapore_month`) in queries when possible
2. **Store timestamps in UTC**, convert to Singapore timezone for business logic
3. **Monitor partition sizes** regularly
4. **Use provided functions** for partition management
5. **Test queries** with `EXPLAIN` to verify partition elimination

---

*Last Updated: December 2024*  
*Related Changeset: 0007_create_partitioned_table_surge_computation_model_api_logs.sql* 
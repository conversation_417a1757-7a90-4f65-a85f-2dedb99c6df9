--liquibase formatted sql
--changeset <EMAIL>:0006_create_ml_create_booking_request_agg_stats.sql splitStatements:false
--create ml_create_booking_request_agg_stats table

CREATE TABLE IF NOT EXISTS ml_create_booking_request_agg_stats
(
    id                       bigint      not null
        constraint PK_ML_CREATE_BOOKING_REQUEST_AGG_STATS
            primary key,
    booking_id               varchar(12) not null,
    fare_id                  varchar(75),
    fare_calc_time           timestamp,
    estimated_trip_time      bigint,
    distance                 bigint,
    area_type                varchar(12),
    pickup_address_ref       varchar(12),
    dest_address_ref         varchar(12),
    pickup_region_id         bigint,
    region_version           varchar(50),
    model_id                 bigint,
    metered_base_fare        numeric(5, 2),
    total_fare               numeric(5, 2),
    dp_base_fare_for_surge   double precision,
    dp_surge_per             double precision,
    dp_final_fare            double precision,
    estimate_fare_lf         numeric(5, 2),
    estimate_fare_rt         numeric(5, 2),
    meter_platform_fee_lower double precision,
    meter_platform_fee_upper double precision,
    flat_platform_fee        double precision,
    create_timestamp         timestamp   not null default CURRENT_TIMESTAMP,
    CONSTRAINT unique_booking_id UNIQUE (booking_id)
);

COMMENT ON TABLE ml_create_booking_request_agg_stats is 'Table to store booking creation fare information for machine learning';
COMMENT ON COLUMN ml_create_booking_request_agg_stats.id is 'Primary key';
COMMENT ON COLUMN ml_create_booking_request_agg_stats.fare_calc_time is 'Timestamp of fare calculation';
COMMENT ON COLUMN ml_create_booking_request_agg_stats.estimated_trip_time is 'Estimated trip duration in seconds';
COMMENT ON COLUMN ml_create_booking_request_agg_stats.distance is 'Estimated trip distance in meters';
COMMENT ON COLUMN ml_create_booking_request_agg_stats.area_type is 'The area type, "zone" or "region"';
COMMENT ON COLUMN ml_create_booking_request_agg_stats.pickup_address_ref is 'Reference ID for pickup location';
COMMENT ON COLUMN ml_create_booking_request_agg_stats.dest_address_ref is 'Reference ID for destination';
COMMENT ON COLUMN ml_create_booking_request_agg_stats.pickup_region_id is 'The region id from address service';
COMMENT ON COLUMN ml_create_booking_request_agg_stats.region_version is 'The region version from address service, e.g.: 0.0.1';
COMMENT ON COLUMN ml_create_booking_request_agg_stats.model_id is 'The model id from table surge_computation_models, only has value when area_type = "region"';
COMMENT ON COLUMN ml_create_booking_request_agg_stats.metered_base_fare is 'The Metered Base Fare value';
COMMENT ON COLUMN ml_create_booking_request_agg_stats.total_fare is 'Total fare amount for this booking';
COMMENT ON COLUMN ml_create_booking_request_agg_stats.dp_base_fare_for_surge is 'Fare which is considered for applying surge';
COMMENT ON COLUMN ml_create_booking_request_agg_stats.dp_surge_per is 'Surge Percentage';
COMMENT ON COLUMN ml_create_booking_request_agg_stats.dp_final_fare is 'Final Fare Amount';
COMMENT ON COLUMN ml_create_booking_request_agg_stats.estimate_fare_lf is 'Lower bound for vehicle type "STD-001"';
COMMENT ON COLUMN ml_create_booking_request_agg_stats.estimate_fare_rt is 'Upper bound for vehicle type "STD-001"';
COMMENT ON COLUMN ml_create_booking_request_agg_stats.meter_platform_fee_lower is 'Platform fee lower bound for vehicle type "FLAT-001"';
COMMENT ON COLUMN ml_create_booking_request_agg_stats.meter_platform_fee_upper is 'Platform fee upper bound for vehicle type "FLAT-001"';
COMMENT ON COLUMN ml_create_booking_request_agg_stats.flat_platform_fee is 'Platform fee for vehicle type "FLAT-001"';

-- Create the sequence for the primary key
CREATE SEQUENCE IF NOT EXISTS SEQ_ml_create_booking_request_agg_stats__id AS BIGINT
    INCREMENT 1
    START 1
    MINVALUE 1
    CACHE 1;

ALTER TABLE ml_create_booking_request_agg_stats
    ALTER COLUMN id SET DEFAULT nextval('SEQ_ml_create_booking_request_agg_stats__id');


CREATE INDEX IF NOT EXISTS idx_ml_create_booking_request_agg_stats_fare_id
    ON ml_create_booking_request_agg_stats (fare_id);

CREATE INDEX IF NOT EXISTS idx_ml_create_booking_request_agg_stats_region
    ON ml_create_booking_request_agg_stats (pickup_region_id, region_version);


--rollback DROP INDEX IF EXISTS idx_ml_create_booking_request_agg_stats_region;
--rollback DROP INDEX IF EXISTS idx_ml_create_booking_request_agg_stats_fare_id;
--rollback DROP SEQUENCE IF EXISTS SEQ_ml_create_booking_request_agg_stats__id;
--rollback DROP TABLE IF EXISTS ml_create_booking_request_agg_stats;
--liquibase formatted sql
--changeset <EMAIL>:NGPME-8266-add-columns-dynp_surges_ngp.sql
--add columns dynp_surges_ngp and dynp_surge_logs

ALTER TABLE dynp_surges_ngp ADD COLUMN IF NOT EXISTS unmet_15 NUMERIC;
ALTER TABLE dynp_surges_ngp ADD COLUMN IF NOT EXISTS unmet_15_pre NUMERIC;
ALTER TABLE dynp_surges_ngp ADD COLUMN IF NOT EXISTS demand_15 INTEGER;


COMMENT ON COLUMN dynp_surges_ngp.unmet_15 IS 'Number of cancelled or failed booking within specified time range';
COMMENT ON COLUMN dynp_surges_ngp.unmet_15_pre IS 'Number of cancelled or failed booking within specified time range';
COMMENT ON COLUMN dynp_surges_ngp.demand_15 IS 'Number of bookings in specified time range';

ALTER TABLE dynp_surge_logs ADD COLUMN IF NOT EXISTS demand_predicted_15 INTEGER;
ALTER TABLE dynp_surge_logs ADD COLUMN IF NOT EXISTS unmet_15 NUMERIC;
ALTER TABLE dynp_surge_logs ADD COLUMN IF NOT EXISTS unmet_15_pre NUMERIC;
ALTER TABLE dynp_surge_logs ADD COLUMN IF NOT EXISTS demand_15 INTEGER;


COMMENT ON COLUMN dynp_surge_logs.unmet_15 IS 'Number of cancelled or failed booking within specified time range';
COMMENT ON COLUMN dynp_surge_logs.unmet_15_pre IS 'Number of cancelled or failed booking within specified time range';
COMMENT ON COLUMN dynp_surge_logs.demand_15 IS 'Number of bookings in specified time range';
COMMENT ON COLUMN dynp_surge_logs.demand_predicted_15 IS 'Predicted number of bookings in specified time range';

-- rollback ALTER TABLE dynp_surges_ngp DROP COLUMN IF EXISTS unmet_15;
-- rollback ALTER TABLE dynp_surges_ngp DROP COLUMN IF EXISTS unmet_15_pre;
-- rollback ALTER TABLE dynp_surges_ngp DROP COLUMN IF EXISTS demand_15;

-- rollback ALTER TABLE dynp_surge_logs DROP COLUMN IF EXISTS demand_predicted_15;
-- rollback ALTER TABLE dynp_surge_logs DROP COLUMN IF EXISTS unmet_15;
-- rollback ALTER TABLE dynp_surge_logs DROP COLUMN IF EXISTS unmet_15_pre;
-- rollback ALTER TABLE dynp_surge_logs DROP COLUMN IF EXISTS demand_15;
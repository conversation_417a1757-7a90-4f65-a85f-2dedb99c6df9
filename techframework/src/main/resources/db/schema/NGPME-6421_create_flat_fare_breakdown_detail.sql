--liquibase formatted sql
--changeset <EMAIL>:NGPME-6421_create_flat_fare_breakdown_detail.sql
--create flat_fare_breakdown_detail table

CREATE TABLE IF NOT EXISTS flat_fare_breakdown_detail (
    FLAT_FARE_BREAKDOWN_ID      BIGINT not null
        constraint MSPC_FLAT_FARE_BRKDOWN
            primary key,
    BOOKING_ID              VARCHAR(12) UNIQUE not null,
    FARE_ID                 VARCHAR(75) UNIQUE,
    TRIP_ID                 VARCHAR(50) UNIQUE,
    PICKUP_ADDRESS_REF      VARCHAR(12),
    PICKUP_ADDRESS_LAT      NUMERIC(20, 10),
    PICKUP_ADDRESS_LNG      NUMERIC(20, 10),
    PIC<PERSON><PERSON>_ZONE_ID          VARCHAR(5),
    DEST_ADDRESS_REF        VARCHAR(12),
    DEST_ADDRESS_LAT        NUMERIC(20, 10),
    DEST_ADDRESS_LNG        NUMERIC(20, 10),
    DEST_ZONE_ID            VARCHAR(5),
    INTERMEDIATE_ADDRESS_REF        VARCHAR(12),
    INTERMEDIATE_ADDRESS_LAT        NUMERIC(20, 10),
    INTERMEDIATE_ADDRESS_LNG        NUMERIC(20, 10),
    INTERMEDIATE_ZONE_ID            VARCHAR(5),
    REQ_DATE                TIMESTAMP(0) not null,
    FLATDOWN_RATE           DECIMAL(5, 2) not null,
    TIER_1_FARE             DECIMAL(5, 2),
    TIER_2_FARE             DECIMAL(5, 2),
    WAIT_TIME_FARE          DECIMAL(5, 2),
    PEAK_HOUR_FARE          DECIMAL(5, 2),
    MID_NIGHT_FARE          DECIMAL(5, 2),
    HOURLY_SURCHARGE        DECIMAL(5, 2),
    BOOKING_FEE             DECIMAL(5, 2) not null,
    DOS_SURGEPRICE          DECIMAL(5, 2),
    LOC_SURCHARGE           DECIMAL(5, 2),
    EVENT_SURCHARGE         DECIMAL(5, 2),
    ADDITIONAL_SURCHARGE    DECIMAL(5, 2),
    ROUTING_DISTANCE        INT8,
    ETT                     INT8,
    ENCODED_POLYLINE        TEXT,
    IDEAL_TIME              DOUBLE PRECISION,
    CAL_METHOD              VARCHAR(30),
    DP_SURGE_PER            DOUBLE PRECISION,
    DP_SURGE_AMT            DOUBLE PRECISION,
    DP_APPLIED_SURGE_AMT    DOUBLE PRECISION,
    DP_BASE_FARE_FOR_SURGE  DOUBLE PRECISION,
    DP_FINAL_FARE           DOUBLE PRECISION,
    DP_BATCH_KEY            INT default 0,
    METERED_BASE_FARE       DECIMAL(5, 2),
    TOTAL_FARE              DECIMAL(5, 2),
    ESTIMATE_FARE_LF        DECIMAL(5, 2),
    ESTIMATE_FARE_RT        DECIMAL(5, 2),
    FLAT_PLATFORM_FEE_ID    INT8,
    FLAT_PLATFORM_FEE       DOUBLE PRECISION,
    METER_PLATFORM_FEE_ID    INT8,
    METER_PLATFORM_FEE_LOWER        DOUBLE PRECISION,
    METER_PLATFORM_FEE_UPPER        DOUBLE PRECISION,
    CREATED_DT          TIMESTAMP      NOT NULL ,
    CREATED_BY          VARCHAR(50)    NOT NULL ,
    UPDATED_DT          TIMESTAMP      NULL     ,
    UPDATED_BY          VARCHAR(50)    NULL
);

COMMENT ON TABLE FLAT_FARE_BREAKDOWN_DETAIL is 'To break down the flatfare in details for every computation by each booking';
COMMENT ON COLUMN FLAT_FARE_BREAKDOWN_DETAIL.TRIP_ID is 'Trip id ODRD';
COMMENT ON COLUMN FLAT_FARE_BREAKDOWN_DETAIL.REQ_DATE is 'The date time when passenger getting the flat fare';
COMMENT ON COLUMN FLAT_FARE_BREAKDOWN_DETAIL.FLATDOWN_RATE is 'The flat down rate when starting the trip';
COMMENT ON COLUMN FLAT_FARE_BREAKDOWN_DETAIL.TIER_1_FARE is 'Every x meter of distance per count the by x rate within start from 1km to 10km';
COMMENT ON COLUMN FLAT_FARE_BREAKDOWN_DETAIL.TIER_2_FARE is 'Every x meter of distance per count the by x rate, distance should based on TIER_2_START_DISTANCE and TIER_2_END_DISTANCE';
COMMENT ON COLUMN FLAT_FARE_BREAKDOWN_DETAIL.WAIT_TIME_FARE is 'Wait Time Charges';
COMMENT ON COLUMN FLAT_FARE_BREAKDOWN_DETAIL.PEAK_HOUR_FARE is 'Peak Hour Charges';
COMMENT ON COLUMN FLAT_FARE_BREAKDOWN_DETAIL.MID_NIGHT_FARE is 'Mid night Charges';
COMMENT ON COLUMN FLAT_FARE_BREAKDOWN_DETAIL.BOOKING_FEE is 'Booking Fee';
COMMENT ON COLUMN FLAT_FARE_BREAKDOWN_DETAIL.DOS_SURGEPRICE is 'The Demand supply surge price';
COMMENT ON COLUMN FLAT_FARE_BREAKDOWN_DETAIL.LOC_SURCHARGE is 'Location Surcharge';
COMMENT ON COLUMN FLAT_FARE_BREAKDOWN_DETAIL.EVENT_SURCHARGE is 'Event Surcharge';
COMMENT ON COLUMN FLAT_FARE_BREAKDOWN_DETAIL.ADDITIONAL_SURCHARGE is 'Additional Surcharge';
COMMENT ON COLUMN FLAT_FARE_BREAKDOWN_DETAIL.ROUTING_DISTANCE is 'The flat fare quoted routing distance in meters';
COMMENT ON COLUMN FLAT_FARE_BREAKDOWN_DETAIL.ETT is 'The flat fare quoted live traffic estimated time';
COMMENT ON COLUMN FLAT_FARE_BREAKDOWN_DETAIL.IDEAL_TIME is 'The flat fare quoted guessing ideal time time in Seconds Format';
COMMENT ON COLUMN FLAT_FARE_BREAKDOWN_DETAIL.CAL_METHOD is 'The Calculation Method based on Google Live Traffic Or QI Wait Time';
COMMENT ON COLUMN FLAT_FARE_BREAKDOWN_DETAIL.DP_SURGE_PER is 'Surge Percentage';
COMMENT ON COLUMN FLAT_FARE_BREAKDOWN_DETAIL.DP_SURGE_AMT is 'Surge Amount respect to base fare before any cap';
COMMENT ON COLUMN FLAT_FARE_BREAKDOWN_DETAIL.DP_APPLIED_SURGE_AMT is 'Applied Surge Amount to calculate dynamic pricing';
COMMENT ON COLUMN FLAT_FARE_BREAKDOWN_DETAIL.DP_BASE_FARE_FOR_SURGE is 'Fare which is considered for applying surge';
COMMENT ON COLUMN FLAT_FARE_BREAKDOWN_DETAIL.DP_FINAL_FARE is 'Final Fare Amount';
COMMENT ON COLUMN FLAT_FARE_BREAKDOWN_DETAIL.DP_BATCH_KEY is 'batch key inserted to calculate the sub zone mapping';
COMMENT ON COLUMN FLAT_FARE_BREAKDOWN_DETAIL.METERED_BASE_FARE is 'The Metered Base Fare value';


ALTER TABLE flat_fare_breakdown_detail
    ALTER COLUMN created_dt SET DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE flat_fare_breakdown_detail
    ALTER COLUMN created_by SET DEFAULT 'SYSTEM';

-- Create the sequence
CREATE SEQUENCE IF NOT EXISTS SEQ_flat_fare_breakdown_detail__flat_fare_breakdown_id AS BIGINT
    INCREMENT 1
    START 1
    MINVALUE 1
    CACHE 1;

ALTER TABLE flat_fare_breakdown_detail ALTER COLUMN flat_fare_breakdown_id SET DEFAULT nextval('SEQ_flat_fare_breakdown_detail__flat_fare_breakdown_id');


--rollback DROP TABLE flat_fare_breakdown_detail
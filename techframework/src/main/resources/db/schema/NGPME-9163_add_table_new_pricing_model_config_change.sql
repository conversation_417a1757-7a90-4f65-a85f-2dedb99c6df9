--liquibase formatted sql
--changeset <EMAIL>:NGPME-9163_add_table_new_pricing_model_config_change.sql
--add table new_pricing_model_config_change

CREATE TABLE new_pricing_model_config_change
(
    unique_payload_hash              VARCHAR(255) NOT NULL,
    config_id                        BIGINT,
    index                            INTEGER,
    zone_id                          VARCHAR(255),
    start_dt                         TIMESTAMP,
    end_dt                           TIMESTAMP,
    additional_surge_high            INTEGER,
    surge_high_tier_rate             DOUBLE PRECISION,
    unmet_rate_1                     DOUBLE PRECISION,
    unmet_rate_2                     DOUBLE PRECISION,
    negative_demand_supply_down_rate DOUBLE PRECISION,
    k1                               DOUBLE PRECISION,
    k2                               DOUBLE PRECISION,
    k3                               DOUBLE PRECISION,
    k4                               DOUBLE PRECISION,
    k5                               DOUBLE PRECISION,
    k6                               DOUBLE PRECISION,
    k7                               DOUBLE PRECISION,
    k8                               DOUBLE PRECISION,
    zone_price_version               VARCHAR(255),
    config_created_dt                TIMESTAMP,
    config_updated_dt                TIMESTAMP,
    config_created_by                VA<PERSON>HA<PERSON>(255),
    config_updated_by                VARCHAR(255),
    created_dt                       TIMESTAMP      NOT NULL ,
    created_by                       VARCHAR(50)    NOT NULL ,
    updated_dt                       TIMESTAMP      NULL     ,
    updated_by                       VARCHAR(50)    NULL,
    entity_change_type               VARCHAR(255),
    CONSTRAINT pk_new_pricing_model_config_change_entity PRIMARY KEY (unique_payload_hash, config_id)
);


COMMENT ON COLUMN new_pricing_model_config_change.unique_payload_hash             IS 'Unique identifier for the payload';
COMMENT ON COLUMN new_pricing_model_config_change.config_id                       IS 'The id of new pricing model config which derive from table properties in CMS';
COMMENT ON COLUMN new_pricing_model_config_change.index                           IS 'Index for sorting or ordering';
COMMENT ON COLUMN new_pricing_model_config_change.zone_id                         IS 'Identifier for the zone';
COMMENT ON COLUMN new_pricing_model_config_change.start_dt                        IS 'Start date and time for the configuration change';
COMMENT ON COLUMN new_pricing_model_config_change.end_dt                          IS 'End date and time for the configuration change';
COMMENT ON COLUMN new_pricing_model_config_change.additional_surge_high           IS 'Additional surge pricing for high demand';
COMMENT ON COLUMN new_pricing_model_config_change.surge_high_tier_rate            IS 'Rate for the surge pricing tier';
COMMENT ON COLUMN new_pricing_model_config_change.unmet_rate_1                    IS 'Rate for unmet demand tier 1';
COMMENT ON COLUMN new_pricing_model_config_change.unmet_rate_2                    IS 'Rate for unmet demand tier 2';
COMMENT ON COLUMN new_pricing_model_config_change.negative_demand_supply_down_rate IS 'Rate for negative demand-supply scenarios';
COMMENT ON COLUMN new_pricing_model_config_change.k1                              IS 'k1 parameter';
COMMENT ON COLUMN new_pricing_model_config_change.k2                              IS 'k2 parameter';
COMMENT ON COLUMN new_pricing_model_config_change.k3                              IS 'k3 parameter';
COMMENT ON COLUMN new_pricing_model_config_change.k4                              IS 'k4 parameter';
COMMENT ON COLUMN new_pricing_model_config_change.k5                              IS 'k5 parameter';
COMMENT ON COLUMN new_pricing_model_config_change.k6                              IS 'k6 parameter';
COMMENT ON COLUMN new_pricing_model_config_change.k7                              IS 'k7 parameter';
COMMENT ON COLUMN new_pricing_model_config_change.k8                              IS 'k8 parameter';
COMMENT ON COLUMN new_pricing_model_config_change.zone_price_version              IS 'Version of the pricing model for the zone';
COMMENT ON COLUMN new_pricing_model_config_change.config_created_dt               IS 'Date and time when the configuration was created';
COMMENT ON COLUMN new_pricing_model_config_change.config_updated_dt               IS 'Date and time when the configuration was last updated';
COMMENT ON COLUMN new_pricing_model_config_change.config_created_by               IS 'User who created the configuration';
COMMENT ON COLUMN new_pricing_model_config_change.config_updated_by               IS 'User who last updated the configuration';
COMMENT ON COLUMN new_pricing_model_config_change.created_by                      IS 'User who created the record';
COMMENT ON COLUMN new_pricing_model_config_change.created_dt                      IS 'Date and time when the record was created';
COMMENT ON COLUMN new_pricing_model_config_change.updated_by                      IS 'User who last updated the record';
COMMENT ON COLUMN new_pricing_model_config_change.updated_dt                      IS 'Date and time when the record was last updated';
COMMENT ON COLUMN new_pricing_model_config_change.entity_change_type              IS 'Type of change made to the entity';


--rollback DROP TABLE IF EXISTS new_pricing_model_config_change;
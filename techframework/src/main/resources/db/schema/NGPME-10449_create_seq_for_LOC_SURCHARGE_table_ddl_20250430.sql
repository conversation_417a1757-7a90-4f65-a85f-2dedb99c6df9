--liquibase formatted sql
--changeset myathin<PERSON><PERSON>@comfortdelgro.com:NGPME-10449_create_seq_for_LOC_SURCHARGE_table_ddl_20250430.sql
--create the sequence for LOC_SURCHARGE table

CREATE SEQUENCE IF NOT EXISTS SEQ_loc_surcharge__loc_surcharge_id AS BIGINT
    INCREMENT 1
    START 1
    MINVALUE 1
    CACHE 1;

SELECT setval('SEQ_loc_surcharge__loc_surcharge_id', (SELECT MAX(loc_surcharge_id) FROM loc_surcharge));

ALTER TABLE loc_surcharge ALTER COLUMN loc_surcharge_id SET DEFAULT nextval('SEQ_loc_surcharge__loc_surcharge_id')

--rollback not required

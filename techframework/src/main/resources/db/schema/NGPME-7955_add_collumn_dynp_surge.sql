--liquibase formatted sql
--changeset <EMAIL>:NGPME-7955_add_collumn_dynp_surge
--add dynp_surges_ngp and dynp_surges column

ALTER TABLE dynp_surges_ngp
ADD COLUMN time_slot_type  VARCHAR(10),
ADD COLUMN zone_price_model  VARCHAR(10),
ADD COLUMN demand_predicted_15  NUMERIC,
ADD COLUMN excess_demand_15  NUMERIC,
ADD COLUMN unmet_m1  NUMERIC,
ADD COLUMN unmet_m2  NUMERIC,
ADD COLUMN q_m1 NUMERIC;

-- <PERSON><PERSON> comments
COMMENT ON COLUMN dynp_surges_ngp.time_slot_type          IS 'Time Slot Type specifies whether the current time is PEAK or MIDNIGHT or OFFPEAK';
COMMENT ON COLUMN dynp_surges_ngp.zone_price_model          IS 'Indicates which pricing model is used';
COMMENT ON COLUMN dynp_surges_ngp.demand_predicted_15     IS 'The predicted demand in the zone in the past interval';
COMMENT ON COLUMN dynp_surges_ngp.excess_demand_15        IS 'demand_predicted_15 - supply';
COMMENT ON COLUMN dynp_surges_ngp.unmet_m1                IS 'job status in failed, cancelled, cancelled, ur, nta';
COMMENT ON COLUMN dynp_surges_ngp.unmet_m2                IS 'job status in failed, cancelled, cancelled, ur, nta';
COMMENT ON COLUMN dynp_surges_ngp.q_m1                    IS 'total booking count';


ALTER TABLE dynp_surges
ADD COLUMN time_slot_type VARCHAR(10),
ADD COLUMN zone_price_model VARCHAR(10),
ADD COLUMN demand_predicted_15 NUMERIC,
ADD COLUMN excess_demand_15 NUMERIC,
ADD COLUMN unmet_m1 NUMERIC,
ADD COLUMN unmet_m2 NUMERIC,
ADD COLUMN q_m1 NUMERIC;

-- Create comments
COMMENT ON COLUMN dynp_surges.time_slot_type          IS 'Time Slot Type specifies whether the current time is PEAK or MIDNIGHT or OFFPEAK';
COMMENT ON COLUMN dynp_surges.zone_price_model          IS 'Indicates which pricing model is used';
COMMENT ON COLUMN dynp_surges.demand_predicted_15     IS 'The predicted demand in the zone in the past interval';
COMMENT ON COLUMN dynp_surges.excess_demand_15        IS 'demand_predicted_15 - supply';
COMMENT ON COLUMN dynp_surges.unmet_m1                IS 'job status in failed, cancelled, cancelled, ur, nta';
COMMENT ON COLUMN dynp_surges.unmet_m2                IS 'job status in failed, cancelled, cancelled, ur, nta';
COMMENT ON COLUMN dynp_surges.q_m1                    IS 'total booking count';

-- rollback alter table dynp_surges_ngp drop column if exists time_slot_type;
-- rollback alter table dynp_surges_ngp drop column if exists zone_price_model;
-- rollback alter table dynp_surges_ngp drop column if exists demand_predicted_15;
-- rollback alter table dynp_surges_ngp drop column if exists excess_demand_15;
-- rollback alter table dynp_surges_ngp drop column if exists unmet_m1;
-- rollback alter table dynp_surges_ngp drop column if exists unmet_m2;
-- rollback alter table dynp_surges_ngp drop column if exists q_m1;

-- rollback alter table dynp_surges drop column if exists time_slot_type;
-- rollback alter table dynp_surges drop column if exists zone_price_model;
-- rollback alter table dynp_surges drop column if exists demand_predicted_15;
-- rollback alter table dynp_surges drop column if exists excess_demand_15;
-- rollback alter table dynp_surges drop column if exists unmet_m1;
-- rollback alter table dynp_surges drop column if exists unmet_m2;
-- rollback alter table dynp_surges drop column if exists q_m1;
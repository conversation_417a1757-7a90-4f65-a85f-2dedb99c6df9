--liquibase formatted sql
--changeset <EMAIL>:NGPME-9428-adjust_dynp_surge_logs_table.sql
--adjust dynp_surge_logs table

ALTER TABLE dynp_surge_logs ADD COLUMN zone_price_model VARCHAR(10);
--rollback ALTER TABLE dynp_surge_logs DROP COLUMN zone_price_model;

ALTER TABLE dynp_surge_logs ADD COLUMN excess_demand_15 NUMERIC;
--rollback ALTER TABLE dynp_surge_logs DROP COLUMN excess_demand_15;

COMMENT ON COLUMN dynp_surge_logs.zone_price_model          IS 'Indicates which pricing model is used';
COMMENT ON COLUMN dynp_surge_logs.excess_demand_15        IS 'demand_predicted_15 - supply';

--liquibase formatted sql
--changeset <EMAIL>:NGPME-10887_create_flatfare_adjustment_conf_table.sql
--init flatfare_adjustment_conf data tables

CREATE TABLE IF NOT EXISTS flatfare_adjustment_conf (
    id                INT           NOT NULL    PRIMARY KEY,
    veh_grp           INT           NOT NULL,
    fixed_val         INT           NULL,
    per_val           INT           NULL,
    is_enabled        BOOLEAN       NOT NULL,
    created_dt        TIMESTAMP     NOT NULL,
    created_by        VARCHAR(50)   NOT NULL,
    updated_dt        TIMESTAMP     NULL,
    updated_by        VARCHAR(50)   NULL
);

-- <PERSON><PERSON> comments
COMMENT ON TABLE flatfare_adjustment_conf is 'to store Comfort Ride fare adjustment by vehicle group.';
COMMENT ON COLUMN flatfare_adjustment_conf.id is 'sequence id generated for every row that is inserted in the table';
COMMENT ON COLUMN flatfare_adjustment_conf.veh_grp is 'vehicle group for which configuration is applicable';
COMMENT ON COLUMN flatfare_adjustment_conf.fixed_val is 'fixed value to apply to the fare';
COMMENT ON COLUMN flatfare_adjustment_conf.per_val is 'percentage to apply to the fare';
COMMENT ON COLUMN flatfare_adjustment_conf.created_dt is 'created_dt';
COMMENT ON COLUMN flatfare_adjustment_conf.created_by is 'created_by';
COMMENT ON COLUMN flatfare_adjustment_conf.updated_dt is 'updated_dt';
COMMENT ON COLUMN flatfare_adjustment_conf.updated_by is 'updated_by';

CREATE SEQUENCE IF NOT EXISTS seq_flatfare_adjustment_conf__id AS INT
    INCREMENT 1
    START 1
    MINVALUE 1
    CACHE 1;

ALTER TABLE flatfare_adjustment_conf ALTER COLUMN id SET DEFAULT nextval('seq_flatfare_adjustment_conf__id');

--rollback not required
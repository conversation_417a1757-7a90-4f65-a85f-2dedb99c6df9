--liquibase formatted sql
--changeset <EMAIL>:NGPME-6105_create_schema.sql
--init master data tables

CREATE TABLE IF NOT EXISTS product (
    product_id                 varchar(20)    NOT NULL ,
    description                 varchar(100)   NULL     ,
    PDT_DEFAULT_VEHICLE_TYPE      INT,
    DEFAULT_MERIT_TIER            VARCHAR(2),
    COMPANY_ID                    VARCHAR(12),
    VALID_START_DATE              TIMESTAMP(0)                    not null,
    VALID_END_DATE                TIMESTAMP(0)                    not null,
    OWNER                         VARCHAR(20),
    IS_FLAT                       VARCHAR(1),
    IS_GST_APPLICABLE             VARCHAR(1),
    IS_ADMIN_CHARGE_APPLICABLE    VARCHAR(1),
    NUM_OF_HOURS                  INT,
    IS_GST_INCLUSIVE              VARCHAR(1),
    IS_ADMIN_INCLUSIVE            VARCHAR(1),
    GST_APPLY_ON                  VARCHAR(10),
    ADMIN_APPLICABLE_PAYMENT_MODE VARCHAR(20),
    TOTAL_FARE_HIDE_PAYMENT_MODE  VARCHAR(20),
    ADMIN_FEE_HIDE_PAYMENT_MODE   VARCHAR(20),
    IS_GST_ABSORBED               VARCHAR(1),
    IVD_CODE                      DOUBLE PRECISION,
    DISPATCH_MODE                 VARCHAR(20)
        constraint CHK_BSPDT_DISPATCH_MODE
            check (dispatch_mode IN ('IMMEDIATE', 'SCHEDULED')),
    IVD_DISPLAY_LABEL             VARCHAR(25),
    IVD_RECEIPT_LABEL             VARCHAR(13),
    IS_CANCEL_FAILED_ALLOWED      VARCHAR(1) default 'N',
    DELTA_ETA                     SMALLINT,
    NOSHOW_TIMING                 DOUBLE PRECISION,
    APP_PRODUCT_DESCRIPTION       VARCHAR(100),
    APP_PRODUCT_DISCLAIMER        VARCHAR(100),
    IS_CABREWARDS_APPLICABLE      VARCHAR(1) default 'Y' not null
        constraint CHK_BS_PDT__IS_CABREW_APPL
            check (is_cabrewards_applicable in ('Y', 'N')),
    created_dt                 timestamp      NOT NULL ,
    created_by                 varchar(50)    NOT NULL ,
    updated_dt                 timestamp      NULL     ,
    updated_by                 varchar(50)    NULL
);

-- Create comments
COMMENT ON COLUMN product.product_id               IS 'Product ID';
COMMENT ON COLUMN product.description      IS 'Product Description';
COMMENT ON COLUMN product.company_id               IS 'Company ID';
COMMENT ON COLUMN product.valid_start_date         IS 'Valid Start Date';
COMMENT ON COLUMN product.valid_end_date           IS 'Valid End Date';
COMMENT ON COLUMN product.owner                    IS 'Owner';
COMMENT ON COLUMN product.is_flat                  IS 'Is Flat';
COMMENT ON COLUMN product.dispatch_mode            IS 'Dispatch Mode';
COMMENT ON COLUMN product.is_cancel_failed_allowed IS 'Is Cancel Failed Allowed';
COMMENT ON COLUMN product.created_dt               IS 'Auditable created date and time';
COMMENT ON COLUMN product.created_by               IS 'Auditable created by username';
COMMENT ON COLUMN product.updated_dt               IS 'Auditable updated date and time';
COMMENT ON COLUMN product.updated_by               IS 'Auditable updated by username';

-- Create constraints
ALTER TABLE product
    DROP CONSTRAINT IF EXISTS PK_product__product_id CASCADE;
ALTER TABLE product
    ADD CONSTRAINT PK_product__product_id PRIMARY KEY (product_id);


-- Create other components (manual)
ALTER TABLE product
ALTER COLUMN created_dt SET DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE product
ALTER COLUMN created_by SET DEFAULT 'system';

-- End create table "product"
------------------------------------------------------------------
-- Create table "ph_type_code"
CREATE TABLE IF NOT EXISTS ph_type_code (
    code          VARCHAR(10)    NOT NULL ,
    description   VARCHAR(200)   NULL     ,
	created_dt    timestamp      NOT NULL ,
    created_by    varchar(50)    NOT NULL ,
    updated_dt    timestamp      NULL     ,
    updated_by    varchar(50)    NULL
);

-- Create comments
COMMENT ON COLUMN ph_type_code.code        IS 'The ph code';
COMMENT ON COLUMN ph_type_code.description IS 'The ph description';
COMMENT ON COLUMN ph_type_code.created_dt    IS 'Auditable created date and time';
COMMENT ON COLUMN ph_type_code.created_by    IS 'Auditable created by username';
COMMENT ON COLUMN ph_type_code.updated_dt    IS 'Auditable updated date and time';
COMMENT ON COLUMN ph_type_code.updated_by    IS 'Auditable updated by username';

-- Create constraints
ALTER TABLE ph_type_code
    DROP CONSTRAINT IF EXISTS PK_ph_type_code__code CASCADE;
ALTER TABLE ph_type_code
    ADD CONSTRAINT PK_ph_type_code__code PRIMARY KEY (code);


-- Create other components (manual)
ALTER TABLE ph_type_code
ALTER COLUMN created_dt SET DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE ph_type_code
ALTER COLUMN created_by SET DEFAULT 'system';

-- End create table "ph_type_code"

------------------------------------------------------------------
-- Create table "zone_info"
CREATE TABLE IF NOT EXISTS zone_info (
    zone_id                        VARCHAR(5)      NOT NULL,
    default_method                 VARCHAR(12)      NULL     ,
    max_booking                    NUMERIC(5)       NULL     ,
    booking_count                  NUMERIC(5)       NULL     ,
    taxi_count                     NUMERIC(5)      NULL     ,
    time_period                    VARCHAR(10)      NULL     ,
    max_ds_ratio                   NUMERIC(5, 2)   NULL     ,
    dispatch_count                 NUMERIC(5)      NULL     ,
    ds_ratio                       NUMERIC(5, 2)   NULL     ,
    zone_desc                      VARCHAR(30)     NULL     ,
    zone_addr_ref                  VARCHAR(10)     NULL     ,
    rainy_flag                     BOOLEAN         NOT NULL ,
    report_frequency               NUMERIC(3)      NULL     ,
    closest_dispatch_perimeter     NUMERIC(5)      NULL     ,
    cbd_zone                       BOOLEAN         NULL     ,
    charging_zone_id               VARCHAR(5)     NOT NULL ,
    updated_by                     VARCHAR(20)      NULL     ,
    updated_dt                     TIMESTAMP       NULL     ,
    buffer_dispatch_perimeter      NUMERIC(5)      NULL     ,
    closest_dispatch_routing_sec   NUMERIC(5)      NULL     ,
    version                        VARCHAR(5)      NULL     ,
    auto_bid_perimeter             NUMERIC(5)      NOT NULL
);

ALTER TABLE zone_info
    DROP CONSTRAINT IF EXISTS PK_zone_info__pk CASCADE;
ALTER TABLE zone_info
    ADD CONSTRAINT PK_zone_info__pk PRIMARY KEY (zone_id);
-- End create table "zone_info"
------------------------------------------------------------------
-- Create table "ett_unit_fare"
CREATE TABLE IF NOT EXISTS ett_unit_fare
(
    DAY_OF_WEEK VARCHAR(3)                  not null,
    EKM         DOUBLE PRECISION                       not null,
    HR          DECIMAL(5, 2)                 not null
        constraint CHK_ETT_UNIT_FARE__HR
            check (HR <= 24 and hr >= 0),
    CHARGE_BY   VARCHAR(6) default 'PICKUP' not null
        constraint CHK_ETT_UNIT_FARE__CRG_BY
            check (CHARGE_BY IN ('PICKUP', 'DEST')),
    TAXI_TYPE   VARCHAR(8)                  not null
        constraint CHK_ETT_UNIT_FARE__TX_TYPE
            check (TAXI_TYPE IN ('LIMO', 'NORMAL')),
    ZONE_ID     VARCHAR(5)                  not null
        constraint CHK_ETT_UNIT_FARE__ZONE
            references ZONE_INFO,
    RATE        DECIMAL(5, 2)                 not null,
    constraint PK_ETT_UNIT_FARE
        primary key (DAY_OF_WEEK, EKM, HR, TAXI_TYPE, ZONE_ID),
    created_dt    timestamp      NOT NULL ,
    created_by    varchar(50)    NOT NULL ,
    updated_dt    timestamp      NULL     ,
    updated_by    varchar(50)    NULL
);

-- Create comments
COMMENT ON COLUMN ett_unit_fare.day_of_week IS 'the flat_fare_conf param id';
COMMENT ON COLUMN ett_unit_fare.ekm         IS 'the PARAMETER KEY';
COMMENT ON COLUMN ett_unit_fare.hr          IS 'The value of key';
COMMENT ON COLUMN ett_unit_fare.charge_by   IS 'The description of the param';
COMMENT ON COLUMN ett_unit_fare.taxi_type   IS 'The parameter is effective for all dates >= this date.';
COMMENT ON COLUMN ett_unit_fare.zone_id     IS 'The parameter is effective for all dates < this date. null means no end.';
COMMENT ON COLUMN ett_unit_fare.rate        IS 'Generated uuid for tracking audit id';
COMMENT ON COLUMN ett_unit_fare.created_dt  IS 'Auditable created date and time';
COMMENT ON COLUMN ett_unit_fare.created_by  IS 'Auditable created by username';
COMMENT ON COLUMN ett_unit_fare.updated_dt  IS 'Auditable updated date and time';
COMMENT ON COLUMN ett_unit_fare.updated_by  IS 'Auditable updated by username';

-- Create constraints
--ALTER TABLE ett_unit_fare
--    ADD CONSTRAINT PK_ett_unit_fare__key PRIMARY KEY (day_of_week, ekm, hr, taxi_type, zone_id);


-- Create other components (manual)
ALTER TABLE ett_unit_fare
ALTER COLUMN created_dt SET DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE ett_unit_fare
ALTER COLUMN created_by SET DEFAULT 'system';

-- End create table "ett_unit_fare"

------------------------------------------------------------------
-- Create table "fare_type_conf"
CREATE TABLE IF NOT EXISTS fare_type_conf (
    fare_type_id    INT           NOT NULL ,
    fare_type       VARCHAR(50)   NOT NULL ,
    default_fixed   NUMERIC       NULL     ,
    default_perc    NUMERIC       NULL     ,
    start_date      DATE     NOT NULL ,
    end_date        DATE     NOT NULL ,
    day             VARCHAR(3)    NOT NULL ,
    hour            VARCHAR(3)    NOT NULL ,
    created_dt    timestamp      NOT NULL ,
    created_by    varchar(50)    NOT NULL ,
    updated_dt    timestamp      NULL     ,
    updated_by    varchar(50)    NULL
);

-- Create comments
COMMENT ON COLUMN fare_type_conf.fare_type_id  IS 'The sequence id generated for every row that is inserted in the table';
COMMENT ON COLUMN fare_type_conf.fare_type     IS 'Fare type denotes the configuration applied on which parameter';
COMMENT ON COLUMN fare_type_conf.default_fixed IS 'This column denotes the default fixed value';
COMMENT ON COLUMN fare_type_conf.default_perc  IS 'This column denotes the default percentage value';
COMMENT ON COLUMN fare_type_conf.start_date    IS 'The date from which the configuration is effective from';
COMMENT ON COLUMN fare_type_conf.end_date      IS 'The date from which the configuration is effective to';
COMMENT ON COLUMN fare_type_conf.day           IS 'This column denotes for which day this configuration is applicable';
COMMENT ON COLUMN fare_type_conf.hour          IS 'This column denotes for which start of hour this configuration is applicable';
COMMENT ON COLUMN fare_type_conf.created_dt  IS 'Auditable created date and time';
COMMENT ON COLUMN fare_type_conf.created_by  IS 'Auditable created by username';
COMMENT ON COLUMN fare_type_conf.updated_dt  IS 'Auditable updated date and time';
COMMENT ON COLUMN fare_type_conf.updated_by  IS 'Auditable updated by username';

-- Create constraints
ALTER TABLE fare_type_conf
    DROP CONSTRAINT IF EXISTS PK_fare_type_conf__fare_type_id CASCADE;
ALTER TABLE fare_type_conf
    ADD CONSTRAINT PK_fare_type_conf__fare_type_id PRIMARY KEY (fare_type_id);


-- Create other components (manual)
ALTER TABLE fare_type_conf
ALTER COLUMN created_dt SET DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE fare_type_conf
ALTER COLUMN created_by SET DEFAULT 'system';

-- End create table "fare_type_conf"

------------------------------------------------------------------
-- Create table "fare_veh_grp_conf"
CREATE TABLE IF NOT EXISTS fare_veh_grp_conf (
    fare_veh_grp_id   BIGINT           NOT NULL ,
    fare_type_id      BIGINT           NOT NULL ,
    veh_grp           INT           NOT NULL ,
    fixed_val         INT           NULL     ,
    perc_val          INT           NULL     ,
    created_dt        TIMESTAMP     NOT NULL ,
    created_by        VARCHAR(50)   NOT NULL ,
    updated_dt        TIMESTAMP     NULL     ,
    updated_by        VARCHAR(50)   NULL
);

-- Create comments
COMMENT ON TABLE FARE_VEH_GRP_CONF is 'Table used to store the fare vehicle group mapping configurations.';
COMMENT ON COLUMN FARE_VEH_GRP_CONF.FARE_VEH_GRP_ID is 'The sequence id generated for every row that is inserted in the table';
COMMENT ON COLUMN FARE_VEH_GRP_CONF.FARE_TYPE_ID is 'This is the foriegn key from MS_FARE_TYPE_CONF';
COMMENT ON COLUMN FARE_VEH_GRP_CONF.VEH_GRP is 'The vehicle group for which configuration is applicable';
COMMENT ON COLUMN FARE_VEH_GRP_CONF.FIXED_VAL is 'This column denotes the fixed value';
COMMENT ON COLUMN FARE_VEH_GRP_CONF.PERC_VAL is 'This column denotes the percentage value';
COMMENT ON COLUMN FARE_VEH_GRP_CONF.CREATED_DT is 'The created date of the record';
COMMENT ON COLUMN FARE_VEH_GRP_CONF.CREATED_BY is 'The user creating the record';
COMMENT ON COLUMN FARE_VEH_GRP_CONF.UPDATED_DT is 'The updated date of the record';
COMMENT ON COLUMN FARE_VEH_GRP_CONF.UPDATED_BY is 'The user updating the record';

-- Create constraints
ALTER TABLE fare_veh_grp_conf
    DROP CONSTRAINT IF EXISTS PK_fare_veh_grp_conf__fare_veh_grp_id CASCADE;
ALTER TABLE fare_veh_grp_conf
    ADD CONSTRAINT PK_fare_veh_grp_conf__fare_veh_grp_id PRIMARY KEY (fare_veh_grp_id);

ALTER TABLE fare_veh_grp_conf
    DROP CONSTRAINT IF EXISTS FK_fare_veh_grp_conf_TO_fare_type_conf CASCADE;
ALTER TABLE fare_veh_grp_conf
    ADD CONSTRAINT FK_fare_veh_grp_conf_TO_fare_type_conf FOREIGN KEY (fare_type_id)
    REFERENCES fare_type_conf (fare_type_id);

-- Create other components (manual)
ALTER TABLE fare_type_conf
ALTER COLUMN created_dt SET DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE fare_type_conf
ALTER COLUMN created_by SET DEFAULT 'system';

-- End create table "fare_veh_grp_conf"

------------------------------------------------------------------
-- Create table "company_holiday"
CREATE TABLE IF NOT EXISTS company_holiday (
    holiday_type_id   VARCHAR(10)    NOT NULL ,
    ph_date           TIMESTAMP      NOT NULL ,
    description       VARCHAR(200)   NULL     ,
    company_id        VARCHAR(12)    NULL     ,
    created_dt        timestamp      NOT NULL ,
    created_by        varchar(50)    NOT NULL ,
    updated_dt        timestamp      NULL     ,
    updated_by        varchar(50)    NULL
);

-- Create comments
COMMENT ON COLUMN company_holiday.holiday_type_id IS 'The holiday type id';
COMMENT ON COLUMN company_holiday.ph_date         IS 'The ph date';
COMMENT ON COLUMN company_holiday.description     IS 'The description of holiday';
COMMENT ON COLUMN company_holiday.company_id      IS 'Reference to transport_company to get info';
COMMENT ON COLUMN company_holiday.created_dt      IS 'Auditable created date and time';
COMMENT ON COLUMN company_holiday.created_by      IS 'Auditable created by username';
COMMENT ON COLUMN company_holiday.updated_dt      IS 'Auditable updated date and time';
COMMENT ON COLUMN company_holiday.updated_by      IS 'Auditable updated by username';

-- Create constraints
ALTER TABLE company_holiday
    DROP CONSTRAINT IF EXISTS PK_company_holiday__holiday_type_id CASCADE;
ALTER TABLE company_holiday
    ADD CONSTRAINT PK_company_holiday__holiday_type_id PRIMARY KEY (holiday_type_id, ph_date);


-- Create other components (manual)
ALTER TABLE company_holiday
ALTER COLUMN created_dt SET DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE company_holiday
ALTER COLUMN created_by SET DEFAULT 'system';

-- End create table "company_holiday"

------------------------------------------------------------------
-- Create table "flat_fare_conf"
CREATE TABLE IF NOT EXISTS flat_fare_conf (
    param_id                  INT            NOT NULL ,
    param_key                 VARCHAR(50)    NOT NULL ,
    param_value               VARCHAR(300)   NOT NULL ,
    param_description         VARCHAR(150)   NOT NULL ,
    param_effective_from_dt   TIMESTAMP      NOT NULL ,
    param_effective_till_dt   TIMESTAMP      NULL     ,
    created_dt                timestamp      NOT NULL ,
    created_by                varchar(50)    NOT NULL ,
    updated_dt                timestamp      NULL     ,
    updated_by                varchar(50)    NULL
);

-- Create comments
COMMENT ON COLUMN flat_fare_conf.param_id                IS 'The parameter id';
COMMENT ON COLUMN flat_fare_conf.param_key               IS 'The parameter key';
COMMENT ON COLUMN flat_fare_conf.param_value             IS 'The value of the value';
COMMENT ON COLUMN flat_fare_conf.param_description       IS 'The description of the parameter';
COMMENT ON COLUMN flat_fare_conf.param_effective_from_dt IS 'The parameter is effective for all dates greater than or equal to this date';
COMMENT ON COLUMN flat_fare_conf.param_effective_till_dt IS 'The parameter is effective for all dates less than this date. Null means no end.';
COMMENT ON COLUMN flat_fare_conf.created_dt              IS 'Auditable created date and time';
COMMENT ON COLUMN flat_fare_conf.created_by              IS 'Auditable created by username';
COMMENT ON COLUMN flat_fare_conf.updated_dt              IS 'Auditable updated date and time';
COMMENT ON COLUMN flat_fare_conf.updated_by              IS 'Auditable updated by username';

-- Create constraints
ALTER TABLE flat_fare_conf
    DROP CONSTRAINT IF EXISTS PK_flat_fare_conf__param_id CASCADE;
ALTER TABLE flat_fare_conf
    ADD CONSTRAINT PK_flat_fare_conf__param_id PRIMARY KEY (param_id);


-- Create other components (manual)
ALTER TABLE flat_fare_conf
ALTER COLUMN created_dt SET DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE flat_fare_conf
ALTER COLUMN created_by SET DEFAULT 'system';

-- End create table "flat_fare_conf"

------------------------------------------------------------------
-- Create table "dynp_surges"
CREATE TABLE IF NOT EXISTS dynp_surges (
    zone_id            VARCHAR(5)    NOT NULL ,
    surge              NUMERIC       NOT NULL ,
    surge_low          NUMERIC       NOT NULL ,
    surge_high         NUMERIC       NOT NULL ,
    demand_recent      NUMERIC       NOT NULL ,
    demand_previous    NUMERIC       NOT NULL ,
    demand_predicted   NUMERIC       NOT NULL ,
    supply             NUMERIC       NOT NULL ,
    excess_demand      NUMERIC       NULL     ,
    last_upd_dt        TIMESTAMP     NOT NULL ,
    prev_surge         NUMERIC       NULL     ,
    batch_key          NUMERIC       NOT NULL ,
    created_dt         timestamp     NOT NULL ,
    created_by         varchar(50)   NOT NULL ,
    updated_dt         timestamp     NULL     ,
    updated_by         varchar(50)   NULL
);

-- Create comments
COMMENT ON COLUMN dynp_surges.zone_id          IS 'Identifies the zone to which the dynamic price surge is applicable.';
COMMENT ON COLUMN dynp_surges.surge            IS 'The dynamic purge surge factor. Negative values indicates desurging.';
COMMENT ON COLUMN dynp_surges.surge_low        IS 'The configured low value till which surge_factor can drop.';
COMMENT ON COLUMN dynp_surges.surge_high       IS 'The configured high value till which surge_factor can increase.';
COMMENT ON COLUMN dynp_surges.demand_recent    IS 'The demand in the zone in the recent duration. Eg. between 30 minute ago and now.';
COMMENT ON COLUMN dynp_surges.demand_previous  IS 'The demand in the zone in the previous duration. Eg. between 60 minute ago and 30 minute ago.';
COMMENT ON COLUMN dynp_surges.demand_predicted IS 'The predicted demand in the zone in the next duration.';
COMMENT ON COLUMN dynp_surges.supply           IS 'The supply as of now.';
COMMENT ON COLUMN dynp_surges.excess_demand    IS 'demand_predicted minus supply';
COMMENT ON COLUMN dynp_surges.last_upd_dt      IS 'Datetime of last DML activity on this record.';
COMMENT ON COLUMN dynp_surges.prev_surge       IS 'Surge value before this record was last updated.';
COMMENT ON COLUMN dynp_surges.batch_key        IS 'batch key configured to calculate the sub zone mapping';
COMMENT ON COLUMN dynp_surges.created_dt       IS 'Auditable created date and time';
COMMENT ON COLUMN dynp_surges.created_by       IS 'Auditable created by username';
COMMENT ON COLUMN dynp_surges.updated_dt       IS 'Auditable updated date and time';
COMMENT ON COLUMN dynp_surges.updated_by       IS 'Auditable updated by username';

-- Create constraints
ALTER TABLE dynp_surges
    DROP CONSTRAINT IF EXISTS PK_dynp_surges__zone_id CASCADE;
ALTER TABLE dynp_surges
    ADD CONSTRAINT PK_dynp_surges__zone_id PRIMARY KEY (zone_id);


-- Create other components (manual)
ALTER TABLE dynp_surges
ALTER COLUMN created_dt SET DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE dynp_surges
ALTER COLUMN created_by SET DEFAULT 'system';
-- End create table "dynp_surges"

------------------------------------------------------------------
-- Create table "dynp_surge_logs"
CREATE TABLE IF NOT EXISTS dynp_surge_logs (
    zone_id            VARCHAR(5)    NOT NULL ,
    surge              NUMERIC       NOT NULL ,
    surge_low          NUMERIC       NOT NULL ,
    surge_high         NUMERIC       NOT NULL ,
    demand_recent      NUMERIC       NOT NULL ,
    demand_previous    NUMERIC       NOT NULL ,
    demand_predicted   NUMERIC       NOT NULL ,
    supply             NUMERIC       NOT NULL ,
    excess_demand      NUMERIC       NULL     ,
    last_upd_dt        TIMESTAMP     NULL     ,
    created_log_dt     TIMESTAMP     NULL     ,
    prev_surge         NUMERIC       NULL     ,
    batch_key          NUMERIC       NOT NULL
);

-- Create comments
COMMENT ON COLUMN dynp_surge_logs.zone_id          IS 'zone id value of both dynp_surges and dynp_surges';
COMMENT ON COLUMN dynp_surge_logs.surge            IS 'surge value of both dynp_surges and dynp_surges';
COMMENT ON COLUMN dynp_surge_logs.surge_low        IS 'surge low value of both dynp_surges and dynp_surges';
COMMENT ON COLUMN dynp_surge_logs.surge_high       IS 'surge high value of both dynp_surges and dynp_surges';
COMMENT ON COLUMN dynp_surge_logs.demand_recent    IS 'demand recent value of both dynp_surges and dynp_surges';
COMMENT ON COLUMN dynp_surge_logs.demand_previous  IS 'demand previous value of both dynp_surges and dynp_surges';
COMMENT ON COLUMN dynp_surge_logs.demand_predicted IS 'demand predicted value of both dynp_surges and dynp_surges';
COMMENT ON COLUMN dynp_surge_logs.supply           IS 'supply value of both dynp_surges and dynp_surges';
COMMENT ON COLUMN dynp_surge_logs.excess_demand    IS 'excess demand value of both dynp_surges and dynp_surges';
COMMENT ON COLUMN dynp_surge_logs.last_upd_dt      IS 'last updated date value of both dynp_surges and dynp_surges';
COMMENT ON COLUMN dynp_surge_logs.created_log_dt   IS 'The created datetime of this configuration. It will only fetch the latest configuration';
COMMENT ON COLUMN dynp_surge_logs.prev_surge       IS 'previous surge value of both dynp_surges and dynp_surges';
COMMENT ON COLUMN dynp_surge_logs.batch_key        IS 'Batch Key is the batch for which surge updated for both dynp_surges AND dynp_surges';

-- Create constraints
ALTER TABLE dynp_surge_logs
    DROP CONSTRAINT IF EXISTS PK_dynp_surge_logs__source CASCADE;
ALTER TABLE dynp_surge_logs
    ADD CONSTRAINT PK_dynp_surge_logs__source PRIMARY KEY (zone_id, batch_key);


-- Create other components (manual)
ALTER TABLE dynp_surge_logs
ALTER COLUMN created_log_dt SET DEFAULT CURRENT_TIMESTAMP;
-- End create table "dynp_surge_logs"

-- Create the sequence
CREATE SEQUENCE IF NOT EXISTS SEQ_deep_link_details__supc_deep_link_id
    INCREMENT 1 -- Increments the sequence value by 1
    START 1 -- Starts the sequence at 1
    MINVALUE 1 -- Minimum value for the sequence
    MAXVALUE 9223372036854775807 -- Maximum value for the sequence
    CACHE 1;

------------------------------------------------------------------
-- Create table "location"
CREATE TABLE IF NOT EXISTS LOCATION (
    LOCATION_ID   BIGINT       not null
        constraint PK_LOCATION
            primary key,
    LOCATION_NAME VARCHAR(50) not null
        constraint CHK_LOC__LOC_NAME
            unique,
    GEO_FENCE_ID  INT,
    START_DT      TIMESTAMP        not null,
    END_DT        TIMESTAMP,
    UPDATED_DT    TIMESTAMP,
    UPDATED_BY    VARCHAR,
    constraint CHK_LOC__START_DT_END_DT
        check (start_dt <= end_dt)
)
;

comment on table LOCATION is 'Location master is region based. A location is collection of address points. MS_LOC_ADDRESS contains the collection of address points';
comment on column LOCATION.LOCATION_ID is 'PK obtained from MSSQ_location';
comment on column LOCATION.GEO_FENCE_ID is 'Identifies a geo fence that surrounds the location';
comment on column LOCATION.START_DT is 'Location is valid from date >= start_dt';

-- End create table "location"

------------------------------------------------------------------
-- Create table "loc_surcharge"
CREATE TABLE IF NOT EXISTS LOC_SURCHARGE(
    LOC_SURCHARGE_ID BIGINT                          not null
        constraint PK_LOC_SURCHARGE
            primary key,
    LOCATION_ID      BIGINT                          not null
        constraint FK_LOC_SURC__LOC
            references LOCATION,
    FARE_TYPE        VARCHAR(10)                    not null
        constraint CHK_LOC_SURC__FARE_TYPE
            check (FARE_TYPE IN ('LOC_SURC', 'EV_SURGE')),
    DAY_INDICATOR    VARCHAR(3)                     not null
        constraint CHK_LOC_SURC__DAY_INDICATOR
            check (DAY_INDICATOR IN ('SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT', 'HOL')),
    START_TIME       VARCHAR(8)                     not null
        constraint CHK_LOC_SURC__START_TIM
            check (to_date('20170101 ' || start_time, 'yyyymmdd hh24:mi:ss') >= to_date('20170101', 'yyyymmdd')),
    END_TIME         VARCHAR(8)                     not null
        constraint CHK_LOC_SURC__END_TIM
            check (to_date('20170101 ' || end_time, 'yyyymmdd hh24:mi:ss') >= to_date('20170101', 'yyyymmdd')),
    CHARGE_BY        VARCHAR(6)  default 'PICKUP'   not null
        constraint CHK_LOC_SURC__CHARGE_BY
            check (CHARGE_BY IN ('PICKUP', 'DEST')),
    SURCHARGE_VALUE  DOUBLE PRECISION                          not null,
    PRODUCT_ID           VARCHAR(20) default 'FLAT-001' not null,
    constraint CHK_LOC_SURC__START_END_TIM
        check (to_date('20170101 ' || end_time, 'yyyymmdd hh24:mi:ss') >=
               to_date('20170101 ' || start_time, 'yyyymmdd hh24:mi:ss'))
);

COMMENT ON TABLE LOC_SURCHARGE is 'Location surcharge values';
COMMENT ON COLUMN LOC_SURCHARGE.LOC_SURCHARGE_ID is 'PK obtained from mssq_loc_surcharge';
COMMENT ON COLUMN LOC_SURCHARGE.FARE_TYPE is 'Fare Type could be location surcharge or event surcharge of certain location.';
COMMENT ON COLUMN LOC_SURCHARGE.DAY_INDICATOR is 'Indicates which day the charge is applicable to. SUN, MON, etc., HOL - Means applicable to holiday.';
COMMENT ON COLUMN LOC_SURCHARGE.START_TIME is 'Location surcharge starting time in hh24:mi:ss format.';
COMMENT ON COLUMN LOC_SURCHARGE.END_TIME is 'Location surcharge ending time in hh24:mi:ss format.';
COMMENT ON COLUMN LOC_SURCHARGE.CHARGE_BY is 'Indicates The charge should apply by pickup location or destination.';
COMMENT ON COLUMN LOC_SURCHARGE.PRODUCT_ID is 'indicates the pdt id to which the location surcharge is applicable';

-- End create table "loc_surcharge"

------------------------------------------------------------------
-- Create table "loc_address"
CREATE TABLE IF NOT EXISTS LOC_ADDRESS (
    LOCATION_ID BIGINT       not null
        constraint FK_LOC_ADDRESS_TO_LOCATION
            references LOCATION,
    ADDRESS_REF VARCHAR(10) not null,
    constraint PK_LOC_ADDRESS
        primary key (LOCATION_ID, ADDRESS_REF)
);

-- Create comments
COMMENT ON TABLE LOC_ADDRESS IS 'All address refs with location ID, this is specific to each location ID';

-- End create table "loc_address"

------------------------------------------------------------------

-- Create table "dynp_pricing_range"
CREATE TABLE IF NOT EXISTS dynp_pricing_range(
    DYNP_PRICING_RANGE_ID  BIGINT                  not null
        constraint PK_DYNP_PRICING_RANGE
            primary key,
    START_PRICE            NUMERIC                    not null,
    END_PRICE              NUMERIC                    not null,
    STEP                   NUMERIC                    not null,
    REFRESH_PERIOD         NUMERIC                    not null,
    QUOTE_VALID_PERIOD     NUMERIC                    not null,
    IS_ENABLED             SMALLINT   default 0     not null,
    DAY                    VARCHAR(3) default 'ALL' not null
        constraint CHK_DYNP_PRICING_DAY
            check (DAY IN ('ALL', 'HOL', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT', 'SUN')),
    HOUR                   VARCHAR(3) default 'ALL' not null
        constraint CHK_DYNP_PRICING_HR
            check (HOUR IN ('ALL', '00', '01', '02', '03', '04', '05', '06', '07', '08', '09', '10',
                            '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23')),
    STEP_POSITIVE          NUMERIC      default 0     not null,
    STEP_NEGATIVE          NUMERIC      default 0     not null,
    CREATED_DT          TIMESTAMP      NOT NULL ,
    CREATED_BY          VARCHAR(50)    NOT NULL ,
    UPDATED_DT          TIMESTAMP      NULL     ,
    UPDATED_BY          VARCHAR(50)    NULL
);

COMMENT ON TABLE DYNP_PRICING_RANGE IS 'Table for configure DYNAMIC PRICING';
COMMENT ON COLUMN DYNP_PRICING_RANGE.DYNP_PRICING_RANGE_ID IS 'Primary key populated by dynpsq_PRICING_RANGE__id';
COMMENT ON COLUMN DYNP_PRICING_RANGE.START_PRICE IS 'Minimum Price to set for Dynamic Pricing';
COMMENT ON COLUMN DYNP_PRICING_RANGE.END_PRICE IS 'Maximum Price to set for Dynamic Pricing';
COMMENT ON COLUMN DYNP_PRICING_RANGE.STEP IS 'Step Price for each interval to set for Dynamic Pricing. This is Obsoleted from CDG-14562.';
COMMENT ON COLUMN DYNP_PRICING_RANGE.REFRESH_PERIOD IS 'Dynamic Pricing will be refreshed every x minutes specified by this column';
COMMENT ON COLUMN DYNP_PRICING_RANGE.QUOTE_VALID_PERIOD IS 'The number of minute the quote will be valid for';
COMMENT ON COLUMN DYNP_PRICING_RANGE.CREATED_DT IS 'The created datetime of this configuration. It will only fetch the latest configuration';
COMMENT ON COLUMN DYNP_PRICING_RANGE.IS_ENABLED is 'Indicate is this range in use, 1 is in use, 0 is not used. Only 1 set mapping to zone mapping can be enabled.';
COMMENT ON COLUMN DYNP_PRICING_RANGE.DAY IS 'The configuration applicable for a DAY. If applicable for Holiday, then the value would be HOL. If applicable for all days, then value is ALL';
COMMENT ON COLUMN DYNP_PRICING_RANGE.HOUR IS 'The configuration applicable for a Hour. If applicable for all days, then value is ALL';
COMMENT ON COLUMN DYNP_PRICING_RANGE.STEP_POSITIVE IS 'Step Price for each interval to set for Dynamic Pricing when surge is positive';
COMMENT ON COLUMN DYNP_PRICING_RANGE.STEP_NEGATIVE IS 'Step Price for each interval to set for Dynamic Pricing when surge is negative';

ALTER TABLE DYNP_PRICING_RANGE
ALTER COLUMN created_dt SET DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE dynp_surges
ALTER COLUMN created_by SET DEFAULT 'system';

-- Create the sequence
CREATE SEQUENCE IF NOT EXISTS dynpsq_PRICING_RANGE__id
    INCREMENT 1
    START 10000000
    MINVALUE 1
    CACHE 1;

ALTER TABLE DYNP_PRICING_RANGE ALTER COLUMN DYNP_PRICING_RANGE_ID SET DEFAULT nextval('dynpsq_PRICING_RANGE__id');

------------------------------------------------------------------
-- Create table "dynp_zone_mapping"
CREATE TABLE IF NOT EXISTS dynp_zone_mapping (
    DYNP_ZONE_MAPPING_ID  BIGINT       NOT NULL
    constraint PK_DYNP_ZONE_MAPPING
                primary key,
    DYNP_PRICING_RANGE_ID BIGINT       NOT NULL
    constraint FK_DYNP_ZONE_MAP_TO_PRC_RANG
                references DYNP_PRICING_RANGE,
    ZONE_ID               VARCHAR(5)    NOT NULL ,
    CREATED_DT            TIMESTAMP        not null,
    VALID_START_DT        TIMESTAMP        not null,
    VALID_END_DT          TIMESTAMP        not null,
    constraint CHK_dynp_zo_mapp__VALID_START_END_DT
        check (valid_end_dt > valid_start_dt)
);

-- Create comments
COMMENT ON TABLE DYNP_ZONE_MAPPING IS 'Table for Mapping of multiple Zone to one single dynamic pricing configuration';
COMMENT ON COLUMN DYNP_ZONE_MAPPING.DYNP_ZONE_MAPPING_ID IS 'Primary key populated by dynpsq_PRICING_RANGE__id';
COMMENT ON COLUMN DYNP_ZONE_MAPPING.VALID_START_DT IS 'The association is valid for dates >= this date.';
COMMENT ON COLUMN DYNP_ZONE_MAPPING.VALID_END_DT IS 'The association is valid for dates < this date.';


--rollback not required
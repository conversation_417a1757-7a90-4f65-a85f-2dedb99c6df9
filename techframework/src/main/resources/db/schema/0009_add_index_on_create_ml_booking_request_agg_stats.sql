--liquibase formatted sql
--changeset <EMAIL>:0009_add_index_on_create_ml_booking_request_agg_stats.sql splitStatements:false runInTransaction:false
--create index on table ml_create_booking_request_agg_stats

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ml_create_booking_request_fare_calc_time__booking_id
    ON ml_create_booking_request_agg_stats (fare_calc_time desc, booking_id);

--rollback DROP INDEX IF EXISTS idx_ml_create_booking_request_fare_calc_time__booking_id;

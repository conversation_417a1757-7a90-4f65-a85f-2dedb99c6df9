# AWS Parameter Store integration
spring.config.import[0]=optional:aws-parameterstore:/config/ngp-me-dynamicpricing-svc_sit/
# AWS Secrets Manager integration
spring.config.import[1]=optional:aws-secretsmanager:/secrets/ngp/sit;/secrets/ngp/sit/ngp-me-dynamicpricing-svc
# Specify the URL of the Config Server to get properties
spring.config.import[2]=optional:configserver:https://mob-common-cms-app.apps.sg.sit.zig.systems/v1.0/config-server
spring.liquibase.enabled=true

# RELEASE VERSION
dps.system.param.applicationRelease=1

# Database config for reader/writer split
# These will be overridden by AWS Parameter Store/Secrets Manager in SIT environment
spring.datasource.writer.url=${spring.datasource.url}
spring.datasource.writer.username=${spring.datasource.username}
spring.datasource.writer.password=${spring.datasource.password}
spring.datasource.writer.driver-class-name=${spring.datasource.driver-class-name}

spring.datasource.reader.url=${spring.datasource.url}
spring.datasource.reader.username=${spring.datasource.username}
spring.datasource.reader.password=${spring.datasource.password}
spring.datasource.reader.driver-class-name=${spring.datasource.driver-class-name}
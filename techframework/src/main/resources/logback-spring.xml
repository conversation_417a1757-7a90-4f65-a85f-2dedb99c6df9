<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <springProfile name="json-logging">
        <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <layout class="ch.qos.logback.contrib.json.classic.JsonLayout">
                <jsonFormatter class="ch.qos.logback.contrib.jackson.JacksonJsonFormatter"/>
                <timestampFormat>yyyy-MM-dd HH:mm:ss.SSS</timestampFormat>
                <appendLineSeparator>true</appendLineSeparator>
            </layout>
        </appender>
    </springProfile>
    <springProfile name="!json-logging">
        <include resource="org/springframework/boot/logging/logback/console-appender.xml"/>
    </springProfile>
    <root level="info">
        <appender-ref ref="CONSOLE"/>
    </root>
</configuration>
# AWS Parameter Store integration
spring.config.import[0]=optional:aws-parameterstore:/config/ngp-me-r2dynamicpricing-svc_sit/
# AWS Secrets Manager integration
spring.config.import[1]=optional:aws-secretsmanager:/secrets/ngp/sit;/secrets/ngp/sit/ngp-me-dynamicpricing-svc
# Specify the URL of the Config Server to get properties
spring.config.import[2]=optional:configserver:https://mob-common-cms-app.apps.sg.sit.zig.systems/v1.0/config-server

spring.liquibase.enabled=true

# RELEASE VERSION
dps.system.param.applicationRelease=2

# API ENDPOINT FOR R2
openfeign.fareClient.name=fareClient
spring.cloud.openfeign.client.config.fareClient.url=https://mob-me-r2fare-app.apps.sg.sit.zig.systems

openfeign.fleetAnalyticClient.name=fleetAnalyticClient
spring.cloud.openfeign.client.config.fleetAnalyticClient.url=https://mob-me-r2fleetanalytic-app.apps.sg.sit.zig.systems
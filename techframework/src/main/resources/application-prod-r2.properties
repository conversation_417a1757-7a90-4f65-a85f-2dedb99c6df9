# AWS Parameter Store integration
spring.config.import[0]=optional:aws-parameterstore:/config/ngp-me-r2dynamicpricing-svc_prod/
# AWS Secrets Manager integration
spring.config.import[1]=optional:aws-secretsmanager:/secrets/ngp/prod;/secrets/ngp/prod/ngp-me-r2dynamicpricing-svc
# Specify the URL of the Config Server to get properties
spring.config.import[2]=optional:configserver:https://mob-common-cms-app.apps.sg.prod.zig.systems/v1.0/config-server

# RELEASE VERSION
dps.system.param.applicationRelease=2

# API ENDPOINT FOR R2
openfeign.fareClient.name=fareClient
spring.cloud.openfeign.client.config.fareClient.url=https://mob-me-r2fare-app.apps.sg.prod.zig.systems

openfeign.fleetAnalyticClient.name=fleetAnalyticClient
spring.cloud.openfeign.client.config.fleetAnalyticClient.url=https://mob-me-r2fleetanalytic-app.apps.sg.prod.zig.systems

# Database config for reader/writer split
# These will be overridden by AWS Parameter Store/Secrets Manager in PROD environment
spring.datasource.writer.url=${spring.datasource.url}
spring.datasource.writer.username=${spring.datasource.username}
spring.datasource.writer.password=${spring.datasource.password}
spring.datasource.writer.driver-class-name=${spring.datasource.driver-class-name}

spring.datasource.reader.url=${spring.datasource.url}
spring.datasource.reader.username=${spring.datasource.username}
spring.datasource.reader.password=${spring.datasource.password}
spring.datasource.reader.driver-class-name=${spring.datasource.driver-class-name}
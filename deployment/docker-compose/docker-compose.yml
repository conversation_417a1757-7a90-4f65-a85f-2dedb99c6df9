version: '3.8'

services:
  postgres:
    profiles:
      - postgres
      - all
    image: postgres:14.7-alpine # We are using 14.7 for managed DB in AWS
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: ngp
      PGDATA: /data/postgres
    volumes:
      - ./postgres:/docker-entrypoint-initdb.d/
    command: >
      bash -c "
        chmod +x /docker-entrypoint-initdb.d/*.sh &&
        docker-entrypoint.sh postgres
      "
    ports:
      - "15500:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d ngp"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    networks:
      - dynamicpricing
  redis:
    profiles:
      - redis
      - all
    image: redis:7.0.7-alpine # We are using 7.0.7 for managed Redis in AWS
    environment:
      - REDIS_DISABLE_COMMANDS=FLUSHDB,FLUSHALL
    ports:
      - '15501:6379'
    healthcheck:
      test: [ "<PERSON><PERSON>", "redis-cli", "ping" ]
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - dynamicpricing

  zookeeper:
    profiles:
      - kafka
      - all
    image: confluentinc/cp-zookeeper:7.3.2
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - 15503:2181
    networks:
      - dynamicpricing

  kafka:
    profiles:
      - kafka
      - all
    image: confluentinc/cp-server:7.3.2
    depends_on:
      - zookeeper
    ports:
      - "15504:9092"
      - "15505:9101"
      - "15506:29092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_LISTENERS: PLAINTEXT://0.0.0.0:29092,PLAINTEXT_HOST://0.0.0.0:9092
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:15504
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_CONFLUENT_LICENSE_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_CONFLUENT_BALANCER_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_JMX_PORT: 9101
      KAFKA_JMX_OPTS: -Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false -Djava.rmi.server.hostname=kafka -Dcom.sun.management.jmxremote.rmi.port=9101
      KAFKA_JMX_HOSTNAME: localhost
      KAFKA_CONFLUENT_SCHEMA_REGISTRY_URL: http://schema-registry:8081
      KAFKA_METRIC_REPORTERS: io.confluent.metrics.reporter.ConfluentMetricsReporter
      CONFLUENT_METRICS_REPORTER_BOOTSTRAP_SERVERS: kafka:29092
      CONFLUENT_METRICS_REPORTER_TOPIC_REPLICAS: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: "true"
      KAFKA_DELETE_TOPIC_ENABLE: "true"
      CONFLUENT_METRICS_ENABLE: 'true'
      CONFLUENT_SUPPORT_CUSTOMER_ID: 'anonymous'
    healthcheck:
      test: kafka-topics --bootstrap-server localhost:29092 --list || exit 1
      interval: 10s
      timeout: 10s
      retries: 5
      start_period: 5s
    networks:
      - dynamicpricing

  schema-registry:
    profiles:
      - kafka
      - all
    image: confluentinc/cp-schema-registry:7.6.0
    ports:
      - 15507:8081
    depends_on:
      - zookeeper
      - kafka
    environment:
      SCHEMA_REGISTRY_KAFKASTORE_BOOTSTRAP_SERVERS: kafka:29092
      SCHEMA_REGISTRY_KAFKASTORE_CONNECTION_URL: zookeeper:2181
      SCHEMA_REGISTRY_KAFKASTORE_SECURITY_PROTOCOL: PLAINTEXT
      SCHEMA_REGISTRY_HOST_NAME: schema-registry
      SCHEMA_REGISTRY_LISTENERS: http://schema-registry:8081
      SCHEMA_REGISTRY_SCHEMA_REGISTRY_INTER_INSTANCE_PROTOCOL: "http"
      SCHEMA_REGISTRY_LOG4J_ROOT_LOGLEVEL: INFO
      SCHEMA_REGISTRY_KAFKASTORE_TOPIC: _schemas
    healthcheck:
      test: curl -f http://schema-registry:8081/subjects || exit 1
      interval: 5s
      timeout: 3s
      retries: 5
      start_period: 5s
    networks:
      - dynamicpricing

  kafka-ui:
    profiles:
      - kafka
      - all
    image: provectuslabs/kafka-ui:latest
    ports:
      - "15502:8080"
    depends_on:
      - zookeeper
      - kafka
      - schema-registry
    environment:
      KAFKA_CLUSTERS_0_NAME: cluster_0
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:29092
      KAFKA_CLUSTERS_0_ZOOKEEPER: zookeeper:2181
      KAFKA_CLUSTERS_0_JMXPORT: 9101
      KAFKA_CLUSTERS_0_SCHEMAREGISTRY: http://schema-registry:8081
    networks:
      - dynamicpricing

  ngp-me-dynamicpricing-svc:
    profiles:
      - app
      - all
    container_name: ngp-me-dynamicpricing-svc
    build:
      context: ../..
      dockerfile: Dockerfile-local
      args:
        - GRADLE_BUILD_COMMAND=clean bootJar -x test -x integrationTest
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      kafka:
        condition: service_healthy
      schema-registry:
        condition: service_healthy
    ports:
      - "15508:8080"
    environment:
      - SPRING_CLOUD_CONFIG_ENABLED=false
      - SPRING_PROFILES_ACTIVE=local
      # Writer datasource configuration
      - SPRING_DATASOURCE_WRITER_URL=********************************************************************
      - SPRING_DATASOURCE_WRITER_USERNAME=postgres
      - SPRING_DATASOURCE_WRITER_PASSWORD=postgres
      # Reader datasource configuration (pointing to same instance)
      - SPRING_DATASOURCE_READER_URL=********************************************************************
      - SPRING_DATASOURCE_READER_USERNAME=postgres
      - SPRING_DATASOURCE_READER_PASSWORD=postgres
      # Legacy datasource configuration (for backward compatibility)
      - SPRING_DATASOURCE_URL=********************************************************************
      - SPRING_DATASOURCE_USERNAME=postgres
      - SPRING_DATASOURCE_PASSWORD=postgres
      - SPRING_DATA_REDIS_HOST=redis
      - SPRING_DATA_REDIS_PORT=6379
      - SPRING_KAFKA_BOOTSTRAP_SERVERS=kafka:29092
      - SPRING_KAFKA_SCHEMA_REGISTRY_URL=http://schema-registry:8081
      - SPRING_LIQUIBASE_ENABLED=true
      # Removed hardcoded schema reference - schema is now configured via datasource URL currentSchema parameter
      - SPRING_CLOUD_OPENFEIGN_CLIENT_CONFIG_ADDRESSCLIENT_URL=http://host.docker.internal:17004
      - SPRING_CLOUD_OPENFEIGN_CLIENT_CONFIG_FLEETANALYTICCLIENT_URL=http://host.docker.internal:17108
      - SPRING_CLOUD_OPENFEIGN_CLIENT_CONFIG_WEATHERRETRIEVALCLIENT_URL=http://host.docker.internal:35100
      - SPRING_CLOUD_OPENFEIGN_CLIENT_CONFIG_SURGECOMPUTATIONMODELCLIENT_URL=http://host.docker.internal:35200
    networks:
      - dynamicpricing

networks:
  dynamicpricing:
    name: dynamicpricing
    driver: bridge

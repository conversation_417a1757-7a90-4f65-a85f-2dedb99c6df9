#!/bin/bash

# Set database connection details
DB_NAME="ngp"
SCHEMA_NAME="ngp_me_dynamic_prc"
DB_USER="postgres"
DB_PASSWORD="postgres"

# Check if the database exists
if PGPASSWORD="$DB_PASSWORD" psql -U "$DB_USER" -lqt | cut -d \| -f 1 | grep -qw "$DB_NAME"; then
    echo "Database '$DB_NAME' already exists."
else
    # Create the database
    PGPASSWORD="$DB_PASSWORD" createdb -U "$DB_USER" "$DB_NAME"
    echo "Database '$DB_NAME' created."
fi

# Connect to the database
PGPASSWORD="$DB_PASSWORD" psql -U "$DB_USER" -d "$DB_NAME" <<EOF

-- Create the schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS $SCHEMA_NAME;

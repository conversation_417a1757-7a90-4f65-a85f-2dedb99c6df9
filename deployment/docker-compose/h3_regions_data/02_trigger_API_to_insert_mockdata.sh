#!/bin/bash

# Get the script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PYTHON_SCRIPT="$SCRIPT_DIR/helper_tools/generate_mockdata_to_db_from_output_json_through_api.py"

# Check if the Python script exists
if [ ! -f "$PYTHON_SCRIPT" ]; then
    echo "Error: Python script not found at $PYTHON_SCRIPT"
    echo "Press any key to exit..."
    read -n 1
    exit 1
fi

# Try to run the Python script
echo "Starting to generate output JSON files..."
echo "Running: python3 $PYTHON_SCRIPT"

# Execute the Python script
if command -v python3 &> /dev/null; then
    python3 "$PYTHON_SCRIPT"
    RESULT=$?
elif command -v python &> /dev/null; then
    echo "python3 not found, trying python instead..."
    python "$PYTHON_SCRIPT"
    RESULT=$?
else
    echo "Error: Neither python3 nor python was found on your system."
    RESULT=1
fi

# Check the execution result
if [ $RESULT -eq 0 ]; then
    echo "Successfully generated output JSON files!"
else
    echo "Error occurred while generating output JSON files."
fi


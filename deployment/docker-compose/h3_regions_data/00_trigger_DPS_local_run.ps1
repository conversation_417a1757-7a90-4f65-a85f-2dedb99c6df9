# Get the script directory
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
$dockerComposeFile = Join-Path -Path $scriptPath -ChildPath "..\docker-compose.yml"

# Check if Docker Compose file exists
if (-not (Test-Path $dockerComposeFile)) {
    Write-Host "Error: Docker Compose file not found at $dockerComposeFile" -ForegroundColor Red
    Write-Host "Press any key to exit..."
    $host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown") | Out-Null
    exit 1
}

# Check if Docker is running
try {
    docker info | Out-Null
    if ($LASTEXITCODE -ne 0) {
        throw "Docker is not running"
    }
} catch {
    Write-Host "Error: Docker is not running. Please start Docker Desktop first." -ForegroundColor Red
    Write-Host "Press any key to exit..."
    $host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown") | Out-Null
    exit 1
}

# Check if docker command is available
try {
    Get-Command docker -ErrorAction Stop | Out-Null
} catch {
    Write-Host "Error: Docker command not found. Please install Docker Desktop." -ForegroundColor Red
    Write-Host "Press any key to exit..."
    $host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown") | Out-Null
    exit 1
}

# Navigate to the correct directory (project root)
$projectRoot = Join-Path -Path $scriptPath -ChildPath "..\..\..\"
Set-Location $projectRoot

Write-Host "Starting Dynamic Pricing Service with Docker Compose..." -ForegroundColor Green
Write-Host "Project directory: $(Get-Location)" -ForegroundColor Yellow
Write-Host "Docker Compose file: $dockerComposeFile" -ForegroundColor Yellow
Write-Host ""
Write-Host "Running: stop ngp-me-dynamicpricing-svc -> remove image -> reset DB schema -> rebuild & start all services" -ForegroundColor Cyan
Write-Host ""

try {
    # Stop and remove only ngp-me-dynamicpricing-svc container and image
    Write-Host "Stopping ngp-me-dynamicpricing-svc container..." -ForegroundColor Yellow
    docker compose -p dynamicpricing-svc -f deployment/docker-compose/docker-compose.yml stop ngp-me-dynamicpricing-svc 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Container not running" -ForegroundColor Gray
    }
    
    Write-Host "Removing ngp-me-dynamicpricing-svc container..." -ForegroundColor Yellow
    docker compose -p dynamicpricing-svc -f deployment/docker-compose/docker-compose.yml rm -f ngp-me-dynamicpricing-svc 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Container not found" -ForegroundColor Gray
    }
    
    Write-Host "Removing dynamicpricing-svc-ngp-me-dynamicpricing-svc image..." -ForegroundColor Yellow
    docker rmi dynamicpricing-svc-ngp-me-dynamicpricing-svc 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Image dynamicpricing-svc-ngp-me-dynamicpricing-svc not found (this is normal for first run)" -ForegroundColor Gray
    }
    
    Write-Host "Resetting database schema to avoid migration conflicts..." -ForegroundColor Yellow
    docker compose -p dynamicpricing-svc -f deployment/docker-compose/docker-compose.yml exec postgres psql -U postgres -d ngp -c "DROP SCHEMA IF EXISTS ngp_me_dynamic_prc CASCADE; CREATE SCHEMA ngp_me_dynamic_prc;" 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Database schema reset skipped (database may not be ready yet)" -ForegroundColor Gray
    }
    
    Write-Host "Starting all services (rebuilding ngp-me-dynamicpricing-svc)..." -ForegroundColor Yellow
    # Execute Docker Compose command - this will rebuild only the ngp-me-dynamicpricing-svc service
    docker compose -p dynamicpricing-svc -f deployment/docker-compose/docker-compose.yml --profile all up -d --build ngp-me-dynamicpricing-svc
    docker compose -p dynamicpricing-svc -f deployment/docker-compose/docker-compose.yml --profile all up -d
    
    # Check execution result
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "✅ Successfully started Dynamic Pricing Service!" -ForegroundColor Green
        Write-Host ""
        Write-Host "Services are now running:" -ForegroundColor Yellow
        Write-Host "- PostgreSQL: http://localhost:15500" -ForegroundColor White
        Write-Host "- Redis: http://localhost:15501" -ForegroundColor White
        Write-Host "- Kafka UI: http://localhost:15502" -ForegroundColor White
        Write-Host "- Zookeeper: http://localhost:15503" -ForegroundColor White
        Write-Host "- Kafka: http://localhost:15504" -ForegroundColor White
        Write-Host "- Schema Registry: http://localhost:15507" -ForegroundColor White
        Write-Host "- Dynamic Pricing Service: http://localhost:15508" -ForegroundColor White
        Write-Host ""
        Write-Host "To stop all services, run:" -ForegroundColor Yellow
        Write-Host "docker compose -p dynamicpricing-svc -f deployment/docker-compose/docker-compose.yml --profile all down" -ForegroundColor Cyan
    } else {
        Write-Host ""
        Write-Host "❌ Error occurred while starting Dynamic Pricing Service." -ForegroundColor Red
        Write-Host "Please check the error messages above." -ForegroundColor Yellow
    }
} catch {
    Write-Host ""
    Write-Host "❌ An error occurred: $_" -ForegroundColor Red
    Write-Host "Please check your Docker installation and try again." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Press any key to exit..."
$host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown") | Out-Null

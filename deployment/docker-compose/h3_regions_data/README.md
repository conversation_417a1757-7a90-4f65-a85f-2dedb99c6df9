# H3 Region Configuration Generator

This directory contains tools for processing region-centric configuration JSON files into parameter-centric format for the dynamic pricing service.

## Overview

The Python script `helper_tools/generate_output_json_from_source_json.py` processes source JSON files from the `datasources` directory and generates three output files for each input model:

1. **Static Region-Based Configurations**: Parameter values for each region (with `postman_body_` prefix)
2. **Model Parameter Mapping**: Model metadata and parameter mappings (with `postman_body_` prefix)
3. **Combined Configurations**: Merged static region-based and model parameter mapping data (with `workbench_upload_` prefix)

Additionally, the script automatically copies and renames two datasource files:
- **H3 Region Definitions**: Geographic definitions for regions (with `workbench_upload_` prefix)
- **Static Time-Based Configurations**: Time-based parameter configurations (with `workbench_upload_` prefix)

## Usage

### Running the Complete Pipeline

The system requires three scripts to be executed in the following order:

#### Step 1: Start Docker Compose

First, start the Docker services:

**On macOS/Linux:**
```bash
./00_trigger_DPS_local_run.sh
```

**On Windows:**
```powershell
.\00_trigger_DPS_local_run.ps1
```

Wait for Docker Compose to fully start up before proceeding to the next step.

#### Step 2: Generate Configuration Files

Once Docker services are running, generate the configuration files for workbench upload and API testing:

**On macOS/Linux:**
```bash
./01_trigger_generate_h3config_workbench_data.sh
```

**On Windows:**
```powershell
.\01_trigger_generate_h3config_workbench_data.ps1
```

This script performs the following operations:
1. **Data Transformation**: Executes the Python script to convert region-centric configurations to parameter-centric format
2. **File Generation**: Creates 6 files (3 per model) with appropriate prefixes:
   - `postman_body_*` files for API testing
   - `workbench_upload_*` files for workbench data upload
3. **File Copying**: Automatically copies and renames datasource files with `workbench_upload_` prefix
4. **Output Organization**: Places all files in the `outputs` directory with clear naming conventions

#### Step 3: Insert Mock Data

Finally, insert the generated data via API:

**On macOS/Linux:**
```bash
./02_trigger_API_to_insert_mockdata.sh
```

**On Windows:**
```powershell
.\02_trigger_API_to_insert_mockdata.ps1
```

**Note:** You need to have Python 3 and Docker installed on your system.

### Input Requirements

Input files should:
- Be located in the `datasources` directory
- Follow the naming pattern: `{MODEL-NAME}_static_config_{DATETIME}.json`
- Contain the following JSON structure:
    - `static_region_based_configurations`: Contains region-centric parameter values
    - `surge_model_definitions`: Contains model metadata and parameter mappings

Additional datasource files (automatically processed):
- `h3_region_definitions_{DATETIME}.json`: Geographic definitions for regions
- `static_time_based_configurations_{DATETIME}.json`: Time-based parameter configurations

### Output Files

Output files will be generated in the `outputs` directory with the following naming patterns:

**For API Testing (postman_body_ prefix):**
- `postman_body_{MODEL-NAME}_static_region_based_configurations_{DATETIME}.json`
- `postman_body_{MODEL-NAME}_model_param_mapping_{DATETIME}.json`

**For Workbench Upload (workbench_upload_ prefix):**
- `workbench_upload_{MODEL-NAME}_static_region_based_and_model_mapping_configurations_{DATETIME}.json`
- `workbench_upload_h3_region_definitions_{DATETIME}.json`
- `workbench_upload_static_time_based_configurations_{DATETIME}.json`

**File Count Summary:**
- 3 files per model (2 models = 6 files total)
- 2 additional datasource files with workbench_upload_ prefix
- **Total: 8 files** (6 processed + 2 copied)

## Execution Summary

1. **Start Services**: Run `00_trigger_DPS_local_run` to start Docker Compose
2. **Generate Configuration Files**: Run `01_trigger_generate_h3config_workbench_data` to create configuration files with proper prefixes for different use cases
3. **Insert Data**: Run `02_trigger_API_to_insert_mockdata` to insert data via API

## Key Features

- **Automated File Processing**: Single script execution handles all data transformation and file copying
- **Differentiated Output**: Files are prefixed based on intended use (API testing vs. workbench upload)
- **Streamlined Workflow**: Reduced from 5 output files to 3 focused files per model
- **Cross-Platform Support**: Both bash (.sh) and PowerShell (.ps1) versions available
- **Flexible Configuration**: Conditional field handling (e.g., `effective_to` only included when present in source)
- **Comprehensive Logging**: Detailed execution feedback and error handling
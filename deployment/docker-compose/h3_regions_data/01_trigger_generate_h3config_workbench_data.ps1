# Get the script directory
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
$pythonScript = Join-Path -Path $scriptPath -ChildPath "helper_tools\generate_output_json_from_source_json.py"

# Check if the Python script exists
if (-not (Test-Path $pythonScript)) {
    Write-Host "Error: Python script not found at $pythonScript" -ForegroundColor Red
    Write-Host "Press any key to exit..."
    $host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown") | Out-Null
    exit 1
}

# Try to run the Python script
Write-Host "Starting to generate output JSON files..." -ForegroundColor Green
Write-Host "Running: python3 $pythonScript" -ForegroundColor Yellow

try {
    # Execute Python script - try python3 first
    python3 $pythonScript
    
    # Check execution result
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Successfully generated output JSON files!" -ForegroundColor Green
        
        # Additional step: Copy h3_region_definitions and static_time_based_configurations files
        Write-Host "Copying additional datasource files to outputs with workbench_upload_ prefix..." -ForegroundColor Yellow
        
        $datasourcesDir = Join-Path -Path $scriptPath -ChildPath "datasources"
        $outputsDir = Join-Path -Path $scriptPath -ChildPath "outputs"
        
        # Find and copy h3_region_definitions files
        $h3Files = Get-ChildItem -Path $datasourcesDir -Name "h3_region_definitions_*.json" -File
        foreach ($file in $h3Files) {
            $sourcePath = Join-Path -Path $datasourcesDir -ChildPath $file
            $destPath = Join-Path -Path $outputsDir -ChildPath "workbench_upload_$file"
            Copy-Item -Path $sourcePath -Destination $destPath
            Write-Host "Copied: workbench_upload_$file" -ForegroundColor Cyan
        }
        
        # Find and copy static_time_based_configurations files
        $timeConfigFiles = Get-ChildItem -Path $datasourcesDir -Name "static_time_based_configurations_*.json" -File
        foreach ($file in $timeConfigFiles) {
            $sourcePath = Join-Path -Path $datasourcesDir -ChildPath $file
            $destPath = Join-Path -Path $outputsDir -ChildPath "workbench_upload_$file"
            Copy-Item -Path $sourcePath -Destination $destPath
            Write-Host "Copied: workbench_upload_$file" -ForegroundColor Cyan
        }
        
        Write-Host "All files processing completed successfully!" -ForegroundColor Green
    } else {
        Write-Host "Error occurred while generating output JSON files." -ForegroundColor Red
    }
} catch {
    Write-Host "An error occurred: $_" -ForegroundColor Red
    
    # Try with python instead of python3
    Write-Host "Trying with 'python' instead of 'python3'..." -ForegroundColor Yellow
    try {
        python $pythonScript
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Successfully generated output JSON files!" -ForegroundColor Green
            
            # Additional step: Copy h3_region_definitions and static_time_based_configurations files
            Write-Host "Copying additional datasource files to outputs with workbench_upload_ prefix..." -ForegroundColor Yellow
            
            $datasourcesDir = Join-Path -Path $scriptPath -ChildPath "datasources"
            $outputsDir = Join-Path -Path $scriptPath -ChildPath "outputs"
            
            # Find and copy h3_region_definitions files
            $h3Files = Get-ChildItem -Path $datasourcesDir -Name "h3_region_definitions_*.json" -File
            foreach ($file in $h3Files) {
                $sourcePath = Join-Path -Path $datasourcesDir -ChildPath $file
                $destPath = Join-Path -Path $outputsDir -ChildPath "workbench_upload_$file"
                Copy-Item -Path $sourcePath -Destination $destPath
                Write-Host "Copied: workbench_upload_$file" -ForegroundColor Cyan
            }
            
            # Find and copy static_time_based_configurations files
            $timeConfigFiles = Get-ChildItem -Path $datasourcesDir -Name "static_time_based_configurations_*.json" -File
            foreach ($file in $timeConfigFiles) {
                $sourcePath = Join-Path -Path $datasourcesDir -ChildPath $file
                $destPath = Join-Path -Path $outputsDir -ChildPath "workbench_upload_$file"
                Copy-Item -Path $sourcePath -Destination $destPath
                Write-Host "Copied: workbench_upload_$file" -ForegroundColor Cyan
            }
            
            Write-Host "All files processing completed successfully!" -ForegroundColor Green
        } else {
            Write-Host "Error occurred while generating output JSON files." -ForegroundColor Red
        }
    } catch {
        Write-Host "Failed to run the script with both 'python3' and 'python'." -ForegroundColor Red
        Write-Host "Make sure Python is installed and in your PATH." -ForegroundColor Yellow
    }
}

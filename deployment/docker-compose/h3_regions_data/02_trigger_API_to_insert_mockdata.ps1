# Get the script directory
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
$pythonScript = Join-Path -Path $scriptPath -ChildPath "helper_tools\generate_mockdata_to_db_from_output_json_through_api.py"

# Check if the Python script exists
if (-not (Test-Path $pythonScript)) {
    Write-Host "Error: Python script not found at $pythonScript" -ForegroundColor Red
    Write-Host "Press any key to exit..."
    $host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown") | Out-Null
    exit 1
}

# Try to run the Python script
Write-Host "Starting to generate output JSON files..." -ForegroundColor Green
Write-Host "Running: python $pythonScript" -ForegroundColor Yellow

try {
    # Execute Python script - try python3 first
    python3 $pythonScript
    
    # Check execution result
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Successfully generated output JSON files!" -ForegroundColor Green
    } else {
        Write-Host "Error occurred while generating output JSON files." -ForegroundColor Red
    }
} catch {
    Write-Host "An error occurred: $_" -ForegroundColor Red
    
    # Try with python instead of python3
    Write-Host "Trying with 'python' instead of 'python3'..." -ForegroundColor Yellow
    try {
        python $pythonScript
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Successfully generated output JSON files!" -ForegroundColor Green
        } else {
            Write-Host "Error occurred while generating output JSON files." -ForegroundColor Red
        }
    } catch {
        Write-Host "Failed to run the script with both 'python3' and 'python'." -ForegroundColor Red
        Write-Host "Make sure Python is installed and in your PATH." -ForegroundColor Yellow
    }
}

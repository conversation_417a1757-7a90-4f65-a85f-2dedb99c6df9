#!/usr/bin/env python3

import json
import os
import re
import glob
import traceback
from datetime import datetime
from collections import OrderedDict

# Configuration settings
DEFAULT_CONFIG = {
    'version_default': '1.0.0',
    'description_default': 'Configuration for region-based surge pricing',
    'file_pattern': '*_static_config_*.json',
    'output_file_prefixes': {
        'static_region': '_static_region_based_configurations_',
        'model_param': '_model_param_mapping_',
        'fe_combined': '_static_region_based_and_model_mapping_configurations_'
    },
    'file_prefixes': {
        'static_region': 'postman_body_',
        'model_param': 'postman_body_',
        'fe_combined': 'workbench_upload_'
    },
    'output_dir': 'outputs',
    'source_dir': 'datasources',
    'datetime_format': '%Y-%m-%d %H:%M:%S',
    'verbose': True,
    'model_description_template': "Rule-based Model {}"
}

def log(message, verbose=True):
    """
    Conditionally print messages based on verbose setting.
    
    Args:
        message: The message to print
        verbose: If True, will only print if DEFAULT_CONFIG['verbose'] is also True
                If False, will always print regardless of DEFAULT_CONFIG
    """
    if DEFAULT_CONFIG['verbose'] or verbose:
        print(message)

def get_file_metadata(filename):
    """
    Extract model name and datetime string from filename.
    
    Parses filenames that follow the pattern: {MODEL-NAME}_static_config_{DATETIME}.json
    
    Returns:
        Tuple of (model_name, datetime_str) or (None, None) if pattern doesn't match
    """
    match = re.match(r'(.+)_static_config_(.+)\.json', filename)
    if not match:
        return None, None
    return match.group(1), match.group(2)

def get_config_metadata(config, config_name, default_description=None):
    """
    Extract and process common configuration metadata.
    
    Extracts and processes common fields across different configurations:
    - configuration_version (with default fallback)
    - effective_from (with current date fallback)
    - effective_to (only if present and has valid value in source)
    - description (with provided default fallback)
    
    Args:
        config: The configuration object to extract metadata from
        config_name: Name of configuration for logging purposes
        default_description: Default description to use if not in config
        
    Returns:
        Dictionary with standardized metadata fields
    """
    # Extract version with default
    version = config.get("configuration_version", DEFAULT_CONFIG['version_default'])
    
    # Process effective dates
    effective_from = config.get("effective_from")
    if not effective_from:
        effective_from = datetime.now().strftime(DEFAULT_CONFIG['datetime_format'])
        log(f"Note: Using current date as effective_from for {config_name}: {effective_from}")
    else:
        log(f"Using effective_from date from source for {config_name}: {effective_from}")
    
    # Handle effective_to - only include if present and has valid value
    result = {
        "version": version,
        "effective_from": effective_from,
        "description": default_description or config.get("description", "")
    }
    
    # Only add effective_to if it exists in source and has a valid value
    if "effective_to" in config and config["effective_to"] is not None and config["effective_to"].strip():
        result["effective_to"] = config["effective_to"]
        log(f"Using effective_to date from source for {config_name}: {config['effective_to']}")
    else:
        log(f"No valid effective_to found in source for {config_name}, excluding from output")
    
    return result

# H3 region definitions processing removed - not needed for simplified output

# Time-based configurations processing removed - not needed for simplified output

def process_region_based_configurations(static_region_configs, filename):
    """
    Process region-based configuration data and convert to parameter-centric format.
    
    Transforms region-based configurations from source format (organized by region)
    to parameter-centric format (organized by parameter with region values).
    
    Steps:
    1. Extract configuration metadata (version, dates, description)
    2. Extract region data from h3_regions or region_configuration
    3. Process region data into parameter objects with region values
    """
    # If it's not a list or empty, we can't proceed with this section
    if not isinstance(static_region_configs, list) or not static_region_configs:
        log(f"Warning: No valid static_region_based_configurations found in {filename}")
        return [], {}
    
    # Get the first configuration in the list
    config = static_region_configs[0]
    config_name = config.get('configuration_name', 'unnamed')
    log(f"Processing configuration: {config_name}")
    
    # Get metadata using the helper function
    metadata = get_config_metadata(
        config, 
        f"region-based config {config_name}", 
        DEFAULT_CONFIG['description_default']
    )
    
    # Get the region data - it might be in h3_regions or region_configuration
    region_data = config.get("h3_regions", config.get("region_configuration", {}))
    
    if not region_data:
        log(f"Warning: No region data found in configuration. Looked for 'h3_regions' and 'region_configuration'")
        return [], {}
    
    # Get total region count
    region_count = len(region_data)
    log(f"Found {region_count} regions to process")
    
    # Create static region-based configurations
    static_region_based_data = create_parameter_objects(region_data, metadata)
    
    # Create an empty model parameter mapping (legacy - this will be replaced by process_model_definition)
    model_param_mapping = {}
    
    return static_region_based_data, model_param_mapping

def process_model_definition(data, model_name, filename):
    """
    Process surge_model_definitions data to create model parameter mapping output.
    This function converts surge_model_definitions data from source JSON file to output format.
    
    The output includes:
    1. Model name (from source model_name field)
    2. Description (from source description field)
    3. Endpoint URL (from source endpoint_url field)
    4. Request field mappings (from source request_fields_mappings, converted to array format)
    
    Input format example:
    {
      "model_name": "model_v4_1",
      "description": "Rule-base Model v4_1",
      "endpoint_url": "/v4/calculate_surge",
      "request_fields_mappings": {
        "additional_surge_high": {
          "mapping_type": "STATIC_REGION_BASED_CONFIGURATION",
          "variable_name": "v4_c_additional_surge_high"
        },
        ...
      }
    }
    
    Output format example:
    {
      "modelName": "model_v4_1",
      "description": "Rule-based Model v4_1",
      "endpointUrl": "/v4/calculate_surge",
      "requestFieldsMappings": [
        {
          "mappingType": "STATIC_REGION_BASED_CONFIGURATION",
          "requestParameterName": "additional_surge_high",
          "mappingConfigurationName": "v4_c_additional_surge_high"
        },
        ...
      ]
    }
    """
    # Get surge model definitions from source data
    model_def = data.get("surge_model_definitions", {})
    if not model_def:
        log(f"Warning: No surge_model_definitions found in {filename}")
        return {}
    
    # Get model name from source or use file name as fallback
    source_model_name = model_def.get("model_name")
    if not source_model_name:
        log(f"Warning: No model_name found in surge_model_definitions, using filename model: {model_name}")
        source_model_name = model_name
    else:
        log(f"Using model name from source: {source_model_name}")
    
    # Use description from source if available
    source_description = model_def.get("description")
    if source_description:
        description = source_description
        log(f"Using description from source: {description}")
    else:
        # Extract model version suffix for default description
        model_version = model_name
        if model_name.startswith("model_"):
            model_version = model_name[6:]  # Remove 'model_' prefix
        
        # Use a default description template from config
        default_desc_template = DEFAULT_CONFIG.get('model_description_template', "Rule-based Model {}")
        description = default_desc_template.format(model_version)
        log(f"No description in source, using default: {description}")

    endpoint_url = model_def.get("endpoint_url")
    if not endpoint_url:
        log(f"Warning: No endpoint_url found in surge_model_definitions of {filename}")

    # Create output structure using direct mapping from source fields
    output = {
        "modelName": source_model_name,  # From model_name in source
        "description": description,      # From description in source (or default)
        "endpointUrl": endpoint_url,     # From endpoint_url in source
        "requestFieldsMappings": []      # Will be populated from request_fields_mappings
    }
    
    # Process request_fields_mappings
    request_fields = model_def.get("request_fields_mappings", {})
    if not request_fields:
        log(f"Warning: No request_fields_mappings found in surge_model_definitions in {filename}")
        return output
    
    # Convert request_fields_mappings object to array format
    region_based_mappings = []
    
    for request_param, mapping in request_fields.items():
        mapping_type = mapping.get("mapping_type", "")
        variable_name = mapping.get("variable_name", "")
        
        if not mapping_type or not variable_name:
            log(f"Warning: Missing mapping_type or variable_name for {request_param} in {filename}")
            continue
        
        # Create mapping entry for output with direct field mappings:
        # mapping_type -> mappingType
        # request_param (key) -> requestParameterName
        # variable_name -> mappingConfigurationName
        mapping_entry = {
            "mappingType": mapping_type,
            "requestParameterName": request_param,
            "mappingConfigurationName": variable_name
        }
        
        # All mappings are now region-based since we removed time-based support
        region_based_mappings.append(mapping_entry)
    
    # Add all region-based mappings to output
    output["requestFieldsMappings"] = region_based_mappings
    
    log(f"Created model parameter mapping with {len(output['requestFieldsMappings'])} parameter mappings")
    return output

def create_parameter_objects(region_data, metadata):
    """
    Create parameter objects from region data.
    
    Transforms data from region-centric format to parameter-centric format:
    
    Input (region-centric):
    {
      "1": {"param1": 0.4, "param2": 0.7},
      "2": {"param1": 0.5, "param2": 0.8}
    }
    
    Output (parameter-centric):
    [
      {
        "name": "param1",
        "version": "1.0.0",
        "effectiveFrom": "2025-01-01",
        "effectiveTo": "2099-12-31",
        "description": "...",
        "regionValues": [
          {"regionId": 1, "value": "0.4"},
          {"regionId": 2, "value": "0.5"}
        ]
      },
      {
        "name": "param2",
        "version": "1.0.0",
        "effectiveFrom": "2025-01-01",
        "effectiveTo": "2099-12-31",
        "description": "...",
        "regionValues": [
          {"regionId": 1, "value": "0.7"},
          {"regionId": 2, "value": "0.8"}
        ]
      }
    ]
    """
    static_region_based_data = []
    
    # Get all parameters preserving order from the first region
    # Find the first region numerically
    try:
        region_ids = sorted([int(r) for r in region_data.keys()])
    except (ValueError, TypeError):
        log(f"Warning: Invalid region IDs found in the configuration")
        return []
        
    if not region_ids:
        log(f"Warning: No valid region IDs found in the configuration")
        return []
        
    first_region_id = str(region_ids[0])
    ordered_params = []
    
    # Get parameters in their original order from the first region
    if first_region_id in region_data and isinstance(region_data[first_region_id], dict):
        ordered_params = list(region_data[first_region_id].keys())
        log(f"Retrieved {len(ordered_params)} ordered parameters from region {first_region_id}")
    
    # Find all unique parameters across all regions (for any we missed in the first region)
    all_params = set()
    for region_id, params in region_data.items():
        if isinstance(params, dict):
            all_params.update(params.keys())
    
    # Add any params not in the first region
    for param in all_params:
        if param not in ordered_params:
            ordered_params.append(param)
    
    log(f"Total unique parameters across all regions: {len(all_params)}")
    
    # For each parameter, create a parameter object with region values
    param_count = 0
    for param in ordered_params:
        # Create a new parameter object for each parameter
        param_obj = OrderedDict([
            ("name", param),
            ("version", metadata["version"]),
            ("effectiveFrom", metadata["effective_from"]),
        ])
        
        # Only add effectiveTo if it exists in metadata
        if "effective_to" in metadata:
            param_obj["effectiveTo"] = metadata["effective_to"]
        
        param_obj["description"] = metadata["description"]
        param_obj["regionValues"] = []
        
        # Add region values to this parameter
        for region_id in sorted([int(r) for r in region_data.keys()]):
            str_region_id = str(region_id)
            if (str_region_id in region_data and 
                isinstance(region_data[str_region_id], dict) and 
                param in region_data[str_region_id]):
                
                param_value = region_data[str_region_id][param]
                # Convert all values to strings for consistency
                param_obj["regionValues"].append({
                    "regionId": region_id,
                    "value": str(param_value)
                })
        
        # Add the parameter object to the array if it has region values
        if param_obj["regionValues"]:
            static_region_based_data.append(param_obj)
            param_count += 1
    
    log(f"Created {param_count} parameter objects with region values")
    return static_region_based_data

def write_output_files(outputs_dir, model_name, datetime_str, static_region_based_data, model_param_mapping):
    """
    Write the processed data to output files.
    
    Generates three output files:
    1. Static region-based configurations - Contains parameter values for each region
    2. Model parameter mapping - Contains model metadata and parameter mappings 
    3. FE combined configurations - Contains merged static region-based and model parameter mapping data
    
    All files follow the naming pattern: {model_name}{prefix}{datetime_str}.json
    """
    # Generate output filenames with different prefixes for different file types
    region_output_file = os.path.join(outputs_dir, f"{DEFAULT_CONFIG['file_prefixes']['static_region']}{model_name}{DEFAULT_CONFIG['output_file_prefixes']['static_region']}{datetime_str}.json")
    param_mapping_file = os.path.join(outputs_dir, f"{DEFAULT_CONFIG['file_prefixes']['model_param']}{model_name}{DEFAULT_CONFIG['output_file_prefixes']['model_param']}{datetime_str}.json")
    fe_combined_file = os.path.join(outputs_dir, f"{DEFAULT_CONFIG['file_prefixes']['fe_combined']}{model_name}{DEFAULT_CONFIG['output_file_prefixes']['fe_combined']}{datetime_str}.json")
    
    # Write output files (overwrite if they already exist)
    with open(region_output_file, 'w') as f:
        json.dump(static_region_based_data, f, indent=2)
    
    with open(param_mapping_file, 'w') as f:
        json.dump(model_param_mapping, f, indent=2)
    
    # Generate the combined FE file
    fe_combined_data = {
        "static_region_based_configurations": static_region_based_data,
        "model_param_mapping": model_param_mapping
    }
    
    with open(fe_combined_file, 'w') as f:
        json.dump(fe_combined_data, f, indent=2)
    
    log(f"Generated combined FE file: {os.path.basename(fe_combined_file)}")
    
    return [
        os.path.basename(region_output_file),
        os.path.basename(param_mapping_file),
        os.path.basename(fe_combined_file)
    ]

def process_input_file(input_file):
    """
    Process a single input file and generate output files.
    
    Steps:
    1. Extract model name and datetime from file name
    2. Read and parse JSON data from the file
    3. Process static_region_based_configurations 
    4. Process surge_model_definitions
    5. Write all data to corresponding output files
    """
    try:
        # Parse input filename to extract MODEL-NAME and DATETIME
        filename = os.path.basename(input_file)
        model_name, datetime_str = get_file_metadata(filename)
        
        if not model_name or not datetime_str:
            log(f"Warning: File {filename} does not match expected naming pattern, skipping")
            return
            
        log(f"Processing file: {filename}")
        log(f"Model name: {model_name}, Datetime: {datetime_str}")
        
        # Read the input file
        with open(input_file, 'r') as f:
            try:
                data = json.load(f)
            except json.JSONDecodeError as je:
                log(f"Error: {filename} contains invalid JSON: {je}")
                return
        
        # 1. Process static_region_based_configurations
        static_region_based_data, _ = process_region_based_configurations(
            data.get("static_region_based_configurations", []), 
            filename
        )
        
        # 2. Process surge_model_definitions
        model_param_mapping = process_model_definition(
            data,
            model_name,
            filename
        )
        
        # Set up output directory
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        outputs_dir = os.path.join(base_dir, DEFAULT_CONFIG['output_dir'])
        os.makedirs(outputs_dir, exist_ok=True)
        
        # Write output files
        output_files = write_output_files(
            outputs_dir, 
            model_name, 
            datetime_str, 
            static_region_based_data,
            model_param_mapping
        )
        
        log(f"Successfully generated output files for {filename}:")
        for file in output_files:
            log(f"  - {file}")
        
    except Exception as e:
        log(f"Error processing file {input_file}: {str(e)}", verbose=True)
        traceback.print_exc()

def generate_output_json():
    """
    Process static configuration files in the /datasources directory and generate three output JSON files for each:
    1. static_region_based_configurations - Parameter values for each region
    2. model_param_mapping - Model metadata and parameter mappings (from surge_model_definitions)
    3. static_region_based_and_model_mapping_configurations - Combined data for workbench upload
    
    Input files should follow the naming pattern: {MODEL-NAME}_static_config_{DATETIME}.json
    """
    # Define paths - relative to current script location
    base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    datasources_dir = os.path.join(base_dir, DEFAULT_CONFIG['source_dir'])
    
    # Get all input files that match the pattern {MODEL-NAME}_static_config_{DATETIME}.json
    input_files = glob.glob(os.path.join(datasources_dir, DEFAULT_CONFIG['file_pattern']))
    
    log(f"Found {len(input_files)} input files to process")
    
    for input_file in input_files:
        process_input_file(input_file)
    
    log("Processing complete")

if __name__ == "__main__":
    generate_output_json()

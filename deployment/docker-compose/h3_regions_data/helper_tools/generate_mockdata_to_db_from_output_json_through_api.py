#!/usr/bin/env python3

import json
import os
import glob
import requests
import logging
import argparse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_config():
    """
    Get configuration from command line arguments and environment variables
    
    Returns:
        Dictionary with configuration values
    """
    parser = argparse.ArgumentParser(description='Upload static time-based configurations to the API')
    
    parser.add_argument('--api-url', dest='api_url', 
                        default=os.environ.get('API_BASE_URL', 'http://localhost:15508'),
                        help='API base URL (default: http://localhost:15508 or API_BASE_URL env var)')
    
    parser.add_argument('--user-id', dest='user_id', 
                        default=os.environ.get('USER_ID', 'admin'),
                        help='User ID for audit purposes (default: admin or USER_ID env var)')
    
    parser.add_argument('--output-dir', dest='output_dir', 
                        default=os.environ.get('OUTPUT_DIR', 'outputs'),
                        help='Output directory containing configuration files (default: outputs or OUTPUT_DIR env var)')
    
    parser.add_argument('--file-pattern', dest='file_pattern', 
                        default=os.environ.get('FILE_PATTERN', 'postman_body_static_time_based_configurations_*.json'),
                        help='File pattern to match configuration files (default: postman_body_static_time_based_configurations_*.json or FILE_PATTERN env var)')
    
    parser.add_argument('--timeout', dest='timeout', type=float,
                        default=float(os.environ.get('API_TIMEOUT', '30')),
                        help='API request timeout in seconds (default: 30 or API_TIMEOUT env var)')

    args = parser.parse_args()
    
    return {
        'api_base_url': args.api_url,
        'user_id': args.user_id,
        'output_dir': args.output_dir,
        'endpoint': '/v1.0/surge-computation/static-time-based-configurations',
        'file_pattern': args.file_pattern,
        'timeout': args.timeout
    }

def api_request(config, method, endpoint, data=None, headers=None):
    """
    Send API request and handle response
    
    Args:
        config: Configuration dictionary
        method: HTTP method ('GET', 'POST', 'PUT', 'DELETE')
        endpoint: API endpoint path
        data: Data to send (for POST/PUT requests)
        headers: HTTP headers
        
    Returns:
        Response object or None (if request failed)
    """
    url = f"{config['api_base_url']}{endpoint}"
    default_headers = {'Content-Type': 'application/json', 'X-User-Id': config['user_id']}
    
    if headers:
        default_headers.update(headers)
    
    # Get timeout from config, with fallback to 30 seconds if not specified
    timeout = config.get('timeout', 30)
    
    try:
        logger.debug(f"Sending {method} request to {url} with timeout {timeout}s")
        if method.upper() == 'GET':
            response = requests.get(url, headers=default_headers, timeout=timeout)
        elif method.upper() == 'POST':
            response = requests.post(url, json=data, headers=default_headers, timeout=timeout)
        elif method.upper() == 'PUT':
            response = requests.put(url, json=data, headers=default_headers, timeout=timeout)
        elif method.upper() == 'DELETE':
            response = requests.delete(url, headers=default_headers, timeout=timeout)
        else:
            logger.error(f"Unsupported HTTP method: {method}")
            return None
        
        # No longer calling raise_for_status(), we'll handle all responses ourselves
        return response
        
    except requests.exceptions.Timeout:
        logger.error(f"API request timed out after {timeout} seconds: {url}")
        return None
    except requests.exceptions.ConnectionError:
        logger.error(f"Connection error when calling API: {url}")
        return None
    except requests.exceptions.RequestException as e:
        logger.error(f"API request failed: {e}")
        if hasattr(e, 'response') and e.response is not None:
            logger.error(f"Response status code: {e.response.status_code}")
            logger.error(f"Response content: {e.response.text}")
        return e.response if hasattr(e, 'response') else None

def upload_static_region_based_config(config):
    """
    Find all matching static region-based configuration files and send to API
    
    Args:
        config: Configuration dictionary
    
    Returns:
        Boolean indicating overall success or failure
    """
    # Get base_dir (two levels up from script location if not absolute path)
    if os.path.isabs(config['output_dir']):
        outputs_dir = config['output_dir']
    else:
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        outputs_dir = os.path.join(base_dir, config['output_dir'])
    
    # Find all static region-based configuration files
    region_pattern = os.path.join(outputs_dir, 'postman_body_*_static_region_based_configurations_*.json')
    region_config_files = sorted(glob.glob(region_pattern))
    
    if not region_config_files:
        logger.error(f"No matching static region-based configuration files found with pattern: {region_pattern}")
        print(f"\nAPI Call Result:")
        print(f"============================================")
        print(f"Status: Failed")
        print(f"Error Message: No matching configuration files found")
        return False
    
    logger.info(f"Found {len(region_config_files)} static region-based configuration files to process")
    
    overall_success = True
    successful_uploads = 0
    
    # Process all matching files
    for file_path in region_config_files:
        filename = os.path.basename(file_path)
        logger.info(f"Processing static region-based file: {filename}")
        
        try:
            # Read file content
            with open(file_path, 'r') as f:
                configs = json.load(f)
            
            # Check if it's a valid configuration array
            if not isinstance(configs, list):
                logger.error(f"File {filename} does not contain a valid configuration array")
                print(f"\nAPI Call Result:")
                print(f"============================================")
                print(f"Status: Failed")
                print(f"Error Message: File does not contain a valid configuration array")
                overall_success = False
                continue
            
            logger.info(f"File {filename} contains {len(configs)} configurations")
            
            # Send the entire array to the API in a single call
            response = api_request(config, 'POST', '/v1.0/surge-computation/static-region-based-configurations', data=configs)
            
            # Print API call HTTP status and content
            print(f"\nAPI Call Result:")
            print(f"============================================")
            
            if response is None:
                print(f"Status: Failed")
                print(f"Error Message: Unable to connect to API server or request timed out")
                overall_success = False
                continue
            
            print(f"HTTP Status Code: {response.status_code}")
            
            if response.status_code in [200, 201, 204]:
                print(f"Status: Success")
                print(f"Successfully uploaded {len(configs)} configurations")
                successful_uploads += 1
            else:
                print(f"Status: Failed")
                try:
                    error_json = response.json()
                    if 'error' in error_json and 'message' in error_json['error']:
                        print(f"Error Message: {error_json['error']['message']}")
                        if 'code' in error_json['error']:
                            print(f"Error Code: {error_json['error']['code']}")
                    else:
                        print(f"Error Message: {error_json}")
                except:
                    print(f"Error Message: {response.text}")
                overall_success = False
                
        except Exception as e:
            logger.error(f"Error processing static region-based file {filename}: {str(e)}")
            print(f"\nAPI Call Result:")
            print(f"============================================")
            print(f"Status: Failed")
            print(f"Error Message: {str(e)}")
            overall_success = False
    
    # Print summary
    print(f"\nStatic Region-Based Configurations Upload Summary:")
    print(f"============================================")
    print(f"Total files found: {len(region_config_files)}")
    print(f"Successfully uploaded: {successful_uploads}")
    print(f"Failed uploads: {len(region_config_files) - successful_uploads}")
    
    return overall_success

def upload_model_param_mapping(config):
    """
    Find all matching model parameter mapping files and send to API
    
    Args:
        config: Configuration dictionary
    
    Returns:
        Boolean indicating overall success or failure
    """
    # Get base_dir (two levels up from script location if not absolute path)
    if os.path.isabs(config['output_dir']):
        outputs_dir = config['output_dir']
    else:
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        outputs_dir = os.path.join(base_dir, config['output_dir'])
    
    # Find all model parameter mapping files
    model_pattern = os.path.join(outputs_dir, 'postman_body_*_model_param_mapping_*.json')
    model_config_files = sorted(glob.glob(model_pattern))
    
    if not model_config_files:
        logger.error(f"No matching model parameter mapping files found with pattern: {model_pattern}")
        print(f"\nAPI Call Result:")
        print(f"============================================")
        print(f"Status: Failed")
        print(f"Error Message: No matching configuration files found")
        return False
    
    logger.info(f"Found {len(model_config_files)} model parameter mapping files to process")
    
    overall_success = True
    successful_uploads = 0
    
    # Process all matching files
    for file_path in model_config_files:
        filename = os.path.basename(file_path)
        logger.info(f"Processing model parameter mapping file: {filename}")
        
        try:
            # Read file content
            with open(file_path, 'r') as f:
                config_data = json.load(f)
            
            # Check if it's a valid configuration object (not array for model mapping)
            if not isinstance(config_data, dict):
                logger.error(f"File {filename} does not contain a valid configuration object")
                print(f"\nAPI Call Result:")
                print(f"============================================")
                print(f"Status: Failed")
                print(f"Error Message: File does not contain a valid configuration object")
                overall_success = False
                continue
            
            logger.info(f"File {filename} contains model parameter mapping configuration")
            
            # Send the configuration object to the API in a single call
            response = api_request(config, 'POST', '/v1.0/surge-computation/models', data=config_data)
            
            # Print API call HTTP status and content
            print(f"\nAPI Call Result:")
            print(f"============================================")
            
            if response is None:
                print(f"Status: Failed")
                print(f"Error Message: Unable to connect to API server or request timed out")
                overall_success = False
                continue
            
            print(f"HTTP Status Code: {response.status_code}")
            
            if response.status_code in [200, 201, 204]:
                print(f"Status: Success")
                print(f"Successfully uploaded 1 configurations")
                successful_uploads += 1
            else:
                print(f"Status: Failed")
                try:
                    error_json = response.json()
                    if 'error' in error_json and 'message' in error_json['error']:
                        print(f"Error Message: {error_json['error']['message']}")
                        if 'code' in error_json['error']:
                            print(f"Error Code: {error_json['error']['code']}")
                    else:
                        print(f"Error Message: {error_json}")
                except:
                    print(f"Error Message: {response.text}")
                overall_success = False
                
        except Exception as e:
            logger.error(f"Error processing model parameter mapping file {filename}: {str(e)}")
            print(f"\nAPI Call Result:")
            print(f"============================================")
            print(f"Status: Failed")
            print(f"Error Message: {str(e)}")
            overall_success = False
    
    # Print summary
    print(f"\nModel Parameter Mapping Upload Summary:")
    print(f"============================================")
    print(f"Total files found: {len(model_config_files)}")
    print(f"Successfully uploaded: {successful_uploads}")
    print(f"Failed uploads: {len(model_config_files) - successful_uploads}")
    
    return overall_success

def upload_static_time_based_config(config):
    """
    Find the first matching static time-based configuration file and send to API
    
    Args:
        config: Configuration dictionary
    
    Returns:
        Boolean indicating success or failure
    """
    # Get base_dir (two levels up from script location if not absolute path)
    if os.path.isabs(config['output_dir']):
        outputs_dir = config['output_dir']
    else:
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        outputs_dir = os.path.join(base_dir, config['output_dir'])
    
    # Find all time-based configuration files matching the pattern
    pattern = os.path.join(outputs_dir, config['file_pattern'])
    time_config_files = sorted(glob.glob(pattern))
    
    if not time_config_files:
        logger.error(f"No matching files found with pattern: {pattern}")
        print(f"\nAPI Call Result:")
        print(f"============================================")
        print(f"Status: Failed")
        print(f"Error Message: No matching configuration files found")
        return False

    merged_configs = {}
    for file_path in time_config_files:
        with open(file_path, 'r') as f:
            configs = json.load(f)

        for item in configs:
            if 'name' in item:
                name = item['name']
                merged_configs[name] = item

    configs = list(merged_configs.values())

    try:
        # Send the entire array to the API in a single call
        response = api_request(config, 'POST', config['endpoint'], data=configs)

        # Print API call HTTP status and content
        print(f"\nAPI Call Result:")
        print(f"============================================")

        if response is None:
            print(f"Status: Failed")
            print(f"Error Message: Unable to connect to API server or request timed out")
            return False

        print(f"HTTP Status Code: {response.status_code}")

        if response.status_code in [200, 201, 204]:
            print(f"Status: Success")
            print(f"Successfully uploaded {len(configs)} configurations")
            return True
        else:
            print(f"Status: Failed")
            try:
                error_json = response.json()
                if 'error' in error_json and 'message' in error_json['error']:
                    print(f"Error Message: {error_json['error']['message']}")
                    if 'code' in error_json['error']:
                        print(f"Error Code: {error_json['error']['code']}")
                else:
                    print(f"Error Message: {error_json}")
            except:
                print(f"Error Message: {response.text}")
            return False
            
    except Exception as e:
        logger.error("Error processing time based configuration")
        print(f"\nAPI Call Result:")
        print(f"============================================")
        print(f"Status: Failed")
        print(f"Error Message: {str(e)}")
        return False

def generate_mockdata_to_db():
    """
    Main function to upload mock data to database through API
    """
    logger.info("Starting to upload mock data to database through API...")
    
    # Get configuration
    config = get_config()
    
    # Track overall success
    overall_success = True
    
    # Upload static time-based configurations from the first matching file
    logger.info("=== Uploading Static Time-Based Configurations ===")
    time_success = upload_static_time_based_config(config)
    
    if time_success:
        logger.info("Static time-based configurations uploaded successfully")
    else:
        logger.error("Failed to upload static time-based configurations")
        overall_success = False

    # Upload static region-based configurations from all matching files
    logger.info("=== Uploading Static Region-Based Configurations ===")
    region_success = upload_static_region_based_config(config)

    if region_success:
        logger.info("Static region-based configurations uploaded successfully")
    else:
        logger.error("Failed to upload static region-based configurations")
        overall_success = False

    # Upload model parameter mapping configurations from all matching files
    logger.info("=== Uploading Model Parameter Mapping Configurations ===")
    model_success = upload_model_param_mapping(config)

    if model_success:
        logger.info("Model parameter mapping configurations uploaded successfully")
    else:
        logger.error("Failed to upload model parameter mapping configurations")
        overall_success = False

    # Final summary
    print(f"\n" + "="*60)
    print(f"FINAL UPLOAD SUMMARY")
    print(f"="*60)
    print(f"Static Time-Based Configurations: {'✅ SUCCESS' if time_success else '❌ FAILED'}")
    print(f"Static Region-Based Configurations: {'✅ SUCCESS' if region_success else '❌ FAILED'}")
    print(f"Model Parameter Mapping Configurations: {'✅ SUCCESS' if model_success else '❌ FAILED'}")
    print(f"Overall Status: {'✅ SUCCESS' if overall_success else '❌ FAILED'}")
    print(f"="*60)

    logger.info("Mock data upload completed")

if __name__ == "__main__":
    generate_mockdata_to_db() 
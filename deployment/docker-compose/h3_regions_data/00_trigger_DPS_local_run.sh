#!/bin/bash

# Get the script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
DOCKER_COMPOSE_FILE="$SCRIPT_DIR/../docker-compose.yml"

# Check if Docker Compose file exists
if [ ! -f "$DOCKER_COMPOSE_FILE" ]; then
    echo "Error: Docker Compose file not found at $DOCKER_COMPOSE_FILE"
    echo "Press any key to exit..."
    read -n 1
    exit 1
fi

# Check if Docker is running
if ! docker info &> /dev/null; then
    echo "Error: Docker is not running. Please start Docker Desktop first."
    echo "Press any key to exit..."
    read -n 1
    exit 1
fi

# Check if docker-compose command is available
if ! command -v docker &> /dev/null; then
    echo "Error: Docker command not found. Please install Docker Desktop."
    echo "Press any key to exit..."
    read -n 1
    exit 1
fi

# Navigate to the correct directory (project root)
PROJECT_ROOT="$SCRIPT_DIR/../../.."
cd "$PROJECT_ROOT"

echo "Starting Dynamic Pricing Service with Docker Compose..."
echo "Project directory: $(pwd)"
echo "Docker Compose file: $DOCKER_COMPOSE_FILE"
echo ""
echo "Running: stop ngp-me-dynamicpricing-svc -> remove image -> reset DB schema -> rebuild & start all services"
echo ""

# Stop and remove only ngp-me-dynamicpricing-svc container and image
echo "Stopping ngp-me-dynamicpricing-svc container..."
docker compose -p dynamicpricing-svc -f deployment/docker-compose/docker-compose.yml stop ngp-me-dynamicpricing-svc 2>/dev/null || echo "Container not running"

echo "Removing ngp-me-dynamicpricing-svc container..."
docker compose -p dynamicpricing-svc -f deployment/docker-compose/docker-compose.yml rm -f ngp-me-dynamicpricing-svc 2>/dev/null || echo "Container not found"

echo "Removing dynamicpricing-svc-ngp-me-dynamicpricing-svc image..."
docker rmi dynamicpricing-svc-ngp-me-dynamicpricing-svc 2>/dev/null || echo "Image dynamicpricing-svc-ngp-me-dynamicpricing-svc not found (this is normal for first run)"

echo "Resetting database schema to avoid migration conflicts..."
docker compose -p dynamicpricing-svc -f deployment/docker-compose/docker-compose.yml exec postgres psql -U postgres -d ngp -c "DROP SCHEMA IF EXISTS ngp_me_dynamic_prc CASCADE; CREATE SCHEMA ngp_me_dynamic_prc;" 2>/dev/null || echo "Database schema reset skipped (database may not be ready yet)"

echo "Starting all services (rebuilding ngp-me-dynamicpricing-svc)..."
# Execute Docker Compose command - this will rebuild only the ngp-me-dynamicpricing-svc service
docker compose -p dynamicpricing-svc -f deployment/docker-compose/docker-compose.yml --profile all up -d --build ngp-me-dynamicpricing-svc
docker compose -p dynamicpricing-svc -f deployment/docker-compose/docker-compose.yml --profile all up -d
RESULT=$?

# Check the execution result
if [ $RESULT -eq 0 ]; then
    echo ""
    echo "✅ Successfully started Dynamic Pricing Service!"
    echo ""
    echo "Services are now running:"
    echo "- PostgreSQL: http://localhost:15500"
    echo "- Redis: http://localhost:15501"
    echo "- Kafka UI: http://localhost:15502"
    echo "- Zookeeper: http://localhost:15503"
    echo "- Kafka: http://localhost:15504"
    echo "- Schema Registry: http://localhost:15507"
    echo "- Dynamic Pricing Service: http://localhost:15508"
    echo ""
    echo "To stop all services, run:"
    echo "docker compose -p dynamicpricing-svc -f deployment/docker-compose/docker-compose.yml --profile all down"
else
    echo ""
    echo "❌ Error occurred while starting Dynamic Pricing Service."
    echo "Please check the error messages above."
fi

echo ""
echo "Press any key to exit..."
read -n 1

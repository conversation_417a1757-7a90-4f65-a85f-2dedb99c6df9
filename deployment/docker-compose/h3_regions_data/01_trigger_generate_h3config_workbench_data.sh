#!/bin/bash

# Get the script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PYTHON_SCRIPT="$SCRIPT_DIR/helper_tools/generate_output_json_from_source_json.py"

# Check if the Python script exists
if [ ! -f "$PYTHON_SCRIPT" ]; then
    echo "Error: Python script not found at $PYTHON_SCRIPT"
    echo "Press any key to exit..."
    read -n 1
    exit 1
fi

# Try to run the Python script
echo "Starting to generate output JSON files..."
echo "Running: python3 $PYTHON_SCRIPT"

# Execute the Python script
if command -v python3 &> /dev/null; then
    python3 "$PYTHON_SCRIPT"
    RESULT=$?
elif command -v python &> /dev/null; then
    echo "python3 not found, trying python instead..."
    python "$PYTHON_SCRIPT"
    RESULT=$?
else
    echo "Error: Neither python3 nor python was found on your system."
    RESULT=1
fi

# Check the execution result
if [ $RESULT -eq 0 ]; then
    echo "Successfully generated output JSON files!"

    # Additional step: Copy h3_region_definitions and static_time_based_configurations files
    echo "Copying additional datasource files to outputs with workbench_upload_ prefix..."

    DATASOURCES_DIR="$SCRIPT_DIR/datasources"
    OUTPUTS_DIR="$SCRIPT_DIR/outputs"

    # Find and copy h3_region_definitions files
    h3_files=$(find "$DATASOURCES_DIR" -name "h3_region_definitions_*.json" -type f)
    for file in $h3_files; do
        filename=$(basename "$file")
        cp "$file" "$OUTPUTS_DIR/workbench_upload_$filename"
        echo "Copied: workbench_upload_$filename"
    done

    # Find and copy static_time_based_configurations files
    time_config_files=$(find "$DATASOURCES_DIR" -name "static_time_based_configurations_*.json" -type f)
    for file in $time_config_files; do
        filename=$(basename "$file")
        cp "$file" "$OUTPUTS_DIR/workbench_upload_$filename"
        echo "Copied: workbench_upload_$filename"
        cp "$file" "$OUTPUTS_DIR/postman_body_$filename"
        echo "Copied: postman_body_$filename"
    done

    echo "All files processing completed successfully!"
else
    echo "Error occurred while generating output JSON files."
fi

package com.cdg.pmg.ngp.me.dynamicpricing.utils;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Optional;
import org.junit.jupiter.api.Test;

class FileUploadUtilsTest {

  private static class SerializableObject implements java.io.Serializable {
    final String testField = "testField";
  }

  @Test
  void givenInvalidObject_whenConvertObjectToFile_thenThrowIOException() throws IOException {
    Object testObject = new Object();
    String fileName = "testFile1";
    // Assert
    assertThrows(
        IOException.class,
        () -> {
          FileUploadUtils.convertObjectToFile(testObject, fileName);
        });
    // Clean up: Delete the test file
    FileUploadUtils.cleanUp(fileName);
  }

  @Test
  void givenFileName_whenConvertObjectToFile_thenReturnFile()
      throws IOException, ClassNotFoundException {
    // Given
    Object testObject = new SerializableObject();
    String fileName = "testFile2";
    // When
    Optional<File> result = FileUploadUtils.convertObjectToFile(testObject, fileName);
    File file = result.get();

    Object deserializedObject = null;
    try (FileInputStream fis = new FileInputStream(file);
        ObjectInputStream ois = new ObjectInputStream(fis)) {
      // Deserialize the file content
      deserializedObject = ois.readObject();
    } finally {
      // Clean up: Delete the test file
      FileUploadUtils.cleanUp(fileName);
    }

    // Then
    assertTrue(result.isPresent());
    assertFalse(file.exists());
    assertNotNull(deserializedObject);
  }

  @Test
  void givenFileName_whenCleanUpFile_thenReturnSuccess() throws IOException {
    // Given
    String fileName = "testFile3";
    // When
    FileUploadUtils.cleanUp(fileName);
    // Then
    assertFalse(Files.exists(Paths.get(fileName)));
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.utils;

import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class CollectionUtilsTest {

  /** Find duplicate elements array null input empty set. */
  @Test
  void givenNullArray_whenFindDuplicateElements_thenReturnEmptySet() {
    final var expected = 0;
    final var result = CollectionUtils.findDuplicateElements((Object[]) null);
    Assertions.assertEquals(expected, result.size());
  }

  /** Find duplicate elements collection null input empty set. */
  @Test
  void givenNullCollections_whenFindDuplicateElements_thenReturnEmptySet() {
    final var expected = 0;
    final var result = CollectionUtils.findDuplicateElements((Collection<Object>) null);
    Assertions.assertEquals(expected, result.size());
  }

  /** Find duplicate string elements array duplicate input set of duplicate. */
  @Test
  void givenDuplicateInputString_whenFindDuplicateElementsArray_thenReturnSetOfDuplicate() {
    final var expectedSet =
        new HashSet<>() {
          {
            add("2");
          }
        };
    final Object[] dummy = new Object[] {null, "2", null, "2"};
    final var result = CollectionUtils.findDuplicateElements(dummy);
    Assertions.assertEquals(expectedSet.size(), result.size());
    Assertions.assertIterableEquals(expectedSet, result);
  }

  /** Find duplicate int elements array duplicate input set of duplicate. */
  @Test
  void givenDuplicateInputInt_whenFindDuplicateElementsArray_thenReturnSetOfDuplicate() {
    final var expectedSet =
        new HashSet<>() {
          {
            add(2);
          }
        };
    final Object[] dummy = new Object[] {null, 2, null, 2};
    final var result = CollectionUtils.findDuplicateElements(dummy);
    Assertions.assertEquals(expectedSet.size(), result.size());
    Assertions.assertIterableEquals(expectedSet, result);
  }

  /** Is empty array empty input true. */
  @Test
  void givenEmptyInput_whenCheckIsEmpty_thenReturnTrue() {
    final var expected = true;
    final Object[] dummy = {};
    final var result = CollectionUtils.isEmpty(dummy);
    Assertions.assertEquals(expected, result);
  }

  /** Is empty array of nulls input true. */
  @Test
  void givenArrayOfNull_whenCheckIsEmpty_thenReturnTrue() {
    final var expected = true;
    final Object[] dummy = {null, null, null};
    final var result = CollectionUtils.isEmpty(dummy);
    Assertions.assertEquals(expected, result);
  }

  /** Is empty array non null empty value array false. */
  @Test
  void givenArrayOfNonNull_whenCheckIsEmpty_thenReturnFalse() {
    final var expected = false;
    final Object[] dummy = {1, 2};
    final var result = CollectionUtils.isEmpty(dummy);
    Assertions.assertEquals(expected, result);
  }

  /** Is empty collection empty input true. */
  @Test
  void givenEmptyCollection_whenCheckIsEmpty_thenReturnTrue() {
    final var expected = true;
    final Collection<Object> dummy = Collections.emptyList();
    final var result = CollectionUtils.isEmpty(dummy);
    Assertions.assertEquals(expected, result);
  }

  /** check an item contains in an empty collection */
  @Test
  void givenEmptyCollection_whenCheckContain_thenReturnFalse() {
    final var expected = false;
    final Collection<Object> emptyList = Collections.emptyList();
    final var result = CollectionUtils.contains(emptyList, 1);
    Assertions.assertEquals(expected, result);
  }

  /** check an item contains in a collection */
  @Test
  void givenCollection_whenCheckContain_thenReturnTrue() {
    final var expected = true;
    final List<String> list = List.of("1", "2");
    final var result = CollectionUtils.contains(list, "1");
    Assertions.assertEquals(expected, result);
  }

  /** check an item doesn't contain in a collection */
  @Test
  void givenCollection_whenCheckContain_thenReturnFalse() {
    final var expected = false;
    final List<String> list = List.of("1", "2");
    final var result = CollectionUtils.contains(list, "3");
    Assertions.assertEquals(expected, result);
  }
}

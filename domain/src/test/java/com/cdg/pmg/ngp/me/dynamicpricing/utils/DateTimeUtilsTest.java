package com.cdg.pmg.ngp.me.dynamicpricing.utils;

import com.cdg.pmg.ngp.me.dynamicpricing.constants.CommonConstant;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class DateTimeUtilsTest {
  private final DateFormat df = new SimpleDateFormat(CommonConstant.YYYY_MM_DD);
  /** Get current date by format */
  @Test
  void givenFormat_whenGetCurrentDateByFormat_thenReturnStringCurrrentDate() {
    final var expected = df.format(new Date());
    final String result = DateTimeUtils.getCurrentDateByFormat(CommonConstant.YYYY_MM_DD);
    Assertions.assertEquals(expected, result);
  }

  /** Get date by format */
  @Test
  void givenFormatAndDate_whenGetDateByFormat_thenReturnStringDate() {
    final var givenDate = new Date();
    final var expected = df.format(givenDate);
    final String result = DateTimeUtils.getDateByFormat(CommonConstant.YYYY_MM_DD, givenDate);
    Assertions.assertEquals(expected, result);
  }
}

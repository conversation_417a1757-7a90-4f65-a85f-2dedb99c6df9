package com.cdg.pmg.ngp.me.dynamicpricing.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;

import org.junit.jupiter.api.Test;

class StringUtilsTest {
  @Test
  void givenMultipleStrings_whenConcat_thenConcatenatedString() {
    String result = StringUtils.concat("Hello", " ", "World", "!");
    assertEquals("Hello World!", result);
  }

  @Test
  void givenSingleString_whenConcat_thenSameString() {
    String result = StringUtils.concat("Hello");
    assertEquals("Hello", result);
  }

  @Test
  void givenNoStrings_whenConcat_thenEmptyString() {
    String result = StringUtils.concat();
    assertEquals("", result);
  }

  @Test
  void givenNullStrings_whenConcat_thenIgnoreNulls() {
    String result = StringUtils.concat("Hello", null, "World", null);
    assertEquals("HelloWorld", result);
  }

  @Test
  void givenEmptyStrings_whenConcat_thenIncludeEmptyStrings() {
    String result = StringUtils.concat("", "Hello", "", "World", "");
    assertEquals("HelloWorld", result);
  }
}

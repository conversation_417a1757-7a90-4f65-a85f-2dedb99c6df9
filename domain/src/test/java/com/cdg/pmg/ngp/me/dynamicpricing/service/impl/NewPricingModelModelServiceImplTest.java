package com.cdg.pmg.ngp.me.dynamicpricing.service.impl;

import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.NewPricingModelConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.BadRequestException;
import com.cdg.pmg.ngp.me.dynamicpricing.outbound.NewPricingModelRepositoryPort;
import com.cdg.pmg.ngp.me.dynamicpricing.outbound.ZoneInfoRepositoryOutboundPort;
import java.lang.reflect.Field;
import java.time.OffsetDateTime;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class NewPricingModelModelServiceImplTest {
  @Mock ZoneInfoRepositoryOutboundPort zoneInfoRepositoryOutboundPort;
  @Mock NewPricingModelRepositoryPort newPricingModelRepositoryPort;
  private NewPricingModelModelServiceImpl newPricingModelService;

  @BeforeEach
  public void init() {
    newPricingModelService =
        new NewPricingModelModelServiceImpl(
            zoneInfoRepositoryOutboundPort, newPricingModelRepositoryPort);
    lenient().when(zoneInfoRepositoryOutboundPort.checkZoneIdExists("01")).thenReturn(true);
    lenient().when(zoneInfoRepositoryOutboundPort.checkZoneIdExists("02")).thenReturn(true);
  }

  @Test
  void givenWhenValidateNewPricingModelConfig_whenZonePriceVersionIsInvalid_thenThrowsException() {
    var entity = new NewPricingModelConfigEntity();
    entity.setZonePriceVersion("ABC");

    String message =
        Assertions.assertThrows(
                BadRequestException.class, () -> newPricingModelService.validate(entity))
            .getMessage();
    Assertions.assertEquals(
        "zonePriceVersion field should in list of value [V1, V2, V3, V2.5]", message);
  }

  @Test
  void givenWhenValidateNewPricingModelConfig_whenZoneIdIsNull_thenThrowsException() {
    var entity = new NewPricingModelConfigEntity();
    entity.setZonePriceVersion("V1");
    entity.setId(10L);

    String message =
        Assertions.assertThrows(
                BadRequestException.class, () -> newPricingModelService.validate(entity))
            .getMessage();
    Assertions.assertEquals("Zone id 'null' is invalid", message);
  }

  @Test
  void givenWhenValidateNewPricingModelConfig_whenStartDtIsNull_thenThrowsException() {
    var entity = new NewPricingModelConfigEntity();
    entity.setZonePriceVersion("V2");
    entity.setId(10L);
    entity.setZoneId("01");

    String message =
        Assertions.assertThrows(
                BadRequestException.class, () -> newPricingModelService.validate(entity))
            .getMessage();
    Assertions.assertEquals("startDt field should not be null", message);
  }

  @Test
  void givenWhenValidateNewPricingModelConfig_whenEndDtIsNull_thenThrowsException() {
    var entity = new NewPricingModelConfigEntity();
    entity.setZonePriceVersion("V2");
    entity.setId(10L);
    entity.setZoneId("01");
    entity.setStartDt(OffsetDateTime.now());
    String message =
        Assertions.assertThrows(
                BadRequestException.class, () -> newPricingModelService.validate(entity))
            .getMessage();
    Assertions.assertEquals("endDt field should not be null", message);
  }

  @Test
  void
      givenWhenValidateNewPricingModelConfigAndZonePriceVersionIsV2_whenSurgeHighTierRateIsNull_thenThrowsException() {
    var entity = new NewPricingModelConfigEntity();
    entity.setZonePriceVersion("V2");
    entity.setId(10L);
    entity.setZoneId("01");
    entity.setStartDt(OffsetDateTime.now());
    entity.setEndDt(OffsetDateTime.now().plusMinutes(2));
    entity.setCreatedDt(OffsetDateTime.now());
    entity.setCreatedBy("SYSTEM");
    entity.setAdditionalSurgeHigh(10);

    String message =
        Assertions.assertThrows(
                BadRequestException.class, () -> newPricingModelService.validate(entity))
            .getMessage();
    Assertions.assertEquals("surgeHighTierRate field should not be null", message);
  }

  @Test
  void
      givenWhenValidateNewPricingModelConfigAndZonePriceVersionIsV2_whenUnmetRate1IsNull_thenThrowsException() {
    var entity = new NewPricingModelConfigEntity();
    entity.setZonePriceVersion("V2");
    entity.setId(10L);
    entity.setZoneId("01");
    entity.setStartDt(OffsetDateTime.now());
    entity.setEndDt(OffsetDateTime.now().plusMinutes(2));
    entity.setCreatedDt(OffsetDateTime.now());
    entity.setCreatedBy("SYSTEM");
    entity.setAdditionalSurgeHigh(10);
    entity.setSurgeHighTierRate(1.0);
    String message =
        Assertions.assertThrows(
                BadRequestException.class, () -> newPricingModelService.validate(entity))
            .getMessage();
    Assertions.assertEquals("unmetRate1 field should not be null", message);
  }

  @Test
  void
      givenWhenValidateNewPricingModelConfigAndZonePriceVersionIsV2_whenUnmetRate2IsNull_thenThrowsException() {
    var entity = new NewPricingModelConfigEntity();
    entity.setZonePriceVersion("V2");
    entity.setId(10L);
    entity.setZoneId("01");
    entity.setStartDt(OffsetDateTime.now());
    entity.setEndDt(OffsetDateTime.now().plusMinutes(2));
    entity.setCreatedDt(OffsetDateTime.now());
    entity.setCreatedBy("SYSTEM");
    entity.setAdditionalSurgeHigh(10);
    entity.setSurgeHighTierRate(1.0);
    entity.setUnmetRate1(1.0);
    String message =
        Assertions.assertThrows(
                BadRequestException.class, () -> newPricingModelService.validate(entity))
            .getMessage();
    Assertions.assertEquals("unmetRate2 field should not be null", message);
  }

  @Test
  void
      givenWhenValidateNewPricingModelConfigAndZonePriceVersionIsV2_whenNegativeDemandSupplyDownRateIsNull_thenThrowsException() {
    var entity = new NewPricingModelConfigEntity();
    entity.setZonePriceVersion("V2");
    entity.setId(10L);
    entity.setZoneId("01");
    entity.setStartDt(OffsetDateTime.now());
    entity.setEndDt(OffsetDateTime.now().plusSeconds(2));
    entity.setCreatedDt(OffsetDateTime.now());
    entity.setCreatedBy("SYSTEM");
    entity.setAdditionalSurgeHigh(10);
    entity.setSurgeHighTierRate(1.0);
    entity.setUnmetRate1(1.0);
    entity.setUnmetRate2(1.0);
    String message =
        Assertions.assertThrows(
                BadRequestException.class, () -> newPricingModelService.validate(entity))
            .getMessage();
    Assertions.assertEquals("negativeDemandSupplyDownRate field should not be null", message);
  }

  @Test
  void
      givenWhenValidateNewPricingModelConfigAndZonePriceVersionIsV2IsValid_thenDoNotThrowsException() {
    var entity = new NewPricingModelConfigEntity();
    entity.setZonePriceVersion("V2");
    entity.setId(10L);
    entity.setZoneId("01");
    entity.setStartDt(OffsetDateTime.now());
    entity.setEndDt(OffsetDateTime.now().plusMinutes(2));
    entity.setCreatedDt(OffsetDateTime.now());
    entity.setCreatedBy("SYSTEM");
    entity.setAdditionalSurgeHigh(10);
    entity.setSurgeHighTierRate(1.0);
    entity.setUnmetRate1(1.0);
    entity.setUnmetRate2(1.0);
    entity.setNegativeDemandSupplyDownRate(1.0);
    entity.setStartDt(OffsetDateTime.now());
    entity.setEndDt(OffsetDateTime.now().plusMinutes(2));
    Assertions.assertDoesNotThrow(() -> newPricingModelService.validate(entity));
  }

  // V3

  @Test
  void
      givenWhenValidateNewPricingModelConfigAndZonePrice_whenAdditionalSurgeHighIsNull_thenThrowsException() {
    var entity = new NewPricingModelConfigEntity();
    entity.setZonePriceVersion("V2");
    entity.setId(10L);
    entity.setZoneId("01");
    entity.setStartDt(OffsetDateTime.now());
    entity.setEndDt(OffsetDateTime.now().plusMinutes(2));
    entity.setCreatedDt(OffsetDateTime.now());
    entity.setCreatedBy("SYSTEM");
    String message =
        Assertions.assertThrows(
                BadRequestException.class, () -> newPricingModelService.validate(entity))
            .getMessage();
    Assertions.assertEquals("additionalSurgeHigh field should not be null", message);
  }

  @Test
  void
      givenWhenValidateNewPricingModelConfigAndZonePriceVersionIsV3_whenK1IsNull_thenThrowsException() {
    var entity = new NewPricingModelConfigEntity();
    entity.setZonePriceVersion("V3");
    entity.setId(10L);
    entity.setZoneId("01");
    entity.setStartDt(OffsetDateTime.now());
    entity.setEndDt(OffsetDateTime.now().plusMinutes(2));
    entity.setCreatedDt(OffsetDateTime.now());
    entity.setCreatedBy("SYSTEM");
    entity.setAdditionalSurgeHigh(10);

    String message =
        Assertions.assertThrows(
                BadRequestException.class, () -> newPricingModelService.validate(entity))
            .getMessage();
    Assertions.assertEquals("k1 field should not be null", message);
  }

  @Test
  void
      givenWhenValidateNewPricingModelConfigAndZonePriceVersionIsV3_whenK2IsNull_thenThrowsException() {
    var entity = new NewPricingModelConfigEntity();
    entity.setZonePriceVersion("V3");
    entity.setId(10L);
    entity.setZoneId("01");
    entity.setStartDt(OffsetDateTime.now());
    entity.setEndDt(OffsetDateTime.now().plusMinutes(2));
    entity.setCreatedDt(OffsetDateTime.now());
    entity.setCreatedBy("SYSTEM");
    entity.setAdditionalSurgeHigh(10);
    entity.setK1(1.0);
    String message =
        Assertions.assertThrows(
                BadRequestException.class, () -> newPricingModelService.validate(entity))
            .getMessage();
    Assertions.assertEquals("k2 field should not be null", message);
  }

  @Test
  void
      givenWhenValidateNewPricingModelConfigAndZonePriceVersionIsV3_whenK3IsNull_thenThrowsException() {
    var entity = new NewPricingModelConfigEntity();
    entity.setZonePriceVersion("V3");
    entity.setId(10L);
    entity.setZoneId("01");
    entity.setStartDt(OffsetDateTime.now());
    entity.setEndDt(OffsetDateTime.now().plusMinutes(2));
    entity.setCreatedDt(OffsetDateTime.now());
    entity.setCreatedBy("SYSTEM");
    entity.setAdditionalSurgeHigh(10);
    entity.setK1(1.0);
    entity.setK2(1.0);
    String message =
        Assertions.assertThrows(
                BadRequestException.class, () -> newPricingModelService.validate(entity))
            .getMessage();
    Assertions.assertEquals("k3 field should not be null", message);
  }

  @Test
  void
      givenWhenValidateNewPricingModelConfigAndZonePriceVersionIsV3_whenK4IsNull_thenThrowsException() {
    var entity = new NewPricingModelConfigEntity();
    entity.setZonePriceVersion("V3");
    entity.setId(10L);
    entity.setZoneId("01");
    entity.setStartDt(OffsetDateTime.now());
    entity.setEndDt(OffsetDateTime.now().plusMinutes(2));
    entity.setCreatedDt(OffsetDateTime.now());
    entity.setCreatedBy("SYSTEM");
    entity.setAdditionalSurgeHigh(10);
    entity.setK1(1.0);
    entity.setK2(1.0);
    entity.setK3(1.0);
    String message =
        Assertions.assertThrows(
                BadRequestException.class, () -> newPricingModelService.validate(entity))
            .getMessage();
    Assertions.assertEquals("k4 field should not be null", message);
  }

  @Test
  void
      givenWhenValidateNewPricingModelConfigAndZonePriceVersionIsV3_whenK5IsNull_thenThrowsException() {
    var entity = new NewPricingModelConfigEntity();
    entity.setZonePriceVersion("V3");
    entity.setId(10L);
    entity.setZoneId("01");
    entity.setStartDt(OffsetDateTime.now());
    entity.setEndDt(OffsetDateTime.now().plusMinutes(2));
    entity.setCreatedDt(OffsetDateTime.now());
    entity.setCreatedBy("SYSTEM");
    entity.setAdditionalSurgeHigh(10);
    entity.setK1(1.0);
    entity.setK2(1.0);
    entity.setK3(1.0);
    entity.setK4(1.0);
    String message =
        Assertions.assertThrows(
                BadRequestException.class, () -> newPricingModelService.validate(entity))
            .getMessage();
    Assertions.assertEquals("k5 field should not be null", message);
  }

  @Test
  void
      givenWhenValidateNewPricingModelConfigAndZonePriceVersionIsV3_whenK6IsNull_thenThrowsException() {
    var entity = new NewPricingModelConfigEntity();
    entity.setZonePriceVersion("V3");
    entity.setId(10L);
    entity.setZoneId("01");
    entity.setStartDt(OffsetDateTime.now());
    entity.setEndDt(OffsetDateTime.now().plusMinutes(2));
    entity.setCreatedDt(OffsetDateTime.now());
    entity.setCreatedBy("SYSTEM");
    entity.setAdditionalSurgeHigh(10);
    entity.setK1(1.0);
    entity.setK2(1.0);
    entity.setK3(1.0);
    entity.setK4(1.0);
    entity.setK5(1.0);
    String message =
        Assertions.assertThrows(
                BadRequestException.class, () -> newPricingModelService.validate(entity))
            .getMessage();
    Assertions.assertEquals("k6 field should not be null", message);
  }

  @Test
  void
      givenWhenValidateNewPricingModelConfigAndZonePriceVersionIsV3_whenK7IsNull_thenThrowsException() {
    var entity = new NewPricingModelConfigEntity();
    entity.setZonePriceVersion("V3");
    entity.setId(10L);
    entity.setZoneId("01");
    entity.setStartDt(OffsetDateTime.now());
    entity.setEndDt(OffsetDateTime.now().plusMinutes(2));
    entity.setCreatedDt(OffsetDateTime.now());
    entity.setCreatedBy("SYSTEM");
    entity.setAdditionalSurgeHigh(10);
    entity.setK1(1.0);
    entity.setK2(1.0);
    entity.setK3(1.0);
    entity.setK4(1.0);
    entity.setK5(1.0);
    entity.setK6(1.0);
    String message =
        Assertions.assertThrows(
                BadRequestException.class, () -> newPricingModelService.validate(entity))
            .getMessage();
    Assertions.assertEquals("k7 field should not be null", message);
  }

  @Test
  void
      givenWhenValidateNewPricingModelConfigAndZonePriceVersionIsV3_whenK8IsNull_thenThrowsException() {
    var entity = new NewPricingModelConfigEntity();
    entity.setZonePriceVersion("V3");
    entity.setId(10L);
    entity.setZoneId("01");
    entity.setStartDt(OffsetDateTime.now());
    entity.setEndDt(OffsetDateTime.now().plusMinutes(2));
    entity.setCreatedDt(OffsetDateTime.now());
    entity.setCreatedBy("SYSTEM");
    entity.setAdditionalSurgeHigh(10);
    entity.setK1(1.0);
    entity.setK2(1.0);
    entity.setK3(1.0);
    entity.setK4(1.0);
    entity.setK5(1.0);
    entity.setK6(1.0);
    entity.setK7(1.0);
    String message =
        Assertions.assertThrows(
                BadRequestException.class, () -> newPricingModelService.validate(entity))
            .getMessage();
    Assertions.assertEquals("k8 field should not be null", message);
  }

  @Test
  void
      givenWhenValidateNewPricingModelConfigAndZonePriceVersionIsV3IsValid_thenDoNotThrowsException() {
    var entity = new NewPricingModelConfigEntity();
    entity.setZonePriceVersion("V3");
    entity.setId(10L);
    entity.setZoneId("01");
    entity.setStartDt(OffsetDateTime.now());
    entity.setEndDt(OffsetDateTime.now().plusMinutes(2L));
    entity.setCreatedDt(OffsetDateTime.now());
    entity.setCreatedBy("SYSTEM");
    entity.setAdditionalSurgeHigh(10);
    entity.setK1(1.0);
    entity.setK2(1.0);
    entity.setK3(1.0);
    entity.setK4(1.0);
    entity.setK5(1.0);
    entity.setK6(1.0);
    entity.setK7(1.0);
    entity.setK8(1.0);
    Assertions.assertDoesNotThrow(() -> newPricingModelService.validate(entity));
  }

  @Test
  void whenGetListNewPricingModelConfigEntityInCms() {
    List<NewPricingModelConfigEntity> expectedList = List.of();
    when(newPricingModelRepositoryPort.getListNewPricingModelConfigEntityInCms())
        .thenReturn(expectedList);
    List<NewPricingModelConfigEntity> result =
        newPricingModelService.getListNewPricingModelConfigEntityInCms();
    Assertions.assertEquals(expectedList, result);
    verify(newPricingModelRepositoryPort).getListNewPricingModelConfigEntityInCms();
  }

  @ParameterizedTest
  @CsvSource(
      value = {
        "V2,surgeHighTierRate,-1",
        "V2,unmetRate1,-1",
        "V2,unmetRate2,-1",
        "V2,negativeDemandSupplyDownRate,-1",
        "V3,k1,-1",
        "V3,k2,-1",
        "V3,k3,-1",
        "V3,k4,-1",
        "V3,k5,-1",
        "V3,k6,-1",
        "V3,k7,-1",
        "V3,k8,-1",
      })
  void givenNewPricingModelConfigEntityV2_whenValidate_thenThrowsBadRequestException(
      String version, String fieldName, Double value)
      throws NoSuchFieldException, IllegalAccessException {

    var entity = new NewPricingModelConfigEntity();
    entity.setZonePriceVersion(version);
    entity.setId(10L);
    entity.setZoneId("01");
    entity.setStartDt(OffsetDateTime.now());
    entity.setEndDt(OffsetDateTime.now().plusMinutes(2));
    entity.setCreatedDt(OffsetDateTime.now());
    entity.setCreatedBy("SYSTEM");
    entity.setAdditionalSurgeHigh(10);
    entity.setSurgeHighTierRate(1.0);
    entity.setUnmetRate1(1.0);
    entity.setUnmetRate2(1.0);
    entity.setNegativeDemandSupplyDownRate(1.0);
    entity.setK1(1.0);
    entity.setK2(1.0);
    entity.setK3(1.0);
    entity.setK4(1.0);
    entity.setK5(1.0);
    entity.setK6(1.0);
    entity.setK7(1.0);
    entity.setK8(1.0);

    Field field = NewPricingModelConfigEntity.class.getDeclaredField(fieldName);
    field.setAccessible(true);
    field.set(entity, value);

    Assertions.assertThrows(
        BadRequestException.class, () -> newPricingModelService.validate(entity));
  }

  @Test
  void
      givenNewPricingModelConfigEntityV3_whenValidateAdditionalSurgeHigh_thenThrowsBadRequestException() {

    var entity = new NewPricingModelConfigEntity();
    entity.setZonePriceVersion("V3");
    entity.setId(10L);
    entity.setZoneId("01");
    entity.setStartDt(OffsetDateTime.now());
    entity.setEndDt(OffsetDateTime.now().plusMinutes(2));
    entity.setCreatedDt(OffsetDateTime.now());
    entity.setCreatedBy("SYSTEM");
    entity.setSurgeHighTierRate(1.0);
    entity.setUnmetRate1(1.0);
    entity.setUnmetRate2(1.0);
    entity.setNegativeDemandSupplyDownRate(1.0);
    entity.setK1(1.0);
    entity.setK2(1.0);
    entity.setK3(1.0);
    entity.setK4(1.0);
    entity.setK5(1.0);
    entity.setK6(1.0);
    entity.setK7(1.0);
    entity.setK8(1.0);
    entity.setAdditionalSurgeHigh(-1);
    Assertions.assertThrows(
        BadRequestException.class, () -> newPricingModelService.validate(entity));
  }

  @Test
  void
      givenInvalidStartDtInNewPricingModelConfigEntityV3_whenValidate_thenThrowsBadRequestException() {

    var entity = new NewPricingModelConfigEntity();
    entity.setZonePriceVersion("V3");
    entity.setId(10L);
    entity.setZoneId("01");
    entity.setStartDt(OffsetDateTime.now().plusMinutes(2));
    entity.setEndDt(OffsetDateTime.now().plusMinutes(2));
    entity.setCreatedDt(OffsetDateTime.now());
    entity.setCreatedBy("SYSTEM");
    entity.setAdditionalSurgeHigh(10);
    entity.setSurgeHighTierRate(1.0);
    entity.setUnmetRate1(1.0);
    entity.setUnmetRate2(1.0);
    entity.setNegativeDemandSupplyDownRate(1.0);
    entity.setK1(1.0);
    entity.setK2(1.0);
    entity.setK3(1.0);
    entity.setK4(1.0);
    entity.setK5(1.0);
    entity.setK6(1.0);
    entity.setK7(1.0);
    entity.setK8(1.0);
    Assertions.assertThrows(
        BadRequestException.class, () -> newPricingModelService.validate(entity));
  }

  @Test
  void
      givenInvalidEndDtInNewPricingModelConfigEntityV3_whenValidate_thenThrowsBadRequestException() {

    var entity = new NewPricingModelConfigEntity();
    entity.setZonePriceVersion("V3");
    entity.setId(10L);
    entity.setZoneId("01");
    entity.setStartDt(OffsetDateTime.now());
    entity.setEndDt(OffsetDateTime.now().plusMinutes(-1));
    entity.setCreatedDt(OffsetDateTime.now());
    entity.setCreatedBy("SYSTEM");
    entity.setAdditionalSurgeHigh(10);
    entity.setSurgeHighTierRate(1.0);
    entity.setUnmetRate1(1.0);
    entity.setUnmetRate2(1.0);
    entity.setNegativeDemandSupplyDownRate(1.0);
    entity.setK1(1.0);
    entity.setK2(1.0);
    entity.setK3(1.0);
    entity.setK4(1.0);
    entity.setK5(1.0);
    entity.setK6(1.0);
    entity.setK7(1.0);
    entity.setK8(1.0);
    Assertions.assertThrows(
        BadRequestException.class, () -> newPricingModelService.validate(entity));
  }

  @Test
  void givenNewPricingModelEntity_whenUpdate_thenSuccessfulUpdate() {
    var entity = new NewPricingModelConfigEntity();
    newPricingModelService.update(entity);
    verify(newPricingModelRepositoryPort).update(entity);
  }

  @Test
  void givenNewPricingModelEntity_whenCreate_thenReturnSuccessful() {
    var entity = new NewPricingModelConfigEntity();
    int expectedIndex = 10;
    newPricingModelService.create(entity, expectedIndex);
    verify(newPricingModelRepositoryPort).create(entity, expectedIndex);
  }
}

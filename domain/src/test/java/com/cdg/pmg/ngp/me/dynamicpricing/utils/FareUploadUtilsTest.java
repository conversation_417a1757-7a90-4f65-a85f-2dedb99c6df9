package com.cdg.pmg.ngp.me.dynamicpricing.utils;

import static com.cdg.pmg.ngp.me.dynamicpricing.constants.CommonConstant.UNDERSCORE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.cdg.pmg.ngp.me.dynamicpricing.constants.BookARideConfigsConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FareUploadConfiguration;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.HourRateConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.S2CellEntity;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class FareUploadUtilsTest {
  @Test
  void givenData_whenComputePeakHourOrMidnightRatesWithParams_thenReturnMidnightData() {
    Map<String, String> flatFareConfg = new HashMap<>();
    flatFareConfg.put("PEAK_HOUR_RATE_0", "0.25");
    flatFareConfg.put("PEAK_HOUR_DAYS_0", "MON,TUE,WED,THU,FRI,SAT,SUN,HOL");
    flatFareConfg.put("PEAK_HOUR_START_TIME_0", "00:00:00");
    flatFareConfg.put("PEAK_HOUR_END_TIME_0", "00:59:59");
    Map<String, HourRateConfig> hourRateConfigMap;
    hourRateConfigMap =
        FareUploadUtils.getPeakHourOrMidnightRates(
            flatFareConfg, BookARideConfigsConstant.PEAK_HOUR_PREFIX);
    String key = "PEAK_HOUR_RATE_0".substring("PEAK_HOUR_RATE_0".lastIndexOf(UNDERSCORE));
    Assertions.assertEquals(0.25, hourRateConfigMap.get(key).getRate());
    Assertions.assertEquals(
        "MON,TUE,WED,THU,FRI,SAT,SUN,HOL", hourRateConfigMap.get(key).getDaysOfWeekIncluded());
    Assertions.assertEquals(LocalTime.parse("00:00:00"), hourRateConfigMap.get(key).getStartTime());
    Assertions.assertEquals(LocalTime.parse("00:59:59"), hourRateConfigMap.get(key).getEndTime());
  }

  @Test
  void givenNullPrefix_whenComputePeakHourOrMidnightRatesWithParams_thenThrowException() {
    Map<String, String> flatFareConfg = new HashMap<>();
    flatFareConfg.put("PEAK_HOUR_RATE_0", "0.25");
    assertThrows(
        IllegalArgumentException.class,
        () -> {
          FareUploadUtils.getPeakHourOrMidnightRates(flatFareConfg, null);
        });
  }

  @Test
  void
      givenDynamicPricingTimeConfigWithParams_whenComputeDynamicPricingTimeConfig_thenReturnExpectedData() {
    Map<String, String> flatFareConfg = new HashMap<>();
    flatFareConfg.put(
        BookARideConfigsConstant.LIVE_TRAFFIC_PREFIX
            + BookARideConfigsConstant.DYNAMIC_PRICING_END_TIME,
        "20000");
    flatFareConfg.put(
        BookARideConfigsConstant.WAIT_TIME_PREFIX
            + BookARideConfigsConstant.DYNAMIC_PRICING_START_TIME,
        "10000");
    FareUploadConfiguration fareUploadConfiguration =
        FareUploadConfiguration.builder()
            .dynamicPricingTimeConfig(FareUploadUtils.getDynamicPricingTimeConfig(flatFareConfg))
            .build();
    ;
    Assertions.assertEquals(
        FareUploadUtils.getDynamicPricingTimeConfig(flatFareConfg)
            .get(BookARideConfigsConstant.LIVE_TRAFFIC_PREFIX)
            .getEndTime(),
        fareUploadConfiguration
            .getDynamicPricingTimeConfig()
            .get(BookARideConfigsConstant.LIVE_TRAFFIC_PREFIX)
            .getEndTime());
    Assertions.assertEquals(
        FareUploadUtils.getDynamicPricingTimeConfig(flatFareConfg)
            .get(BookARideConfigsConstant.WAIT_TIME_PREFIX)
            .getStartTime(),
        fareUploadConfiguration
            .getDynamicPricingTimeConfig()
            .get(BookARideConfigsConstant.WAIT_TIME_PREFIX)
            .getStartTime());
  }

  @Test
  void givenData_whenCalculateEndPointSurchargeAreas_thenReturnRightData() {
    // Arrange
    S2CellEntity cell1 = Mockito.mock(S2CellEntity.class);
    S2CellEntity cell2 = Mockito.mock(S2CellEntity.class);
    Mockito.when(cell1.getS2CellLocationId()).thenReturn("1");
    Mockito.when(cell2.getS2CellLocationId()).thenReturn("2");

    List<S2CellEntity> s2CellList = Arrays.asList(cell1, cell2);
    Map<Integer, Integer> endPointSurchargeMap = FareUploadUtils.mapAreaIdsToIndex(s2CellList);

    // Assert
    assertEquals(3, endPointSurchargeMap.size());
    assertEquals(Integer.valueOf(0), endPointSurchargeMap.get(1));
    assertEquals(Integer.valueOf(1), endPointSurchargeMap.get(2));
    assertEquals(Integer.valueOf(2), endPointSurchargeMap.get(0));
  }
}

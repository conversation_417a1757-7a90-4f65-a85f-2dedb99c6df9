package com.cdg.pmg.ngp.me.dynamicpricing.entities;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DynamicSurgesEntity implements Serializable {
  private String zoneId;
  private int surge;
  private int surgeLow;
  private int surgeHigh;
  private int demandRecent;
  private int demandPrevious;
  private int demandPredicted;
  private int supply;
  private int excessDemand;
  private Timestamp lastUpdDt;
  private int prevSurge;
  private int batchKey;
  private String zonePriceModel;
  private int predictedDemand15;
  private int demand15;
  private double previousUnmet15;
  private double unmet15;
  private int excessDemand15;

  @Override
  public String toString() {
    return "{" + "zoneId=" + zoneId + ", surge=" + surge + '}';
  }
}

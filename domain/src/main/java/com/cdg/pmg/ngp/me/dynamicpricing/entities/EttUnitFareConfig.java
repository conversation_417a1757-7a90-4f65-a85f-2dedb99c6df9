package com.cdg.pmg.ngp.me.dynamicpricing.entities;

import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EttUnitFareConfig implements Serializable {
  @Serial private static final long serialVersionUID = -1817016189253350832L;

  private String zoneIds;
  private String dayOfWeek;
  private Double ekm;
  private Double hr;
  private String chargeBy;
  private String taxiType;
  private Double rate;
}

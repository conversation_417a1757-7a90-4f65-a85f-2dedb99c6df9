package com.cdg.pmg.ngp.me.dynamicpricing.queries;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.LocationSurchargeConfigEntity;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LocationSurchargeConfigQueryResponse implements Serializable {
  @Serial private static final long serialVersionUID = 3401120614751040626L;
  private List<LocationSurchargeConfigEntity> configs;
  private boolean hasMoreRecords;
}

package com.cdg.pmg.ngp.me.dynamicpricing.entities;

import java.io.Serializable;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class FlatFareConfigCacheEntity implements Serializable {
  private Map<String, Map<String, String>> eventSurgeAddresses;
  private Map<String, Map<String, String>> eventSurgeZones;
  private Map<String, Map<String, String>> additionalCharges;
  private Map<String, Map<String, String>> dynamicPriceSchedulers;
  private Map<String, Map<String, String>> driverSurgeLevelIndications;
  private Map<String, String> otherConfigs;
  private Map<String, String> singleConfigs;
}

package com.cdg.pmg.ngp.me.dynamicpricing.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum JobTypeEnum {
  IMMEDIATE("IMMEDIATE"),
  ADVANCE("ADVANCE"),
  STREET("STREET");

  private String value;

  JobTypeEnum(String value) {
    this.value = value;
  }

  public static boolean isAdvance(String jobType) {
    return ADVANCE.name().equalsIgnoreCase(jobType);
  }

  public static boolean isStreet(String jobType) {
    return STREET.name().equalsIgnoreCase(jobType);
  }
}

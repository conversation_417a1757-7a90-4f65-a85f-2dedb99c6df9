package com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation;

import java.time.Instant;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Data class for surge computation model. This class represents the domain model for surge
 * computation models.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModelEntity {

  private Long id;
  private String modelName;
  private String description;
  private String endpointUrl;
  private List<RequestFieldMapping> requestFieldsMappings;
  private String createdBy;
  private Instant createdDate;
  private String updatedBy;
  private Instant updatedDate;

  public List<RequestFieldMapping> getRequestFieldsMappings() {
    return requestFieldsMappings == null ? List.of() : requestFieldsMappings;
  }

  public List<String> getRegionBasedConfigNames() {
    return getRequestFieldsMappings().stream()
        .filter(RequestFieldMapping::isStaticRegionBasedConfiguration)
        .map(RequestFieldMapping::getMappingConfigurationName)
        .filter(Objects::nonNull)
        .toList();
  }
}

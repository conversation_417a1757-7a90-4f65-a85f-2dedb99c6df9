package com.cdg.pmg.ngp.me.dynamicpricing.enums;

import com.cdg.pmg.ngp.me.dynamicpricing.constants.ErrorMessageConstant;

public enum ErrorEnum {
  INVALID_START_DATE(3, ErrorMessageConstant.INVALID_START_DATE),
  INVALID_END_DATE(4, ErrorMessageConstant.INVALID_END_DATE),
  NOT_FOUND_PRICING_RANGE_CONFIG(5, ErrorMessageConstant.NOT_FOUND_PRICING_RANGE_CONFIG),
  SETTING_MINIMUM_CAP_ERROR(9, ErrorMessageConstant.ERROR_WHILE_SETTING_MINIMUM_CAP),

  SETTING_MAXIMUM_CAP_ERROR(9, ErrorMessageConstant.ERROR_WHILE_SETTING_MAXIMUM_CAP),

  PARSING_HOLIDAY_ERROR(9, ErrorMessageConstant.ERROR_WHILE_PARSING_HOLIDAY),

  GET_LIST_DOUBLE_CONFIGS_ERROR(9, ErrorMessageConstant.GET_LIST_DOUBLE_CONFIGS_ERROR),

  GET_DRIVER_SURGE_CONFIGS_ERROR(9, ErrorMessageConstant.GET_DRIVER_SURGE_CONFIGS_ERROR),

  GET_LIST_EVENT_SURGE_ADDRESS_CONFIG_ERROR(
      9, ErrorMessageConstant.GET_LIST_EVENT_SURGE_ADDRESS_CONFIG_ERROR),

  COMPUTE_SURGE_INDICATOR_ERROR(9, ErrorMessageConstant.COMPUTE_SURGE_INDICATOR_ERROR),

  FLAT_FARE_COMPUTED_NOT_VALID(9, ErrorMessageConstant.FLAT_FARE_COMPUTED_NOT_VALID),

  CALCULATE_BREAKDOWN_FARE_ERROR(9, ErrorMessageConstant.CALCULATE_BREAKDOWN_FARE_ERROR),

  UPDATE_FARE_TYPE_CONFIG_PARSE_ERROR(10, ErrorMessageConstant.UPDATE_FARE_TYPE_CONFIG_PARSE_ERROR),

  GET_PLAT_FORM_FEEED(11, ErrorMessageConstant.GET_PLATFORM_FEE_FAILED),

  GET_BOOKING_FEE_FAILED(11, ErrorMessageConstant.GET_BOOKING_FEE_FAILED),
  GET_BOOKING_FEE_LIST_FAILED(11, ErrorMessageConstant.GET_BOOKING_FEE_LIST_FAILED),

  SETTING_MIN_MAX_CAP_FOR_TOTAL_FARE_ERROR(
      12, ErrorMessageConstant.SETTING_MIN_MAX_CAP_FOR_TOTAL_FARE_ERROR),

  GET_FARE_BREAK_DOWN_ERROR(13, ErrorMessageConstant.GET_FARE_BREAK_DOWN_ERROR),

  FAILED_TO_CREATE_FARE_BREAKDOWN(14, ErrorMessageConstant.FAILED_TO_CREATE_FARE_BREAKDOWN),

  INVALID_TRIP_ID(15, ErrorMessageConstant.TRIP_ID_IS_NULL),

  TRIP_ID_BLANK(16, ErrorMessageConstant.TRIP_ID_BLANK),

  NOT_FOUND_ROUTE_INFO(16, ErrorMessageConstant.NOT_FOUND_ROUTE_INFO),

  ERROR_MAP_TO_ROUTE_INFO(17, ErrorMessageConstant.ERROR_MAP_TO_ROUTE_INFO),

  FARE_BREAKDOWN_EXISTED(19, ErrorMessageConstant.FARE_BREAKDOWN_NOT_FOUND),

  SEARCH_FARE_BREAKDOWN_REQUEST_INVALID(
      20, ErrorMessageConstant.SEARCH_FARE_BREAKDOWN_REQUEST_INVALID),

  NOT_FOUND_FARE_BREAKDOWN(21, ErrorMessageConstant.NOT_FOUND_FARE_BREAKDOWN),
  MULTI_FARE_NOT_FOUND(13, ErrorMessageConstant.MULTI_FARE_NOT_FOUND),
  GET_ROUTE_FAIL(22, ErrorMessageConstant.GET_ROUTE_FAILED),
  INVALID_ROUTE(23, ErrorMessageConstant.INVALID_ROUTE),
  NOT_FOUND_ROUTE(24, ErrorMessageConstant.NOT_FOUND_ROUTE_MSG),
  INVALID_BOOKING_CHANNEL(25, ErrorMessageConstant.INVALID_BOOKING_CHANNEL),
  INVALID_JOB_TYPE(26, ErrorMessageConstant.INVALID_JOB_TYPE),
  NO_VEH_TYPE_ID_AVAILABLE(27, ErrorMessageConstant.NO_VEH_TYPE_ID_AVAILABLE),
  NULL_VALUE(28, ErrorMessageConstant.FIELD_SHOULD_NOT_BE_NULL),
  ZONE_ID_INVALID(29, ErrorMessageConstant.ZONE_ID_INVALID),
  LIST_OF_VALUES(30, ErrorMessageConstant.LIST_OF_VALUES),
  START_DT_AND_END_DT_INVALID(31, ErrorMessageConstant.START_DT_AND_END_DT_INVALID),
  NOT_FOUND_NEW_PRICING_MODEL_INDEX(32, ErrorMessageConstant.NOT_FOUND_NEW_PRICING_MODEL_ID),
  NOT_FOUND_PROPERTY_ID(33, ErrorMessageConstant.NOT_FOUND_PROPERTY_ID),
  DUPLICATE_VALUE(34, ErrorMessageConstant.DUPLICATE_VALUE),
  NOT_FOUND_VALUE(35, ErrorMessageConstant.NOT_FOUND_VALUE),
  JSON_PARSE_ERROR(36, ErrorMessageConstant.JSON_PARSE_ERROR),
  NEGATIVE_ERROR(37, ErrorMessageConstant.NEGATIVE_ERROR),
  INVALID_CONFIG_TYPE(38, ErrorMessageConstant.INVALID_CONFIG_TYPE),
  CALC_EVENT_SURCHARGE_DYNP_ERROR(39, ErrorMessageConstant.CALC_EVENT_SURCHARGE_DYNP_ERROR),
  CALC_LOC_SURCHARGE_DYNP_ERROR(40, ErrorMessageConstant.CALC_LOC_SURCHARGE_DYNP_ERROR),
  INVALID_VEH_TYPE_ID(41, ErrorMessageConstant.INVALID_VEH_TYPE_ID),
  NOT_FOUND_LOCATION_SURCHARGE(42, ErrorMessageConstant.NOT_FOUND_LOCATION_SURCHARGE),
  SEARCH_FARE_BREAKDOWN_CONVERT_ERROR(43, ErrorMessageConstant.SEARCH_FARE_BREAKDOWN_CONVERT_ERROR),
  ERROR_TO_PARSING_JSON(44, ErrorMessageConstant.ERROR_TO_PARSING_JSON),
  ERROR_TO_CONVERTING_TO_JSON(45, ErrorMessageConstant.ERROR_TO_CONVERTING_TO_JSON),
  ERROR_TO_UPLOAD_FILE(46, ErrorMessageConstant.ERROR_UPLOAD_FILE),
  ERROR_BUILDING_TAXI_AREA(47, ErrorMessageConstant.ERROR_BUILDING_TAXI_AREA),
  GET_ADDITIONAL_CHARGE_FEE_CONFIG_FAILED(
      48, ErrorMessageConstant.GET_ADDITIONAL_CHARGE_FEE_CONFIG_FAILED),
  GET_ADDITIONAL_CHARGE_FEE_CONFIG_BY_ID_FAILED(
      49, ErrorMessageConstant.GET_ADDITIONAL_CHARGE_FEE_CONFIG_BY_ID_FAILED),
  RELOAD_ALL_CONFIG_FAIL(30, ErrorMessageConstant.RELOAD_ALL_CONFIG_FAIL),
  UPDATE_CBD_ADDRESS_BAD_REQUEST(31, ErrorMessageConstant.UPDATE_CBD_ADDRESS_BAD_REQUEST),
  RELOAD_CBD_CONFIG_CACHE_FAIL(32, ErrorMessageConstant.RELOAD_CBD_CONFIG_CACHE_FAIL),
  NOT_FOUND_REGION_MODEL_DISTRIBUTION(50, ErrorMessageConstant.NOT_FOUND_REGION_MODEL_DISTRIBUTION),
  REGION_MODEL_DISTRIBUTION_CREATE_ERROR(
      51, ErrorMessageConstant.REGION_MODEL_DISTRIBUTION_CREATE_ERROR),
  REGION_MODEL_DISTRIBUTION_INVALID_REGION_ID(
      52, ErrorMessageConstant.REGION_MODEL_DISTRIBUTION_INVALID_REGION_ID),
  NOT_FOUND_SURGE_COMPUTATION_MODEL(54, ErrorMessageConstant.NOT_FOUND_SURGE_COMPUTATION_MODEL),
  SURGE_COMPUTATION_MODEL_PERCENTAGE_ERROR(
      55, ErrorMessageConstant.SURGE_COMPUTATION_MODEL_PERCENTAGE_ERROR),
  SURGE_COMPUTATION_MODEL_OVERLAPPING_DATES_ERROR(
      56, ErrorMessageConstant.SURGE_COMPUTATION_MODEL_OVERLAPPING_DATES_ERROR),
  GET_EFFECTIVE_H3_REGIONS_FAILED(57, ErrorMessageConstant.GET_EFFECTIVE_H3_REGIONS_FAILED),
  REGION_DEMAND_SUPPLY_STATISTICS_ERROR(
      58, ErrorMessageConstant.REGION_DEMAND_SUPPLY_STATISTICS_ERROR),
  NOT_FOUND_SURGE_COMPUTATION_MAPPING_TYPE(
      59, ErrorMessageConstant.NOT_FOUND_SURGE_COMPUTATION_MAPPING_TYPE),
  SURGE_COMPUTATION_MODEL_DUPLICATE_NAME_ERROR(
      60, ErrorMessageConstant.SURGE_COMPUTATION_MODEL_DUPLICATE_NAME_ERROR),
  SURGE_COMPUTATION_MODEL_CREATE_ERROR(
      61, ErrorMessageConstant.SURGE_COMPUTATION_MODEL_CREATE_ERROR),
  SURGE_COMPUTATION_MODEL_UPDATE_ERROR(
      62, ErrorMessageConstant.SURGE_COMPUTATION_MODEL_UPDATE_ERROR),
  SURGE_COMPUTATION_MODEL_DELETE_ERROR(
      63, ErrorMessageConstant.SURGE_COMPUTATION_MODEL_DELETE_ERROR),
  SURGE_COMPUTATION_MODEL_TIME_BASED_CONFIGURATION_CREATE_ERROR(
      64, ErrorMessageConstant.SURGE_COMPUTATION_MODEL_TIME_BASED_CONFIGURATION_CREATE_ERROR),
  SURGE_COMPUTATION_MODEL_TIME_BASED_CONFIGURATION_OVERLAPPING_DATES_ERROR(
      65,
      ErrorMessageConstant
          .SURGE_COMPUTATION_MODEL_TIME_BASED_CONFIGURATION_OVERLAPPING_DATES_ERROR),
  SURGE_COMPUTATION_MODEL_TIME_BASED_CONFIGURATION_UPDATE_ERROR(
      66, ErrorMessageConstant.SURGE_COMPUTATION_MODEL_TIME_BASED_CONFIGURATION_UPDATE_ERROR),
  SURGE_COMPUTATION_MODEL_REGION_BASED_CONFIGURATION_OVERLAPPING_DATES_ERROR(
      67,
      ErrorMessageConstant
          .SURGE_COMPUTATION_MODEL_REGION_BASED_CONFIGURATION_OVERLAPPING_DATES_ERROR),
  SURGE_COMPUTATION_MODEL_REGION_BASED_CONFIGURATION_CREATE_ERROR(
      68, ErrorMessageConstant.SURGE_COMPUTATION_MODEL_REGION_BASED_CONFIGURATION_CREATE_ERROR),
  SURGE_COMPUTATION_MODEL_REGION_BASED_CONFIGURATION_UPDATE_ERROR(
      69, ErrorMessageConstant.SURGE_COMPUTATION_MODEL_REGION_BASED_CONFIGURATION_UPDATE_ERROR),
  SURGE_COMPUTATION_MODEL_REGION_BASED_CONFIGURATION_NAME_MISMATCH_ERROR(
      70,
      ErrorMessageConstant.SURGE_COMPUTATION_MODEL_REGION_BASED_CONFIGURATION_NAME_MISMATCH_ERROR),
  SURGE_COMPUTATION_MODEL_STATIC_BASED_CONFIGURATION_VERSION_NOT_SAME_ERROR(
      71,
      ErrorMessageConstant
          .SURGE_COMPUTATION_MODEL_STATIC_BASED_CONFIGURATION_VERSION_NOT_SAME_ERROR),
  SURGE_COMPUTATION_MODEL_STATIC_BASED_CONFIGURATION_TIME_ZONE_ERROR(
      72, ErrorMessageConstant.SURGE_COMPUTATION_MODEL_STATIC_BASED_CONFIGURATION_TIME_ZONE_ERROR),
  REGION_MODEL_DISTRIBUTION_INVALID_MODEL_ID(
      73, ErrorMessageConstant.REGION_MODEL_DISTRIBUTION_INVALID_MODEL_ID),
  REGION_MODEL_DISTRIBUTION_MISSING_MODEL_ID(
      74, ErrorMessageConstant.REGION_MODEL_DISTRIBUTION_MISSING_MODEL_ID),
  REGION_MODEL_DISTRIBUTION_INVALID_REGION_IDS(
      75, ErrorMessageConstant.REGION_MODEL_DISTRIBUTION_INVALID_REGION_IDS),
  REGION_MODEL_DISTRIBUTION_MISSING_REGION_IDS(
      76, ErrorMessageConstant.REGION_MODEL_DISTRIBUTION_MISSING_REGION_IDS),
  ;

  private final long errorCode;
  private final String message;

  private ErrorEnum(long errorCode, String message) {
    this.errorCode = errorCode;
    this.message = message;
  }

  public long getErrorCode() {
    return errorCode;
  }

  public String getMessage() {
    return message;
  }
}

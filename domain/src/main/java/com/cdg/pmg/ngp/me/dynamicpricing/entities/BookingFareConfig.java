package com.cdg.pmg.ngp.me.dynamicpricing.entities;

import java.time.LocalTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** BookingFareConfig information. */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BookingFareConfig {

  private String pdtId;
  private String vehTypeId;
  private String tariffTypeCode;
  private Double fareAmt;
  private Double levyAmt;
  private LocalTime startTime;
  private LocalTime endTime;
  private List<String> applicableDays;
}

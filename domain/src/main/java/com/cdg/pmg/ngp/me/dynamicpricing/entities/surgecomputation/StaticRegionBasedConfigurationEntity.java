package com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Data class for static region-based configuration. This class represents the domain model for
 * static region-based configurations.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StaticRegionBasedConfigurationEntity {

  private Long id;
  private String name;
  private String version;
  private Instant effectiveFrom;
  private Instant effectiveTo;
  private String description;

  @Builder.Default
  private List<RegionValue> regionValues = new ArrayList<>(); // JSONB array of region values

  private String createdBy;
  private Instant createdDate;
  private String updatedBy;
  private Instant updatedDate;

  /** Inner class representing a region value entry in the JSONB array. */
  @Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  public static class RegionValue {
    private Long regionId;
    private String value;
  }
}

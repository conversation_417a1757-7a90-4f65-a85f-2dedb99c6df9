package com.cdg.pmg.ngp.me.dynamicpricing.constants;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class BookARideConfigsConstant {

  public static final int ONE_THOUSAND = 1000;
  public static final String SEPARATE_CHARACTER = ",";

  public static final String COMMA = ",";

  public static final String ZERO_VALUE = "0";

  // Book a Ride Configurations
  public static final String BOOK_RIDE_SCHEDULING_MIN = "BOOK_RIDE_SCHEDULING_MIN";
  public static final String FEED_ID = "FEED_ID";
  public static final String CURRENCY_CODE = "CURRENCY_CODE";
  public static final String BOOK_RIDE_VEHICLE_GROUP = "BOOK_RIDE_VEHICLE_GROUP";
  public static final String BOOK_RIDE_PRODUCT = "BOOK_RIDE_PRODUCT";
  public static final String COMFORT_LOCALISED_NAME = "COMFORT_LOCALISED_NAME";
  public static final String METERED_LOCALISED_NAME = "METERED_LOCALISED_NAME";
  public static final String COMFORT_INTERNAL_NAME = "COMFORT_INTERNAL_NAME";
  public static final String METERED_INTERNAL_NAME = "METERED_INTERNAL_NAME";
  public static final String WAITING_TIME_SECONDS = "WAITING_TIME_SECONDS";
  public static final String LOW_RANGE_ESTIMATE_MULTIPLIER_VALUE =
      "LOW_RANGE_ESTIMATE_MULTIPLIER_VALUE";
  public static final String HIGH_RANGE_ESTIMATE_MULTIPLIER_VALUE =
      "HIGH_RANGE_ESTIMATE_MULTIPLIER_VALUE";
  public static final String BOOK_RIDE_PROD_CATEGORY = "BOOK_RIDE_PROD_CATEGORY";
  public static final String CAR_ICON_ID = "CAR_ICON_ID";
  public static final String MINIMUM_FIXED = "MINIMUM_FIXED";

  public static final String FILE_NAME = "FILE_NAME";

  public static final String RT = "RATE_";
  public static final String DAYS = "DAYS_";

  public static final String START_TIME_PREFIX = "START_TIME_";
  public static final String END_TIME_PREFIX = "END_TIME_";
  public static final String PEAK_HOUR_PREFIX = "PEAK_HOUR_";
  public static final String MID_NIGHT_PREFIX = "MID_NIGHT_";
  public static final String EST_WAIT_TIME_MID_NIGHT_RATE_0 = "EST_WAIT_TIME_MID_NIGHT_RATE_0";
  public static final String EST_WAIT_TIME_MID_NIGHT_DAYS_0 = "EST_WAIT_TIME_MID_NIGHT_DAYS_0";
  public static final String EST_WAIT_TIME_MID_NIGHT_START_TIME_0 =
      "EST_WAIT_TIME_MID_NIGHT_START_TIME_0";
  public static final String EST_WAIT_TIME_MID_NIGHT_END_TIME_0 =
      "EST_WAIT_TIME_MID_NIGHT_END_TIME_0";
  public static final String DYNAMIC_PRICING_START_TIME = "DYNAMIC_PRICING_START_TIME";
  public static final String DYNAMIC_PRICING_END_TIME = "DYNAMIC_PRICING_END_TIME";
  public static final String DYNAMIC_PRICING_PREFIX = "DYNAMIC_PRICING_";
  public static final String LIVE_TRAFFIC_PREFIX = "LIVE_TRAFFIC_";
  public static final String WAIT_TIME_PREFIX = "WAIT_TIME_";
  public static final String COMFORT_RIDE_PRODUCT = "COMFORT";
  public static final String METER_FARE_PRODUCT = "METERED";
  public static final String IS_LIVE_TRAFFIC = "IS_LIVE_TRAFFIC";
  public static final String DYNAMIC_PRICING_ENABLED = "DYNAMIC_PRICING_ENABLED";
  public static final String DYNAMIC_PRICING_VEH_GROUP_IDS = "DYNAMIC_PRICING_VEH_GROUP_IDS";
  public static final String EST_LIVE_TRAFFIC_PREFIX = "EST_LIVE_TRAFFIC_";
  public static final String EST_WAIT_TIME_PREFIX = "EST_WAIT_TIME_";
  public static final String FLAG_DOWN_RATE = "FLAG_DOWN_RATE";
  public static final String TIER_1_PER_COUNT_FARE = "TIER_1_PER_COUNT_FARE";
  public static final String TIER_1_PER_COUNT_METER = "TIER_1_PER_COUNT_METER";
  public static final String TIER_1_START_DIST = "TIER_1_START_DISTANCE";
  public static final String TIER_1_END_DIST = "TIER_1_END_DISTANCE";

  public static final String TIER_2_PER_COUNT_FARE = "TIER_2_PER_COUNT_FARE";
  public static final String TIER_2_PER_COUNT_METER = "TIER_2_PER_COUNT_METER";
  public static final String TIER_2_START_DIST = "TIER_2_START_DISTANCE";
  public static final String TIER_2_END_DIST = "TIER_2_END_DISTANCE";
  public static final String DURATION_TIME_RATE = "DURATION_RATE";
  public static final String TARIFF_CJ_BOOKING_FEE = "CBKC";
  public static final String TARIFF_PDT_BOOKING_FEE = "PDT";
  public static final String HOL = "HOL";
  public static final String DYNAMIC_PRICING_PEAK_HOUR_RATE = "DYNAMIC_PRICING_PEAK_HOUR_RATE";
  public static final String DYNAMIC_PRICING_MID_NIGHT_RATE = "DYNAMIC_PRICING_MID_NIGHT_RATE";
  public static final String DYNAMIC_PRICING_MIN_CAP = "DYNAMIC_PRICING_MIN_CAP";

  public static final String TOTAL_FARE_ESTIMATE_LF = "TOTAL_FARE_ESTIMATE_LF";
  public static final String TOTAL_FARE_ESTIMATE_RT = "TOTAL_FARE_ESTIMATE_RT";

  public static final String TARIFF_CJBOOKINGFEE = "CBKC";

  public static final String TARIFF_PDTBOOKINGFEE = "PDT";

  public static final String ENABLED = "Y";
  public static final String BOOK_RIDE_PROD_CATEGORY_DEFAULT_VALUE = "5";
  public static final String PICK_UP = "PICKUP";
  public static final String DEST = "DEST";
  public static final String REST = "REST";
  public static final String EN = "en";
  public static final String LOCALISED_NAME = "_LOCALISED_NAME";
  public static final String INTERNAL_NAME = "_INTERNAL_NAME";
}

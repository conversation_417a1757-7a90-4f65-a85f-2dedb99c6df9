package com.cdg.pmg.ngp.me.dynamicpricing.entities;

import java.time.OffsetDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ConfigKeyValueConfigEntity {
  private String uniquePayloadHash;
  private String key;
  private String value;
  private String createdBy;
  private OffsetDateTime createdDt;
  private String updatedBy;
  private OffsetDateTime updatedDt;
}

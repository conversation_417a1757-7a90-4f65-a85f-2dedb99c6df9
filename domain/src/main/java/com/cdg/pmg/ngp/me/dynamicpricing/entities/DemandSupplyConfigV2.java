package com.cdg.pmg.ngp.me.dynamicpricing.entities;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DemandSupplyConfigV2 implements Serializable {
  private String zoneId;
  private int demand15;
  private int demand30;
  private int demand60;
  private int supply;
  private int predictedDemand15;
  private int predictedDemand30;
  private int excessDemand15;
  private int excessDemand30;
  private int previousDemand15;
  private int batchCounter;
  private double unmet15;
  private double previousUnmet15;
  private int surgeLow;
  private int surgeHigh;
  private int stepPositive;
  private int stepNegative;
}

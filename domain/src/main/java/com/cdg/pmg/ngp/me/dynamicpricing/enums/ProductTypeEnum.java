package com.cdg.pmg.ngp.me.dynamicpricing.enums;

import lombok.Getter;

/** The enum product type. */
@Getter
public enum ProductTypeEnum {
  COMFORT_RIDE_PRODUCT("COMFORT", "FLAT-001"),
  METER_FARE_PRODUCT("METERED", "STD001");

  private final String productType;
  private final String productCode;

  ProductTypeEnum(String productType, String productCode) {
    this.productType = productType;
    this.productCode = productCode;
  }
}

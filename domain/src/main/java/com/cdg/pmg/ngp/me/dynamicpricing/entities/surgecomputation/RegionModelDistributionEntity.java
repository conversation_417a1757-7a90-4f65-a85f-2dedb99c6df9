package com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation;

import java.util.List;
import lombok.*;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class RegionModelDistributionEntity extends ConfigurationEffectiveEntity {

  private Long id;
  private Long regionId;
  private List<ModelPercentage> models;

  public RegionModelDistributionEntity(final Long regionId, final List<ModelPercentage> models) {
    this.regionId = regionId;
    this.models = models;
  }
}

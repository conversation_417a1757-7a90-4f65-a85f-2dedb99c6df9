package com.cdg.pmg.ngp.me.dynamicpricing.utils;

import static com.cdg.pmg.ngp.me.dynamicpricing.constants.CommonConstant.UNDERSCORE;

import com.cdg.pmg.ngp.me.dynamicpricing.constants.BookARideConfigsConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.DynamicPricingTimeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.HourRateConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.S2CellEntity;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.experimental.UtilityClass;

/** FareUploadUtils */
@UtilityClass
public class FareUploadUtils {
  /**
   * Extracts and constructs a map of {@link HourRateConfig} objects from flat fare configuration
   * data. This method filters and processes entries in the provided flat fare configuration map
   * based on the specified prefix. The prefix is used to identify relevant keys for peak hour or
   * midnight rate configurations.
   *
   * @param flatFareConfig The map containing flat fare configuration key-value pairs.
   * @param prefix The prefix string used to filter and identify the relevant configuration entries.
   *     This should correspond to either peak hour or midnight rate configurations.
   * @return A map where each key represents a unique identifier (extracted from the flat fare
   *     configuration keys) and each value is an {@link HourRateConfig} object populated with rate,
   *     days of week, start time, and end time information.
   * @throws IllegalArgumentException If the prefix is null.
   */
  public static Map<String, HourRateConfig> getPeakHourOrMidnightRates(
      final Map<String, String> flatFareConfig, final String prefix) {
    if (Objects.isNull(prefix)) {
      throw new IllegalArgumentException("Prefix cannot be null");
    } else {
      final Map<String, HourRateConfig> hourRateConfig = new HashMap<>();
      flatFareConfig.forEach(
          (flatFareKey, value) -> {
            String configKey = flatFareKey.substring(flatFareKey.lastIndexOf(UNDERSCORE));
            if (flatFareKey.contains(StringUtils.concat(prefix, BookARideConfigsConstant.RT))) {
              hourRateConfig.compute(
                  configKey,
                  (hourRateKey, hourRateValue) -> {
                    HourRateConfig hourRate =
                        Objects.isNull(hourRateValue) ? new HourRateConfig() : hourRateValue;
                    hourRate.setRate(Double.valueOf(value));
                    return hourRate;
                  });
            }
            if (flatFareKey.contains(StringUtils.concat(prefix, BookARideConfigsConstant.DAYS))) {
              hourRateConfig.compute(
                  configKey,
                  (hourRateKey, hourRateValue) -> {
                    HourRateConfig hourRate =
                        Objects.isNull(hourRateValue) ? new HourRateConfig() : hourRateValue;
                    hourRate.setDaysOfWeekIncluded(value);
                    return hourRate;
                  });
            }

            if (flatFareKey.contains(
                StringUtils.concat(prefix, BookARideConfigsConstant.START_TIME_PREFIX))) {
              hourRateConfig.compute(
                  configKey,
                  (hourRateKey, hourRateValue) -> {
                    HourRateConfig hourRate =
                        Objects.isNull(hourRateValue) ? new HourRateConfig() : hourRateValue;
                    hourRate.setStartTime(LocalTime.parse(value));
                    return hourRate;
                  });
            }
            if (flatFareKey.contains(
                StringUtils.concat(prefix, BookARideConfigsConstant.END_TIME_PREFIX))) {
              hourRateConfig.compute(
                  configKey,
                  (hourRateKey, hourRateValue) -> {
                    HourRateConfig hourRate =
                        Objects.isNull(hourRateValue) ? new HourRateConfig() : hourRateValue;
                    hourRate.setEndTime(LocalTime.parse(value));
                    return hourRate;
                  });
            }
          });
      return hourRateConfig;
    }
  }
  /**
   * Processes a map of flat fare configuration settings and generates a map of {@link
   * DynamicPricingTimeConfig} objects. This method specifically extracts and sets the start and end
   * times for dynamic pricing from the provided configurations.
   *
   * @param flatFareConfig A map containing the flat fare configuration settings. It is expected to
   *     include keys for dynamic pricing start and end times.
   * @return A map where each key corresponds to an identifier (extracted from the input keys) and
   *     the value is a {@link DynamicPricingTimeConfig} object with the appropriate start and end
   *     times set.
   */
  public static Map<String, DynamicPricingTimeConfig> getDynamicPricingTimeConfig(
      final Map<String, String> flatFareConfig) {
    final Map<String, DynamicPricingTimeConfig> dynamicPricingTimeConfig = new HashMap<>();
    final Map<String, String> dynamicPricingStartTimeConfig =
        flatFareConfig.entrySet().stream()
            .filter(
                element ->
                    element.getKey().contains(BookARideConfigsConstant.DYNAMIC_PRICING_START_TIME))
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    final Map<String, String> dynamicPricingEndTimeConfig =
        flatFareConfig.entrySet().stream()
            .filter(
                element ->
                    element.getKey().contains(BookARideConfigsConstant.DYNAMIC_PRICING_END_TIME))
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

    dynamicPricingStartTimeConfig.forEach(
        (key, value) -> {
          final String startTimeKeyIndex =
              key.substring(0, key.lastIndexOf(BookARideConfigsConstant.DYNAMIC_PRICING_PREFIX));
          dynamicPricingTimeConfig.compute(
              startTimeKeyIndex,
              (k, v) -> {
                DynamicPricingTimeConfig newConfig =
                    Objects.isNull(v) ? new DynamicPricingTimeConfig() : v;
                newConfig.setStartTime(Long.valueOf(value));
                return newConfig;
              });
        });

    dynamicPricingEndTimeConfig.forEach(
        (key, value) -> {
          final String endTimeKeyIndex =
              key.substring(0, key.lastIndexOf(BookARideConfigsConstant.DYNAMIC_PRICING_PREFIX));
          dynamicPricingTimeConfig.compute(
              endTimeKeyIndex,
              (k, v) -> {
                DynamicPricingTimeConfig newConfig =
                    Objects.isNull(v) ? new DynamicPricingTimeConfig() : v;
                newConfig.setEndTime(Long.valueOf(value));
                return newConfig;
              });
        });
    return dynamicPricingTimeConfig;
  }
  /**
   * Maps each unique area ID to a sequential index number.
   *
   * <p>This method processes a list of S2CellEntity objects to create a mapping. Each unique
   * S2CellLocationId (considered as an area ID) in the list is assigned an increasing index number
   * starting from 0. Additionally, the method assigns the index number to a default area ID of 0,
   * which can represent cells without a specified location.
   *
   * @param s2CellList A List of S2CellEntity objects.
   * @return A Map where the key is the area ID (extracted and converted from the S2CellEntity's
   *     S2CellLocationId) and the value is a sequentially increasing number.
   */
  public static Map<Integer, Integer> mapAreaIdsToIndex(final List<S2CellEntity> s2CellList) {
    final Map<Integer, Integer> endPointSurchargeMap = new HashMap<>();
    Map<String, List<S2CellEntity>> s2CellWithLocationMap =
        s2CellList.stream()
            .filter(s2Cell -> s2Cell.getS2CellLocationId() != null)
            .collect(Collectors.groupingBy(S2CellEntity::getS2CellLocationId));
    int index = 0;
    for (Map.Entry<String, List<S2CellEntity>> s2CellEntry : s2CellWithLocationMap.entrySet()) {
      endPointSurchargeMap.put(Integer.valueOf(s2CellEntry.getKey()), index);
      index += 1;
    }
    endPointSurchargeMap.put(0, index);
    return endPointSurchargeMap;
  }
}

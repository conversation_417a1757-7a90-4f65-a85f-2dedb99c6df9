package com.cdg.pmg.ngp.me.dynamicpricing.utils;

import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.experimental.UtilityClass;

/** The type Collection utils. */
@UtilityClass
public class CollectionUtils {

  /**
   * Find duplicate elements set.
   *
   * @param <T> the type parameter
   * @param array the array
   * @return the set
   */
  public static <T> Set<T> findDuplicateElements(final T[] array) {
    if (array == null) {
      return new HashSet<>();
    }
    return findDuplicateElements(Arrays.asList(array));
  }

  /**
   * Find duplicate elements set.
   *
   * @param <T> the type parameter
   * @param collection the collection
   * @return the set
   */
  @SuppressWarnings("unchecked")
  public static <T> Set<T> findDuplicateElements(final Collection<T> collection) {
    if (collection == null) {
      return new HashSet<>();
    }
    var uniqueValues = new HashSet<T>();
    return collection.stream()
        .filter(Objects::nonNull)
        .map(
            s -> {
              if (s.getClass().isAssignableFrom(String.class)) {
                return (T) s.toString().strip();
              }
              return s;
            })
        .filter(e -> !uniqueValues.add(e))
        .collect(Collectors.toSet());
  }

  /**
   * Is empty boolean.
   *
   * @param objects the objects
   * @return the boolean
   */
  public static boolean isEmpty(final Object[] objects) {
    return objects == null || objects.length == 0 || Stream.of(objects).noneMatch(Objects::nonNull);
  }

  /**
   * Is empty boolean.
   *
   * @param <T> the type parameter
   * @param collection the collection
   * @return the boolean
   */
  public static <T> boolean isEmpty(final Collection<T> collection) {
    return collection == null || collection.isEmpty();
  }

  /**
   * Is not empty boolean.
   *
   * @param <T> the type parameter
   * @param collection the collection
   * @return the boolean
   */
  public static <T> boolean isNotEmpty(final Collection<T> collection) {
    return !isEmpty(collection);
  }

  /**
   * Check the list contains an object or not.
   *
   * @param <T> the type parameter
   * @param collection the collection
   * @param object the object
   * @return the boolean
   */
  public static <T> boolean contains(final Collection<T> collection, final T object) {
    return !isEmpty(collection) && collection.contains(object);
  }
}

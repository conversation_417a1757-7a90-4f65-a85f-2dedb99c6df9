package com.cdg.pmg.ngp.me.dynamicpricing.entities;

import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDate;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FareTypeConfig implements Serializable {
  private int fareTypeId;
  private String fareType;
  private String day;
  private String hour;
  private Integer vehGrp;
  private Double fixedValue;
  private Double percentValue;
  private Double defaultFixed;
  private Double defaultPercent;
  private LocalDate startDate;
  private LocalDate endDate;
  private int dayPriority;
  private Instant createdDate;
  private String createdBy;
  private Instant updatedDate;
  private String updatedBy;
  private Date cacheUpdateDate;

  @Override
  public String toString() {
    return "FareTypeConfig{"
        + "fareTypeId="
        + fareTypeId
        + ", fareType='"
        + fareType
        + '\''
        + ", day='"
        + day
        + '\''
        + ", hour='"
        + hour
        + '\''
        + ", vehGrp="
        + vehGrp
        + ", fixedValue="
        + fixedValue
        + ", percentValue="
        + percentValue
        + ", defaultFixed="
        + defaultFixed
        + ", defaultPercent="
        + defaultPercent
        + ", startDate="
        + startDate
        + ", endDate="
        + endDate
        + ", dayPriority="
        + dayPriority
        + ", createdDate="
        + createdDate
        + ", createdBy='"
        + createdBy
        + '\''
        + ", updatedDate="
        + updatedDate
        + ", updatedBy='"
        + updatedBy
        + '\''
        + ", cacheUpdateDate="
        + cacheUpdateDate
        + '}';
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Entity representing surge data for a specific model, including all regions and their surge
 * values. This entity is used for monitoring endpoints to provide current surge values across
 * models.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModelSurgeDataEntity implements Serializable {
  @Serial private static final long serialVersionUID = 1L;

  private Long modelId;
  private String modelName;
  private List<H3RegionSurgeEntity> regions;
}

package com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.enums.DayOfWeekEnum;
import java.time.Instant;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Data class for static time-based configuration. This class represents the domain model for static
 * time-based configurations.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StaticTimeBasedConfigurationEntity {

  private Long id;
  private String name;
  private String version;
  private Instant effectiveFrom;
  private Instant effectiveTo;
  private String description;
  private List<AppliedHour> appliedHours; // JSONB array of applied hours
  private String timeZoneOffset;
  private String createdBy;
  private Instant createdDate;
  private String updatedBy;
  private Instant updatedDate;

  /** Inner class representing an applied hour entry in the JSONB array. */
  @Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  public static class AppliedHour {
    private DayOfWeekEnum dayOfWeek; // MON, TUE, WED, THU, FRI, SAT, SUN, PUBLIC_HOLIDAY
    private Integer hourOfDay; // 0-23
    private String value; // The value to apply for this day and hour
  }
}

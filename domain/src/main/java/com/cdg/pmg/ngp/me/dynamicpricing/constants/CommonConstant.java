package com.cdg.pmg.ngp.me.dynamicpricing.constants;

public class CommonConstant {
  private CommonConstant() {}

  public static final String YYYY_MM_DD = "yyyy/MM/dd";
  public static final String YYYYMMDDHHMMSS = "yyyyMMddhhmmss";
  public static final String DD_MM_YYYY = "dd/MM/yyyy";
  public static final String UNDERSCORE = "_";
  public static final Double DEFAULT_MULTIPLIER = 0d;
  public static final double DEFAULT_SURGE = 0;
  public static final double DEFAULT_DYNAMIC_PRICING_CONFIG_VALUE = 0d;
  public static final int ONE_KILOMETER = 1000;
  public static final int ONE_MINUTE = 60;
  public static final String SYSTEM_USER = "SYSTEM";
  public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd hh:mm:ss";
  public static final String NEW_PRICING_MODEL_SUFFIX = "new-pricing-config";
  public static final String NEW_PRICING_MODEL_CMS_KEY = "newPricingModelConfig.items";
  public static final int RELEASE_VER_DEFAULT = 1;
  public static final int RELEASE_1 = 1;
  public static final int RELEASE_2 = 2;

  public static final String DYNP_SURGES_NGP_KEY_CACHE =
      RedisKeyConstant.DYNAMIC_PRICING
          .concat(RedisKeyConstant.COLON)
          .concat(RedisKeyConstant.DYNP_SURGES_NGP);

  public static final String DYNP_SURGES_KEY_CACHE =
      RedisKeyConstant.DYNAMIC_PRICING
          .concat(RedisKeyConstant.COLON)
          .concat(RedisKeyConstant.DYNP_SURGES);

  public static final String MOCK_ENCODE_POLYLINE =
      "w|cGkicyRA~@EJUPQNiAcA[YGGSSrByB@KVYUSuBxBg@f@o@n@G|@?R?f@@RdBbB~@dA^f@l@|@~@xALRJP^p@LV~@bBNBV`@b@z@j@hA\\z@P`@tArD@";
}

package com.cdg.pmg.ngp.me.dynamicpricing.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ValidateFareEntity implements Serializable {
  private String fareId;
  private String countryCode;
  private String mobile;
  private String pickupAddressRef;
  private String dropoffAddressRef;
  private String intermediateAddrRef;
  private int vehTypeId;
  private BigDecimal fareAmount;
}

package com.cdg.pmg.ngp.me.dynamicpricing.queries;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.S2CellEntity;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class S2CellListConfigQueryResponse implements Serializable {

  @Serial private static final long serialVersionUID = 4502686088387136657L;

  private List<S2CellEntity> s2CellList;
}

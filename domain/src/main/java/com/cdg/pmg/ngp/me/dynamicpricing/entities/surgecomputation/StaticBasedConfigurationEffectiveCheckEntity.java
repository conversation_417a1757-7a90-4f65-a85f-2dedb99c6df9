package com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation;

import java.time.Instant;
import lombok.Data;

@Data
public class StaticBasedConfigurationEffectiveCheckEntity {

  /** Will be true if the configuration out of date or close to expiration */
  private Boolean isWarning;
  /** Will be true only the configuration is effective */
  private Boolean isEffective;

  private String version;
  private Instant effectiveFrom;
  private Instant effectiveTo;
}

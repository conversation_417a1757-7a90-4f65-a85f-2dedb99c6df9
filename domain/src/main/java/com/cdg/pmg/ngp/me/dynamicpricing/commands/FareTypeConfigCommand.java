package com.cdg.pmg.ngp.me.dynamicpricing.commands;

import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/** The type Fare type config command. */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FareTypeConfigCommand implements Serializable {
  @Serial private static final long serialVersionUID = 6136685665550454480L;

  private String fareType;
  private String userChange;
  private double defaultFixed;
  private double defaultPercent;
  private String day;
  private String hour;
  private String startDate;
  private String endDate;
}

package com.cdg.pmg.ngp.me.dynamicpricing.entities;

import java.io.Serial;
import java.io.Serializable;
import lombok.*;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class FlatFareAdjustmentConfEntity implements Serializable {

  @Serial private static final long serialVersionUID = 1493628712332623034L;

  private int id;
  private int vehGrp;
  private Double fixedVal;
  private Double perVal;
}

package com.cdg.pmg.ngp.me.dynamicpricing.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RouteInfo {
  private String tripId;
  private Long routingDistance;
  private Long ett;
  private String encodedPolyline;
  private String pickupAddressRef;
  private Double pickupAddressLat;
  private Double pickupAddressLng;
  private String pickupZoneId;
  private String destAddressRef;
  private Double destAddressLat;
  private Double destAddressLng;
  private String destZoneId;
  private String intermediateAddrRef;
  private Double intermediateAddrLat;
  private Double intermediateAddrLng;
  private String intermediateZoneId;
}

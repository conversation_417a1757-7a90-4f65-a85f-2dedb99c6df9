package com.cdg.pmg.ngp.me.dynamicpricing.service;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.NewPricingModelConfigEntity;
import java.util.List;

public interface NewPricingModelService {

  /**
   * @param newPricingModel the new pricing model to validate before inserting or using
   * @throws com.cdg.pmg.ngp.me.dynamicpricing.exceptions.BadRequestException when validation failed
   */
  void validate(NewPricingModelConfigEntity newPricingModel);

  /**
   * @param newPricingModel the new pricing model to validate for update
   * @throws com.cdg.pmg.ngp.me.dynamicpricing.exceptions.BadRequestException when validation failed
   */
  void validateIgnoreZoneCheck(NewPricingModelConfigEntity newPricingModel);

  List<NewPricingModelConfigEntity> getListNewPricingModelConfigEntityInCms();

  NewPricingModelConfigEntity update(NewPricingModelConfigEntity newPricingModelConfigEntity);

  NewPricingModelConfigEntity create(
      NewPricingModelConfigEntity newPricingModelConfigEntity, int index);
}

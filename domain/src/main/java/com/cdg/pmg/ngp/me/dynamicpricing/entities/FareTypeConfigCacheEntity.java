package com.cdg.pmg.ngp.me.dynamicpricing.entities;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class FareTypeConfigCacheEntity implements Serializable {
  private Map<String, List<FareTypeConfig>> fareTypeConfigMapByDay;
}

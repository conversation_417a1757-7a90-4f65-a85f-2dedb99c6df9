package com.cdg.pmg.ngp.me.dynamicpricing.entities;

import java.io.Serial;
import java.io.Serializable;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LocationSurchargeConfigEntity implements Serializable {
  @Serial private static final long serialVersionUID = -4281069377383347263L;

  private Integer locationId;
  private String locationName;
  private String addressRef;
  private String fareType;
  private String chargeBy;
  private Double surchargeValue;
  private String productId;
  // Data type of startTime is String. Because in DB, Data type of startTime is varchar(8)
  private String startTime;
  // Data type of endTime is String. Because in DB, Data type of endTime is varchar(8)
  private String endTime;
  private String dayIndicator;

  public boolean isValidConfig() {
    return !(locationId == null
        || locationName.isEmpty()
        || fareType.isEmpty()
        || dayIndicator.isEmpty()
        || chargeBy.isEmpty()
        || surchargeValue == null
        || startTime.isEmpty()
        || endTime.isEmpty()
        || addressRef.isEmpty());
  }

  @Override
  public boolean equals(Object obj) {
    if (this == obj) return true;
    if (obj == null || getClass() != obj.getClass()) return false;
    LocationSurchargeConfigEntity config = (LocationSurchargeConfigEntity) obj;
    return Objects.equals(locationId, config.locationId)
        && Objects.equals(addressRef, config.addressRef)
        && Objects.equals(chargeBy, config.chargeBy)
        && Objects.equals(productId, config.productId)
        && Objects.equals(startTime, config.startTime)
        && Objects.equals(endTime, config.endTime)
        && Objects.equals(dayIndicator, config.dayIndicator);
  }

  @Override
  public int hashCode() {
    return Objects.hash(
        locationId, addressRef, chargeBy, productId, startTime, endTime, dayIndicator);
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.entities;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class VGProductFareBean {
  private Double flagDown;
  private Double tier1Fare;
  private Double tier2Fare;
  private String vehicleGroupId;
  private String productId;
  private Double tier1Start;
  private Double tier2Start;
  private Double tier1End;
  private Double tier2End;
  private Double tier1PerCountMeter;
  private Double tier2PerCountMeter;
  private Double waitTimeFare;
  private Double peakHrFare;
  private Double midNightFare;
  private Double bookingFee;
  private Double estimatedFareLF;
  private Double estimatedFareRT;
  private Double dpMinFareCap;
  private boolean isPeakHour;
  private boolean isMidNight;
  private boolean isSurgeRequired;
}

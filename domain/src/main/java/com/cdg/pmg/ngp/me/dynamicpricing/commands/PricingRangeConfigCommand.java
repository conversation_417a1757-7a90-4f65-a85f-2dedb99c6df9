package com.cdg.pmg.ngp.me.dynamicpricing.commands;

import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PricingRangeConfigCommand implements Serializable {
  @Serial private static final long serialVersionUID = -6188517326002648062L;

  private Integer isEnabled;
  private Double startPrice;
  private Double endPrice;
  private Double step;
  private Integer refreshPeriod;
  private Integer quoteValidPeriod;
  private String day;
  private String hour;
  private Double stepPositive;
  private Double stepNegative;
  private Instant createdDate;
  private String createdBy;
  private Instant updatedDate;
  private String updatedBy;
}

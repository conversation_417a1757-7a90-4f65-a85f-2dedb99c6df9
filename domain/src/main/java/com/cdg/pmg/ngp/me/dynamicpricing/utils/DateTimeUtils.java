package com.cdg.pmg.ngp.me.dynamicpricing.utils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import lombok.experimental.UtilityClass;

/** The type Date Time utils. */
@UtilityClass
public class DateTimeUtils {

  /**
   * Get current date by format
   *
   * @param format format of date
   * @return the current date with type String
   */
  public static String getCurrentDateByFormat(String format) {
    DateFormat df = new SimpleDateFormat(format);
    return df.format(new Date());
  }

  /**
   * Get date by format
   *
   * @param format format of date
   * @return the date with type String
   */
  public static String getDateByFormat(String format, Date date) {
    DateFormat df = new SimpleDateFormat(format);
    return df.format(date);
  }
}

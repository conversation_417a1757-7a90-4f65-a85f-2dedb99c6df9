package com.cdg.pmg.ngp.me.dynamicpricing.queries;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.EttUnitFareConfig;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EttUnitFareConfigQueryResponse implements Serializable {
  @Serial private static final long serialVersionUID = -4531830108576700713L;

  private List<EttUnitFareConfig> configs;
}

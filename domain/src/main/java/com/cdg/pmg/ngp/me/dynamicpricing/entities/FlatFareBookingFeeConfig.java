package com.cdg.pmg.ngp.me.dynamicpricing.entities;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FlatFareBookingFeeConfig implements Serializable {
  private String productId;
  private int vehTypeId;
  private String tariffTypeCode;
  private Double fareAmt;
  private Double levyAmt;
  private String startTime;
  private String endTime;
  private String applicableDays;
}

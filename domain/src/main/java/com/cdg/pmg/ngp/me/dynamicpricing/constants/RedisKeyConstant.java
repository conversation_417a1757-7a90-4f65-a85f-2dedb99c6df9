package com.cdg.pmg.ngp.me.dynamicpricing.constants;

public class RedisKeyConstant {
  private RedisKeyConstant() {}

  /*** Delimiter Cache Key ***/
  public static final String COLON = ":";

  /*** Pattern Cache Key ***/
  public static final String WILDCARD = "*";

  /*** Prefix Cache Key ***/
  public static final String DYNAMIC_PRICING = "DYNAMIC_PRICING";

  public static final String FARE_TYPE = "FARE_TYPE";
  public static final String FLAT_FARE = "FLAT_FARE";
  public static final String FLAT_FARE_ADJUSTMENT = "FLAT_FARE_ADJUSTMENT";
  public static final String COMMON = "COMMON";

  public static final String DYNAMIC_PRICING_FLAT_FARE_PREFIX =
      DYNAMIC_PRICING + COLON + FLAT_FARE + COLON;

  public static final String DYNAMIC_PRICING_FARE_TYPE_PREFIX =
      DYNAMIC_PRICING + COLON + FARE_TYPE + COLON;

  public static final String DYNAMIC_PRICING_COMMON_FLAT_FARE_PREFIX =
      DYNAMIC_PRICING + COLON + COMMON + COLON + FLAT_FARE + COLON;

  public static final String DYNAMIC_PRICING_COMMON_FARE_TYPE_PREFIX =
      DYNAMIC_PRICING + COLON + COMMON + COLON + FARE_TYPE + COLON;

  public static final String COMPANY_HOLIDAY = "COMPANY_HOLIDAY";
  public static final String FLATFARE_BOOKING_FEE = "FLATFARE_BOOKING_FEE";
  public static final String LOC_SURC = "LOC_SURC";
  public static final String ETT_FARE_TYPE = "ETT_FARE_TYPE";
  public static final String MULTI_FARE = "MULTI_FARE";
  public static final String BREAKDOWN = "BREAKDOWN";
  public static final String DYNP_SURGES = "DYNP_SURGES";
  public static final String DYNP_SURGES_NGP = "DYNP_SURGES_NGP";

  /*** Flat fare Config Cache Key ***/
  public static final String EVENT_SURGE_ADDRESS_KEY_PREFIX = "EVENT_SURGE_ADDRESS_";

  public static final String EVENT_SURGE_ZONE_KEY_PREFIX = "EVENT_SURGE_ZONE_";
  public static final String ADDITIONAL_CHARGE_KEY_PREFIX = "ADDITIONAL_CHARGE_";
  public static final String DYNP_PRICE_SCHEDULER_KEY_PREFIX = "DYNP_PRICE_SCHEDULER_";
  public static final String DRV_SURGE_LEVEL_KEY_PREFIX = "DRIVER_SURGE_LEVEL_INDICATION_";
  public static final String FLATFARE_OTHER_CONF_KEY = "OTHER_CONFIGS";
  public static final String LIVE_TRAFFIC_KEY_PREFIX = "LIVE_TRAFFIC_";
  public static final String LIVE_TRAFFIC = "StandardFlatFare";
  public static final String EMPTY = "";
  public static final String EST_WAIT_TIME_KEY_PREFIX = "EST_WAIT_TIME_";
  public static final String EST_LIVE_TRAFFIC_KEY_PREFIX = "EST_LIVE_TRAFFIC_";
  public static final String EST_LIVE_TRAFFIC = "EstStandardFlatFare";
  public static final String EST_LIMO_LIVE_TRAFFIC_KEY_PREFIX = "EST_LIMO_LIVE_TRAFFIC_";
  public static final String EST_LIMO_LIVE_TRAFFIC = "LimoFlatFare";
  public static final String PEAK_HOUR_KEY = "PEAK_HOUR_";
  public static final String MIDNIGHT_KEY = "MID_NIGHT_";
  public static final String FLAG_DOWN_RATE = "FLAG_DOWN_RATE";
  public static final String PEAK_HOUR_WC = "PEAK_HOUR_*";
  public static final String MID_NIGHT_WC = "MID_NIGHT_*";
  public static final String DRIVER_SURGE_LEVEL_INDICATION_WC = "DRIVER_SURGE_LEVEL_INDICATION_*";

  public static final String EVENT_SURGE_ADDRESS = "EVENT_SURGE_ADDRESS";

  public static final String TIER_1 = "TIER_1_";
  public static final String TIER_2 = "TIER_2_";
  public static final String PER_COUNT_FARE = "PER_COUNT_FARE";
  public static final String PER_COUNT_METER = "PER_COUNT_METER";
  public static final String START_DISTANCE = "START_DISTANCE";
  public static final String END_DISTANCE = "END_DISTANCE";

  public static final String TOTAL_FARE_ESTIMATE_LF = "TOTAL_FARE_ESTIMATE_LF";
  public static final String TOTAL_FARE_ESTIMATE_RT = "TOTAL_FARE_ESTIMATE_RT";

  public static final String DURATION_UNIT = "DURATION_UNIT";
  public static final String DURATION_RATE = "DURATION_RATE";

  public static final String MAX_FLATFARE_CAP = "MAX_FLATFARE_CAP";
  public static final String ADDITIONAL_CHARGE_WC = "ADDITIONAL_CHARGE_*";

  public static final String PAX_SURGE_INDICATOR_THRESHOLD = "PAX_SURGE_INDICATOR_THRESHOLD";
  public static final String PAX_SURGE_INDICATOR_THRESHOLD_0 = "PAX_SURGE_INDICATOR_THRESHOLD_0";
  public static final String LIMO_FLAT_FARE_VEH_IDS = "LIMO_FLAT_FARE_VEH_IDS";
  public static final String EST_LIMO_FLAT_FARE_VEH_IDS = "EST_LIMO_FLAT_FARE_VEH_IDS";
  public static final String EST_FARE_VEH_GROUP_IDS = "EST_FARE_VEH_GROUP_IDS";
  public static final String ADVANCE_VEH_GROUP_IDS = "ADVANCE_VEH_GROUP_IDS";
  public static final String VEH_GRP_SHOW_METER_ONLY = "VEH_GRP_SHOW_METER_ONLY";
  public static final String VEH_GRP_SHOW_FF_ONLY = "VEH_GRP_SHOW_FF_ONLY";
  public static final String LIVE_TRAFFIC_DURATION_UNIT = "LIVE_TRAFFIC_DURATION_UNIT";
  public static final String LIVE_TRAFFIC_DURATION_RATE = "LIVE_TRAFFIC_DURATION_RATE";
  public static final String LIVE_TRAFFIC_FLAG_DOWN_RATE = "LIVE_TRAFFIC_FLAG_DOWN_RATE";
  public static final String LIVE_TRAFFIC_TIER_1_PER_COUNT_FARE =
      "LIVE_TRAFFIC_TIER_1_PER_COUNT_FARE";
  public static final String LIVE_TRAFFIC_TIER_1_PER_COUNT_METER =
      "LIVE_TRAFFIC_TIER_1_PER_COUNT_METER";
  public static final String LIVE_TRAFFIC_TIER_1_START_DISTANCE =
      "LIVE_TRAFFIC_TIER_1_START_DISTANCE";
  public static final String LIVE_TRAFFIC_TIER_1_END_DISTANCE = "LIVE_TRAFFIC_TIER_1_END_DISTANCE";
  public static final String LIVE_TRAFFIC_TIER_2_PER_COUNT_FARE =
      "LIVE_TRAFFIC_TIER_2_PER_COUNT_FARE";
  public static final String LIVE_TRAFFIC_TIER_2_PER_COUNT_METER =
      "LIVE_TRAFFIC_TIER_2_PER_COUNT_METER";
  public static final String LIVE_TRAFFIC_TIER_2_START_DISTANCE =
      "LIVE_TRAFFIC_TIER_2_START_DISTANCE";
  public static final String LIVE_TRAFFIC_TOTAL_FARE_ESTIMATE_LF =
      "LIVE_TRAFFIC_TOTAL_FARE_ESTIMATE_LF";
  public static final String LIVE_TRAFFIC_TOTAL_FARE_ESTIMATE_RT =
      "LIVE_TRAFFIC_TOTAL_FARE_ESTIMATE_RT";
  public static final String EST_LIVE_TRAFFIC_DURATION_UNIT = "EST_LIVE_TRAFFIC_DURATION_UNIT";
  public static final String EST_LIVE_TRAFFIC_DURATION_RATE = "EST_LIVE_TRAFFIC_DURATION_RATE";
  public static final String EST_LIVE_TRAFFIC_FLAG_DOWN_RATE = "EST_LIVE_TRAFFIC_FLAG_DOWN_RATE";
  public static final String EST_LIVE_TRAFFIC_TIER_1_PER_COUNT_FARE =
      "EST_LIVE_TRAFFIC_TIER_1_PER_COUNT_FARE";
  public static final String EST_LIVE_TRAFFIC_TIER_1_PER_COUNT_METER =
      "EST_LIVE_TRAFFIC_TIER_1_PER_COUNT_METER";
  public static final String EST_LIVE_TRAFFIC_TIER_1_START_DISTANCE =
      "EST_LIVE_TRAFFIC_TIER_1_START_DISTANCE";
  public static final String EST_LIVE_TRAFFIC_TIER_1_END_DISTANCE =
      "EST_LIVE_TRAFFIC_TIER_1_END_DISTANCE";
  public static final String EST_LIVE_TRAFFIC_TIER_2_PER_COUNT_FARE =
      "EST_LIVE_TRAFFIC_TIER_2_PER_COUNT_FARE";
  public static final String EST_LIVE_TRAFFIC_TIER_2_PER_COUNT_METER =
      "EST_LIVE_TRAFFIC_TIER_2_PER_COUNT_METER";
  public static final String EST_LIVE_TRAFFIC_TIER_2_START_DISTANCE =
      "EST_LIVE_TRAFFIC_TIER_2_START_DISTANCE";
  public static final String EST_LIVE_TRAFFIC_TIER_2_END_DISTANCE =
      "EST_LIVE_TRAFFIC_TIER_2_END_DISTANCE";
  public static final String EST_LIVE_TRAFFIC_TOTAL_FARE_ESTIMATE_LF =
      "EST_LIVE_TRAFFIC_TOTAL_FARE_ESTIMATE_LF";
  public static final String EST_LIVE_TRAFFIC_TOTAL_FARE_ESTIMATE_RT =
      "EST_LIVE_TRAFFIC_TOTAL_FARE_ESTIMATE_RT";
  public static final String EST_LIMO_LIVE_TRAFFIC_DURATION_UNIT =
      "EST_LIMO_LIVE_TRAFFIC_DURATION_UNIT";
  public static final String EST_LIMO_LIVE_TRAFFIC_DURATION_RATE =
      "EST_LIMO_LIVE_TRAFFIC_DURATION_RATE";
  public static final String EST_LIMO_LIVE_TRAFFIC_FLAG_DOWN_RATE =
      "EST_LIMO_LIVE_TRAFFIC_FLAG_DOWN_RATE";
  public static final String EST_LIMO_LIVE_TRAFFIC_TIER_1_PER_COUNT_FARE =
      "EST_LIMO_LIVE_TRAFFIC_TIER_1_PER_COUNT_FARE";
  public static final String EST_LIMO_LIVE_TRAFFIC_TIER_1_PER_COUNT_METER =
      "EST_LIMO_LIVE_TRAFFIC_TIER_1_PER_COUNT_METER";
  public static final String EST_LIMO_LIVE_TRAFFIC_TIER_1_START_DISTANCE =
      "EST_LIMO_LIVE_TRAFFIC_TIER_1_START_DISTANCE";
  public static final String EST_LIMO_LIVE_TRAFFIC_TIER_1_END_DISTANCE =
      "EST_LIMO_LIVE_TRAFFIC_TIER_1_END_DISTANCE";
  public static final String EST_LIMO_LIVE_TRAFFIC_TIER_2_PER_COUNT_FARE =
      "EST_LIMO_LIVE_TRAFFIC_TIER_2_PER_COUNT_FARE";
  public static final String EST_LIMO_LIVE_TRAFFIC_TIER_2_PER_COUNT_METER =
      "EST_LIMO_LIVE_TRAFFIC_TIER_2_PER_COUNT_METER";
  public static final String EST_LIMO_LIVE_TRAFFIC_TIER_2_START_DISTANCE =
      "EST_LIMO_LIVE_TRAFFIC_TIER_2_START_DISTANCE";
  public static final String EST_LIMO_LIVE_TRAFFIC_TIER_2_END_DISTANCE =
      "EST_LIMO_LIVE_TRAFFIC_TIER_2_END_DISTANCE";
  public static final String EST_LIMO_LIVE_TRAFFIC_TOTAL_FARE_ESTIMATE_LF =
      "EST_LIMO_LIVE_TRAFFIC_TOTAL_FARE_ESTIMATE_LF";
  public static final String EST_LIMO_LIVE_TRAFFIC_TOTAL_FARE_ESTIMATE_RT =
      "EST_LIMO_LIVE_TRAFFIC_TOTAL_FARE_ESTIMATE_RT";
  public static final String DYNAMIC_PRICING_ENABLED = "DYNAMIC_PRICING_ENABLED";
  public static final String DYNAMIC_PRICING_VEH_GROUP_IDS = "DYNAMIC_PRICING_VEH_GROUP_IDS";
  public static final String CACHE_TIMER_MINS_BREAKDOWN_FLATFARE =
      "CACHE_TIMER_MINS_BREAKDOWN_FLATFARE";
  public static final String CACHE_TIMER_MINS_MULTI_FLATFARE = "CACHE_TIMER_MINS_MULTI_FLATFARE";

  /*** Fare Type Config Cache Key ***/
  public static final String DYNP_FLAG_DOWN_RATE = "DYNP_FLAG_DOWN_RATE";

  public static final String DYNP_TIER_1_START_DIST = "DYNP_TIER_1_START_DIST";
  public static final String DYNP_TIER_1_END_DIST = "DYNP_TIER_1_END_DIST";
  public static final String DYNP_TIER_1_PRICE_MULTIPLIER = "DYNP_TIER_1_PRICE_MULTIPLIER";
  public static final String DYNP_TIER_2_START_DIST = "DYNP_TIER_2_START_DIST";
  public static final String DYNP_TIER_2_END_DIST = "DYNP_TIER_2_END_DIST";
  public static final String DYNP_TIER_2_PRICE_MULTIPLIER = "DYNP_TIER_2_PRICE_MULTIPLIER";
  public static final String DYNP_DURATION_RATE = "DYNP_DURATION_RATE";
  public static final String DYNP_PEAK_MIDNIGHT_HOUR_RATE = "DYNP_PEAK_MIDNIGHT_HOUR_RATE";
  public static final String DYNP_BOOKING_FEE = "DYNP_BOOKING_FEE";
  public static final String DYNP_SURGE_BUFFER = "DYNP_SURGE_BUFFER";
  public static final String DYNP_DESURGE_MAX_CAP = "DYNP_DESURGE_MAX_CAP";
  public static final String DYNP_MIN_SURGE_AMOUNT = "DYNP_MIN_SURGE_AMOUNT";
  public static final String DYNP_MIN_CAP = "DYNP_MIN_CAP";
  public static final String DYNP_MAX_CAP = "DYNP_MAX_CAP";
  public static final String ALL = "ALL";
  public static final String HOL = "HOL";
  public static final String LIMO_CONFIG_SET = "LIMO_CONFIG_SET";
  public static final String STANDARD_CONFIG_SET = "STANDARD_CONFIG_SET";
  public static final String EST_STANDARD_CONFIG_SET = "EST_STANDARD_CONFIG_SET";
  public static final String COMMON_CONFIG_SET = "COMMON_CONFIG_SET";
  public static final String FARE_TYPE_CONFIG_SET = "FARE_TYPE_CONFIG_SET";
  public static final String MULTI_DESTINATION_ADDITIONAL_SURCHARGE =
      "MULTI_DESTINATION_ADDITIONAL_SURCHARGE";

  public static final long COUNT_SCAN = 10000;
  public static final String S2_CELL = "S2_CELL";
  public static final String CACHE_CLEAR_SUCCESS_MESSAGE = "Cache Cleared Successfully.";

  public static final String CACHE_KEY_COMMON_CONFIG_SET =
      DYNAMIC_PRICING + COLON + COMMON + COLON + FLAT_FARE + COLON + COMMON_CONFIG_SET;
  public static final String CACHE_KEY_LIMO_CONFIG_SET =
      DYNAMIC_PRICING + COLON + COMMON + COLON + FLAT_FARE + COLON + LIMO_CONFIG_SET;
  public static final String CACHE_KEY_EST_STANDARD_CONFIG_SET =
      DYNAMIC_PRICING + COLON + COMMON + COLON + FLAT_FARE + COLON + EST_STANDARD_CONFIG_SET;

  public static final String CACHE_KEY_FARE_TYPE_CONFIG_SET =
      DYNAMIC_PRICING + COLON + COMMON + COLON + FARE_TYPE + COLON + FARE_TYPE_CONFIG_SET;

  public static final String CACHE_KEY_H3_REGION_SURGE_MODEL_PREFIX =
      DYNAMIC_PRICING + COLON + "H3_REGION_SURGE_MODEL" + COLON;

  public static final String CACHE_KEY_GET_FARE_COUNT_PREFIX =
      DYNAMIC_PRICING + COLON + "GET_FARE_COUNT" + COLON;
}

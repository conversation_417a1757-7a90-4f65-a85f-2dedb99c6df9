package com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.enums.SurgeAreaTypeEnum;
import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@ToString
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class GetFareCountEntity implements Serializable {
  @Serial private static final long serialVersionUID = 5045970196534679151L;

  private SurgeAreaTypeEnum areaType;
  private Long regionId;
  private String regionVersion;
  private Long modelId;
  private Instant requestTime;
}

package com.cdg.pmg.ngp.me.dynamicpricing.utils;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.ObjectOutputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Optional;
import lombok.experimental.UtilityClass;
import org.apache.commons.io.FileUtils;

@UtilityClass
public class FileUploadUtils {

  //  TODO: convertObjectToFile will be called in FareUploadScheduler to convert TaxiFeed object
  // into file

  /**
   * Serializes a given object and writes it to a file.
   *
   * <p>This method takes an Object and a file name as input. It serializes the object into a byte
   * array using {@link ObjectOutputStream} and then writes this byte array to the specified file.
   *
   * <p>Note: The method returns an {@link Optional} of {@link File}. If the operation is
   * successful, the Optional contains the File. Otherwise, it will be empty.
   *
   * @param object the object to be serialized and written to file.
   * @param fileName the name of the file to which the object is to be written. If the file does not
   *     exist, it will be created.
   * @return an {@link Optional} containing the file if successful, or an empty Optional if an
   *     IOException occurs.
   * @throws IOException if an I/O error occurs during writing to the file. This exception is
   *     sneaked as an unchecked exception.
   */
  public static Optional<File> convertObjectToFile(final Object object, final String fileName)
      throws IOException {
    final Path filePath = Paths.get(fileName);
    final ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();

    try {
      if (!filePath.toFile().exists()) {
        Files.createFile(filePath);
      }
      try (OutputStream outputStream = Files.newOutputStream(filePath)) {
        ObjectOutputStream objectOutputStream = new ObjectOutputStream(byteArrayOutputStream);
        objectOutputStream.writeObject(object);
        final byte[] result = byteArrayOutputStream.toByteArray();
        outputStream.write(result);
      }
    } catch (Exception e) {
      throw new IOException("Error while writing object to file", e);
    }
    return Optional.of(filePath.toFile());
  }

  // Clean up: Delete the test file
  public static void cleanUp(final String fileName) throws IOException {
    final File file = new File(fileName);
    if (file.exists()) {
      try {
        FileUtils.forceDelete(file);
      } catch (IOException e) {
        throw new IOException("Failed to delete file: " + fileName, e);
      }
    }
  }
}

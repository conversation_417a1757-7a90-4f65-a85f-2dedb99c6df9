package com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.enums.MappingTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Data class for request field mapping. This class represents a mapping between a request parameter
 * and a configuration.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RequestFieldMapping {
  private MappingTypeEnum mappingType;
  private String requestParameterName;
  private String mappingConfigurationName;

  public boolean isStaticRegionBasedConfiguration() {
    return mappingType == MappingTypeEnum.STATIC_REGION_BASED_CONFIGURATION;
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.entities;

import java.io.Serializable;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class FlatFareHourRateConfCacheEntity implements Serializable {
  private Map<String, Map<String, String>> peakHoursRates;
  private Map<String, Map<String, String>> midnightHoursRate;
  private Map<String, String> otherConfigs;
  private Map<String, String> singleConfigs;
}

package com.cdg.pmg.ngp.me.dynamicpricing.entities;

import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SearchFareBreakdownResponse {
  private String fareId;
  private String tripId;
  private String bookingId;
  private Double flagDownRate;
  private Double waitTimeFare;
  private Long routingDistance;
  private Long ett;
  private Double dpFinalFare;
  private Double meteredBaseFare;
  private BigDecimal totalFare;
  private BigDecimal estimatedFareLF;
  private BigDecimal estimatedFareRT;
  private Long flatPlatformFeeId;
  private Double flatPlatformFee;
  private Long meterPlatformFeeId;
  private Double meterPlatformFeeLower;
  private Double meterPlatformFeeUpper;
  private String updatedBy;
  private List<AdditionalChargeConfigItem> additionalCharges;
}

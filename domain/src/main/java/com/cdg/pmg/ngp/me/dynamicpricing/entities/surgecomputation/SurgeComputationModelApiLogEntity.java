package com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation;

import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Data class for surge computation model API logs. This class represents the domain model for API
 * calls to surge computation models.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SurgeComputationModelApiLogEntity implements Serializable {
  @Serial private static final long serialVersionUID = 1L;

  private Long id;
  private Long modelId;
  private String modelName;
  private String endpointUrl;
  private Instant createTimestamp;
  private Integer statusCode;
  private Object requestParams;
  private Object responseBody;
}

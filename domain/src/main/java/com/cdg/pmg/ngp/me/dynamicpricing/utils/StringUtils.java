package com.cdg.pmg.ngp.me.dynamicpricing.utils;

import java.math.BigDecimal;

public class StringUtils {
  private StringUtils() {}
  /**
   * Concatenates multiple strings into a single string.
   *
   * @param strings Varargs of String objects to concatenate.
   * @return The concatenated string.
   */
  public static String concat(String... strings) {
    StringBuilder sb = new StringBuilder();
    for (String str : strings) {
      if (str != null) {
        sb.append(str);
      }
    }
    return sb.toString();
  }

  /**
   * Converts a string to a Long value.
   *
   * <p>This method first converts the input string to a BigDecimal, then to a BigInteger, and
   * finally to a Long. It throws an ArithmeticException if the BigDecimal is not an exact integer
   * value or if the BigInteger is out of the range of a Long.
   *
   * @param inputString the string to be converted to a Long. It should represent a valid numeric
   *     value.
   * @return the Long representation of the input string.
   * @throws NumberFormatException if the input string is not a valid representation of a
   *     BigDecimal.
   * @throws ArithmeticException if the BigDecimal is not an exact integer value or if the
   *     BigInteger is out of the range of a Long.
   */
  public static Long convertStringToLong(String inputString) {
    return new BigDecimal(inputString).toBigIntegerExact().longValueExact();
  }
}

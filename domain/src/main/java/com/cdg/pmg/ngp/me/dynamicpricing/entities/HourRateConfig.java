package com.cdg.pmg.ngp.me.dynamicpricing.entities;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalTime;
import lombok.Data;
import lombok.NoArgsConstructor;

/** HourRateConfig information */
@Data
@NoArgsConstructor
public class HourRateConfig implements Serializable {

  @Serial private static final long serialVersionUID = -7576819747347819752L;

  private String daysOfWeekIncluded;
  private LocalTime startTime;
  private LocalTime endTime;
  private Double rate;
}

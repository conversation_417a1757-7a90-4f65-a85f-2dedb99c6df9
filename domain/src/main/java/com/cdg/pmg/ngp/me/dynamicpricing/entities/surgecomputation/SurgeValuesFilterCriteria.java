package com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Filter criteria for retrieving surge values with pagination and filtering support. Used to handle
 * large datasets efficiently for monitoring endpoints.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SurgeValuesFilterCriteria implements Serializable {
  @Serial private static final long serialVersionUID = 1L;

  /** Page number for pagination (0-based) */
  private int page;

  /** Number of models per page */
  private int size;

  /** List of specific model IDs to filter by (optional) */
  private List<Long> modelIds;

  /** List of specific model names to filter by (optional) */
  private List<String> modelNames;

  /** Creates default filter criteria with standard pagination settings. */
  public static SurgeValuesFilterCriteria defaultCriteria() {
    return SurgeValuesFilterCriteria.builder().page(0).size(20).build();
  }

  /** Checks if any model filters are applied. */
  public boolean hasModelFilters() {
    return (modelIds != null && !modelIds.isEmpty())
        || (modelNames != null && !modelNames.isEmpty());
  }
}

package com.cdg.pmg.ngp.me.dynamicpricing.entities;

import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class S2CellEntity implements Serializable {

  @Serial private static final long serialVersionUID = 1493628712332623034L;

  private Long s2CellSeqId;
  private String s2CellId;
  private String s2CellTokenId;
  private Double s2CellLatitude;
  private Double s2CellLongitude;
  private Integer s2CellLevel;
  private String s2CellZoneId;
  private String s2CellDesc;
  private String s2CellLocationId;
  private String s2CellLocDesc;
  private String createAt;
  private String createBy;
  private String updateAt;
  private String updateBy;
}

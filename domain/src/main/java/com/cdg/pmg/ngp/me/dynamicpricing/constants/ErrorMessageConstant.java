package com.cdg.pmg.ngp.me.dynamicpricing.constants;

public class ErrorMessageConstant {
  private ErrorMessageConstant() {}

  public static final String DEFAULT_FIXED_NOT_IN_RANGE = "Default fixed must be a number";
  public static final String DEFAULT_PERCENT_NOT_IN_RANGE = "Default percent must be a number";
  public static final String INVALID_START_DATE =
      "Start date must be equal or after from current date";
  public static final String INVALID_END_DATE = "End date must be equal or after from start date";
  public static final String GET_PLATFORM_FEE_FAILED =
      "Get platform fee from Fare Service failed at vehGroupId={0} productId={1} bookingChannel={2}";
  public static final String TRIP_ID_IS_NULL = "Trip id can be null";
  public static final String NOT_FOUND_ROUTE_INFO = "Not found route information by trip id";
  public static final String TRIP_ID_BLANK = "Trip id can be blank";
  public static final String SEARCH_FARE_BREAKDOWN_REQUEST_INVALID =
      "Search fare breakdown request is invalid";
  public static final String NOT_FOUND_FARE_BREAKDOWN = "Not found fare breakdown";

  public static final String NOT_FOUND_PRICING_RANGE_CONFIG = "Not found pricing range config";

  public static final String ERROR_WHILE_SETTING_MINIMUM_CAP =
      "Exception occurred while setting Minimum cap for dynamic pricing total fare";

  public static final String ERROR_WHILE_SETTING_MAXIMUM_CAP =
      "Exception occurred while setting Maximum cap for dynamic pricing total fare";
  public static final String ERROR_WHILE_PARSING_HOLIDAY =
      "Exception occurred while parsing holiday";
  public static final String GET_LIST_DOUBLE_CONFIGS_ERROR =
      "Error when get list double configs from cache";
  public static final String GET_DRIVER_SURGE_CONFIGS_ERROR = "Error to get driver surge config";
  public static final String GET_LIST_EVENT_SURGE_ADDRESS_CONFIG_ERROR =
      "Error at getListEventSurgeAddressConfig";
  public static final String COMPUTE_SURGE_INDICATOR_ERROR =
      "Error at computeSurgeIndicator message";
  public static final String FLAT_FARE_COMPUTED_NOT_VALID = "Flat fare computed is not valid";
  public static final String CALCULATE_BREAKDOWN_FARE_ERROR = "CALCULATE_BREAKDOWN_FARE_ERROR";
  public static final String SETTING_MIN_MAX_CAP_FOR_TOTAL_FARE_ERROR =
      "Exception occurred while setting minimum/maximum cap for dynamic pricing total fare";
  public static final String UPDATE_FARE_TYPE_CONFIG_PARSE_ERROR =
      "ParseException in insertOrUpdateFareTypeConfig when mapping";
  public static final String GET_FARE_BREAK_DOWN_ERROR = "Not found fare breakdown in cache key";
  public static final String FAILED_TO_CREATE_FARE_BREAKDOWN =
      "Create fare breakdown in database failed";
  public static final String ERROR_MAP_TO_ROUTE_INFO = "Error at getGeneratedRouteByTripId";
  public static final String FARE_BREAKDOWN_NOT_FOUND = "Fare breakdown already existed";
  public static final String MULTI_FARE_NOT_FOUND = "Multi fare not found";
  public static final String GET_BOOKING_FEE_FAILED =
      "Get booking fee from Fare Service failed at vehTypeId={0} fareType={1} productId={2} jobType={3} isHoliday={4} requestDate={5}";
  public static final String GET_BOOKING_FEE_LIST_FAILED =
      "Get booking fee by list from Fare Service failed at jobType={0} isHoliday={1} requestDate={2}";
  public static final String GET_ROUTE_FAILED = "Get route from address service failed";
  public static final String INVALID_ROUTE = "Invalid route";
  public static final String NOT_FOUND_ROUTE_MSG = "Not found route from address service";
  public static final String INVALID_BOOKING_CHANNEL = "Invalid booking channel";
  public static final String INVALID_JOB_TYPE = "Invalid job type";
  public static final String NO_VEH_TYPE_ID_AVAILABLE = "No vehTypeId available to calculate fare";
  public static final String FIELD_SHOULD_NOT_BE_NULL = "{0} field should not be null";
  public static final String ZONE_ID_INVALID = "Zone id ''{0}'' is invalid";
  public static final String LIST_OF_VALUES = "{0} field should in list of value {1}";
  public static final String START_DT_AND_END_DT_INVALID =
      "Start date time and end date time are invalid, currentDate must be between startDt and endDt";
  public static final String NOT_FOUND_NEW_PRICING_MODEL_ID =
      "Not found for new pricing model index {0}";

  public static final String NOT_FOUND_PROPERTY_ID = "Not found for new pricing model id {0}";
  public static final String DUPLICATE_VALUE = "Value {0} is duplicated";
  public static final String NOT_FOUND_VALUE = "Value {0} not found in {1}";
  public static final String JSON_PARSE_ERROR = "JSON parse exception";
  public static final String NEGATIVE_ERROR = "Field {0} must greater or equals to zero";
  public static final String INVALID_CONFIG_TYPE = "Invalid config type";
  public static final String SEARCH_FARE_BREAKDOWN_CONVERT_ERROR =
      "Search fare breakdown convert data from CN3 to NGP error";
  public static final String SEARCH_FARE_BREAKDOWN_FOUND =
      "Search fare breakdown bookingId={0} fareId={1} tripId={2}: {3}";
  public static final String CALC_LOC_SURCHARGE_DYNP_ERROR =
      "Calc loc surcharge for dynamic pricing error";
  public static final String CALC_EVENT_SURCHARGE_DYNP_ERROR =
      "Calc event surcharge for dynamic pricing error";
  public static final String INVALID_VEH_TYPE_ID = "Invalid vehicle type id";

  public static final String NOT_FOUND_LOCATION_SURCHARGE = "Not found location surcharge";
  public static final String GET_S2_CELL_CACHE_FAILED = "Get S2 Cell from cache failed";
  public static final String GET_S2_CELL_DB_FAILED = "Get S2 Cell from database failed";
  public static final String ERROR_TO_PARSING_JSON = "Errors occurred while parsing JSON data";
  public static final String ERROR_TO_CONVERTING_TO_JSON = "Error Converting object to json";

  public static final String ERROR_UPLOAD_FILE = "Error in uploading Book-A-Ride file to FTP";

  public static final String ERROR_BUILDING_TAXI_AREA =
      "Exception occurred while building TaxiArea";
  public static final String GET_ADDITIONAL_CHARGE_FEE_CONFIG_FAILED =
      "Get additional charge fee config from Fare Service failed at chargeIds={0} chargeTypes={1} bookingChannel={2}";
  public static final String GET_ADDITIONAL_CHARGE_FEE_CONFIG_BY_ID_FAILED =
      "Get additional charge fee config from Fare Service failed at chargeId={0}";
  public static final String RELOAD_ALL_CONFIG_FAIL = "Reload all config fail";
  public static final String UPDATE_CBD_ADDRESS_BAD_REQUEST = "CBD Address list is empty!";
  public static final String RELOAD_CBD_CONFIG_CACHE_FAIL = "Reload CBD Address config Cache fail!";
  public static final String NOT_FOUND_REGION_MODEL_DISTRIBUTION =
      "Not found region model distribution for id {0}";
  public static final String NOT_FOUND_SURGE_COMPUTATION_MODEL =
      "Not found surge computation model";
  public static final String SURGE_COMPUTATION_MODEL_PERCENTAGE_ERROR =
      "The sum of all percentages must be 100, but got {0}";
  public static final String SURGE_COMPUTATION_MODEL_OVERLAPPING_DATES_ERROR =
      "There is overlapping effective dates in this region";
  public static final String REGION_MODEL_DISTRIBUTION_CREATE_ERROR =
      "Failed to create region model distribution";
  public static final String GET_EFFECTIVE_H3_REGIONS_FAILED = "Get effective h3 regions failed";
  public static final String NOT_FOUND_SURGE_COMPUTATION_MAPPING_TYPE =
      "Surge computation mapping type not found";
  public static final String REGION_DEMAND_SUPPLY_STATISTICS_ERROR =
      "Region demand supply statistics are empty";
  public static final String SURGE_COMPUTATION_MODEL_DUPLICATE_NAME_ERROR =
      "Model name is duplicated";
  public static final String SURGE_COMPUTATION_MODEL_CREATE_ERROR =
      "Failed to create surge computation model";
  public static final String SURGE_COMPUTATION_MODEL_UPDATE_ERROR =
      "Failed to update surge computation model";
  public static final String SURGE_COMPUTATION_MODEL_DELETE_ERROR =
      "This surge model is used by surge model distribution, can not be deleted";
  public static final String SURGE_COMPUTATION_MODEL_TIME_BASED_CONFIGURATION_CREATE_ERROR =
      "Failed to create surge computation model time based configuration";
  public static final String SURGE_COMPUTATION_MODEL_TIME_BASED_CONFIGURATION_UPDATE_ERROR =
      "Failed to update surge computation model time based configuration";
  public static final String
      SURGE_COMPUTATION_MODEL_TIME_BASED_CONFIGURATION_OVERLAPPING_DATES_ERROR =
          "There is overlapping effective date ranges";
  public static final String
      SURGE_COMPUTATION_MODEL_REGION_BASED_CONFIGURATION_OVERLAPPING_DATES_ERROR =
          "There is overlapping effective date ranges";
  public static final String SURGE_COMPUTATION_MODEL_REGION_BASED_CONFIGURATION_CREATE_ERROR =
      "Failed to create surge computation model region based configuration";
  public static final String SURGE_COMPUTATION_MODEL_REGION_BASED_CONFIGURATION_UPDATE_ERROR =
      "Failed to update surge computation model region based configuration";
  public static final String
      SURGE_COMPUTATION_MODEL_REGION_BASED_CONFIGURATION_NAME_MISMATCH_ERROR =
          "The configuration names in the request do not match the ones defined in the model";
  public static final String
      SURGE_COMPUTATION_MODEL_STATIC_BASED_CONFIGURATION_VERSION_NOT_SAME_ERROR =
          "All the versions and effective period and time zone offset must be same";
  public static final String SURGE_COMPUTATION_MODEL_STATIC_BASED_CONFIGURATION_TIME_ZONE_ERROR =
      "The time zone offset is invalid";
  public static final String REGION_MODEL_DISTRIBUTION_INVALID_REGION_ID = "Invalid region id {0}";
  public static final String REGION_MODEL_DISTRIBUTION_INVALID_MODEL_ID =
      "Invalid model ids {0} for regionId {1}, model ids not found in system";
  public static final String REGION_MODEL_DISTRIBUTION_MISSING_MODEL_ID =
      "Missing model ids {0} for regionId {1}, all model ids must be included";
  public static final String REGION_MODEL_DISTRIBUTION_INVALID_REGION_IDS =
      "Invalid region ids {0}, region ids not found in system";
  public static final String REGION_MODEL_DISTRIBUTION_MISSING_REGION_IDS =
      "Missing region ids {0}, all region ids must be included";
}

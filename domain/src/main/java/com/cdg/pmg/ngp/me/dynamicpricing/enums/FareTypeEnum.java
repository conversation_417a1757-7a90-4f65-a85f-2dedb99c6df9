package com.cdg.pmg.ngp.me.dynamicpricing.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum FareTypeEnum {
  EST_LIVE_TRAFFIC("EST_LIVE_TRAFFIC", "EST_LIVE_TRAFFIC_"),
  EST_LIMO_LIVE_TRAFFIC("EST_LIMO_LIVE_TRAFFIC", "EST_LIMO_LIVE_TRAFFIC_");

  private String value;
  private String prefix;

  FareTypeEnum(String value, String prefix) {
    this.value = value;
    this.prefix = prefix;
  }
}

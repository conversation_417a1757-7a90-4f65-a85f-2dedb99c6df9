package com.cdg.pmg.ngp.me.dynamicpricing.enums;

import com.cdg.pmg.ngp.me.dynamicpricing.message.MessageKey;
import lombok.RequiredArgsConstructor;

/** The enum Common message key. */
@RequiredArgsConstructor
public enum CommonMessageKey implements MessageKey {

  /** Invalid state common message key. */
  INVALID_STATE("info.message.please-refresh-page"),
  /** Something went wrong common message key. */
  SOMETHING_WENT_WRONG("error.message.something-went-wrong"),
  /** Empty result common message key. */
  EMPTY_RESULT("MSG101"),
  /** Illegal argument common message key. */
  ILLEGAL_ARGUMENT("MSG2"),
  /** Invalid common field common message key. */
  INVALID_COMMON_FIELD("MSG23");
  private final String key;

  @Override
  public String getKey() {
    return this.key;
  }
}

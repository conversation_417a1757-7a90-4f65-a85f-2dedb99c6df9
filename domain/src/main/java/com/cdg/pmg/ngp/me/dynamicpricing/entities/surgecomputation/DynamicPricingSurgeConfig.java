package com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation;

import java.util.concurrent.ThreadLocalRandom;
import lombok.Setter;

@Setter
public class DynamicPricingSurgeConfig {

  /**
   * The global rate config how many percent request will use region fare, min 0, max 100.
   *
   * <p>e.g.: value = 80, means:
   *
   * <ul>
   *   <li>random num in (0,80] will use region based
   *   <li>random num in (80,100] will use zone based
   * </ul>
   */
  private int globalRateToRegionForGetFare;

  public boolean isRandomToRegion() {
    // Make sure the rate value is in [0,100]
    int rateToRegion = Math.max(0, Math.min(100, globalRateToRegionForGetFare));

    int value = ThreadLocalRandom.current().nextInt(1, 101); // [1, 101) -> (0, 100]
    return value <= rateToRegion;
  }

  /**
   * The get fare count sampling period in minutes.
   *
   * <ul>
   *   <li>"10,0", means between (now - 10) and (now - 0)
   *   <li>"60,30", means between (now - 60) and (now - 30)
   * </ul>
   */
  private String dynamicPricingSurgeSamplingPeriodInMinutes;

  public DynamicPricingSurgeSamplingPeriod getSamplingPeriod() {
    String[] arr = dynamicPricingSurgeSamplingPeriodInMinutes.split(",");
    return new DynamicPricingSurgeSamplingPeriod(
        Integer.parseInt(arr[0]), Integer.parseInt(arr[1]));
  }
}

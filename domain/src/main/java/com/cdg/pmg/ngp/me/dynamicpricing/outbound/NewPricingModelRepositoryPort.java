package com.cdg.pmg.ngp.me.dynamicpricing.outbound;

import static com.cdg.pmg.ngp.me.dynamicpricing.constants.CommonConstant.NEW_PRICING_MODEL_SUFFIX;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.NewPricingModelConfigEntity;
import java.util.List;

public interface NewPricingModelRepositoryPort {

  NewPricingModelConfigEntity update(NewPricingModelConfigEntity newPricingModelConfigEntity);

  NewPricingModelConfigEntity create(
      NewPricingModelConfigEntity newPricingModelConfigEntity, int index);

  List<NewPricingModelConfigEntity> getListNewPricingModelConfigEntityInCms();

  static String generateDescriptionKey(Integer index) {
    return "-" + index + "-" + NEW_PRICING_MODEL_SUFFIX;
  }
}

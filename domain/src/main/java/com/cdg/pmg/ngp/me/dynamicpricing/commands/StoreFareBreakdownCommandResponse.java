package com.cdg.pmg.ngp.me.dynamicpricing.commands;

import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StoreFareBreakdownCommandResponse implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  private Boolean success;
}

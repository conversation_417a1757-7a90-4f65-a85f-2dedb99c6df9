package com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ModelPercentage implements Serializable {
  @Serial private static final long serialVersionUID = -8809644566361877756L;

  private Long modelId;
  private BigDecimal percentage;

  // Only for expansion, will not store in db
  private String modelName;
}

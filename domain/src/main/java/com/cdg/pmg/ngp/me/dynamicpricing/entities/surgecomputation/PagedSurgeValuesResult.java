package com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Paginated result for surge values containing both data and pagination metadata. Used to
 * efficiently handle large datasets for monitoring endpoints.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PagedSurgeValuesResult implements Serializable {
  @Serial private static final long serialVersionUID = 1L;

  /** List of model surge data for the current page */
  private List<ModelSurgeDataEntity> data;

  /** Current page number (0-based) */
  private int page;

  /** Number of items per page */
  private int size;

  /** Total number of models available */
  private long totalElements;

  /** Total number of pages */
  private int totalPages;

  /** Whether there are more pages available */
  private boolean hasNext;

  /** Whether there are previous pages available */
  private boolean hasPrevious;

  /** Creates a paginated result from a list of models and pagination info. */
  public static PagedSurgeValuesResult of(
      List<ModelSurgeDataEntity> data, int page, int size, long totalElements) {

    int totalPages = (int) Math.ceil((double) totalElements / size);

    return PagedSurgeValuesResult.builder()
        .data(data)
        .page(page)
        .size(size)
        .totalElements(totalElements)
        .totalPages(totalPages)
        .hasNext(page < totalPages - 1)
        .hasPrevious(page > 0)
        .build();
  }
}

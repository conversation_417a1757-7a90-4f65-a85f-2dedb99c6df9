package com.cdg.pmg.ngp.me.dynamicpricing.entities;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class LocationSurchargeConfigCacheEntity implements Serializable {

  @Serial private static final long serialVersionUID = -6989326383759563464L;

  private Map<String, List<LocationSurchargeConfigEntity>> locationSurchargeMapByDayAndAddressRef;
}

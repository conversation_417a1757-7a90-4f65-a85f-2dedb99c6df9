package com.cdg.pmg.ngp.me.dynamicpricing.entities;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Builder;
import lombok.Data;

/** This class stores all configurations needed for calculating fare */
@Data
@Builder
public class FareUploadConfiguration {
  @Builder.Default private Map<String, Integer> surgeConfig = new HashMap<>();
  @Builder.Default private List<String> holidayConfig = new ArrayList<>();

  @Builder.Default
  private Map<Integer, List<LocationSurchargeConfigEntity>> locationSurChargeConfig =
      new HashMap<>();

  @Builder.Default private Map<String, HourRateConfig> peakHourRateConfig = new HashMap<>();
  @Builder.Default private Map<String, HourRateConfig> midNightRateConfig = new HashMap<>();

  @Builder.Default private Map<Integer, Integer> endPointSurchargeAreas = new HashMap<>();
  @Builder.Default private Map<String, String> flatFareConfig = new HashMap<>();
  @Builder.Default private List<S2CellEntity> s2CellList = new ArrayList<>();
  @Builder.Default private List<BookingFareConfig> bookingFareConfig = new ArrayList<>();
  @Builder.Default private Map<String, Integer> locationPickupIndex = new HashMap<>();

  @Builder.Default
  private Map<String, DynamicPricingTimeConfig> dynamicPricingTimeConfig = new HashMap<>();

  @Builder.Default private Map<String, String> bookARideConfiguration = new HashMap<>();

  @Builder.Default
  private Map<Integer, Map<String, VGProductFareBean>> vgProductFareMap = new HashMap<>();
  //    TODO: The others field will be added in another ticket
}

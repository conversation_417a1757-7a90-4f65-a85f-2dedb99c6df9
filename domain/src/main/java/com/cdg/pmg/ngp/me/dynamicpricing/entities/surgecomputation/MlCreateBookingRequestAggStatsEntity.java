package com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.DomainEntity;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/** Entity class representing booking creation fare information for machine learning. */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MlCreateBookingRequestAggStatsEntity implements Serializable, DomainEntity<Long> {

  @Serial private static final long serialVersionUID = 1L;

  private Long id;
  private String bookingId;
  private String fareId;
  private Instant fareCalcTime;
  private Long estimatedTripTime;
  private Long distance;
  private String areaType;
  private String pickupAddressRef;
  private String destAddressRef;
  private Long pickupRegionId;
  private String regionVersion;
  private Long modelId;
  private Double meteredBaseFare;
  private BigDecimal totalFare;
  private Double dpBaseFareForSurge;
  private Double dpSurgePer;
  private Double dpFinalFare;
  private BigDecimal estimatedFareLF;
  private BigDecimal estimatedFareRT;
  private Double meterPlatformFeeLower;
  private Double meterPlatformFeeUpper;
  private Double flatPlatformFee;
  private Instant createTimestamp;
}

package com.cdg.pmg.ngp.me.dynamicpricing.queries;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PricingRangeCalDemandSurgeQueryResponse implements Serializable {
  Integer surgeLow;
  Integer surgeHigh;
  Integer stepPositive;
  Integer stepNegative;
  String zoneId;
}

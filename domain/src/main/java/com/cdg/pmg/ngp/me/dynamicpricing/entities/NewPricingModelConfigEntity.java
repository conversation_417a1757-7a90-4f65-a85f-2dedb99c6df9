package com.cdg.pmg.ngp.me.dynamicpricing.entities;

import java.io.Serial;
import java.io.Serializable;
import java.time.OffsetDateTime;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class NewPricingModelConfigEntity implements Serializable {
  @Serial private static final long serialVersionUID = -1817016189253350832L;

  private Long id;
  private Integer index;
  private String zoneId;
  private OffsetDateTime startDt;
  private OffsetDateTime endDt;
  private Integer additionalSurgeHigh;
  private Double surgeHighTierRate;
  private Double unmetRate1;
  private Double unmetRate2;
  private Double negativeDemandSupplyDownRate;
  private String createdBy;
  private OffsetDateTime createdDt;
  private String updatedBy;
  private OffsetDateTime updatedDt;
  private Double k1;
  private Double k2;
  private Double k3;
  private Double k4;
  private Double k5;
  private Double k6;
  private Double k7;
  private Double k8;
  private String zonePriceVersion;

  @Override
  public final boolean equals(Object object) {
    if (this == object) return true;
    if (!(object instanceof NewPricingModelConfigEntity that)) return false;

    return Objects.equals(id, that.id)
        && Objects.equals(index, that.index)
        && Objects.equals(zoneId, that.zoneId)
        && Objects.equals(startDt, that.startDt)
        && Objects.equals(endDt, that.endDt)
        && Objects.equals(additionalSurgeHigh, that.additionalSurgeHigh)
        && Objects.equals(surgeHighTierRate, that.surgeHighTierRate)
        && Objects.equals(unmetRate1, that.unmetRate1)
        && Objects.equals(unmetRate2, that.unmetRate2)
        && Objects.equals(negativeDemandSupplyDownRate, that.negativeDemandSupplyDownRate)
        && Objects.equals(createdBy, that.createdBy)
        && Objects.equals(createdDt, that.createdDt)
        && Objects.equals(k1, that.k1)
        && Objects.equals(k2, that.k2)
        && Objects.equals(k3, that.k3)
        && Objects.equals(k4, that.k4)
        && Objects.equals(k5, that.k5)
        && Objects.equals(k6, that.k6)
        && Objects.equals(k7, that.k7)
        && Objects.equals(k8, that.k8)
        && Objects.equals(zonePriceVersion, that.zonePriceVersion);
  }

  @Override
  public int hashCode() {
    int result = Objects.hashCode(id);
    result = 31 * result + Objects.hashCode(index);
    result = 31 * result + Objects.hashCode(zoneId);
    result = 31 * result + Objects.hashCode(startDt);
    result = 31 * result + Objects.hashCode(endDt);
    result = 31 * result + Objects.hashCode(additionalSurgeHigh);
    result = 31 * result + Objects.hashCode(surgeHighTierRate);
    result = 31 * result + Objects.hashCode(unmetRate1);
    result = 31 * result + Objects.hashCode(unmetRate2);
    result = 31 * result + Objects.hashCode(negativeDemandSupplyDownRate);
    result = 31 * result + Objects.hashCode(createdBy);
    result = 31 * result + Objects.hashCode(createdDt);
    result = 31 * result + Objects.hashCode(k1);
    result = 31 * result + Objects.hashCode(k2);
    result = 31 * result + Objects.hashCode(k3);
    result = 31 * result + Objects.hashCode(k4);
    result = 31 * result + Objects.hashCode(k5);
    result = 31 * result + Objects.hashCode(k6);
    result = 31 * result + Objects.hashCode(k7);
    result = 31 * result + Objects.hashCode(k8);
    result = 31 * result + Objects.hashCode(zonePriceVersion);
    return result;
  }
}

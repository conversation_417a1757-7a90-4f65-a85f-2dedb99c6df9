package com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Data class for surge computation model surges. This class represents the domain model for surge
 * values calculated by the surge computation model service.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SurgeComputationModelSurgeEntity implements Serializable {
  @Serial private static final long serialVersionUID = 1L;

  private Long id;
  private Long modelId;
  private Long regionId;
  private BigDecimal prevSurge;
  private BigDecimal surge;
  private Instant lastUpdDt;
}

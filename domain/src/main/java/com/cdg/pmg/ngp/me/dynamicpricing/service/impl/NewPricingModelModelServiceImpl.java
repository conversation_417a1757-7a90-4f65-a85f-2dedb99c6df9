package com.cdg.pmg.ngp.me.dynamicpricing.service.impl;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.ConfigKeyConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.NewPricingModelConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.BadRequestException;
import com.cdg.pmg.ngp.me.dynamicpricing.outbound.NewPricingModelRepositoryPort;
import com.cdg.pmg.ngp.me.dynamicpricing.outbound.ZoneInfoRepositoryOutboundPort;
import com.cdg.pmg.ngp.me.dynamicpricing.service.NewPricingModelService;
import java.text.MessageFormat;
import java.time.OffsetDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;

@ServiceComponent
@RequiredArgsConstructor
public class NewPricingModelModelServiceImpl implements NewPricingModelService {

  private final ZoneInfoRepositoryOutboundPort zoneInfoRepository;
  private final NewPricingModelRepositoryPort newPricingModelRepository;

  @Override
  public void validate(NewPricingModelConfigEntity newPricingModel) {
    validate(newPricingModel, true);
  }

  private void validate(NewPricingModelConfigEntity newPricingModel, boolean shouldCheckZone) {
    validateZonePriceVersion(newPricingModel);
    validateByZonePriceVersion(newPricingModel, shouldCheckZone);
    if (shouldCheckZone) {
      validateZoneId(newPricingModel);
    }
  }

  @Override
  public void validateIgnoreZoneCheck(NewPricingModelConfigEntity newPricingModel) {
    validate(newPricingModel, false);
  }

  @Override
  public List<NewPricingModelConfigEntity> getListNewPricingModelConfigEntityInCms() {
    return newPricingModelRepository.getListNewPricingModelConfigEntityInCms();
  }

  @Override
  public NewPricingModelConfigEntity update(
      NewPricingModelConfigEntity newPricingModelConfigEntity) {
    return newPricingModelRepository.update(newPricingModelConfigEntity);
  }

  @Override
  public NewPricingModelConfigEntity create(
      NewPricingModelConfigEntity newPricingModelConfigEntity, int index) {
    return newPricingModelRepository.create(newPricingModelConfigEntity, index);
  }

  private void validateZoneId(NewPricingModelConfigEntity newPricingModel) {
    // This only used when perform create new pricing model config entity so it doesn't impact
    // performance
    if (!zoneInfoRepository.checkZoneIdExists(newPricingModel.getZoneId())) {
      throw new BadRequestException(
          MessageFormat.format(ErrorEnum.ZONE_ID_INVALID.getMessage(), newPricingModel.getZoneId()),
          ErrorEnum.ZONE_ID_INVALID.getErrorCode());
    }
  }

  private void validateZonePriceVersion(NewPricingModelConfigEntity newPricingModel) {
    var versions =
        Arrays.asList(
            ConfigKeyConstant.V1,
            ConfigKeyConstant.V2,
            ConfigKeyConstant.V3,
            ConfigKeyConstant.V2_POINT_5);
    if (!versions.contains(newPricingModel.getZonePriceVersion())) {
      throw new BadRequestException(
          MessageFormat.format(
              ErrorEnum.LIST_OF_VALUES.getMessage(), "zonePriceVersion", versions.toString()),
          ErrorEnum.LIST_OF_VALUES.getErrorCode());
    }
  }

  private void validateStartDtAndEndDt(NewPricingModelConfigEntity newPricingModel) {
    var startDt = newPricingModel.getStartDt();
    var endDt = newPricingModel.getEndDt();
    var currentDate = OffsetDateTime.now();
    // CurrentDate >= startDate
    // CurrentDate <= endDate
    if (!((currentDate.isEqual(startDt) || currentDate.isAfter(startDt))
        && (currentDate.isEqual(endDt) || currentDate.isBefore(endDt)))) {
      String message = ErrorEnum.START_DT_AND_END_DT_INVALID.getMessage();
      throw new BadRequestException(message, ErrorEnum.START_DT_AND_END_DT_INVALID.getErrorCode());
    }
  }

  private void validateByZonePriceVersion(
      NewPricingModelConfigEntity newPricingModel, boolean validateDateTime) {
    if (Arrays.asList(ConfigKeyConstant.V2, ConfigKeyConstant.V3, ConfigKeyConstant.V2_POINT_5)
        .contains(newPricingModel.getZonePriceVersion())) {
      validateNotNullField(newPricingModel.getZoneId(), "zoneId");
      validateNotNullField(newPricingModel.getStartDt(), "startDt");
      validateNotNullField(newPricingModel.getEndDt(), "endDt");
      if (validateDateTime) {
        validateStartDtAndEndDt(newPricingModel);
      }
      validateNotNullField(newPricingModel.getAdditionalSurgeHigh(), "additionalSurgeHigh");
      validatePositiveValue(newPricingModel.getAdditionalSurgeHigh(), "additionalSurgeHigh");
      if (ConfigKeyConstant.V2.equals(newPricingModel.getZonePriceVersion())
          || ConfigKeyConstant.V2_POINT_5.equals(newPricingModel.getZonePriceVersion())) {
        validateRequireFieldsV2AndV2Point5(newPricingModel);
      } else if (ConfigKeyConstant.V3.equals(newPricingModel.getZonePriceVersion())) {
        validateRequireFieldsV3(newPricingModel);
      }
    }
  }

  private void validateRequireFieldsV3(NewPricingModelConfigEntity newPricingModel) {
    validateNotNullField(newPricingModel.getK1(), "k1");
    validatePositiveValue(newPricingModel.getK1(), "k1");
    validateNotNullField(newPricingModel.getK2(), "k2");
    validatePositiveValue(newPricingModel.getK2(), "k2");
    validateNotNullField(newPricingModel.getK3(), "k3");
    validatePositiveValue(newPricingModel.getK3(), "k3");
    validateNotNullField(newPricingModel.getK4(), "k4");
    validatePositiveValue(newPricingModel.getK4(), "k4");
    validateNotNullField(newPricingModel.getK5(), "k5");
    validatePositiveValue(newPricingModel.getK5(), "k5");
    validateNotNullField(newPricingModel.getK6(), "k6");
    validatePositiveValue(newPricingModel.getK6(), "k6");
    validateNotNullField(newPricingModel.getK7(), "k7");
    validatePositiveValue(newPricingModel.getK7(), "k7");
    validateNotNullField(newPricingModel.getK8(), "k8");
    validatePositiveValue(newPricingModel.getK8(), "k8");
  }

  private void validateRequireFieldsV2AndV2Point5(NewPricingModelConfigEntity newPricingModel) {
    validateNotNullField(newPricingModel.getSurgeHighTierRate(), "surgeHighTierRate");
    validatePositiveValue(newPricingModel.getSurgeHighTierRate(), "surgeHighTierRate");
    validateNotNullField(newPricingModel.getUnmetRate1(), "unmetRate1");
    validatePositiveValue(newPricingModel.getUnmetRate1(), "unmetRate1");
    validateNotNullField(newPricingModel.getUnmetRate2(), "unmetRate2");
    validatePositiveValue(newPricingModel.getUnmetRate2(), "unmetRate2");
    validateNotNullField(
        newPricingModel.getNegativeDemandSupplyDownRate(), "negativeDemandSupplyDownRate");
    validatePositiveValue(
        newPricingModel.getNegativeDemandSupplyDownRate(), "negativeDemandSupplyDownRate");
  }

  private void validatePositiveValue(Number number, String fieldName) {
    if (number.doubleValue() < 0) {
      throw new BadRequestException(
          MessageFormat.format(ErrorEnum.NEGATIVE_ERROR.getMessage(), fieldName),
          ErrorEnum.NEGATIVE_ERROR.getErrorCode());
    }
  }

  private void validateNotNullField(Object value, String fieldName) {
    if (Objects.isNull(value)) {
      throw new BadRequestException(
          MessageFormat.format(ErrorEnum.NULL_VALUE.getMessage(), fieldName),
          ErrorEnum.NULL_VALUE.getErrorCode());
    }
  }
}

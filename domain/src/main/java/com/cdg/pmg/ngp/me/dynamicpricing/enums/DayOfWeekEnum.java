package com.cdg.pmg.ngp.me.dynamicpricing.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * Enum representing days of the week and public holidays. Used for surge computation time-based
 * static configurations.
 */
@Getter
@RequiredArgsConstructor
public enum DayOfWeekEnum {
  MON("MON", 1),
  TUE("TUE", 2),
  WED("WED", 3),
  THU("THU", 4),
  FRI("FRI", 5),
  SAT("SAT", 6),
  SUN("SUN", 7),
  PUBLIC_HOLIDAY("PUBLIC_HOLIDAY", 0);

  private final String value;
  private final Integer code;

  /**
   * Get DayOfWeekEnum by code.
   *
   * @param code the integer code (0-7)
   * @return the corresponding DayOfWeekEnum, or null if not found
   */
  public static DayOfWeekEnum getByCode(Integer code) {
    if (code == null) {
      return null;
    }
    for (DayOfWeekEnum dayOfWeek : values()) {
      if (dayOfWeek.getCode().equals(code)) {
        return dayOfWeek;
      }
    }
    return null;
  }

  /**
   * Get DayOfWeekEnum by value.
   *
   * @param value the string value
   * @return the corresponding DayOfWeekEnum, or null if not found
   */
  public static DayOfWeekEnum getByValue(String value) {
    if (value == null) {
      return null;
    }
    for (DayOfWeekEnum dayOfWeek : values()) {
      if (dayOfWeek.getValue().equals(value)) {
        return dayOfWeek;
      }
    }
    return null;
  }
}

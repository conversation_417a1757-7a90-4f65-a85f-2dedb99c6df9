package com.cdg.pmg.ngp.me.dynamicpricing.utils;

import com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.FareTypeEnum;

/***
 * <PERSON><PERSON><PERSON>eyUtils - Helper class to get the cache key
 *
 * <AUTHOR>
 */
public class CacheKeyUtils {

  public static String getFlatFarePrefix() {
    return RedisKeyConstant.DYNAMIC_PRICING_FLAT_FARE_PREFIX;
  }

  public static String getFlatFarePrefix(FareTypeEnum fareTypeEnum) {
    return RedisKeyConstant.DYNAMIC_PRICING_FLAT_FARE_PREFIX + fareTypeEnum.getPrefix();
  }

  public static String getFlatFareTierPrefix(FareTypeEnum fareTypeEnum, int tier) {
    return CacheKeyUtils.getFlatFarePrefix(fareTypeEnum)
        + (tier == 1 ? RedisKeyConstant.TIER_1 : RedisKeyConstant.TIER_2);
  }
}

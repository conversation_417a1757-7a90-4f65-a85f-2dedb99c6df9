package com.cdg.pmg.ngp.me.dynamicpricing.constants;

public class ConfigKeyConstant {
  private ConfigKeyConstant() {}

  /*** Flat fare config key ***/
  public static final String PEAK_HOUR_PREFIX = "PEAK_HOUR_";

  public static final String MIDNIGHT_PREFIX = "MID_NIGHT_";
  public static final String EVENT_SURGE_ADDR = "EVENT_SURGE_ADDR_";
  public static final String EVENT_SURGE_ZONE = "EVENT_SURGE_ZONE_";
  public static final String ADDITIONAL_CHARGE = "ADDITIONAL_CHARGE_";
  public static final String DYNAMIC_PRICING_START_TIME = "_DYNAMIC_PRICING_START_TIME";
  public static final String DYNAMIC_PRICING_END_TIME = "_DYNAMIC_PRICING_END_TIME";
  public static final String LIVE_TRAFFIC_PREFIX = "LIVE_TRAFFIC_";
  public static final String EST_LIVE_TRAFFIC_PREFIX = "EST_LIVE_TRAFFIC_";
  public static final String EST_LIMO_LIVE_TRAFFIC_PREFIX = "EST_LIMO_LIVE_TRAFFIC_";
  public static final String EST_WAIT_TIME_PREFIX = "EST_WAIT_TIME_";
  public static final String DRV_SURGE_LEVEL = "_DRV_SURGE_LEVEL";
  public static final String DRV_SURGE_PERC_FROM = "_DRV_SURGE_PERC_FROM";
  public static final String DRV_SURGE_PERC_TO = "_DRV_SURGE_PERC_TO";
  public static final String DRV_SURGE_COLOR_HEX = "_DRV_SURGE_COLOR_HEX";
  public static final String DRV_EFFECT_FROM_TS = "_DRV_EFFECT_FROM_TS";
  public static final String DRV_EFFECT_TO_TS = "_DRV_EFFECT_TO_TS";
  public static final String START_TIME = "_START_TIME";
  public static final String END_TIME = "_END_TIME";
  public static final String DAYS = "_DAYS";
  public static final String IS_LIVE_TRAFFIC = "IS_LIVE_TRAFFIC";
  public static final String EST_WAIT_TIME_PEAK_HOUR = "EST_WAIT_TIME_PEAK_HOUR";
  public static final String EST_WAIT_TIME_MID_NIGHT = "EST_WAIT_TIME_MID_NIGHT";
  public static final String EST_LIVE_TRAFFIC_PEAK_HOUR = "EST_LIVE_TRAFFIC_PEAK_HOUR";
  public static final String EST_LIVE_TRAFFIC_MID_NIGHT = "EST_LIVE_TRAFFIC_MID_NIGHT";
  public static final String DYNP_MIN_CAP = "DYNP_MIN_CAP";
  public static final String DYNP_BOOKING_FEE = "DYNP_BOOKING_FEE";

  // FlatFareConfigServiceImplTest constant
  public static final String EVENT_SURGE_ADDR_DAYS_0 = "EVENT_SURGE_ADDR_DAYS_0";
  public static final String EVENT_SURGE_ZONE_DAYS_0 = "EVENT_SURGE_ZONE_DAYS_0";
  public static final String ADDITIONAL_CHARGE_0 = "ADDITIONAL_CHARGE_0";
  public static final String LIVE_TRAFFIC_PEAK_HOUR_DAYS_0 = "LIVE_TRAFFIC_PEAK_HOUR_DAYS_0";
  public static final String LIVE_TRAFFIC_MID_NIGHT_DAYS_0 = "LIVE_TRAFFIC_MID_NIGHT_DAYS_0";
  public static final String LIVE_TRAFFIC_TIER_2_END_DISTANCE = "LIVE_TRAFFIC_TIER_2_END_DISTANCE";
  public static final String EST_LIVE_TRAFFIC_PEAK_HOUR_DAYS_0 =
      "EST_LIVE_TRAFFIC_PEAK_HOUR_DAYS_0";
  public static final String EST_LIMO_LIVE_TRAFFIC_PEAK_HOUR_DAYS_0 =
      "EST_LIMO_LIVE_TRAFFIC_PEAK_HOUR_DAYS_0";
  public static final String EST_WAIT_TIME_PEAK_HOUR_DAYS_0 = "EST_WAIT_TIME_PEAK_HOUR_DAYS_0";
  public static final String EST_PEAK_HOUR_RATE_0 = "EST_PEAK_HOUR_RATE_0";
  public static final String DYNAMIC_PRICING_START_TIME_0 = "0_DYNAMIC_PRICING_START_TIME";
  public static final String DYNAMIC_PRICING_END_TIME_0 = "0_DYNAMIC_PRICING_END_TIME";
  public static final String DRV_EFFECT_FROM_TS_0 = "0_DRV_EFFECT_FROM_TS";
  public static final String DRV_EFFECT_TO_TS_0 = "0_DRV_EFFECT_TO_TS";
  public static final String DRV_SURGE_COLOR_HEX_0 = "0_DRV_SURGE_COLOR_HEX";
  public static final String DRV_SURGE_LEVEL_0 = "0_DRV_SURGE_LEVEL";
  public static final String DRV_SURGE_PERC_FROM_0 = "0_DRV_SURGE_PERC_FROM";
  public static final String DRV_SURGE_PERC_TO_0 = "0_DRV_SURGE_PERC_TO";
  public static final String V1 = "V1";
  public static final String V2 = "V2";
  public static final String V3 = "V3";
  public static final String V2_POINT_5 = "V2.5";
}

package com.cdg.pmg.ngp.me.dynamicpricing.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * Enum representing the types of mappings for surge computation model request fields. Used to
 * specify the type of configuration that a request parameter is mapped to.
 *
 * @see
 *     com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.impl.factory.ConfigurationProviderFactory
 * @see
 *     com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.impl.factory.ConfigurationDataProvider
 */
@Getter
@RequiredArgsConstructor
public enum MappingTypeEnum {
  /**
   * If there are new types here, be sure to add the corresponding implementation logic in {@link
   * com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.impl.factory.ConfigurationProviderFactory}
   */
  STATIC_TIME_BASED_CONFIGURATION("STATIC_TIME_BASED_CONFIGURATION"),
  STATIC_REGION_BASED_CONFIGURATION("STATIC_REGION_BASED_CONFIGURATION"),
  LIVE_STANDARD_INPUT("LIVE_STANDARD_INPUT");

  private final String value;
}

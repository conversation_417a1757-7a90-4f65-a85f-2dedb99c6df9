package com.cdg.pmg.ngp.me.dynamicpricing.entities;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DemandSupplyConfig implements Serializable {
  private String zoneId;
  private int totalDemand30;
  private int totalDemand60;
  private int predicted;
  private int totalFree;
  private int batchCounter;
  private int surgeLow;
  private int surgeHigh;
  private int stepPositive;
  private int stepNegative;
  private int excessDemand;
}

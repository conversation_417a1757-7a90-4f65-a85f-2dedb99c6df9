package com.cdg.pmg.ngp.me.dynamicpricing.entities;

import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PricingRangeConfigEntity implements Serializable {
  @Serial private static final long serialVersionUID = -2634196087293573042L;

  private Integer pricingRangeId;
  private Integer isEnabled;
  private Double startPrice;
  private Double endPrice;
  private Double step;
  private Integer refreshPeriod;
  private Integer quoteValidPeriod;
  private String day;
  private String hour;
  private Double stepPositive;
  private Double stepNegative;
  private Instant createdDate;
  private String createdBy;
  private Instant updatedDate;
  private String updatedBy;
}

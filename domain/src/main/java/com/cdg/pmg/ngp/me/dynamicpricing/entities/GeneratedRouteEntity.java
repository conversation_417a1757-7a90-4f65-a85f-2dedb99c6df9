package com.cdg.pmg.ngp.me.dynamicpricing.entities;

import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GeneratedRouteEntity implements Serializable {
  @Serial private static final long serialVersionUID = -4778556663299683307L;
  private LatLng pickupPoint;
  private LatLng intermediatePoint;
  private LatLng destinationPoint;
  private Long distance;
  private Long duration;
  private String encodedPolyline;
}

{"essential": true, "image": "<IMAGE1_NAME>", "logConfiguration": {"logDriver": "awsfire<PERSON>s", "options": {}}, "name": "{NAMESPACE}-{APPNAME}-{ENV}", "portMappings": [{"containerPort": "{CONTAINER_PORT}", "hostPort": "{CONTAINER_PORT}", "protocol": "tcp"}], "environment": [{"name": "DD_ENV", "value": "{ENV}"}, {"name": "DD_SERVICE", "value": "{NAMESPACE}-{APPNAME}"}, {"name": "DD_VERSION", "value": "0.1"}, {"name": "DD_RUNTIME_METRICS_ENABLED", "value": "true"}, {"name": "DD_AGENT_HOST", "value": "localhost"}, {"name": "DD_TRACE_AGENT_PORT", "value": "9529"}], "cpu": "{APP_CPU}", "memory": "{APP_MEMORY}"}
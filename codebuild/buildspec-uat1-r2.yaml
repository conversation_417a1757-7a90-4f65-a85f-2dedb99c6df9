version: 0.2

######################################
# DEV TEAM CAN MODIFY ENV BLOCK
# For App specific variable, Dev team can CRUD themself in this file env block.
# For more detail you can look into this guide: https://blog.shikisoft.com/define-environment-vars-aws-codebuild-buildspec/
######################################
env:
  # Plain text variable configuration (Dev team can modify this)
  variables:
    # This is the default value for ECS CPU and Memory configuration, Dev can modify this
    # NOTE:
    # These limits for a ECS task definition
    # To calculate this:
    # CPU = APP_CPU + (SIDECAR_CPU)*(NUMBER_OF_SIDE_CAR)
    # MEMORY = APP_MEMORY + (SIDECAR_MEMORY)*(NUMBER_OF_SIDE_CAR)
    CPU: 8192
    MEMORY: 16384

    # NOTE:
    # These limits are for the main application
    APP_CPU: 0
    APP_MEMORY: 12288

    # NOTE:
    # These are the limits for the sidecars
    # Currently we have 2 sidecars (might be more in future)
    SIDECAR_CPU: 1024
    SIDECAR_MEMORY: 2048
    CONTAINER_PORT: 8080

  # If you want to add variable from parameter store, do as follow:
  # parameter-store:
  #   S3_BUCKET: "my_s3_bucket"
  #   ORG_BUCKET: "/my_org/s3_bucket"
  #   DB_USERNAME: "db_username"

  # If you want to add app specific secret environment, do as follow:
  # secrets-manager:
  #   ParameterName: "<secret-name>:<secret-key>"

phases:
  ######################################
  # DO NOT MODIFY PRE_BUILD PHASE
  ######################################
  pre_build:
    on-failure: ABORT
    commands:
      # Logging into ECR
      - echo ________ Connecting to Amazon ECR ________
      - aws ecr get-login-password --region ap-southeast-1 | docker login --username AWS --password-stdin $ECR_URI

      # Caculate ECR Tag based on date
      - REPOSITORY_URI=$ECR_URI/$ECR_IMAGE_NAME
      - IMAGE_TAG=$(echo build_$(echo `date -d '+7 hours' +%F`)_$(echo `date -d '+7 hours' +%T`) | awk ' { gsub (":", ".")} 1 ')

      # Get the sidecar taskdef template from s3 bucket
      - echo ________ Downloading template taskdef.json ________
      - aws s3 sync $TEMPLATE_S3 ./codebuild/

  ######################################
  # DEV TEAM CAN MODIFY THIS BUILD PHASE ACCORDING TO THEIR APP REQUIREMENT
  ######################################
  build:
    on-failure: ABORT
    commands:
      - aws s3api get-object --bucket rediscloud-ca-cert --key redis_ca.pem redis_ca.pem
      # MODIFY YOUR DOCKER BUILD CODE HERE:
      - ./gradlew clean build
      - >
        docker build --platform linux/amd64
        --build-arg SONAR_TOKEN=$SONAR_TOKEN
        --build-arg SONAR_HOST=$SONAR_HOST
        --build-arg SONAR_PROJECT=$SONAR_PROJECT
        --build-arg SONAR_ORGANIZATION=$SONAR_ORGANIZATION
        --build-arg SONAR_BRANCH=$SONAR_BRANCH
        --build-arg ECR_URI=$ECR_URI
        --build-arg profile=$profile
        -t $REPOSITORY_URI:latest .

      # DOCKER TAG
      - docker tag $REPOSITORY_URI:latest $REPOSITORY_URI:$IMAGE_TAG

      # PUSHING DOCKER IMAGE TO ECR
      - echo ________ Pushing the Docker images ________
      - docker push $REPOSITORY_URI:latest
      - docker push $REPOSITORY_URI:$IMAGE_TAG

  ######################################
  # DO NOT MODIFY POST_BUILD PHASE
  ######################################
  post_build:
    commands:
      # Generate taskdef.json file
      - echo ________ Writing taskdef.json file ________
      - export CONTAINERDEF=$(cat ./codebuild/containerdef.json)
      - printf '%s' "$(jq ".containerDefinitions += [$CONTAINERDEF]" ./codebuild/taskdef.json)" > ./codebuild/taskdef.json

      - sed -i.bak -e 's/'"{NAMESPACE}"'/'$NAMESPACE'/g'                ./codebuild/taskdef.json
      - sed -i.bak -e 's/'"{ENV}"'/'$ENV'/g'                            ./codebuild/taskdef.json
      - sed -i.bak -e 's/'"{APPNAME}"'/'$APPNAME'/g'                    ./codebuild/taskdef.json
      - sed -i.bak -e 's/'"{SOURCE}"'/'$SOURCE'/g'                      ./codebuild/taskdef.json
      - sed -i.bak -e "s/\"{CONTAINER_PORT}\"/$CONTAINER_PORT/g"        ./codebuild/taskdef.json
      - sed -i.bak -e 's/'"{CPU}"'/'$CPU'/g'                            ./codebuild/taskdef.json
      - sed -i.bak -e 's/'"{MEMORY}"'/'$MEMORY'/g'                      ./codebuild/taskdef.json
      - sed -i.bak -e 's/'"\"{SIDECAR_CPU}\""'/'$SIDECAR_CPU'/g'        ./codebuild/taskdef.json
      - sed -i.bak -e 's/'"\"{SIDECAR_MEMORY}\""'/'$SIDECAR_MEMORY'/g'  ./codebuild/taskdef.json
      - sed -i.bak -e 's/'"\"{APP_CPU}\""'/'$APP_CPU'/g'                ./codebuild/taskdef.json
      - sed -i.bak -e 's/'"\"{APP_MEMORY}\""'/'$APP_MEMORY'/g'          ./codebuild/taskdef.json
      - sed -i.bak -e 's/'"{AWS_ACCOUNT_ID}"'/'$AWS_ACCOUNT_ID'/g'      ./codebuild/taskdef.json
      - sed -i.bak -e 's/'"{AWS_REGION}"'/'$AWS_REGION'/g'              ./codebuild/taskdef.json

      # Generate imageDetail.json file
      - echo ________ Writing imageDetail.json file ________
      - printf '{"ImageURI":"%s"}' $REPOSITORY_URI:$IMAGE_TAG  > imageDetail.json

      # Generate appspec.yaml file
      - echo ________ Writing appspec.yaml file ________
      - sed -i.bak -e 's/'"{NAMESPACE}"'/'$NAMESPACE'/g'                ./codebuild/appspec.yaml
      - sed -i.bak -e 's/'"{ENV}"'/'$ENV'/g'                            ./codebuild/appspec.yaml
      - sed -i.bak -e 's/'"{APPNAME}"'/'$APPNAME'/g'                    ./codebuild/appspec.yaml
      - sed -i.bak -e 's/'"{CONTAINER_PORT}"'/'$CONTAINER_PORT'/g'      ./codebuild/appspec.yaml

######################################
# DEV TEAM CAN MODIFY CACHE BLOCK
# Modify the path to the location you want to cache
# This default value is for caching maven library
######################################
cache:
  paths:
    - "/root/.gradle/caches/**/*"

######################################
# DO NOT MODIFY ARTIFACTS
######################################
artifacts:
  files:
    - imageDetail.json
    - ./codebuild/appspec.yaml
    - ./codebuild/taskdef.json
  discard-paths: yes
